sub DataInit() 
  m.top.debug= true 
  m.api.url = m.config.mfwk.host + "/services/user/modifycontrolpin"
  status_channel_pin= 1
  ' si ya tiene pin osea no es creacion de pin se debe traer su estado
  if m.top.isCreatePin= false then
    status_channel_pin= ghGetChild(m.global.status_pin,"pin_channel", invalid)
  end if
  m.api.query.Append({
    "api_version": ghGetChild(m.global.config, "api.version.StatusControlPin", m.global.config.api.versions.default),
    "region": ghGetRegistry("region")
    "status_channel_pin": status_channel_pin
    "status_parental_pin": ghGetChild(m.global.status_pin,"pin_parental",invalid)
    "status_purchase_pin": ghGetChild(m.global.status_pin,"pin_purchase",invalid)
    "pin": m.top.pin    
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- query="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")
  errors = ghGetChild(res, "errors")
  
  if m.top.debug then
    error = ghGetChild(errors, "error")
    print ghLogHead();"ProcessData -- body = ";res.entry
    print ghLogHead();"ProcessData -- response = ";response
    print ghLogHead();"ProcessData -- errors = ";errors
    print ghLogHead();"ProcessData -- error = ";error
  end if

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  msg = ghGetChild(response, "msg")
  ' si ta todo ok viene con ok

  m.top.content = { response: msg }
end sub