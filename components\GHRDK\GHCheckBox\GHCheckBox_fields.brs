' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

' texto
sub updateFieldText()
  if m.top.text <> invalid then m.label.text = m.top.text
end sub
' posicion, ancho y alto
sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldHeight()
  if m.top.width <> invalid then recalcLabelSizeAndPadding()
end sub

' align del texto
sub updateFieldHorizAlign()
  if m.top.horizAlign <> invalid then m.label.horizAlign = m.top.horizAlign
end sub
sub updateFieldVertAlign()
  if m.top.vertAlign <> invalid then m.label.vertAlign = m.top.vertAlign
end sub

' color del fondo
sub updateFieldBackColor()
  if m.top.backColor <> invalid then m.background.color = m.top.backColor
end sub

' color del texto
sub updateFieldColor()
  if m.top.color <> invalid then m.label.color = m.top.color
end sub

' padding
sub updateFieldPadding()
  if m.top.padding <> invalid then recalcLabelSizeAndPadding()
end sub

' utils ------------
sub Refresh()
  recalcLabelSizeAndPadding()
  recalcColors()
end sub

sub recalcLabelSizeAndPadding()
  ' general
  width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))
  padding = ghXToAbstract(val(m.top.padding))
  tilde_width = ghXToAbstract(48)
  tilde_height = ghXToAbstract(48)
  background_padding = ghXToAbstract(7)
  ' border
  m.border.translation = ghVal2Trans(padding, padding)
  m.border.width = tilde_width + (2 * background_padding)
  m.border.height = tilde_height + (2 * background_padding)
  ' tilde
  m.tilde.translation = ghVal2Trans(padding + background_padding, padding + background_padding)
  m.background.translation = ghVal2Trans(padding + background_padding, padding + background_padding)
  label_x = (padding * 2) + (background_padding * 2) + tilde_width
  label_y = padding
  m.label.translation = ghVal2Trans(label_x, label_y)
  m.label.width = width - (tilde_width + padding)
  m.label.height = height

  if m.top.debug then print ghLogHead();"recalc. ----------------------"
  if m.top.debug then print ghLogHead();"recalc: m.border.width=";m.border.width
  if m.top.debug then print ghLogHead();"recalc: m.border.height=";m.border.height
  if m.top.debug then print ghLogHead();"recalc: m.border.translation=";ghDumpTrans(m.border.translation)
  if m.top.debug then print ghLogHead();"recalc: m.tilde.translation=";ghDumpTrans(m.tilde.translation)
  if m.top.debug then print ghLogHead();"recalc: m.background.translation=";ghDumpTrans(m.background.translation)
  if m.top.debug then print ghLogHead();"recalc: m.label.translation=";ghDumpTrans(m.label.translation)
  if m.top.debug then print ghLogHead();"recalc: m.label.width=";m.label.width
  if m.top.debug then print ghLogHead();"recalc: m.label.height=";m.label.height
  if m.top.debug then print ghLogHead();"recalc. ----------------------"
end sub

sub recalcColors()
  if m.top.focus then
    m.border.visible = true
    m.background.color = m.top.selBackColor
    m.label.color = m.top.selColor
  else
    m.border.visible = false
    m.background.color = m.top.backColor
    m.label.color = m.top.color
  end if
end sub




'
'
' END FILE ------------------