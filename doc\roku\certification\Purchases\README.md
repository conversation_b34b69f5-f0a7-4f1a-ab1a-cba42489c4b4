# Purchases

## Status

2.1 Futuro

2.2 Futuro

2.3 Futuro

2.4 Futuro

2.5 Futuro

2.6 Futuro

2.7 Ok

2.8 Futuro



## Items

**2.1** Channels with transactional content (SVOD, TVOD, and other subscription services) must include an on-device [Roku Pay billing flow](https://developer.roku.com/docs/developer-program/roku-pay/implementation/overview.md). Partners must disclose integration/use of all third-party SDKs, libraries, or other software systems that enable payment transactions, and Roku has the right to approve or deny such third-party SDKs, libraries, or other software systems.

**2.2** For all subscription services that have streamed more than an average of 5 million hours per month over the last three months (and new subscription services projected to reach the specified streaming hour threshold shortly after launch): Channels must implement Roku Event Dispatcher (RED) in the signup workflow. A RED event must be fired upon loading each page within the signup flow and submission of the final page to help track where users are abandoning the process. This includes, but is not limited to, the following pages: landing, sign up, registration, device activation, subscription selection, payment, purchase confirmation, and cancellation. If the channel's sign up flow is contained within a form that covers one or more pages, a RED event must be fired when each element in the form is completed. See [Tracking signup abandonment](https://developer.roku.com/docs/developer-program/roku-pay/implementation/tracking-signup-abandonment.md). Streaming hours per month information is available in the Developer Dashboard.

**2.3** For authenticated transactional channels (SVOD, TVOD, and other subscription services): Channels must complete account sign-ups and sign-ins on the device using [On-device authentication](https://developer.roku.com/docs/developer-program/authentication/on-device-authentication.md), without visiting an external webpage. The sign-up and sign-in workflows may not include links to off-device promotional and marketing materials. AVOD and TVE channels are excluded from this requirement.

Channels must allow upgrades and downgrades to be completed on the device, without visiting an external webpage. See [On-device upgrade and downgrade](https://developer.roku.com/docs/developer-program/roku-pay/implementation/on-device-upgrade-downgrade.md).

**2.4** SVOD channels that have streamed more than an average of 10 million hours per month over the last three months must participate in [Roku’s Instant Signup program](https://developer.roku.com/docs/developer-program/discovery/instant-signup.md). This requirement is also applicable to new SVOD channels projected to reach the specified streaming hour threshold shortly after launch. Channels offering Premium Subscriptions on The Roku Channel are exempt from this requirement. Streaming hours per month information is available in the Developer Dashboard.

**2.5** Content or subscriptions through Roku Pay must be automatically entitled across all devices tied to the purchasing Roku account. The [getPurchases](https://developer.roku.com/docs/references/brightscript/interfaces/ifchannelstore.md#getpurchases-as-void) API can be used to return the transactionID for an active subscription on channel launch and an entitlement server can be put in place to look up an account via a transactionID.

**2.6** Content or subscriptions through Roku Pay must protect against multiple purchases before passing new orders to the Channel Store service. The Channel Store service inherently protects against purchasing the same subscription code multiple times, but preventing the purchase of a free trial subscription and a non-free trial subscription must be done in the channel. See [Best Practices for Roku Pay](https://developer.roku.com/docs/developer-program/roku-pay/roku-pay-best-practices.md).

**2.7** Content and Application Nesting Not Permitted: Channel does not include third-party content offerings, nor does it allow use of browsers or applications within the channel or cross-functionality with other Roku channels.

**2.8** SVOD channels must add any set of mutually exclusive subscription products to a product group (for example, if a channel offers both monthly and annual subscription products, for which a customer may only be subscribed to one at any given time, both monthly and annual subscriptions must be added to the same product group). (Required after March 31, 2021). See [In-channel purchases](https://developer.roku.com/docs/developer-program/roku-pay/quickstart/in-channel-products.md#adding-product-groups).****



