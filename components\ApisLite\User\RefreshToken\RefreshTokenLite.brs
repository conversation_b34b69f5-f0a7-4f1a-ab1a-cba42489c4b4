' RefreshTokenLite
' https://app.swaggerhub.com/apis/ClaroVideo/refreshtoken/1.0.0#/default/get_services_user_v1_refresh_token

' ======================================================
'
' OJO! ESTA LLAMA A LA COMUN !!!!
' si no, queda reentrante!!!!
' se llama a si misma desde el apiTask
'
' ======================================================

' -----------------------

sub DataInit()
  m.top.debug = true

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/v1/refresh_token"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })
  m.api.query.Append({
    "user_token": ghGetRegistry("user_token", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if

end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print "****************************************"
    print "API REFESHTOKEN"
    print "****************************************"
    print ghLogHead();"ProcessData -- Body= ";res
    if raw <> invalid then
      ' print ghLogHead();"ProcessData -- raw= ";raw
      print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
    else
      print "No hay raw"
    end if
    if res <> invalid then
      if res.experience <> invalid then
        print ghLogHead();"ProcessData -- experience= ";res.experience
      else
        print ghLogHead();"ProcessData -- No hay .experience"
      end if
    end if
    print "****************************************"
    print "****************************************"
  end if

  err = res.errors
  response = res.data
  if err <> invalid then ' dio un error
    print ghLogHead();"ProcessData -- ERROR = ";FormatJson(err)
    'm.top.error = err
    m.top.error = { error: "en refresh token" }

  else
    ghSetRegistry("user_token", response, "user")
    m.top.content = m.lastResponse
  end if
end sub
