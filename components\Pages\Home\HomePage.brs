sub Init()
  ' m.top.debug = true

  m.top.getScene().updateTheme = m.global.config.theme

  m.clientZone = m.top.findNode("clientZone")
  m.clientZone.ObserveField("itemFocused", "OnItemFocused")
  m.clientZone.ObserveField("contenido", "OnCardSelected")


  m.menu = m.top.findNode("menu")
  m.menu.ObserveField("backSelected", "OnButtonBack")
  m.menu.ObserveField("reloadHome", "handleReload")

  m.headerAlert = m.top.findNode("headerAlert")
  ' m.headerAlert.ObserveField("routerChild", "OnHeaderAlertRouting")


  m.showHome = true
  m.showLongOk = false

  ' -- MAPA DE COMPONENTES -------------------------|
  if m.headerAlert.visible then
    m.map = {
      "headerAlert": { "up": invalid, "right": invalid, "down": "menu", "left": invalid },
      "menu": { "up": "headerAlert", "right": invalid, "down": "clientZone", "left": invalid },
      "clientZone": { "up": "menu", "right": invalid, "down": invalid, "left": invalid },
    }
  else
    m.map = {
      "menu": { "up": "invalid", "right": invalid, "down": "clientZone", "left": invalid },
      "clientZone": { "up": "menu", "right": invalid, "down": invalid, "left": invalid },
    }
  end if

  setupLongOkMensaje()

  ' sacar el loading
  setLoading(false)

  '   testVideo()

  ' if m.global.SUS_plugin <> invalid then
  '   print "+++++++++++++++++++++ CHEQUEO EN LOGIN!!!!"
  '   m.global.SUS_plugin.tick = true
  ' end if

  m.Timer = CreateObject("roSGNode", "Timer")
  m.Timer.duration = 3
  m.Timer.repeat = false


  if m.global.channelProvider = invalid
    print ghLogHead(); "channelProvider update"
    ghCallApi("Channels", "onGetChannels", "onGetChannelsError")
  end if

  ' ROKUPAY
  ' m.pruebasRokuPay = CreateObject("roSGNode", "Timer")
  ' m.pruebasRokuPay.duration = -1
  ' m.pruebasRokuPay.repeat = false
  ' m.pruebasRokuPay.ObserveField("fire", "RokuPayTests")
  ' m.pruebasRokuPay.control = "start"
  ' ROKUPAY

end sub

sub timerFire()
end sub

sub onGetChannels()
  print ghLogHead();"onGetChannels success"
end sub

sub onGetChannelsError()
  print ghLogHead();"onGetChannels failed"
  ghCallApi("Channels")
end sub

sub setupLongOkMensaje()
  m.LongOkMensaje = CreateObject("roSGNode", "GHToastMessage")
  m.LongOkMensaje.setFields({
    id: "LongOkMensaje"
    time: -1 ' no termina
    ' translation: [640, 600] '[1280/2, alto...]
    ' X: 489px Y: 944px Width: 943px Height: 96px
    translation: [640, 880] '[1280/2, alto...]
    alignement: "center"
    separatorWidth: 10
    title: ghTranslate("generic_alert_pressAndHold_label", "Presiona y sostén")
    iconImage: ghGetAsset("", "pkg:/images/iconOK.png") 'Falta key
    ' iconImage: ghGetAsset("generic_alert_pressAndHold_img", "pkg://images/knotknot2.png")
    divisor: ""
    body: ghTranslate("deleteContent_alert_pressAndHold_label", "...")
  })
end sub

sub testVideo()
  m.DL = {
    contenttype: 1
    deeplinking: true
    id: "755521"
    idplayable: true
    mediatype: "movie"
  }
  m.tmrSeguir = CreateObject("roSGNode", "Timer")
  m.tmrSeguir.ObserveField("fire", "DLTmrSeguir")
  m.tmrSeguir.repeat = false ' una vez
  m.tmrSeguir.duration = 2 ' 1 sec
  m.tmrSeguir.control = "start"
end sub

sub onItemFocused(event)
  data = event.getData()

  m.showLongOk = false

  cinta = data.cinta
  primerItem = cinta.getChild(0)

  m.LongOkMensaje.translation = [640, 600]
  if ghGetChild(primerItem, "data.rowtype", "") = "Carrouselvertical" then
    m.LongOkMensaje.translation = [640, 760]
  end if

  if LCase(cinta.longoktype) = "seen" or LCase(cinta.longoktype) = "favorited" or LCase(cinta.longoktype) = "favoritedlive" then
    ' al iniciar vienen 4 items vacios, que despues se llenan
    ' ahi vuelve con un item con id 'empty', y no se muestra el mensaje
    if primerItem.id <> invalid and primerItem.id <> "empty" then
      m.showLongOk = true ' para al volver a la pantalla, mostrar el mensaje
      if m.showHome = true then
        showLongOkMensaje()
      end if
    else
      hideLongOkMensaje()
    end if
  else
    hideLongOkMensaje()
  end if
end sub

sub handleFavoriteSelected(event)
  root = event.getRoSGNode()
  data = event.getData()

  if data <> invalid then
    if LCase(root.longOkType) = "seen" then
      m.logger.debug("eliminando continuar viendo", { data: data })

      apiFavorite = ghCallApi("SeenDel", "handleEliminarItemOk", invalid, false)
      apiFavorite.group_id = data
      apiFavorite.control = "run"
    else if LCase(root.longOkType) = "favorited" or LCase(root.longoktype) = "favoritedlive" then
      m.logger.debug("eliminando favorito", { data: data })

      apiFavorite = ghCallApi("Favorite", "handleEliminarItemOk", invalid, false)
      apiFavorite.add = false
      if LCase(root.longoktype) = "favoritedlive" then
        apiFavorite.isChannel = true
      end if
      apiFavorite.group_id = data
      apiFavorite.control = "run"
    end if
  end if
end sub

sub handleEliminarItemOk()
  m.clientZone.reloadLevelUser = true
end sub

sub handleNodes(event)
  data = event.GetRoSGNode()
  print "llega a handdlenodes"
  print data

  if data.refresh = true then
    m.menu.refresh = true

    if data.node_id <> "" then
      m.clientZone.node = ghGetChild(data, "node_id", "")

    else
      m.clientZone.reloadLevel = true
      m.clientZone.reloadLevelUser = true

    end if
  end if

  'si refresh es true, entro acá, y luego si node_id tiene algo. voy al nodo
  'si node_id es inválido, o no tiene info, tengo que ir a la home
  'if data.refresh = true then
  '  handleReload(event)
  'm.clientZone.reloadLevel = true
  'if data.node_id <> "" then
  '  pagina = CreateObject("roSGNode", "HomePage" + m.versionSuffix)
  '  pagina.id = "HomePage02"
  '  m.top.routerChild = {
  '    page: pagina,
  '    fields: {
  '      title: getNavChildByCode(ghGetChild(data, "node_id", ""))
  '      nodo: ghGetChild(data, "node_id", "")
  '      order: 1
  '    }
  '  }
  'end if
  'end if
end sub

sub onWasShown() ' event
  ' data = event.getData()
  if m.top.debug then print ghLogHead();"onWasShown *** "
  m.top.routerAddRouterTo = m.menu ' habilito al menu a usar router
  m.showHome = true
  turnFocusTo("clientZone")
end sub

sub OnTitleChange(event)
  title = event.getData()

  m.clientZone.title = title
end sub

sub OnNodoChange(event)
  m.node = event.getData()

  if m.node = "" then
    m.node = m.menu.menuSelected
  else
    m.global.setFields({ navSelect: m.node })
    m.menu.refresh = true
  end if

  ghCallApi("LastTouch", "propagateNodeChange") ' actualizo lasttouch

end sub

sub propagateNodeChange()
  m.clientZone.node = m.node
end sub

function getNavChildByCode(code)
  if code = "" then
    return ""
  end if

  for each item in m.global.nav.childs
    if item.code = code then
      return item.text
    end if
  end for

  return ""
end function

sub pressOk(data, info)
  m.logger.debug("pressOk", { data: data, info: info })

  if LCase(info.longoktype) = "seen" or LCase(info.longoktype) = "favorited" or LCase(info.longoktype) = "favoritedlive" then
    group_id = ghGetChild(data, "group_id", "")
    if group_id = "" then
      group_id = ghGetChild(data, "id", "")
    end if

    favorite = CreateObject("roSGNode", "GHFavoriteRemove")
    favorite.UnobserveField("selected")
    favorite.ObserveField("selected", "handleFavoriteSelected")

    m.top.routerChild = {
      page: favorite,
      fields: {
        carouselId: info.carouselId
        group_id: group_id
        title: ghGetChild(data, "title")
        description: ghGetChild(data, "description")
        image: ghGetChild(data, "image_small")
        longoktype: info.longoktype
      }
    }
  end if
end sub

sub itemSelected(data, info)
  m.logger.debug("itemSelected", { data: data, info: info })
  content_list = ghGetChild(info, "carousetitle", "")

  ' si viene de deepLink, para ir a canal o trailer
  if ghGetChild(data, "deeplinking", false) then
    if ghGetChild(data, "contentType") = 6 then
      data.live_enabled = 1
    else if ghGetChild(data, "contentType") = 7 then
      data.trailer = true
    end if
  end if

  'nodos 3er nivel
  if ghGetChild(data, "rowtype", "") = "Listadoinfinito" and content_list = "" then
    content_list = ghGetChild(info, "curr_filter_title", "")
  end if

  if content_list = "" then
    content_list = ghGetChild(info, "carouselid", "")
  end if

  infoEvent = {
    screen_name: "content selection"
    screen_class: "/content-selection"
    content_section: getContentSectionName(m.node) ' seccion (home start, home movies, home my content, etc)
    content_id: data.product_id, ' !
    content_name: data.title ' ghGetChild(data, "title", "")
    content_type: "movie"
    content_category: "not apply"
    user_type: getUserTypeGA4(true),
    country: ghGetRegistry("country_code", "user"),
    content_availability: "by subscription"
    content_list: getContentListFormatName(content_list) ' nombre carousel
    user_id: ghGetRegistry("user_id", "user"),

    'content_brand: data.producttype
    'content_price: data?.price?.amount
  }

  if ghGetChild(data, "rowtype", "") = "PlansOfferV2" then
    m.logger.debug("soy PlansOfferV2")
    ' GA4
    infoEvent.content_name = ghGetChild(data, "producttype")
    infoEvent.content_type = "tv channel"

    infoEventPlanSelector = {
      content_id: data.product_id,
      content_name: ghGetChild(data, "producttype"),
      content_type: "not apply",
      content_category: "not apply",
      'content_section: getContentSectionName(m.node),
      content_availability: "by suscription addon",
      content_episode: "not apply",
      content_brand: ghGetChild(data, "producttype"),
      content_list: getContentSectionName(m.node), 'LCase(content_list),
      content_price: ghGetChild(data, "price.amount")'116.00', -- precio disponible en los datos
      user_type: getUserTypeGA4(true),
      country: getCountryCode(ghGetRegistry("country_code", "user")),
      user_id: ghGetRegistry("user_id", "user"),
      screen_name: "content selection",
      screen_class: "/content-selection"
    }

    GA4Event("plan_selector", infoEventPlanSelector)

    doPlansOfferV2(data)
  else if ghGetChild(data, "rowtype", "") = "PremiumImage" then
    m.logger.debug("soy PremiumImage")

    pagina = CreateObject("roSGNode", "HomePage" + m.versionSuffix)
    pagina.id = "HomePageNode"
    nodo = ghGetChild(data, "properties.section", "")
    m.top.routerChild = {
      page: pagina,
      fields: {
        title: getNavChildByCode(nodo)
        nodo: nodo
        order: 1
      }
    }
  else if ghGetChild(info, "carouselId", "") = "Plan-selector" then
    m.logger.debug("soy Plan-selector")

    ' GA4 --
    infoEvent.content_name = info?.data
    infoEvent.content_type = invalid

    buyView = CreateObject("roSGNode", "BuyViewV2")
    buyView.id = "BuyView"
    ' crear otro que no capture error
    buyView.ObserveField("wasClosed", "handleNodes")
    m.top.routerChild = {
      page: buyView,
      fields: {
        buyB: ghGetChild(info, "data.data", {})
        accessCode: {
          enabled: false
          msgAccessCode: "Protege tus pagos y activa el control parental con tu PIN de protección. Configúralo desde tu computadora en el menú de tu perfil."
        }
        data: {}
      }
    }
    ' si es un canal, ir a live
  else if ghGetChild(data, "channel_number", "") <> "" or ghGetChild(data, "live_enabled", "0") = "1" then
    m.logger.debug("soy live")

    livePage = CreateObject("roSGNode", "LivePage")
    livePage.id = "Live"
    livePage.ObserveField("reloadHome", "handleReload")

    m.top.routerChild = {
      page: livePage,
      fields: { group_id: ghGetChild(data, "group_id", ghGetChild(data, "id", "")) }
    }
  else if ghGetChild(data, "rowtype", "") = "navchilds" then
    m.logger.debug("soy navchilds")

    pagina = CreateObject("roSGNode", "HomePage" + m.versionSuffix)
    pagina.id = "HomePageChilds"
    m.top.routerChild = {
      page: pagina,
      fields: {
        title: ghGetChild(data, "title", "")
        nodo: ghGetChild(data, "data.code", "")
        order: 1
      }
    }
  else if ghGetChild(data, "type", "") = "node" then
    m.logger.debug("soy node")

    pagina = CreateObject("roSGNode", "HomePage" + m.versionSuffix)
    pagina.id = "HomePageNode"
    m.top.routerChild = {
      page: pagina,
      fields: {
        title: getNavChildByCode(ghGetChild(data, "section", ""))
        nodo: ghGetChild(data, "section", "")
        order: 1
      }
    }
  else if ghGetChild(data, "id") <> invalid or ghGetChild(data, "group_id") <> invalid then
    m.logger.debug("soy vcard")

    vcard = CreateObject("roSGNode", "Vcard")
    vcard.id = "Vcard"
    vcard.ObserveField("reloadHome", "handleReload")
    vcard.ObserveField("reloadHomeUser", "handleReloadUser")

    contenido = data

    group_id = ghGetChild(data, "group_id", "")
    if group_id = "" then
      group_id = ghGetChild(data, "id", "")
    end if

    m.logger.debug("group_id", { group_id: group_id })

    ' si viene mal la informacion (ej: highlight)
    isSerie = ghGetChild(contenido, "is_series", false)

    ' group es pelicula o serie live
    'node
    if isSerie = false then
      seasonNumber = ghGetChild(contenido, "season_number", invalid)
      if seasonNumber <> invalid then
        isSerie = true
      end if
    end if
    if contenido.contentType = invalid then
      contenido.contentType = 1
      if isSerie = true then
        contenido.contentType = 2
      end if
    end if
    ' ==========================================

    contenido.groupId = group_id

    ' enviando a google analytics
    data = contenido

    ' por las dudas apago el mensaje
    hideLongOkMensaje()

    ' m.logger.debug("llamo a vcard con contenido", { contenido: contenido })
    m.logger.debug("llamo a vcard con contenido")

    m.showHome = false
    m.top.routerChild = {
      page: vcard,
      fields: {
        data: contenido
      }
    }
  end if

  isSerie = ghGetChild(data, "is_series", false)
  liveType = ghGetChild(data, "live_type", "0")
  section = ghGetChild(data, "section")
  episode_number = ghGetChild(data, "episode_number", "")
  season_number = ghGetChild(data, "season_number", "")
  typeContent = ghGetChild(data, "type")

  contentType = "movie"
  if liveType = "1" then
    contentType = "tv chanel"
  else if isSerie = true or episode_number <> "" or season_number <> "" then
    contentType = "series"
    'else if episode_number <> false and season_number <> false then
    '  contentType = "tv chanel"
  end if

  ' ga4 event config
  contentId = data.group_id
  if contentId = invalid and data.groupId <> invalid
    contentId = data.groupId
  else if contentId = invalid and data.id <> invalid
    contentId = data.id
  end if

  order = ghGetChild(data, "order", 1) - 1
  ' ga4 event config
  infoEvent.content_id = contentId
  infoEvent.content_type = contentType
  infoEvent.content_availability = "not apply"
  infoEvent.content_position = order
  infoEvent.content_section = getContentSectionName(m.node)
  GA4Event("select_content", infoEvent)

end sub

sub OnCardSelected(event)
  print ghLogHead("DL");"OnCardSelected *** "

  info = event.getData()

  data = ghGetChild(info, "data.data")

  ' si viene de deepLinking
  if data = invalid then
    data = info
  end if

  ' cuando esta vacio, en realidad tiene un item
  ' pero diferencio con el rowtype
  if data <> invalid and data.rowtype <> "emptywithmessage" then
    hideLongOkMensaje()

    if info.pressLong = true then
      pressOk(data, info)
    else
      itemSelected(data, info)
    end if
  else
    print ghLogHead("DL");"OnCardSelected *** NO ACTION."

    ' cuando capturo el foco siempre entra por aca
    if m.showLongOk = true then
      if m.showHome = true then
        showLongOkMensaje()
      end if
    end if

  end if
end sub

sub handleReloadUser(event)
  obj = event.getRoSGNode()

  m.logger.debug("Tengo que recargar userLevel")

  m.clientZone.reloadLevelUser = true
end sub

sub handleReload(event)
  obj = event.getRoSGNode()

  m.logger.debug("recargo toda la home")

  m.menu.refresh = true

  m.Timer.ObserveField("fire", "levelAfterLogin")
  m.Timer.control = "start"
end sub

sub levelAfterLogin()
  m.logger.debug("levelAfterLogin")

  node = m.menu.callFunc("getMenuSelected", { test: "bien" })
  m.clientZone.node = node
end sub

function onKeyEvent(key, press) as boolean
  handled = false

  if press then
    m.logger.debug("keyEvent: ", { key: key })
    if key <> "back" then
      changeFocusBasedOnKey(key)

      handled = true
    else if m.top.order = 0 then ' si no estoy en segundo nivel
      m.logger.debug("keyEvent back a menu")
      hideLongOkMensaje()
      handled = true
    end if
  end if

  return handled
end function

sub OnDeepLink(event)
  data = event.getData()
  contentTypeValue = getContentTypeValue(data.mediatype)
  if (contentTypeValue <> data.contenttype) then
    data.contenttype = contentTypeValue
  end if

  m.logger.debug("OnDeepLink", { data: data })

  if data <> invalid then
    m.DL = data
    m.tmrSeguir = CreateObject("roSGNode", "Timer")
    m.tmrSeguir.ObserveField("fire", "DLTmrSeguir")
    m.tmrSeguir.repeat = false ' una vez
    m.tmrSeguir.duration = 1 ' 1 sec
    m.tmrSeguir.control = "start"
  end if
end sub

sub DLTmrSeguir()
  m.logger.debug("DLTmrSeguir", { dl: m.DL })

  m.clientZone.setFields({
    node: ghGetChild(m.global, "navSelect", "homeuser") ' ivan puede ir la  _lite
    contenido: m.DL
  })
end sub

' SALIDA
' -----------------------------
sub On_msgDialog_buttonSelected() ' event as object
  dialog = m.top.GetScene().dialog
  ' cerrar dialog
  if dialog <> invalid then dialog.close = true
  if dialog.buttonSelected = 0 then
    ' ir a perfil
  else if dialog.buttonSelected = 1 then
    m.top.GetScene().exitChannel = true
  end if
end sub

sub OnButtonBack()
  dialog = createObject("roSGNode", "Dialog")
  dialog.ObserveField("buttonSelected", "On_msgDialog_buttonSelected")
  dialog.title = "Seguro que desea salir"
  ' dialog.message = "Press * To Dismiss"
  dialog.iconUri = ghGetImageByMode("")
  dialog.dividerUri = ghGetImageByMode("Divider-separator-line.png")
  dialog.titleColor = "#FFFFFF"
  dialog.optionsDialog = true
  dialog.buttons = ["No", "Si"]
  dialog.buttonGroup.focusBitmapUri = ghGetImageByMode("Keyboard-focus01.9.png")
  dialog.buttonGroup.focusedIconUri = ghGetImageByMode("")
  dialog.buttonGroup.iconUri = ghGetImageByMode("")
  dialog.buttonGroup.focusedTextColor = "0xFEFEFEFF"
  dialog.buttonGroup.textColor = "0xFEFEFEFF"
  dialog.backgroundUri = ghGetImageByMode("PopUp-Notification.9.png")

  m.top.GetScene().dialog = dialog
end sub

sub showLongOkMensaje()
  if m.top.debug then print ghLogHead();"showLongOkMensaje"

  m.global.WIDGET.run = { cmd: "add", componente: m.LongOkMensaje }

  if m.top.debug then print ghLogHead();"showLongOkMensaje -- Ya esta prendido"
end sub

sub hideLongOkMensaje()
  if m.top.debug then print ghLogHead();"hideLongOkMensaje"

  m.global.WIDGET.run = { cmd: "remove", id: "LongOkMensaje" }
end sub

sub doPlansOfferV2(data)
  setLoading(true)

  m.product_id = data.product_id
  offersPBI = ghCallApi("PbiLite", "PbiLiteOk", "PbiLiteError")
  offersPBI.setFields({
    object_type: "A" ' subscripciones no compradas
  })
  offersPBI.control = "run"
end sub

sub PbiLiteOk(event)
  data = event.getData()
  botones = ghGetChild(data, "listButtons.button")

  ' encuentro el boton correcto
  buyB = getCurrentButton(m.product_id, botones)
  ' llamo a la BuyVieLiute
  buyView = CreateObject("roSGNode", "BuyViewV2") ' TODO
  buyView.id = "offersV2"
  buyView.ObserveField("wasClosed", "handleNodes")

  setLoading(false)
  m.top.routerChild = {
    page: buyView,
    fields: {
      buyB: buyB
      data: data
      object_type: "A"
    }
  }
end sub

sub PbiLiteError()
  setLoading(false)
end sub

function getCurrentButton(product_id, buttons)
  res = invalid

  cant = buttons.Count()
  for b = 0 to cant - 1
    if buttons[b].product_id = product_id then
      res = buttons[b]
    end if
  end for

  return res
end function

sub onGetCatalog(event)
  data = event.getData()

  m.logger.debug("onGetCatalog ejecutado", { data: data })
  print data
  print "======"

  cant = data.getChildCount()
  for p = 0 to cant - 1
    child = data.getChild(p)

    print child
  end for

  print "fin catalogo"
end sub

sub onOrderStatus(event)
  data = event.getData()

  m.logger.debug("onOrderStatus ejecutado", { data: data })
  print data
end sub

sub onGetPurchases(event)
  data = event.getData()

  m.logger.debug("onGetPurchases ejecutado", { data: data })

  cant = data.getChildCount()
  for p = 0 to cant - 1
    child = data.getChild(p)

    print child
  end for

  print "fin purchases"
end sub

sub onGetUserData(event)
  data = event.getData()

  m.logger.debug("onGetUserData ejecutado")
  print data
end sub

sub onStoreChannelCredData(event)
  data = event.getData()

  m.logger.debug("onStoreChannelCredData ejecutado", { data: data })
  print data
end sub

sub onGetChannelCred(event)
  data = event.getData()

  m.logger.debug("onGetChannelCred ejecutado", { data: data })
  print data
end sub

sub onDoRecovery(event)
  data = event.getData()

  m.logger.debug("onDoRecovery ejecutado", { data: data })

  print ghGetChild(data, "result")
  print ghGetChild(data, "result.recoveryProducts")

  cant = ghGetChild(data, "result.recoveryProducts", []).Count()
  for p = 0 to cant - 1
    child = data.getChild(p)

    print child
  end for
end sub

sub RokuPayTests()
  m.store = CreateObject("roSGNode", "ChannelStore")
  m.store.ObserveField("catalog", "onGetCatalog")
  m.store.observeField("orderStatus", "onOrderStatus")
  m.store.ObserveField("purchases", "onGetPurchases")
  m.store.observeField("requestStatus", "onDoRecovery")
  m.store.ObserveField("userData", "onGetUserData")
  m.store.ObserveField("storeChannelCredDataStatus", "onStoreChannelCredData")
  m.store.ObserveField("channelCred", "onGetChannelCred")

  ' m.store.command = "getPurchases"
  ' m.store.command = "getAllPurchases"
  m.store.command = "getCatalog"

  ' m.store.command = "getChannelCred"
  ' info = CreateObject("roSGNode", "ContentNode")
  ' info.addFields({ context: "signin" })
  ' m.store.requestedUserDataInfo = info
  ' m.store.requestedUserData = "email"
  ' m.store.command = "getUserData"

  ' myOrder = CreateObject("roSGNode", "ContentNode")
  ' itemPurchased = myOrder.createChild("ContentNode")
  ' itemPurchased.addFields({ "code": "MX70004153", "name": "Claro Video Mensual", "qty": 1 })
  ' m.store.order = myOrder
  ' m.store.command = "doOrder"

  ' request = {
  '   command: "DoRecovery"
  ' }
  ' m.store.request = request
  ' m.store.command = "DoRecovery"

  ' m.store.channelCredData = "test"
  ' m.store.command = "storeChannelCredData"




  ' print ">>>>>>>>>>>>>> ROKUPAY PRUEBAS DE PANTALLAS"
  ' ' ROKUPAY PRUEBAS DE PANTALLAS
  ' ' -----------------------------------
  ' if m.global.RPAY <> invalid then
  '   print " "
  '   print " "
  '   print " "
  '   print ">>>>>>>>>>>>>>> TENGO RPAY >>>>>>>>>>> INI"
  '   ' TEST DE PANTALLAS
  '    m.global.RPAY.cmd = {
  '      cmd: "testScreenOngrace"
  '      data: {}
  '    }
  '   ' m.global.RPAY.cmd = {
  '   '   cmd: "testScreenSuspended"
  '   '   data: {}
  '   ' }
  '   ' m.global.RPAY.cmd = {
  '   '   cmd: "testScreenCanceled"
  '   '   data: {}
  '   ' }

  '   m.global.RPAY.ObserveField("cmdResult", "pruebaResult")
  '   m.global.RPAY.cmd = {
  '     cmd: "verifyPurchase"
  '     data: {
  '       id: "c616a11e-04c7-11f0-83c2-caf47146c639"
  '     }
  '   }

  '   ' TEST DE DORECOVERY
  '   ' m.global.RPAY.ObserveField("cmdResult", "pruebaResult")
  '   ' m.global.RPAY.cmd = {
  '   '   cmd: "doRecovery"
  '   '   data: {}
  '   ' }
  '   print ">>>>>>>>>>>>>>> TENGO RPAY >>>>>>>>>>> END"
  '   print " "
  '   print " "
  '   print " "
  ' end if
  ' -----------------------------------
  ' ROKUPAY PRUEBAS DE PANTALLAS
end sub
sub pruebaResult(event)
  data = event.getData()
  m.global.RPAY.UnobserveField("cmdResult")
  print " "
  print " "
  print " "
  print " "
  print "pruebaResult"
  print "------------------------------"
  print data
  print "------------------------------"
  'if data.cmd = "verifyPurchase" then
  'print data.status
  'print data.purchase
  'print "--data------------------------"
  'print data.purchase.data
  'print "--check-----------------------"
  'print data.purchase.check
  'print "------------------------------"
  'end if
  print " "
  print " "
  print " "
  print " "
end sub
function getContentTypeValue(mediaType)
  m.logger.debug("getNextPropertyIndex", { mediaType: mediaType })

  supported = {
    "series": 2,
    "season": 3,
    "episode": 4,
    "audio": 5,
    "live": 6,
    "tvSpecial": 6,
    "shortFormVideo": 7
  }

  value = supported[mediaType]

  if value <> invalid
    return value
  end if

  return 1
end function
