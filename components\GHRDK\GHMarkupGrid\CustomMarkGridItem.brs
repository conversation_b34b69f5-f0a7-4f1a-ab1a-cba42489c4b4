sub Init()
    m.box = m.top.findNode("box")
    m.customLayout = m.top.findNode("customLayout")
end sub

sub itemContentChanged()
    itemContent = m.top.itemContent
    m.box.height = m.top.height
    m.box.width = m.top.width
    if itemContent.title <> ""
        centerText1 = m.customLayout.createChild("label")
        centerText1.setFields({
            id: "centeredText1", text: itemContent.title, horizAlign: "center", vertAlign: "center", color: "0xFFFFFFFF", font: ghGetFont(handlingSizeForHD(36), "regular"), width: m.top.width - 4
        })
    end if
    if itemContent.description <> ""
        centerText2 = m.customLayout.createChild("label")
        centerText2.setFields({
            id: "centeredText2", text: itemContent.description, horizAlign: "center", vertAlign: "center", color: "0xFFFFFFFF", font: ghGetFont(handlingSizeForHD(36), "regular"), width: m.top.width - 4
        })
    end if
    if itemContent.shortDescriptionLine1 <> ""
        centerText3 = m.customLayout.createChild("label")
        centerText3.setFields({
            id: "centeredText3", text: itemContent.shortDescriptionLine1, horizAlign: "center", vertAlign: "center", color: "0xFFFFFFFF", font: ghGetFont(handlingSizeForHD(36), "regular"), width: m.top.width - 4
        })
    end if
    yAxis = (m.top.height - m.customLayout.boundingRect().height) / 2
    m.customLayout.translation = [2, yAxis]

    if itemContent.title <> ""
        if itemContent.id = "year"
            centerText1.font = ghGetFont(handlingSizeForHD(36), "regular")
        else if itemContent.id = "month"
            centerText1.font = ghGetFont(handlingSizeForHD(36), "regular")
        else if itemContent.id = "country"
            centerText1.font = ghGetFont(handlingSizeForHD(33), "regular")
        else if itemContent.id = "service"
            centerText1.font = ghGetFont(handlingSizeForHD(36), "regular")
        end if
    end if
end sub



