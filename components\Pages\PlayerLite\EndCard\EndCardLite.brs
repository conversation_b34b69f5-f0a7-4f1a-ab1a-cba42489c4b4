' ENDCARD
' ------------------------

sub Init()
  ' focusedChild used internally to handle first time shown - what item on View should be shown
  ' m.top.ObserveField("focusedChild", "OnFocusedChildChange")
  ' posterShape is passed to Grid
  ' m.top.posterShape = "16x9"
  ' m.top.ObserveField("posterShape", "OnPosterShape")
  overhang = m.top.FindNode("overhang")
  m.top.RemoveChild(overhang)
  m.top.ObserveField("content", "OnContentChanged") ' content field is node passed directly to grid
  m.isEndcardFirstFocus = true
  m.time = 0
  if m.LastThemeAttributes <> invalid then
    SGDEX_SetBackgroundTheme(m.LastThemeAttributes)
  end if

  ' -----------------------
  ' NUEVO
  ' -----------------------
  m.botonera = m.top.findNode("botonera")
  m.image = m.top.findNode("image")
  m.gradient = m.top.findNode("gradient")
  m.gradient.uri = ghGetImageByMode ("FinPlayerGradient.png")
  'm.conteiner = m.top.findNode("conteiner")
  'm.conteiner.uri = ghGetImageByMode("FinPlayerbg.png")
  'm.backArrow = m.top.findNode("backArrow")
  'm.backArrow.uri = ghGetImageByMode("finplayer-back_icon.png")
  ' m.btnCancel = m.top.findNode("btnCancel")
  m.btnNext = m.top.findNode("btnNext")
  m.contador = m.top.findNode("contador")
  ' eventos
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnBackSelected")
  m.top.ObserveField("visible", "OnShow")
  ' contador
  m.txtContador = "Comienza en {@SEG} segundos"
  m.contador.font = ghGetFont(18, "bold")
  m.description = m.top.findNode("description")
  m.description.font = ghGetFont(18, "regular")
  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(22, "medium")
  m.recomendacion = m.top.findNode("recomendacion")
  m.recomendacion.font = ghGetFont(22, "bold")
  'm.cerrar = m.top.findNode("cerrar")
  'm.cerrar.font = ghGetFont(18, "bold")
  ' timer
  m.tmrSeguir = CreateObject("roSGNode", "Timer")
  m.tmrSeguir.ObserveField("fire", "tmrTriggerSeguir")
  m.tmrSeguir.repeat = true ' chequea varias veces...
  m.tmrSeguir.duration = 1 ' 1 sec
  m.tmrCount = 10
  ' -----------------------
end sub
' CREATE
sub onCommonChange() ' event
  if m.top.debug then print ghLogHead();"onCommonChange."
  refreshEndCard()
end sub
sub refreshEndCard()
  print ghLogHead();"refreshEndCard -- REDIBUJO!"
  ' print m.top.common, print m.top.common.extendedcommon
  m.image.loadSync = false
  m.image.width = 240
  m.image.height = 129
  m.image.loadDisplayMode = "scaleToZoom"
  m.image.uri = ghGetChild(m.top.common, "image_small", "pkg:/images/loading.png") ' 290x163
  m.title.text = "Temp. " + ghGetChild(m.top.common, "extendedcommon.media.episode.season", "") + " | Ep. " + ghGetChild(m.top.common, "extendedcommon.media.episode.number", "") + ": " + ghGetChild(m.top.common, "title", "") 'no anda, falta concatenar el formato
  m.description.text = ghGetChild(m.top.common, "description")
  m.recomendacion.text = ghGetChild(m.top.common, "extendedcommon.media.serie.title")
  setTimerCount()
end sub
sub setTimerCount()
  if m.top.debug then print "*************************************"
  m.tmrCount = ghGetChild(m.top.common, "timerbuttontime", -1)
  m.contador.text = ghReplaceStr(m.txtContador, "{@SEG}", m.tmrCount.toStr())
  if m.top.debug then print ghLogHead();"setTimerCount -- tmrCount= ";m.tmrCount
  if m.top.debug then print "*************************************"
end sub
' FOCUS
sub OnShow() ' event
  if m.top.debug then print ghLogHead();"OnShow -- ";m.top.visible
  if m.top.visible then
    if m.top.debug then print ghLogHead();"OnShow -- Jumping"
    ghFocusJumpTo("botonera")
    m.top.wasClosed = false ' reinicio
    m.top.value = invalid
    setTimerCount()
    m.tmrSeguir.control = "start"
  else
    if m.top.debug then print ghLogHead();"OnShow -- Invisible!"
  end if
end sub
' BOTONERA
sub OnButtonSelected() ' event
  if m.top.debug then print ghLogHead();"OnButtonSelected -- boton=";m.botonera.value
  if m.botonera.value = "btnNext" then
    ' m.top.visible = false
    closeToNext()
    ' else if m.botonera.value = "btnCancel" then
    '   m.top.value = "CANCEL"
    '   closeToVideo()
  end if
end sub
sub OnBackSelected() ' event
  if m.botonera.backSelected then
    if m.top.debug then print ghLogHead();"OnBackSelected -- boton=";m.botonera.value
    m.top.value = "CANCEL"
    closeToVideo()
  else
    if m.top.debug then print ghLogHead();"OnBackSelected -- cambio a no selected"
  end if
end sub
sub closeToNext()
  m.tmrSeguir.control = "stop"
  m.top.value = ghGetChild(m.top.common, "id")
  m.top.visible = false
end sub
sub closeToVideo()
  if m.top.debug then print ghLogHead();"closeToVideo -- "
  m.tmrSeguir.control = "stop"
  m.top.visible = false
  m.top.wasClosed = true
end sub
sub tmrTriggerSeguir()
  if m.top.debug then print ghLogHead();"tmrTriggerSeguir -- entrada ";m.tmrCount
  if m.tmrCount > 0 then
    m.tmrCount -= 1 ' contamos
    if m.top.debug then print ghLogHead();"tmrTriggerSeguir -- counting ";m.tmrCount
    m.contador.text = ghReplaceStr(m.txtContador, "{@SEG}", m.tmrCount.toStr())
    if m.tmrCount < 1 then
      if m.top.debug then print ghLogHead();"tmrTriggerSeguir -- NEXT!"
      m.tmrSeguir.control = "stop"
      closeToNext()
    end if
  else
    m.tmrSeguir.control = "stop"
  end if
end sub
sub SGDEX_SetBackgroundTheme(theme as object)
  if GetInterface(theme.backgroundImageURI, "ifString") <> invalid then
    colorTheme = { backgroundImageURI: { backgroundImage: "uri" } } ' don't use backgroundColor for blending color as it's used for other Views so developers don't want it to be applied to this View
  else
    colorTheme = { backgroundColor: { topRectangle: "color" } }
  end if
  colorTheme.Append({
    endcardGridBackgroundColor: { bottomRectangle: "color" }
  })
end sub
function SGDEX_GetViewType() as string
  return "videoView"
end function
