sub onGetPreferences(event)
	data = event.getData()

	userLang = {
		audio: ghGetRegistry("preference_live_audio", "user"),
		subtitle: ghGetRegistry("preference_live_subtitle", "user"),
	}

	m.logger.debug("Idioma de usuario: ", { lang: userLang })

	if data <> invalid and ghGetChild(data, "audio", "") <> "" then
		userLang = data
	end if

	m.logger.debug("Idioma de live: ", { data: data })

	langsFiltered = ghGetChild(m.dataContent, "group.common.extendedcommon.media.language.options.option", [])
	m.langs = langsFiltered

	m.logger.debug("todos los idiomas: ", { data: langsFiltered })

	langSelect = invalid
	for i = 0 to langsFiltered.Count() - 1
		l = langsFiltered[i]

		' selecciona primer lang, por si no hay match con el lang del profile
		if langSelect = invalid then
			langSelect = l
		end if

		if LCase(ghGetChild(userLang, "audio", "")) = LCase(ghGetChild(l, "audio", ".")) then
			m.logger.debug("coincide el audio: ", { audio: l.audio })

			if userLang.subtitle = invalid or userLang.subtitle = "" then
				m.logger.debug("sin subtitulo, se encontro lenguaje con opcion de usuario: ", { lang: l })

				langSelect = l
				exit for
			else if LCase(ghGetChild(userLang, "subtitle", "")) = LCase(ghGetChild(l, "subtitle", ".")) then
				m.logger.debug("con subtitulo, se encontro lenguaje con opcion de usuario: ", { lang: l })

				langSelect = l
				exit for
			end if
		end if
	end for

	if langSelect = invalid then
		showMessage({ message: "Contenido no displonible para esta plataforma" })
	end if

	m.logger.debug("Lang seleccionado: ", { lang: langSelect })

	m.groupId = langSelect.group_id
	m.contentId = langSelect.content_id

	m.preferred_audio = langSelect.audio
	m.preferred_subtitle = langSelect.subtitle

	OpenVideoPlayer(m.groupId, m.contentId, m.payway)
end sub

sub OpenVideoPlayer(groupId as string, contentId = "" as string, payway = "" as string)

	m.currentStreamType = 0
	m.groupId = groupId
	m.payway = payway
	m.streamTypes = getEncodesSupported()
	m.contentId = contentId

	getMedia(m.groupId, m.contentId, m.payway, m.streamTypes[m.currentStreamType])
end sub

sub getMedia(groupId as string, contentId = "" as string, payway = "" as string, streamType = "smooth_streaming_ma" as string) as object
	apiGetMedia = ghCallApi("GetMedia2", "OnMainContentLoaded", "OnError", false)

	m.logger.debug("getMedia", { groupId: groupId, contentId: contentId, streamType: streamType, payway_token: payway })

	apiGetMedia.groupId = groupId
	apiGetMedia.contentId = contentId
	apiGetMedia.streamType = streamType
	apiGetMedia.paywayToken = payway

	apiGetMedia.preferred_audio = m.preferred_audio
	apiGetMedia.preferred_subtitle = m.preferred_subtitle

	apiGetMedia.control = "run"
end sub

sub OnChangeContent(event)
	data = event.getData()
	if data <> invalid then
		m.logger.debug("OnChangeContent -- ", { data: data })

		m.player.UnobserveField("selected")
		m.currentStreamType = 0

		m.contentId = ghGetChild(data, "content_id", m.contentId)
		m.groupId = ghGetChild(data, "group_id", "")

		m.streamTypes = getEncodesSupported()

		getMedia(m.groupId, m.contentId, m.payway, m.streamTypes[m.currentStreamType])
	end if
end sub

function iniciarReproduccion(event)
	m.logger.debug("iniciarReproduccion")

	' si vino de un carousel y llego a reproduccion. pongo flag a false para evitar salir si esta bloqueado
	m.initializeWithCarousel = false

	' mostrar spinner
	m.top.loading = true

	' por si viene del pin parental, o viene directo
	data = event
	if type(event) = "roSGNodeEvent" then
		roNode = event.getRoSGNode()
		data = ghGetChild(roNode, "parametersCallback", {})
	end if

	' para tener la info si falla poder repetir accion
	m.groupId = ghGetChild(data, "groupId", "")
	m.payway = ghGetChild(data, "payway", "")
	m.contentId = ghGetChild(data, "contentId", "")

	m.logger.debug("iniciando reproduccion: ", { groupId: m.groupId, contentId: m.contentId, payway: m.payway })

	' llamo a content data para saber los idiomas disponibles
	apiContentData = ghCallApi("ContentData" + m.versionSuffix, "handleContentData", "handleContentData", false)
	apiContentData.group_id = m.groupId
	apiContentData.control = "run"
end function

sub handleContentData(event)
	data = event.getData()
	print data

	m.logger.debug("handleContentData", { data: data })

	m.dataContent = data

	' primero consultar a api getPreferences
	' si no hay idioma en preferences, ver registry
	getPreferences = ghCallApi("GetPreferences", "onGetPreferences", "onGetPreferences", false)
	getPreferences.group_id = m.groupId
	getPreferences.control = "run"
end sub

sub OnMainContentLoaded(event)
	m.logger.debug("OnMainContentLoaded")

	' se usa para mensaje de canal bloqueado al ingresar mal el pin
	m.iniciado = true

	roNode = event.getRoSGNode()
	' guardo ultimo canal que funciono por si hay error y tengo que volver a este
	if ghGetChild(m.channelOld, "group_id", "") <> ghGetChild(roNode, "groupId", "") then
		m.channelOld = m.channel
		' guardo el ultimo canal en la session del usuario
		ghSetRegistry("channel", ghGetChild(m.channel, "group_id", ""), "user")
	end if

	m.error = false ' reseteo flag de error

	datos = event.getData()

	' en la getMedia viene la info
	' seteo url de track
	trackInfoChange(datos.trackInfo)

	dataContent = createObject("roSGNode", "ContentNode")
	dataContent.Update(datos, true) ' lo lleno

	' todo bien, oculto el panel
	m.video.panelVisible = false

	' al sintonizar canal, guardo posicion de grilla y epg
	m.video.channelPosition = ghGetChild(m.channel, "channelPosition", 0)

	m.languages = datos.languages
	m.top.SetFields({ content: dataContent })
end sub

sub OnError(event)
	datos = event.getData()

	m.logger.debug("Error en getMedia", { datos: datos })

	code = ghGetChild(datos, "errors.#0.code", "DESCONOCIDO")
	code = code.toStr()

	m.top.loading = false

	m.logger.error("error code: ", { code: code })

	if code = "PLY_PLY_00009" or code = "PLY_PLY_00001" then
		m.logger.debug("entrando PLY_PLY_00009")

		if m.currentStreamType = m.streamTypes.count() - 1
			m.logger.debug("ya hice todos los intentos con diferentes encodes")

			errorMsg = ghTranslate("PLY_PLY_00009", "El contenido que quieres ver no se encuentra disponible por el momento.")
			showError({ message: errorMsg })
		else
			m.currentStreamType = m.currentStreamType + 1

			m.logger.debug("pruebo siguiente encode: ", { streamsTypes: m.streamTypes[m.currentStreamType] })

			getMedia(m.groupId, m.contentId, m.payway, m.streamTypes[m.currentStreamType])
		end if
	else if code = "PLY_CSS_00001"
		if m.error = true then
			errorMsg = ghTranslate("PLY_CSS_00001", "Se ha alcanzado el ímite de reproducciones simultáneas con esta cuenta. Para continuar deja de reproducir en alguno de tus dispositivos activos")
			showError({ message: errorMsg })
		else
			' reintento
			setPlayer(m.channel)
		end if
	else if code = "PLY_DEV_00010"
		if m.error = true then
			errorMsg = ghTranslate("PLY_DEV_00010", "Detectamos que superaste el máximo de dispositivos. Si deseas ver este contenido aquí, debes ir al sitio web de Claro video. Entra a tu usuario e ingresa en Mis Dispositivos, podrás reemplazar o eliminar alguno.")
			showError({ message: errorMsg })
		else
			' reintento
			setPlayer(m.channel)
		end if
	else if code = "PLY_DEV_00006"
		if m.error = true then
			errorMsg = ghTranslate("PLY_DEV_00006", "En este momento tienes el máximo de dispositivos asociados a tu cuenta. Puedes ver este contenido en los dispositivos utilizados previamente y reemplazar uno de ellos ingresando a la sección Mis Dispositivos desde la web.")
			showError({ message: errorMsg })
		else
			' reintento
			setPlayer(m.channel)
		end if
	else if code = "PLY_PLY_00004"
		if m.error = true then
			' si ya se cambio el payway token y sigue igual muestro mensaje
			showError({ onAccept: "showGridChannels" })
		else
			' buscar nuevo payway_token
			getChannelsPayway("reintentar")
		end if
	else
		if m.error = true then
			errorMsg = ghTranslate(code, "El contenido que quieres ver no se encuentra disponible por el momento.")
			showError({ message: errorMsg })
		else
			if ghGetChild(m.channel, "group_id", "") <> "" then
				' reintento
				setPlayer(m.channel)
			else
				errorMsg = ghTranslate(code, "El contenido que quieres ver no se encuentra disponible por el momento.")
				showError({ message: errorMsg })
			end if
		end if
	end if

	if m.error = false then
		m.error = true
	end if

end sub