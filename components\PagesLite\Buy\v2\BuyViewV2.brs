sub Init()
  m.top.debug = true

  InitBuyStateMachine()
end sub

sub onWasShown()
  if m.buy.state = invalid then JumpTo("start")
end sub

sub InitBuyStateMachine()
  m.buy = {
    data: {
      group: invalid,
      button: invalid,
      accessCode: invalid,
    }
    state: invalid,
    states: {
      start: { state: invalid },
      login: { state: invalid },
      alreadyhas: { state: invalid },
      buypin: { state: invalid },
      checkout: { state: invalid },
      purchase: { state: invalid },
      ticket: { state: invalid },
      out: { state: invalid }
    }
  }
end sub

sub BuyStateMachine()
  state = m.buy.state
  substate = m.buy.states[state]?.state

  m.logger.debug("BuyStateMachine", { state: state, substate: substate })

  if state = "start" then ' initialization and data check
    stateStart()

  else if state = "login" then ' check an login flow
    stateLogin()

  else if state = "alreadyhas" then ' check an login flow
    StateAlreadyHas()

  else if state = "buypin" then ' check if pin and gets it
    stateBuyPin()

  else if state = "checkout" then ' check all necesary data
    stateCheckout()

  else if state = "purchase" then ' (depends on PaymentMethod)
    statePurchase()

  else if state = "ticket" then ' (depends on PaymentMethod)
    stateTicket()

  else if state = "out" then ' leaves the flow OK/FAIL, resets m.buy data
    stateOut()

  else
    JumpTo("out", "FAIL")
  end if
end sub

sub JumpTo(state = invalid, substate = invalid)
  m.logger.debug("JumpTo", { state: state, substate: substate })

  if state <> invalid then
    m.buy.state = state

    if m.buy.states[state] <> invalid then
      m.buy.states[state].state = substate
    end if

    BuyStateMachine()
  end if
end sub