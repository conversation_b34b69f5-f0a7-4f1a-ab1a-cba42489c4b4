function getConfigurationCards()
  return {
    "highlight": {
      "rowItemSize": [1176, 312],
      "rowItemSpacing": [21, 0],
      "rowHeights": 312,
      "rowSpacings": 20,
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": false,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "carrouselvertical": {
      "rowItemSize": [217, 320],
      "rowItemSpacing": [21, 0]
      "rowHeights": 320,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "carrouselhorizontal": {
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0]
      "rowHeights": 200, ' cuando tiene foco el carousel. si esto no es mas grande que en rowItemSize, se corta el texto en caso de tener
      "rowSpacings": 50
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "carrousellomastop": {
      "rowItemSize": [350, 154],
      "rowItemSpacing": [32, 0]
      "rowHeights": 154,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "premiumchannel": { ' --------------------
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0]
      "rowHeights": 154,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "carrouselverticalampliado": { ' --------------------
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0]
      "rowHeights": 154,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "tooncharacter": {
      "rowItemSize": [210, 210],
      "rowItemSpacing": [0, 0]
      "rowHeights": 250,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false
      "focusBitmapUri": ghGetImageByMode("4px_toon-FocusRound.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "navchilds": {
      "rowItemSize": [254, 60],
      "rowItemSpacing": [21, 0]
      "rowHeights": 60,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "talents": {
      "rowItemSize": [210, 210],
      "rowItemSpacing": [20, 0]
      "rowHeights": 500,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false
      "focusBitmapUri": ghGetImageByMode("4px_toon-FocusRound.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "recommendations": {
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0],
      "rowHeights": 154,
      "rowSpacings": 70,
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "episodes": {
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0],
      "rowHeights": 154,
      "rowSpacings": 70,
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelEpisodes"
    },
    "episodesPlayer": {
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0],
      "rowHeights": 200,
      "rowSpacings": 50,
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelEpisodes"
    },
    "emptywithmessage": {
      "rowItemSize": [1176, 250],
      "rowItemSpacing": [21, 0],
      "rowHeights": 324,
      "rowSpacings": 20,
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "gridchannel": {
      "rowItemSize": [274, 154],
      "rowItemSpacing": [21, 0]
      "rowHeights": 154,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "plansofferv2": {
      "rowItemSize": [165, 394],
      "rowItemSpacing": [21, 0]
      "rowHeights": 500,
      "rowSpacings": 30
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "PremiumImage": {
      "focusBitmapUri": ghGetImageByMode(""), 'PROBAR HACER 3 TRIANGULOS, 1 DENTRO DEL OTRO
      ' "focusBitmapUriTranslation": [-300, 659],
      "rowItemSize": [1427, 659],
      "rowItemSpacing": [101, 0]
      "rowHeights": 560, ' cuando tiene foco el carousel. si esto no es mas grande que en rowItemSize, se corta el texto en caso de tener
      "rowSpacings": -20
      "focusXOffset": 50,
      "rowLabelOffset": [0, -50],
      "showRowLabel": false,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle",
    },
    "planselector": {
      "rowItemSize": [165, 394],
      "rowItemSpacing": [21, 0]
      "rowHeights": 500,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false,
      "rowTitleComponentName": "RowLabelTitle"
    },
    "ProfileSelect": {
      "rowItemSize": [210, 210],
      "rowItemSpacing": [25, 0]
      "rowHeights": 280,
      "rowSpacings": 70
      "focusXOffset": 10,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false
      "focusBitmapUri": ghGetImageByMode("4px_toon-FocusRound.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "ProfileManagementLite": {
      "rowItemSize": [210, 210],
      "rowItemSpacing": [25, 0]
      "rowHeights": 280,
      "rowSpacings": 70
      "focusXOffset": 10,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false
      "focusBitmapUri": ghGetImageByMode("4px_toon-FocusRound.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "ProfileAvatarLite": {
      "rowItemSize": [210, 210],
      "rowItemSpacing": [0, 0]
      "rowHeights": 250,
      "rowSpacings": 70
      "focusXOffset": 50,
      "rowLabelOffset": [50, 10],
      "showRowLabel": true,
      "showRowCounter": false
      "focusBitmapUri": ghGetImageByMode("4px_toon-FocusRound.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "CheckoutSelectPayment": {
      "rowItemSize": [480, 285],
      "rowItemSpacing": [140, 0],
      "rowHeights": 324,
      "rowSpacings": 20,
      "focusXOffset": 410,
      "rowLabelOffset": [0, 15],
      "showRowLabel": true,
      "showRowCounter": false,
      "focusBitmapUri": ghGetImageByMode("4px_Focus.9.png"),
      "rowTitleComponentName": "RowLabelTitle"
    },
    "paymentOption": {
      "rowItemSize": [handlingSizeForHD(264), handlingSizeForHD(264)],
      "rowItemSpacing": [handlingSizeForHD(46), 0],
      "rowHeights": 324,
      ' "rowSpacings": 20,
      "showRowLabel": true,
      "showRowCounter": false,
    }

  }
end function

sub refreshTypesGrid(content, grid)
  m.rowConfig = getConfigurationCards()

  rowHeights = []
  rowSpacings = []
  rowItemSize = []
  rowItemSpacing = []
  focusXOffset = []
  focusBitmapUri = []
  rowLabelOffset = []
  showRowLabel = []
  showRowCounter = []
  rowTitleComponent = []

  cantCintas = content.getChildCount()
  for c = 0 to cantCintas - 1
    cinta = content.getChild(c)

    if cinta.visible = true then
      cType = ghGetChild(cinta, "type", "carrouselhorizontal")
      rowConfig = ghGetchild(m.rowConfig, cType)

      rowHeights.push(ghGetChild(rowConfig, "rowHeights"))
      rowSpacings.push(ghGetChild(rowConfig, "rowSpacings"))
      rowItemSize.push(ghGetChild(rowConfig, "rowItemSize"))
      rowItemSpacing.push(ghGetChild(rowConfig, "rowItemSpacing"))
      focusBitmapUri.push(ghGetChild(rowConfig, "focusBitmapUri", ghGetImageByMode("4px_Focus.9.png")))
      focusXOffset.push(ghGetChild(rowConfig, "focusXOffset"))
      rowLabelOffset.push(ghGetChild(rowConfig, "rowLabelOffset"))
      showRowLabel.push(ghGetChild(rowConfig, "showRowLabel"))
      rowTitleComponent.push(ghGetChild(rowConfig, "rowTitleComponentName", "RowLabelTitle"))
      showRowCounter.push(ghGetChild(rowConfig, "showRowCounter"))
    end if
  end for

  grid.setFields({
    rowHeights: rowHeights,
    rowSpacings: rowSpacings,
    rowItemSize: rowItemSize,
    rowItemSpacing: rowItemSpacing,
    focusXOffset: focusXOffset,
    focusBitmapUris: focusBitmapUri,
    rowLabelOffset: rowLabelOffset,
    showRowLabel: showRowLabel,
    showRowCounter: showRowCounter
    rowTitleComponentName: rowTitleComponent,
  })
end sub