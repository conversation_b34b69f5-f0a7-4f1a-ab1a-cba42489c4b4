<?xml version="1.0" encoding="utf-8" ?>

<component name="LandingPage" extends="Page">

  <script type="text/brightscript" uri="LandingPage.brs" />

  <interface>
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />
    <Poster id="logo" translation="[500,104]" width="280" height="57" />
    <Label id="title" focusable="false" translation="[272,200]" width="696" height="56" horizAlign = "center" text="¡Todo lo que te gusta en sólo lugar!" />
    <Label id="descrip" focusable="false" translation="[356,264]" width="568" height="28" horizAlign = "center" text="El mejor contenido, canales de TV, películas y series." />
    <!-- Botonera -->
    <GHButtonGroup id="b01" layout="childs" orientation="vertical">
      <GHButton value="RegisterPage" id="btnRegister" width= "368" height= "72" text="REGÍSTRATE" translation="[464,344]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
      <GHButton value="LoginPage" id="btnLogin" width= "368" height= "72" text="INICIA SESIÓN" translation="[464,416]" backcolor="#6C57A0" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#6C57A0" focusColor="0xFFFFFFFF" />
      <!-- <GHButton id="bfree" text="VER GRATIS" value="page_home" translation="[450,440]" backcolor="0x1437AD" selBackColor="0x1437AD" focusColor="0xFF5F0088" /> -->
    </GHButtonGroup>

  </children>

</component>
