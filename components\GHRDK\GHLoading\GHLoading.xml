<?xml version="1.0" encoding="utf-8" ?>
<component name="GHLoading" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHLoading.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <field id="width" type="string" value="1280" alias="background.width" onChange="RecalcSpinner" alwaysNotify="true"/>
    <field id="height" type="string" value="720" alias="background.height" onChange="RecalcSpinner" alwaysNotify="true"/>
    <field id="backColor" type="string" value="0x000000DD" alias="background.color" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="background" color="0x000000DD" translation="[0,0]" width="1280" height="720" focusable="true">
      <BusySpinner id="spinner" uri="" visible="true" />
    </Rectangle>
  </children>

</component>
