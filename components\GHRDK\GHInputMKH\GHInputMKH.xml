<?xml version="1.0" encoding="utf-8" ?>
<component name="GHInputMKH" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHInputMKH.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- posicion y tamaño -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" alwaysNotify="true"/>
    <field id="width" type="integer" value="737" onChange="refreshKeyboard" />
    <field id="inputWidth" type="integer" value="608" onChange="buildInput" />
    <!-- keyboard -->
    <field id="keyColor" type="string" value= "0xFFFFFF" onChange="updateKeyColor"/>
    <field id="borderColor" type="string" value= "0x272727" onChange="updateKeyColor"/>
    <field id="KeyBackColor" type="string" value="0x981C15" alias="keySelect.color" />
    <field id="keyHeight" type="integer" value= "40" onChange="refreshKeyboard"/>
    <field id="keyPadding" type="integer" value= "1" onChange="refreshKeyboard"/>
    <field id="KeyboardBackImg" type="string" value="pkg:/images/mkh_back.jpg" alias="keyBack.uri" />
    <field id="KeyboardBackImgMode" type="string" value="scaleToZoom" alias="keyBack.loadDisplayMode" />
    <field id="KeyboardBackColor" type="string" value="0x4B4B4B" alias="keyBoard.color"/>
    <field id="keyboardPadding" type="integer" value= "0" onChange="refreshKeyboard"/>

    <!-- value -->
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="value" type="string" value="" onChange="updateFieldText" alias="inputText.text" />
    <!-- focus -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />

    <!-- end -->
  </interface>

  <children>
    <!-- Input -->
    <!-- <Rectangle id="inputBack" translation="[0,0]" color="0x880088FF" width="600" height="50" />
    <Label id="inputText" translation="[10,10]" height="44" width="0" text="Valor Inicial" horizAlign="left" vertAlign="center" /> -->
    <TextEditBox id="inputText" hintText="Títulos, géneros, artistas" text="" translation="[0,0]" textColor="0xFFFFFF" width="0" height="0" />

    <!-- keyboard -->
    <Poster id="keyBack" translation="[0,60]" width="0.0" height="0.0" loadWidth="100.0" loadDisplayMode="scaleToZoom" uri="" />
    <Rectangle id="keyBoard" translation="[0,60]">
      <Rectangle id="keySelect" translation="[100,100]" width="20" height="20" color="0xFF0000" visible="false"/>
    </Rectangle>

    <Animation id="selAnimation" duration="0.1" repeat="false" easeFunction="outQuad">
      <Vector2DFieldInterpolator id="selMover" key="[0.0, 1.0]" keyValue="[ [0, 0], [100.0, 100.0] ]" fieldToInterp="keySelect.translation" />
      <FloatFieldInterpolator id="selWidther" key="[0.0, 1.0]" keyValue="[0, 100.0 ]" fieldToInterp="keySelect.width" />
    </Animation>

  </children>

</component>
