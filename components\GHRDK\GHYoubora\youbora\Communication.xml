<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2016 Roku Corp.  All Rights Reserved. -->

<component name="Communication" extends="Task">
  <interface>

    <!-- fields -->
    <field id = "httpSecure" type = "boolean" value = "false" />
    <field id = "code" type = "string" />
    <!-- <field id = "view" type = "integer" value = "-1" /> -->
    <field id = "pingTime" type = "integer" />
    <field id = "requestHost" type = "string" value = "a-fds.youborafds01.com" />
    <field id = "youboraId" type = "string"/>

    <!-- commands -->
    <field id = "request" type = "assocarray" />
    <field id = "addPreloader" type = "string" />
    <field id = "removePreloader" type = "string" />
    <field id = "invalidFastDataResponse" type = "boolean" />
    <field id = "close" type = "boolean" />
    <field id = "nextView" type = "assocarray" />

  </interface>

  <script type="text/brightscript" uri="Utils.brs"/>
  <script type="text/brightscript" uri="Communication.brs"/>
  <script type="text/brightscript" uri="Request.brs"/>
  <script type="text/brightscript" uri="YBConstants.brs"/>

</component>