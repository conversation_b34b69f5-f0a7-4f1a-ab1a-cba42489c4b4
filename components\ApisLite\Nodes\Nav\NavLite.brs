' NavLite
' https://app.swaggerhub.com/apis-docs/ClaroVideo/NAVDATA/1.0.0#/Nav%2FData/get_services_nav_v1_data
' SI https://app.swaggerhub.com/apis/ClaroVideo/v1_experience/1.0.0#/Microframework/get_services_nav_v1_experience
' -----------------------
sub DataInit()
  m.top.debug = false

  m.api.url = m.config.mfwk.host + "/services/nav/v1/experience"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    "region": ghGetRegistry("region"),
    "module_version": "v2" ' HARDCO
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  ' if raw <> invalid then m.logger.debug("ProcessData -- raw= ", {raw: raw})
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  if res = invalid then
    m.logger.error("ProcessData -- INVALID_ERROR ")
    m.top.error = { "error": "Connection Error: Invalid" }
    return
  end if

  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("ProcessData -- ERROR ", { errors: errors })
    res.raw = raw
    m.top.error = res
    return
  end if

  childs = getNodeChilds(res.data.nodes)
  ghSetRegistry("nav", res.data.nav_type)

  ' para pruebas de menu con imagen
  items = [
    {
      "app_behaviour": {
        "node_config": {
          "show_node": true,
          "show_title": false
        }
      },
      "code": "homeuser_lite",
      "id": "113977",
      "id_parent": "113028",
      "image": invalid,
      "image_over": invalid,
      "level": 1,
      "menu_id": "2416",
      "minor_module_version": "v1",
      "order": "1",
      "provider_name": invalid,
      "status": "1",
      "text": "Inicio",
      "type": "dest"
    },
    {
      "app_behaviour": {
        "layout": "guidechannels",
        "node_config": {
          "show_node": true,
          "show_title": false
        }
      },
      "code": "guia_lite",
      "id": "113978",
      "id_parent": "113028",
      "image": "https://clarovideocdn9.clarovideo.net/pregeneracion/cms/apa_aup/531eed34tvfy7b73a818a234/ic_imgmenu_clarosports.png?**********"
      "image_over": invalid,
      "level": 1,
      "menu_id": "2416",
      "minor_module_version": "v1",
      "order": "4",
      "provider_name": invalid,
      "status": "1",
      "text": "Canales",
      "type": "dest"
    },
    {
      "app_behaviour": {
        "node_config": {
          "show_node": true,
          "show_title": false
        }
      },
      "code": "premium_lite",
      "id": "113979",
      "id_parent": "113028",
      "image": invalid,
      "image_over": invalid,
      "level": 1,
      "menu_id": "2416",
      "minor_module_version": "v1",
      "order": "5",
      "provider_name": invalid,
      "status": "1",
      "text": "Premium",
      "type": "dest"
    },
    {
      "app_behaviour": {
        "layout": "playandgo",
        "node_config": {
          "show_node": true,
          "show_title": false
        }
      },
      "code": "telmex",
      "id": "113980",
      "id_parent": "113028",
      "image": invalid,
      "image_over": invalid,
      "level": 1,
      "menu_id": "2416",
      "minor_module_version": "v1",
      "order": "8",
      "provider_name": invalid,
      "status": "1",
      "text": "Telmex",
      "type": "dest"
    },
    {
      "app_behaviour": {
        "layout": "playandgo",
        "node_config": {
          "show_node": true,
          "show_title": false
        }
      },
      "code": "telcel",
      "id": "113981",
      "id_parent": "113028",
      "image": invalid,
      "image_over": invalid,
      "level": 1,
      "menu_id": "2416",
      "minor_module_version": "v1",
      "order": "8",
      "provider_name": invalid,
      "status": "1",
      "text": "Telcel",
      "type": "dest"
    }
  ]

  m.global.setFields({
    nav: {
      type: res.data.nav_type,
      typeChapitas: getTypeByModeAndExperience(res.data.nav_type)
      items: res.data.nodes,
      ' items: items
      childs: childs
    },
  })

  m.top.content = res
end sub

function getNodeChilds(nodes)
  childs = []
  for i = 0 to nodes.count() - 1
    item = nodes[i]
    if item.childs <> invalid then
      childs.push(item)

      childs01 = item.childs
      for i01 = 0 to childs01.count() - 1
        item01 = childs01[i01]
        if item01.childs <> invalid then
          childs.push(item01)
        end if
      end for
    end if
  end for

  return childs
end function

function getTypeByModeAndExperience(tipo)
  ' freemium = anonymous
  ' freemium_registrado = no_susc
  ' preemium = susc
  if tipo <> invalid then ' estoy en lite
    if tipo = "nav_premium" then
      ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > susc"
      ' return "susc"
      return "preemium"
    else if tipo = "nav_freemium_logged" then
      ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > no_susc"
      ' return "no_susc"
      return "freemium_registrado"
    else ' nav_freemium_not_logged
      ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > anonymous"
      ' return "anonymous"
      return "freemium"
    end if
  else ' estoy en classic
    ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (classic) > susc"
    return "susc"
  end if
end function