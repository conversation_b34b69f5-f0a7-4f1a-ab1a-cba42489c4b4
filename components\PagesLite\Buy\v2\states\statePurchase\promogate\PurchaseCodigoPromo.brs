' CodigoPromo

sub Init()
  m.title = m.top.findNode("title")
  m.descrip = m.top.findNode("descrip")
  m.botonera = m.top.findNode("botonera")
  m.cvv = m.top.findNode("cvv")
  m.accept = m.top.findNode("accept")
  m.cancel = m.top.findNode("cancel")
  m.error = m.top.findNode("error")

  componentsInit()
end sub

sub componentsInit()
  m.title.setFields({
    font: ghGetFont(40, "regular")
    text: ghTranslate("codigopromo_label_title", "Confirma tu transacción")
    horizAlign: "center"
  })

  m.descrip.setFields({
    font: ghGetFont(28, "regular")
    text: ghTranslate("codigopromo_label_description", "¿Cuál es tu código promocional?")
    horizAlign: "center"
  })

  m.cvv.setFields({
    placeholder: ""
    title: ghTranslate("codigopromo_keyboard_title", "Confirma tu transacción")
    message: ghTranslate("codigopromo_keyboard_msg", "¿Cuál es tu código promocional?")
    value: ""
    ' value: "005709473724283705121" ' codigo hardco
  })

  m.accept.setFields({ text: ghTranslate("codigopromo_option_button_next", "ACEPTAR") })

  m.cancel.setFields({ text: ghTranslate("codigopromo_option_button_cancel", "CANCELAR") })

  m.botonera.setFields({
    map: {
      "cvv": { "up": invalid, "right": invalid, "down": "accept", "left": invalid },
      "accept": { "up": "cvv", "right": invalid, "down": "cancel", "left": invalid }
      "cancel": { "up": "accept", "right": invalid, "down": invalid, "left": invalid }
    }
  })
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")

  m.error.ObserveField("wasClosed", "BackFromError")
end sub

sub OnButtonSelected(event)
  child = event.getRoSGNode()

  if child.value = "CANCEL" then
    m.top.value = {
      opcion: "BACK"
      data: ""
    }
    m.top.close = true

  else
    m.top.value = {
      opcion: "SELECT"
      data: m.cvv.value
    }
    m.top.close = true

    if m.cvv.value <> ""
      m.top.value = {
        opcion: "SELECT"
        data: m.cvv.value
      }
      ' addErrorInfo()
      m.top.close = true

    else
      print "Please enter valid number"
    end if
  end if
end sub

sub addErrorInfo()
  m.error.visible = true
  m.error.title = "Hubo un error inesperado"
  m.error.descrip = "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (MS-01)"
end sub

sub BackTo() 
  m.top.value = {
    opcion: "BACK"
    data: ""
  }
  m.top.close = true
end sub

sub BackFromError()
  turnFocusTo("botonera")
end sub

sub updateFieldFocus()
  turnFocusTo("botonera")
end sub

function onKeyEvent(key, press)
  handled = false

  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
    end if
  end if

  return handled
end function

function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function

sub turnFocusTo(id)
  current = getCurrentFocus()

  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false
    end if

    if m.top.findNode(id).focus <> invalid then
      m.top.findNode(id).focus = true
    end if
  end if
end sub

function getCurrentFocus()
  current = invalid

  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if

  return current
end function
