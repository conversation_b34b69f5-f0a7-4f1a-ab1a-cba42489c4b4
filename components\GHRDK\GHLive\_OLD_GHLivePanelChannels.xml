<?xml version="1.0" encoding="utf-8" ?>

<component name="_OLD_GHLivePanelChannels" extends="Group">
  <script type="text/brightscript" uri="_OLD_GHLivePanelChannels.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="channels" type="array" onChange="onDataUpdate" />
    <field id="info" type="assocarray" onChange="onInfoUpdate" />

    <field id="selected" type="assocarray" alwaysNotify="true" />

    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- <Rectangle id="fondo" visible="false" color="#000000AA" translation = "[400,300]" width="500" height="300" /> -->
    <Rectangle id="panelLeft">
      <Label id="title"/>
      <Label id="time"/>
      <Label id="description" wrap= "true"/>
    </Rectangle>
    <LayoutGroup id="infoSerie" translation="[80,258]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[10]" visible="false">
      <Label id="infoSeason" text="infoSeason"/>
      <Label id="infoBar2" text="|"/>
      <Label id="infoEpisode" text="infoEpisode"/>
      <Label id="infoBar3" text=":"/>
      <Label id="infoEpisodeTitle" text="infoEpisodeTitle"/>
    </LayoutGroup>
    <Rectangle id="panelRight">
      <Label id="langLabel" height= "24" width="272"/>
      <MarkupGrid id="languages" />
    </Rectangle>
  </children>

</component>
