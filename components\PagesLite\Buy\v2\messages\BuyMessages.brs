' BuyMessages
' --------------------------

' ShowGenericErrorMessage
' muestra un mensaje de error con titulo, mensaje, accept button, callback de ok y callback de back
' --------------------------
sub ShowGenericErrorMessage(title, message, accept = "OK", callbackfwd = invalid, callbackback = invalid)
  if m.top.debug then print ghLogHead();"ShowGenericErrorMessage -- ";title;"|";message;"|";callbackfwd;"|";callbackfwd
  ScrErrorMessage = CreateObject("roSGNode", "ScrBuyGenericMessage")
  ScrErrorMessage.SetFields({
    title: title,
    message: message,
    accept: accept
    fwd: callbackfwd,
    back: callbackback
  })
  ' ScrErrorMessage.ObserveField("fwd", callbackfwd)
  ' ScrErrorMessage.ObserveField("back", callbackback)
  ScrErrorMessage.ObserveField("wasClosed", "ShowGenericErrorMessage_Return")
  m.top.routerChild = { page: ScrErrorMessage }
end sub
sub ShowGenericErrorMessage_Return(event)
  value = event.getRoSGNode().value
  if m.top.debug then print ghLogHead();"ShowGenericErrorMessage_Return -- ";value

  m.top.setFocus(true) ' vuelvo a donde tengo que estar. BuyViewV2

  state = value.data.state
  substate = value.data.substate
  JumpTo(state, substate)
end sub


' VIEJO, a MEJORAR
' --------------------------
' sub ErrorManagement(errorCode)

'   defTitle = "Error en la operación"
'   defContent = "Lo sentimos, ha ocurrido un error (" + errorCode + ")." + chr(10) + "Por favor vuelve a intentarlo."
'   codigo = findBuyConfirmErrorCode(m.buy.options.data)
'   if codigo <> invalid then
'     msg = ghTranslate(codigo, codigo) ' por ahora hardco
'     msgDividido = ghDividirTitCont(msg, defTitle, defContent)
'   else
'     ' msg = gh Translate("error_generic_description", "Ha ocurrido un error. Por favor vuelve a intentarlo.") +
'     msgDividido = {
'       title: defTitle,
'       content: defContent
'     }
'   end if
'   if m.top.debug then print ghLogHead();"BUY:ErrorManagement: ";msgDividido
'   ShowMessage(msgDividido.title, msgDividido.content, ghTranslate("buyconfirm_error_msg_button_ok", "ACEPTAR"), "select", "select", true)
' end sub







