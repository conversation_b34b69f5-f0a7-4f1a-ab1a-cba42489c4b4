<?xml version="1.0" encoding="utf-8" ?>

<component name="GA4Service" extends="Group">

  <script type="text/brightscript" uri="GA4Service.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="event" type="assocarray" alwaysNotify="true" onChange="onEvent" />
    <field id="tick" type="boolean" alwaysNotify="true" onChange="onTick" />
    <field id="debug" type="boolean" value="false" onChange="onDebugTurn" />
  </interface>

  <children>
    <Group id="display" visible="false">
      <Rectangle id="background" />
      <InfoPane id="panel" />
    </Group>
  </children>

</component>