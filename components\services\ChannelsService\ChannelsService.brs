' ChannelsService
' ---------------------------

' DEFINICION
' ------------------
sub Init()
  ' m.top.debug = true
  ' m.test = 1 ' -- solo para pruebas
  m.background = m.top.findNode("background")
  m.panel = m.top.findNode("panel")
  ' vars
  m.enabled = false
  m.paused = false
  m.refreshHours = 1 ' por defecto cada vez
  m.refreshHoursCount = 0 ' cuenta de horas de refresco
  ' internos declaracion de variables internas
  m.corrida = 0 ' un contador...
  ' ---------------------

  drawDebugScreen()
  ShouldStartService()
  initializeChannels()
end sub
sub ShouldStartService()
  m.enabled = true
  print ghLogHead("CHN");"ShouldStartService -- enable=";m.enabled
end sub
' EVENTOS
' ------------------
sub onTick()
  if m.top.debug then print ghLogHead("CHN");"onTick -- enabled=";m.enabled;" paused=";m.paused
  if m.enabled then ' habilitacion por config
    m.refreshHoursCount += 1
    if m.refreshHoursCount >= m.refreshHours then
      'accion !
      if m.top.debug then print ghLogHead("CHN");"onTick -- RUN! hours=";m.refreshHours;" actual=";m.refreshHoursCount
      if not m.paused then
        RefreshChannels()
        m.refreshHoursCount = 0
      end if
    else
      if m.top.debug then print ghLogHead("CHN");"onTick -- NOT NOW hours=";m.refreshHours;" actual=";m.refreshHoursCount
    end if
    panelRefresh()
  end if
end sub
sub onDebugTurn(event)
  data = event.getData()
  print ghLogHead("CHN");"onDebugTurn -- ";data
  m.top.findNode("display").visible = data
end sub
sub onPauseTurn(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("CHN");"onPauseTurn -- pause=";data
  m.paused = data
  panelRefresh()
end sub
sub onCmd(event)
  data = event.getData()
  ' tokenizer
  cmd = data.cmd
  if cmd <> invalid then
    if m.top.debug then print ghLogHead("CHN");"COMANDO! onCmd -- ";cmd
    if cmd = "run" then RefreshChannels()
  end if
  ' ---------------
  panelRefresh()
end sub
' DEBUG
' ------------------
sub drawDebugScreen()
  translation = [650, 10]
  height = 350
  width = 600
  m.background.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    color: "#b0ffc5"
    opacity: 0.5
  })
  m.panel.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    infoText: "** ChannelsService **" '	string	none	READ_WRITE	The text to be displayed in the label.
    textColor: "0x00CCFF" '		color	0xFFFFFFFF	READ_WRITE	The color of the text displayed in the label.
    bulletText: ["***"] '		array of strings	none	READ_WRITE	List of strings preceded by a bullet (for example, ["Bullet 1","Bullet 2"]).
    infoText2: "Experience." '		string	none	READ_WRITE	A second text string that can be offset from the first.
    infoText2Color: "#FF0000" '		color	0xFFFFFFFF	READ_WRITE	Specifies the infoText2 string color
    infoText2BottomAlign: true '		boolean	false	READ_WRITE	Specifies whether the infoText2 string is vertically aligned to the bottom of the info pane
  })
end sub
sub panelRefresh()

  ' status
  enabled = "false"
  if m.enabled then enabled = "true"
  paused = ""
  if m.paused then paused = " [PAUSED]"
  m.panel.infoText2 = "[STATUS] enabled=" + enabled + paused

  log = []
  log.push("hours=" + StrI(m.refreshHours) + " actual=" + StrI(m.refreshHoursCount))
  log.push("corrida >> " + str(m.corrida))
  m.panel.bulletText = log
end sub


' --------------------------------------------------

sub RefreshChannels()
  if m.top.debug then print ghLogHead("CHN");"RefreshChannels --  GO!"

  initializeChannels()

  ' contador de prueba
  m.corrida += 1
  ' print ghLogHead("CHN")"RUN >> [";m.corrida;"]";

  ' for a = 1 to 10
  '   print a;
  ' end for
  ' print " "

  if m.top.debug then print ghLogHead("CHN");"RefreshChannels --  READY."
end sub

