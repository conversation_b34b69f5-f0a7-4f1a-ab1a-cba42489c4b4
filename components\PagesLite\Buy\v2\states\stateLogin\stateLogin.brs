' stateLogin
' -----------------------

sub stateLogin()
  if m.top.debug then print ghloghead();"stateLogin LOGIN ";m.buy.states["login"]

  substate = m.buy.states["login"].state
  if substate = invalid then
    loginIsLoggedIn()
  else if substate = "ok" then
    JumpTo("buypin")
  else if substate = "fail" then
    JumpTo("out", "fail")
  else if substate = "doLogin" then
    LoginDo() ' performs the login
  else if substate = "alreadyhas" then
    JumpTo("alreadyhas")
  else if substate = "reloadbuyflow"
    JumpTo("start")
  end if
end sub

sub loginIsLoggedIn()
  if m.top.debug then print ghLogHead();"loginIsLoggedIn ** "
  m.buy.states["login"].isLoggedIn = ghGetRegistry("isLoggedIn")
  if m.buy.states["login"].isLoggedIn = "true" then
    m.buy.states["login"].thereWasLogin = false
    JumpTo("login", "ok")
    ' JumpTo("login", "doLogin") ' MOCK to test login flow
  else
    m.buy.states["login"].thereWasLogin = true
    JumpTo("login", "doLogin")
  end if
end sub

sub LoginDo(newstate = invalid)
  if m.buy.debug then print ghLogHead();"LoginDo ** "
  ghDoLoginLiteInFlow("loginDoReturn")
end sub
sub loginDoReturn(event)
  scr = event.getRoSGNode()
  if m.buy.debug then print ghLogHead();"loginDoReturn -- ";scr.value.opcion;" ";scr.value.data

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    if m.buy.debug then print ghLogHead();"loginDoReturn -- NOT Logged"
    JumpTo("login", "fail")
  else if scr.value.opcion = "SELECT" then
    m.buy.states["login"].thereWasLogin = true
    if m.buy.debug then print ghLogHead();"loginDoReturn -- Now Logged"

    if m.top.object_type = "G" then ' es un vod ----------------
      if m.buy.debug then print ghLogHead();"loginDoReturn -- [G] VOD"
      m._purchaseNode = ghCallApi("PurchaseNodeIdLite", "loginBoughtOk", "loginBoughtFail", false)
      m._purchaseNode.object_id = m.buy.data.group.id
      m._purchaseNode.control = "run"
    else if m.top.object_type = "A" then ' es una subscripcion
      if m.buy.debug then print ghLogHead();"loginDoReturn -- [A] Subscription"
      m._purchaseNode = ghCallApi("PurchaseNodeIdLite", "loginBoughtOk", "loginBoughtFail", false)
      m._purchaseNode.object_id = m.buy.data.button.product_id
      m._purchaseNode.control = "run"
    else
      print "ERROR : loginDoReturn -- tipo desconocido [";m.top.object_type;"]"
      ' ShowMessage("ERROR", "NETWORK ERROR", "ACEPTAR", "outok", "outok")
      ' JumpTo("login", "fail")
      ShowGenericErrorMessage("Error", "Network Error", "OK", { state: "login", substate: "fail" }, { state: "login", substate: "fail" })
    end if
  end if
end sub

' TODO: -- revisar el flow de login y de compra
' TODO: -- revisar que el alreadyhas sale por dos lados, incluso en la falla...
' TODO: -- revisar el tema de los mensajes, porque no tiene mucha logica, deberia ser algo del estado y no uno generico

sub loginBoughtOk(event) ' El usuario ya lo tiene comprado.
  data = event.getData()
  if m.buy.debug then print ghLogHead();"loginBoughtOk -- Ya lo compro"
  m.buy.states["alreadyhas"].alreadyBought = true
  m.buy.states["alreadyhas"].node_id = data.node_id ' a donde tengo que mandarlo
  JumpTo("login", "alreadyhas")
end sub
sub loginBoughtFail(event) ' el usuario NO lo tiene comprado
  data = event.getData()
  if m.buy.debug then
    print ghLogHead();"BUY:Comprado_Return -- NO lo tiene comprado"
    print " "
    print " "
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print "Comprado_Return = ";data
    print "Comprado_Return = ";data.errors
    print "Comprado_Return = ";data.errors[0]
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print "Comprado_Return.listButtons.button = ";ghGetChild(data, "listButtons.button")
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print "Comprado_Return.listButtons.button.0 = ";ghGetChild(data, "listButtons.button.#0")
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print "--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*--*"
    print " "
    print " "
  end if
  m._pbi = ghCallApi("PbiLite", "loginPBIReturn", "loginPBIError", false)
  m._pbi.object_type = m.top.object_type
  if m.top.object_type = "G" then ' es un vod ----------------
    m._pbi.group_id = m.buy.data.group.id
  end if
  m._pbi.control = "run"
end sub

sub loginPBIReturn(event)
  data = event.getData()
  if m.buy.debug then print ghLogHead();"loginPBIReturn -- Refreshing button data."
  if m.top.object_type = "G" then
    refreshButtonDataVOD(data.listButtons.button)
  else if m.top.object_type = "A" then ' es una subscripcion
    button = ghGetChild(data, "listButtons.button", []) 'usé el ghChild y le pasé un default porque sino rompía
    refreshButtonDataSUS(button)
  end if
  if m.buy.data.button.waspurchased = "1" then
    if m.buy.debug then print ghLogHead();"loginPBIReturn -- YA LO TENIA COMPRADO !!"
    ' m.buy.alreadyBought = true
    m.buy.states["alreadyhas"].alreadyBought = true
    ' JumpTo("confirm")
    JumpTo("login", "alreadyhas")
  else
    JumpTo("login", "reloadbuyflow")
  end if
end sub

sub loginPBIError(event)
  data = event.getData()
  if m.buy.debug then
    print ghLogHead();"loginPBIError **"
    print "**********************************************"
    print "PBI ERROR!!"
    print data
    print "**********************************************"
  end if
  ' ShowMessage("ERROR", "NETWORK ERROR", "ACEPTAR", "outok", "outok")
  ' ShowMessage("ERROR", "NETWORK ERROR", "ACEPTAR", "loginErrorOut", "loginErrorOut")
  ShowGenericErrorMessage("Error", "Network Error", "OK", { state: "login", substate: "fail" }, { state: "login", substate: "fail" })
end sub

sub loginErrorOut()
  JumpTo("login", "fail")
end sub

sub refreshButtonDataVOD(buttons)
  if m.top.debug then print ghLogHead();"BUY:refreshButtonDataVOD."
  cant = buttons.Count()
  for b = 0 to cant - 1
    if buttons[b].offerid = m.buy.data.button.offerid then
      if m.top.debug then print ghLogHead();"BUY:refreshButtonDataVOD -- found=";buttons[b]
      m.buy.data.button = buttons[b]
    end if
  end for
end sub

sub refreshButtonDataSUS(buttons)
  if m.top.debug then print ghLogHead();"BUY:refreshButtonDataSUS."
  cant = buttons.Count()
  if m.top.debug then print ghLogHead();"BUY:refreshButtonDataSUS. cant=";cant

  for b = 0 to cant - 1
    if m.top.debug then print ghLogHead();"BUY:refreshButtonDataSUS. ";b, buttons[b].product_id, m.buy.data.button.product_id, buttons[b]
    if buttons[b].product_id = m.buy.data.button.product_id then
      print ghLogHead();"BUY:refreshButtonDataSUS -- found=";buttons[b]
      m.buy.data.button = buttons[b]
    end if
  end for
end sub