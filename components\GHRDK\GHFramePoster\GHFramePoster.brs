' GHFramePoster
' by<PERSON><PERSON>e(2023) <EMAIL>

sub Init()
  ' m.top.debug = true
  m.border = m.top.findNode("border")
  m.img = m.top.findNode("img")
  if m.top.debug then print ghLogHead();"Init **"
end sub

sub setFormat()
  m.border.setField("uri", "pkg:/images/icon_hd.jpg")
  m.img.setField("uri", "pkg:/images/icon_hd.jpg")
end sub
sub refresh(event)
  field = event.getField()
  data = event.getData()
  if m.top.debug then print ghLogHead();"refresh -- ";field;"=";data
end sub

