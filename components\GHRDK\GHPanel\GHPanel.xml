<?xml version="1.0" encoding="utf-8" ?>

<component name="GHPanel" extends="Group">
  <script type="text/brightscript" uri="GHPanel.brs" />
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" /> -->

  <interface>
    <!-- tamanio -->
    <field id="width" type="integer" value="100" onChange="resize" />
    <field id="height" type="integer" value="100" onChange="resize" />
    <!-- posicion -->
    <field id="offsetX" type="integer" value="0" onChange="resize" />
    <field id="padX" type="integer" value="5" onChange="resize" />
    <field id="padY" type="integer" value="5" onChange="resize" />
    <field id="padItem" type="integer" value="3" onChange="resize" />
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- <Rectangle id="background" color="0xFF00FF88" translation="[0,0]" width="100" height="100" visible="true"/> -->
    <LayoutGroup id="leftPanel"/>
    <LayoutGroup id="rightPanel"/>
  </children>

</component>