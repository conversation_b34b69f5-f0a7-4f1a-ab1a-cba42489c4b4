sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init -ini- "
  ' definiciones
  ' -----------------------------
  m.pBackground = m.top.findNode("pBackground")
  m.title = m.top.findNode("titleChannel")
  ' -----------------------------
  ' m.languages = m.top.findNode("languages")
  ' m.languages.ObserveField("itemSelected", "onItemSelected")
  ' -----------------------------
  if m.top.debug then print ghLogHead();"Init -end- "

  ' timer ---
  buildKeyTimer()
end sub

' -----------------------------
' FORMAT AND DATA
' -----------------------------
sub FormatJoystick()
  if m.top.debug then print ghLogHead();"FormatJoystick -ini- "
  if m.top.focus then
    m.pBackground.setFields({
      translation: [0, 0]
      width: m.top.width
      height: m.top.height
      color: "#770000"
    })
    m.title.setFields({
      translation: [m.top.offset, m.top.offset]
      width: m.top.width - (2 * m.top.offset)
      height: m.top.height - (2 * m.top.offset)
      font: ghGetFont(20, "regular")
      horizAlign: "left"
      color: "#FFFFFF"
    })
  else
    m.pBackground.setFields({
      translation: [0, 0]
      width: m.top.width
      height: m.top.height
      color: "#000077"
    })
    m.title.setFields({
      translation: [m.top.offset, m.top.offset]
      width: m.top.width - (2 * m.top.offset)
      height: m.top.height - (2 * m.top.offset)
      font: ghGetFont(15, "regular")
      horizAlign: "left"
      color: "#CCCCCC"
    })
  end if
  if m.top.debug then print ghLogHead();"FormatJoystick -end- "
end sub
sub onTitleChange(event)
  title = event.getData()
  if m.top.debug then print ghLogHead();"onTitleChange -title ";title
  m.title.text = title
  FormatJoystick()
end sub
' -----------------------------
' EVENTS
' -----------------------------
sub buildKeyTimer()
  print ghLogHead();"buildKeyTimer -ini- "
  m.kTimer = CreateObject("roSGNode", "Timer")
  m.kTimer.duration = 1
  m.kTimer.repeat = false
  m.kTimer.ObserveField("fire", "triggerKeyTimer")
  print ghLogHead();"buildKeyTimer -end- "
end sub
sub triggerKeyTimer()
  print ghLogHead();"triggerKeyTimer -ini- "
  m.kkey = "long_" + m.kkey
  triggerKey()
  print ghLogHead();"triggerKeyTimer -end- "
end sub
sub updateFieldFocus(event)
  status = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -status ";status
  FormatJoystick()
  if status
    m.kkey = ""
    m.kpress = false
  end if
end sub
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  if press then
    m.ktimer.control = "start"
    m.kkey = key
    m.kpress = true
  else
    if m.kpress then
      m.ktimer.control = "stop"
      triggerKey()
    end if
  end if
  return true
end function
sub triggerKey()
  if m.top.debug then print ghLogHead();"triggerKey -ini- "
  m.top.action = m.kkey ' evento para arriba
  m.title.text = m.kkey ' para debug
  if m.top.debug then print ghLogHead();"triggerKey -end- "
end sub

' ANDA
' function onKeyEvent(key, press) as boolean
'   if press then
'     print ghLogHead();"onKeyEvent -- key=";key;" press=";press;" focus=";m.top.focus
'     m.top.action = key ' evento para arriba
'     m.title.text = key ' para debug
'   end if
'   return true
' end function
