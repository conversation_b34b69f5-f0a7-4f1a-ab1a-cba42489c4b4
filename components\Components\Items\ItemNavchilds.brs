sub Init()
  if m.top.debug then print ghLogHead();"Init"
  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.background = m.top.findNode("background")
  m.background.color = "#26272A"
  m.title = m.top.findNode("title")
  m.title.color = "0xFFFFFF80"
end sub

sub showfocus()
  if m.top.focusPercent = 1
    ' m.itemPoster.opacity = "1"
    m.background.color = "#28292F"
    m.title.setFields({
      color: "0xFFFFFF"
    })

  else
    ' m.itemPoster.opacity = "0.3"
    m.background.color = "#26272A"
    m.title.setFields({
      color: "0xFFFFFF50"
    })
  end if
end sub

sub itemContentChanged()
  ' item = m.top.itemContent
  m.data = ghGetChild(m.top.itemContent, "data.data")
  if m.top.debug then
    print ghLogHead();"Refresh -------------------- [";m.rowType;"]"
    print ghLogHead();m.data
    print ghLogHead();"---------------------------- [";m.rowType;"]"
  end if

  m.title.setFields({
    text: UCase(m.data.text)
    font: ghGetFont(18, "regular")
    translation: [0, 0]
    wrap: "true"
    lineSpacing: "0"
    vertAlign: "center"
    horizAlign: "center"
  })
end sub
