' -----------------------------
' GHButtonGroup

function Init()
  ' m.top.debug = true
  m.top.valueFocused = m.top.foco
  if m.top.debug then print ghLogHead();"init -- getChildCount ";m.top.getChildCount()
end function
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  ' handled = true
  ' m.top.handleKey, si es false, la tecla llega hasta el padre del componente, ej: home
  ' y puede manejar que hacer, ej con la tecla back
  handled = m.top.handleKey
  if m.top.enabled then
    if press then
      if key = "back" then ' apreto el back?
        if m.top.backSelEnable then
          m.top.backSelected = true
          if m.top.debug then print ghLogHead();"onKeyEvent -- Salgo por BACK selected >>"
          return true ' forzado, sacame de aca.
        else
          if m.top.debug then print ghLogHead();"onKeyEvent -- Salgo por BACK bubbling >>"
          ' m.top.backSelected = false
          handled = false
        end if
      else
        getChilds()
        focusTo = guessFocusTo(key)
        if focusTo <> invalid then
          if m.top.debug then print ghLogHead();"onKeyEvent -- foco interno key=";key
          handleFocus(focusTo)
        else
          if isValidExit(key) then
            if m.top.debug then print ghLogHead();"onKeyEvent -- foco invalido! exiting by ";key
            handled = false
          else
            if m.top.debug then print ghLogHead();"onKeyEvent -- foco invalido! sin bubbling... key=";key
            if m.top.handleKey then
              handled = true
            end if
          end if
        end if
      end if
    end if
  end if
  ' print handled
  return handled
end function
function guessFocusTo(direction)
  focusTo = invalid
  ' si o si tiene que pasar por uno de estos
  if m.top.layout = "map" then
    focusTo = guessFocusToByMap(direction)
  else if m.top.layout = "childs" then
    focusTo = guessFocusToByChilds(direction)
  end if
  if m.top.debug then print ghLogHead();"guessFocusTo -- type ";m.top.layout;" to ";focusTo
  return focusTo
end function
function guessFocusToByMap(direction, focused = invalid)
  if focused = invalid then focused = m.top.valueFocused
  if m.top.debug then print ghLogHead();"guessFocusToByMap focused=";focused;" -- direction=";direction
  if m.top.map[focused] <> invalid then
    focusTo = m.top.map[focused][direction]
    ' FALTA TESTEAR ESTO!!!
    ' if m.top.findNode(focusTo).visible = false then
    ' targetNode = m.top.findNode(focusTo)
    ' if targetNode.hasField("enabled") and targetNode.enabled = false then
    '   print "***************************** NO esta ";focusTo
    '   focusTo = guessFocusToByMap(direction, focusTo) ' sigo de largo en la misma direccion
    ' else
    '   print "***************************** ESTA ";focusTo
    ' end if
  else
    focusTo = m.top.valueFocused 'm.top.getChild(0).id
  end if
  return focusTo
end function
function isValidExit(key)
  valid = false
  if key = "up" then
    if m.top.exitUp then valid = true
  else if key = "down" then
    if m.top.exitDown then valid = true
  else if key = "left" then
    if m.top.exitLeft then valid = true
  else if key = "right" then
    if m.top.exitRight then valid = true
    ' else if key = "back" then
    '   if m.top.exitBack then valid = true
  end if
  if m.top.debug then print ghLogHead();"isValidExit -- ";valid
  return valid
end function
function guessFocusToByChilds(direction, desde = invalid)
  focusTo = invalid
  ' en cual estoy?
  selectedChild = getChildIndexById(m.top.valueFocused)
  if desde <> invalid then
    selectedChild = desde
    print "NUEVO DESDE ";desde
  end if

  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- selectedChild ";selectedChild

  ' orientacion
  if m.top.orientation = "vertical" then
    dPrev = "up"
    dNext = "down"
  else if m.top.orientation = "horizontal" then
    dPrev = "left"
    dNext = "right"
  end if

  ' a cual voy?
  newChild = invalid
  if direction = dPrev then
    newChild = selectedChild - 1
  else if direction = dNext then
    newChild = selectedChild + 1
  end if

  ' if m.top.debug then
  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- newChild ";newChild
  if newChild <> invalid then
    if newChild < 0 or newChild >= m.childs.count() then
      newChild = invalid
    end if
    if m.top.debug then print ghLogHead();"guessFocusToByChilds -- newChild (CORRECT) ";newChild
  end if

  if newChild <> invalid then
    if m.top.debug then print ghLogHead();"guessFocusToByChilds -- newChild (B) ";newChild

    targetNode = m.top.getChild(newChild)
    if targetNode <> invalid then
      if m.top.debug then print "!!!";targetNode.hasField("enabled"), targetNode.enabled
      if targetNode.hasField("enabled") and targetNode.enabled = false then
        if m.top.debug then print "**guessFocusToByChilds*************************** NO esta ";targetNode.id
        newChild = guessFocusToByChilds(direction, newChild) ' sigo de largo en la misma direccion
      else
        if m.top.debug then print "**guessFocusToByChilds*************************** ESTA ";targetNode.id
      end if
    end if

  end if

  if newChild <> invalid then
    if m.top.debug then print ghLogHead();"guessFocusToByChilds -- newChild (C) ";newChild
    if newChild > -1 and newChild < m.childs.Count() then ' si me pase me quedo
      focusTo = m.childs[newChild]
    end if
  end if



  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- focusTo ";focusTo

  return focusTo
end function
' CHILDS
' -----------------------------
sub getChilds()
  m.childs = [] ' traigo todos
  for i = 0 to m.top.getChildCount() - 1
    m.childs.push(m.top.getChild(i).id)
  end for
  if m.top.debug then print ghLogHead();"getChilds -- childs ";m.childs.Count();" items."
end sub
function getChildIndexById(id) as integer
  child = invalid
  for i = 0 to m.childs.Count() - 1
    if m.childs[i] = id then child = i
  end for
  if m.top.debug then print ghLogHead();"getChildIndexById -- id ";id;" is ";child
  return child
end function
' FIELDS
' -----------------------------
sub updateFieldFocus(event)
  if m.top.debug then print ghLogHead();"updateFieldFocus -- "
  ' cuando entro al componente y obtengo foco o salgo del mismo y dejo de tener foco
  if event.getData() then
    cant = m.top.getChildCount()
    if cant > 0 then
      if m.top.valueFocused = invalid or m.top.valueFocused = "" then
        m.top.valueFocused = m.top.getChild(0).id
      end if
    end if
    handleFocus(m.top.valueFocused)
    getChilds()
    for i = 0 to m.childs.Count() - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
      child.ObserveField("selected", "onChildSelected")
    end for
  else
    handleFocusOut()
    tot = m.top.getChildCount()
    for i = 0 to tot - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
    end for
  end if
end sub
sub onChildSelected(event)
  if m.top.debug then print ghLogHead();"onChildSelected -- "
  child = event.getRoSGNode()
  m.top.value = child.value
end sub
sub handleFocus(id)
  if m.top.debug then print ghLogHead();"handleFocus -- "
  if id <> invalid then
    idNode = m.top.findNode(id)
    if idNode <> invalid then
      if m.top.debug then print ghLogHead();"handleFocused id ";id
      ' apago seleccion anterior
      idNodePrev = m.top.findNode(m.top.valueFocused)
      if m.top.valueFocused <> id then
        idNodePrev.focus = false
      end if
      idNode.focus = true
      m.top.valueFocused = id
    end if
  end if
end sub
sub handleFocusOut()
  if m.top.debug then print ghLogHead();"handleFocusOut -- "
  ' apago ultimo foco
  idNodePrev = m.top.findNode(m.top.valueFocused)
  if idNodePrev <> invalid then
    idNodePrev.focus = false
  end if
end sub
sub updateValue()
  if m.top.debug then print ghLogHead();"updateValue -- "
  if m.top.value = invalid or m.top.value = "" then
    m.top.valueFocused = m.top.getChild(0).id
  end if
  m.top.selected = true
end sub
sub updateFoco(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"updateFoco -- "
  handleFocus(data)
end sub
' -----------------------------
