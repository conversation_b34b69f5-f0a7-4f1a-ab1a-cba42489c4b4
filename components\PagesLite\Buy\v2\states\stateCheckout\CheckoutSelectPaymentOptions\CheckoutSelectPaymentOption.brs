' ScrSelect

sub Init()
  m.top.debug = true

  m.map = {
    "theGrid": { "up": invalid, "right": invalid, "down": "botonera", "left": invalid }
    "botonera": { "up": "theGrid", "right": invalid, "down": invalid, "left": invalid }
  }

  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "closeScreen")

  m.cancel = m.top.findNode("cancel")
  m.add = m.top.findNode("add")

  m.msgNoMethod = m.top.findNode("msgNoMethod")
  m.title = m.top.findNode("title")

  m.theGrid = m.top.findNode("theGrid")
  m.theGrid.itemComponentName = "ItemPolymorphic"
  m.theGrid.itemSize = [1035, 210]
  m.theGrid.itemSpacing = [0, 0]
  m.theGrid.rowFocusAnimationStyle = "floatingFocus"
  m.theGrid.numRows = 1
  m.theGrid.numColumns = 5
  m.theGrid.visible = true
  m.theGrid.ObserveField("position", "OnCardOver")
  m.theGrid.ObserveField("value", "OnCardSelect")
end sub

sub OnButtonSelected(event)
  data = event.getData()
  child = event.getRoSGNode()

  m.logger.debug("onButtonSelected", { value: child.value })

  if child.value = "CANCEL" then
    closeScreen("CANCEL")
  else if child.value = "ADD" then
    closeScreen("ADD")
  end if
end sub

sub updateFieldFocus()
  turnFocusTo("theGrid")
end sub

function onKeyEvent(key, press) as boolean
  handled = false

  if press then
    m.logger.debug("keyEvent: ", { key: key })

    if key <> "back" then
      changeFocusBasedOnKey(key)

      handled = true
    else
      closeScreen("BACK")
    end if
  end if

  return handled
end function

sub ScreenSetup(cantidad)

  if cantidad > 0 then
    ' m.cancel.text = ghTranslate("payment_method_option_button_cancel", "CANCELAR")
    ' m.add.text = ghTranslate("payment_method_option_button_add", "AGREGAR")

    ' m.title.text = ghTranslate("payment_method_title_label", "Selecciona tu medio de pago")
    ' m.title.font = ghGetFont(32, "regular")

    m.title.setFields({
      font: ghGetFont(handlingSizeForHD(41), "regular")
      translation: [0, handlingSizeForHD(137)]
      text: ghTranslate("MDP_AgregarMediodepago_TextoTitulo", "Agregar medio de pago")
    })

    m.cancel.setFields({
      text: ghTranslate("MDP_AgregarMediodepago_TextoBotonSecundario", "CANCELAR")
      width: handlingSizeForHD(516)
      font: ghGetFont(handlingSizeForHD(33), "bold")
      translation: [handlingSizeForHD(702), handlingSizeForHD(883.5)]
    })

  else

    m.msgNoMethod.setFields({
      text: ghTranslate("payment_message_no_method_description", "Adquiere este contenido desde la web o app de Claro video. Una vez adquirido podrás disfrutarlo en tu Roku.")
      font: ghGetFont(24, "regular")
      visible: true
    })

    m.title.translation = [0, 240]
    m.title.text = ghTranslate("payment_no_method_title", "Error en la operación")
    m.title.font = ghGetFont(28, "bold")

    m.cancel.text = ghTranslate("payment_no_method_button", "ACEPTAR")
  end if
end sub

function GridContent(metodos)
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.type = "paymentOption" 'oldone: "CheckoutSelectPayment"
  obCinta.visible = true

  for v = 0 to metodos.Count() - 1
    op = metodos[v]
    op.rowtype = "paymentOption" 'oldone: "CheckoutSelectPayment"
    if isValidPaywayMethod(op) then
      it = CreateObject("roSGNode", "GHContent")
      it.id = op.gateway
      it.data = op
      obCinta.appendChild(it)
    end if
  end for

  result = CreateObject("roSGNode", "GHContent")
  result.appendChild(obCinta)

  return result
end function

function isValidPaywayMethod(method) as boolean
  tipo = ghGetChild(method, "gateway")
  print "tipo: " method

  ' amcogate -- promogate > van si o si, en los demas solo si es valido
  valido = false
  if tipo = "amcogate" or tipo = "promogate" or tipo = "rokugate" or tipo = "hubfacturafijagate" or tipo = "hubgate" or tipo = "telmexmexicogate" or tipo = "claropagosgate" then
    valido = true
  else
    if ghGetChild(method, "paymentMethodData.account") <> invalid then
      valido = true
    end if
  end if

  return valido
end function

sub refreshData(event)
  data = event.getData()

  m.logger.debug("methods", { methods: data })

  result = GridContent(data)
  cantidad = result.getChild(0).getChildCount()

  ScreenSetup(cantidad)

  refreshTypesGrid(result, m.theGrid)
  m.theGrid.content = result
  adjustPositionRowList(m.theGrid)
  ' m.theGrid.jumpToRowItem = [0, 0]
end sub

sub OnCardSelect(event)
  data = ghGetChild(event.getData(), "data.data", {})

  closeScreen("SELECT", data)
end sub

sub closeScreen(value = "BACK", data = invalid)
  if value <> invalid then
    m.top.value = {
      option: value
      data: data
    }
  end if

  m.top.close = true
end sub

sub adjustPositionRowList(RowList)
  if RowList <> invalid
    uiResolution = ghGetGlobalWH()
    rowCount = RowList.content.getChild(0).getChildCount()
    itemWidth = RowList.rowItemSize[0][0]
    spacing = RowList.rowItemSpacing[0][0]
    if rowCount <> invalid
      totalWidth = (itemWidth * rowCount) + (spacing * rowCount)
      centerX = (uiResolution.w - totalWidth) / 2
      positionY = ghYtoAbstract(RowList.translation[1])
      RowList.translation = [centerX, positionY]
    end if
  end if
end sub