' --------------------------------------------------------------------------------
' Sistema de Logging Mejorado
' --------------------------------------------------------------------------------
' Características Principales

' Niveles de Log:
' 	DEBUG: Mensajes detallados para diagnóstico.
' 	INFO: Información general sobre el estado del sistema.
' 	WARN: Advertencias sobre condiciones que podrían requerir atención.
' 	ERROR: Registro de errores críticos que deben ser corregidos.

' Formato del Log:
' 	Cada registro consta de:
' 	Un mensaje principal (message) como string.
' 	Datos adicionales opcionales (data) como JSON, array o string.
' 	Configuración de Depuración:

' Activa los logs configurando m.top.debug = true o m.debug = true.

' Soporte para Consola y Envío a New Relic:
' 	Los logs se imprimen en la consola de Roku y, si está configurado, se envían a New Relic.

function CreateLogger() as object
	m.logLevels = {
		DEBUG: 0,
		INFO: 1,
		WARN: 2,
		ERROR: 3
	}

	' Nivel de log a imprimir
	m.currentLogLevel = m.logLevels.DEBUG

	debug = function(message as string, data = invalid)
		printLog(0, message, data)
	end function

	info = function(message as string, data = invalid)
		printLog(1, message, data)
	end function

	warn = function(message as string, data = invalid)
		printLog(2, message, data)
	end function

	error = function(message as string, data = invalid)
		printLog(3, message, data)
	end function

	return {
		debug: debug,
		info: info,
		warn: warn,
		error: error
	}
end function

function SerializeSGNode(node as object, depth = 1) as string
	if node = invalid or Type(node) <> "roSGNode" then return "{}"
	if depth <= 0 then return "{}" ' Límite de profundidad para evitar ciclos infinitos

	aa = CreateObject("roAssociativeArray")

	' Intenta obtener los campos del nodo
	fields = node.getFields()
	for each field in fields
		value = node[field]
		if Type(value) = "roArray" or Type(value) = "roAssociativeArray" then
			' Convertir directamente si es un array o un associative array
			aa[field] = value
		else if Type(value) = "roSGNode" then
			' Serializar nodos hijos recursivamente (con límite de profundidad)
			aa[field] = SerializeSGNode(value, depth - 1)
		else
			' Otros tipos de valores (int, float, string, etc.)
			aa[field] = value.toStr()
		end if
	end for

	return FormatJson(aa)
end function

sub printLog(level as integer, message as string, data = invalid)
	debug = true
	if m.top <> invalid and m.top.debug <> invalid then
		debug = m.top.debug
	end if
	' if m.debug <> invalid then
	' 	debug = m.debug
	' end if

	if debug = true then
		if level >= m.currentLogLevel then
			' Formatear mensaje principal
			formattedMessage = message.toStr()

			' Si hay datos adicionales, procesarlos para el mensaje
			if data <> invalid then
				formattedMessage += " " + SafeFormatJson(data)
			end if

			' Imprimir en consola
			print formatLogMessage(formattedMessage, level)

			' Enviar a New Relic (si está configurado)
			if type(ghCallApi) = "Function" then
				sendLog = ghCallApi("SendLogNew", "LogOK", "LogError", false)
				sendLog.type = getLogLevelName(level)
				sendLog.description = message
				sendLog.file = m.top.subType()

				' Procesar `data` para el log en New Relic
				if data <> invalid then
					if Type(data) = "roAssociativeArray" then
						sendLog.data = data
					else if Type(data) = "roArray" then
						sendLog.data = { data: data }
					end if
				end if

				sendLog.control = "run"
			end if
		end if
	end if
end sub

function formatLogMessage(message as string, level as integer) as string
	prefix = getLogLevelName(level)
	date = getDate()

	return date + " [" + prefix + "] [" + m.top.subType() + "] " + message
end function

function getLogLevelName(level as integer) as string
	if level = m.logLevels.DEBUG then
		return "DEBUG"
	else if level = m.logLevels.INFO then
		return "INFO"
	else if level = m.logLevels.WARN then
		return "WARN"
	else if level = m.logLevels.ERROR then
		return "ERROR"
	else
		return "UNKNOWN"
	end if
end function

function getDate() as string
	date = CreateObject("roDateTime")
	date.ToLocalTime()

	return date.ToISOString() + "[" + StrI(date.GetMilliseconds()) + "]"
end function


' Función segura para formatear cualquier estructura de datos a JSON
function SafeFormatJson(data) as string
	if data = invalid
		return "null"
	end if

	dataType = Type(data)

	if dataType = "roAssociativeArray" then
		return FormatAssociativeArray(data)
	else if dataType = "roArray" then
		return FormatArray(data)
	else if dataType = "String" or dataType = "roString" then
		return """" + data + """"
	else if dataType = "Integer" or dataType = "roInteger" or dataType = "Float" or dataType = "roFloat" or dataType = "Double" or dataType = "roDouble" then
		return data.ToStr()
	else if dataType = "Boolean" or dataType = "roBoolean" then
		if data = true then
			return "true"
		else
			return "false"
		end if
	else if dataType = "roSGNode" then
		' Para nodos SG, solo devolvemos información básica
		return """[SG Node: " + data.subtype() + "]"""
	else
		' Para otros tipos desconocidos
		return """[" + dataType + "]"""
	end if
end function

' Función auxiliar para formatear arrays asociativos
function FormatAssociativeArray(aa as object) as string
	result = "{"
	first = true

	for each key in aa
		if not first then result += ","
		first = false

		result += """" + key + """:"
		result += SafeFormatJson(aa[key]) ' Llamada recursiva para manejar valores anidados
	end for

	result += "}"
	return result
end function

' Función auxiliar para formatear arrays
function FormatArray(arr as object) as string
	result = "["
	first = true

	for each item in arr
		if not first then result += ","
		first = false

		result += SafeFormatJson(item) ' Llamada recursiva para manejar elementos anidados
	end for

	result += "]"
	return result
end function