sub Init()
  if m.top.debug then print ghLogHead();"Init"

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.info = m.top.findNode("info")
  m.info.font = ghGetFont(31, "bold")
  m.progress = m.top.findNode("progress")
  m.fondoInfo = m.top.findNode("fondoInfo")
  m.focoCarruselInfo = m.top.findNode("focoCarruselInfo")
  m.infoCarrusel = m.top.findNode("infoCarrusel")
  m.infoCarrusel.visible = true
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")
  m.info.text = ghTranslate(ghGetChild(data, "node_id", "") + "_Image_TextoBotonPrimario", "SUSCRIBIRME MENSUAL") 'falta key
  m.itemPoster.translation = [0, -45]
  m.itemPoster.width = 1162
  m.itemPoster.height = 471
  m.itemPoster.uri = ghGetChild(data.properties, "image_highlight", "pkg:/images/loading_horizontal.png") 'ESTO PARA CUANDO VENGAN LAS IMAGENES CON HEADER

  drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height)

  ' drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)
  initTimer() ' para chapitas

  ' if data <> invalid and getIdMiLista(data.carouselId) or getIdContinuarViendo(data.carouselId) then
  'm.title.setFields({
  '  ' width: m.itemPoster.width - 20
  '  text: ghGetChild(data, "title")
  '  ' text: "titulo de la pelicula largo para ver como queda"
  '  font: ghGetFont(20, "medium")
  '  translation: [0, 160]
  '  color: "0xFFFFFF50"
  '  horizAlign: "center"
  '  vertAlign: "top"
  '})
  'm.trash.translation = [316, 629.33]
  ' m.trash.width = 628.67
  ' m.trash.height = 64
  ' m.trash.uri = "pkg:/images/Molecules_ATV_ButtonOptions.png"
  ' m.info.setFields({
  '   ' text: ghGetChild(data, "title")
  '   text: gh Translate("contentToDelete_hold_ok", "PRESS OK")
  '   font: ghGetFont(20, "medium")
  '   translation: [185, 300]
  '   color: "0xFFFFFF50"
  '   horizAlign: "center"
  '   vertAlign: "top"
  ' })
  ' end if
end sub

sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")
  if m.top.rowHasFocus = true and data <> invalid then
    m.focoCarruselInfo.visible= true
  else 
    m.focoCarruselInfo.visible= false
  end if
end sub