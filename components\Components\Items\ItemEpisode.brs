sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init"
  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.progress = m.top.findNode("progress")
end sub

sub itemContentChanged()
  ' item = m.top.itemContent
  data = ghGetChild(m.top.itemContent, "data")
  m.rowType = ghGetChild(data, "rowtype", "std")
  ' order = ghGetChild(data, "order", 1)
  if m.top.debug then
    print ghLogHead();"Refresh -------------------- [";m.rowType;"]"
    print ghLogHead();m.top.itemContent
    print ghLogHead();m.top.itemContent.data
    print ghLogHead();"---------------------------- [";m.rowType;"]"
  end if

  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 274
  m.itemPoster.height = 154
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height)
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)
  initTimer()
end sub
