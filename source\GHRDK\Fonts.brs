' ------------------------------------------------
' FONTS UTILS
' byGoose(2020) (<EMAIL>)
' ------------------------------------------------

'
'''''''''
' ghGetFont: gets a standar font
'
' @param {numeric} [size=20]
' @param {string} [font="regular"]
' @return {object}
'''''''''
function ghGetFont(size = 20, mode = "regular") as object
  font = CreateObject("roSGNode", "Font")

  ' type
  if mode = "medium" then
    font.uri = "pkg:/images/fonts/Roboto_Medium.ttf"
  else if mode = "bold" then
    font.uri = "pkg:/images/fonts/Roboto_Bold.ttf"
  else
    font.uri = "pkg:/images/fonts/Roboto_Regular.ttf"
  end if

  ' size
  font.size = size

  return font
end function

function ghGetComponentFont(component) as object
  if component = "GHButton" then
    return ghGetFont(21.33, "bold")
  else if component = "GHButtonDesc" then
    return ghGetFont(12, "regular")
  else if component = "GHButtonDescBLabel" then
    return ghGetFont(21.33, "bold")
  else if component = "GHCheckBox" then
    return ghGetFont(16, "regular")
  else if component = "GHInputTitle" then
    return ghGetFont(30, "bold")
  else if component = "GHInputMessage" then
    return ghGetFont(24, "regular")
  else if component = "GHErrorTitle" then
    return ghGetFont(28, "bold")
  else if component = "GHErrorMessage" then
    return ghGetFont(24, "regular")
  else if component = "GHTextScroll" then
    return ghGetFont(16, "regular")
  else if component = "GHInputMKH_keys" then
    return ghGetFont(24, "regular")
  else if component = "GHOption" then
    return ghGetFont(21, "regular")
    ' else if component = "GHInputMKH_input" then
    '   return ghGetFont(16, "regular")
  else
    return ghGetFont() ' un default
  end if
end function