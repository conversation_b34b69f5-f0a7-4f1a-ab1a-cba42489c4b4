function init()
  '   ghCall<PERSON>pi("Purchase", "getSubscriptions", "handleError")
  ' en caso de no existir la api hay que agregarla en la carpeta apis
  m.title = m.top.findNode("titleText")
  m.title.font = ghGetFont(38, "medium")
  m.description = m.top.findNode("descriptionText")
  m.image = m.top.findNode("imagePoster")
  m.info = m.top.findNode("info")
  m.info.font = ghGetFont(24, "regular")
  ' m.buttonOK = m.top.findNode("ok")
  ' m.buttonOK.text= gh Translate("contentToDelete_modal_option_button_deleteRecord","ELIMINAR")
  ' m.buttonCancel= m.top.findNode("cancel")
  ' m.buttonCancel.text= gh Translate("contentToDelete_modal_option_button_cancel","CANCELAR")
  ' '+ chr(10)
  ' ' buttons
  ' m.botonera = m.top.findNode("botonera")
  ' m.botonera.ObserveField("selected", "ButtonSelected")
  ' m.botonera.ObserveField("backSelected", "BackSelected")
  ' m.botonera.map = {
  '   "ok": { "up": invalid, "right": invalid, "down": "cancel", "left": invalid },
  '   "cancel": { "up": "ok", "right": invalid, "down": invalid, "left": invalid },
  ' }
  '
  ' ghFocusJumpTo("botonera")
end function

'sub updateFieldFocus() ' event
' if m.top.focus then
'   ghFocusJumpTo("botonera")
' else
'   m.top.close = true
' end if
'end sub

sub handleContent() ' event
  m.title.text = ghTranslate("contentToDelete_modal_titleFavorite_label", "Eliminar contenido") 'falta key
  m.description.text = m.top.description
  m.image.uri = ghGetChild(m.top, "image", "pkg:/images/loading_horizontal.png")
  m.info.text = ghTranslate("contentToDelete_modal_description_label", "¿Seguro que deseas eliminar ") + chr(10) + m.top.title + ghTranslate("contentToDelete_modal_descriptionEnd_label", "?") 'falta key
end sub

'sub ButtonSelected(event)
' child = event.getRoSGNode()
'
' if child.value = "OK" then
'   m.top.selected = m.top.group_id
'   m.top.close = true
' else
'   m.top.close = true
' end if
'end sub

'sub BackSelected(event)
'  data = event.getData()
'
'  m.top.close = true
'end sub