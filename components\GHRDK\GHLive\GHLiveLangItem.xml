<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLiveLangItem" extends="Group">

  <script type="text/brightscript" uri="GHLiveLangItem.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz estandar de entrada -->
    <field id="width" type="float" onChange="widthChanged"/>
    <field id="height" type="float" onChange="heightChanged"/>
    <field id="index" type="integer" />
    <field id="gridHasFocus" type="boolean" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <field id="focusPercent" type="float" />
    <field id="itemHasFocus" type="boolean" onChange="onFocus" />
  </interface>
  <children>
    <Rectangle id="fondo" color="#AC0000" visible="true"/>
    <Label id="title" />
    <Poster id="icon" />
  </children>

</component>