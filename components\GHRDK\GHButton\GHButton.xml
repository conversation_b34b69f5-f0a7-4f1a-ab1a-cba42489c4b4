<?xml version="1.0" encoding="utf-8" ?>
<component name="GHButton" extends="Group" initialFocus="label">
<script type="text/brightscript" uri="GHButton.brs"/>
<script type="text/brightscript" uri="GHButton_fields.brs"/>
<script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs"/>
<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs"/>
<interface>
<!--  text  -->
<field id="text" type="string" value="MyButton" onChange="updateFieldText"/>
<field id="wrap" type="boolean" value="false" onChange="updateFieldWrap"/>
<field id="font" type="node" alias="label.font"/>
<!--  position  -->
<field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation"/>
<field id="width" type="string" value="350" onChange="updateFieldWidth"/>
<field id="height" type="string" value="80" onChange="updateFieldHeight"/>
<!--  align  -->
<field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign"/>
<field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign"/>
<!--  colors  -->
<field id="backColor" type="string" value="0xFFFFFF" onChange="updateFieldBackColor"/>
<field id="color" type="string" value="0x000000" onChange="updateFieldColor"/>
<field id="selBackColor" type="string" value="0xFFFFFF" onChange="updateFieldSelBackColor"/>
<field id="selColor" type="string" value="0x000000" onChange="updateFieldSelColor"/>
<!--  padding and margin  -->
<field id="padding" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true"/>
<field id="focusPadding" type="string" value="12" onChange="updateFieldFocusPadding" alwaysNotify="true"/>
<field id="focusMap" type="string" value="focus01.9.png"/>
<field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor"/>
<!--  focus and select  -->
<field id="enabled" type="boolean" value="true" onChange="updateEnabled"/>
<field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true"/>
<field id="selected" type="boolean" value="false" alwaysNotify="true"/>
<field id="value" type="string" value="mybutton"/>
<!--  label de mayor que  -->
<field id="rightLabelContent" type="string" value="" onChange="refresh"/>
<field id="rightLabelFont" type="node" onChange="refresh"/>
<!--  debug  -->
<field id="debug" type="boolean" value="false"/>
<field id="disableButton" type="boolean" value="false" alias="disableButtonGradient.visible"/>
</interface>
<children>
<Poster id="border" translation="[0,0]" width="360" height="120" uri="" visible="false" blendColor="0x0000ff"/>
<Rectangle id="background" color="0xffffff" translation="[0,0]" width="360" height="80">
<Label id="label" text="Ok" color="0x000000" translation="[0,0]" width="360" height="80" vertAlign="center" horizAlign="center"/>
<Label id="rightLabel" text="" color="0x000000" translation="[0,0]" width="0" height="0" vertAlign="center" horizAlign="right" visible="false"/>
</Rectangle>
<Rectangle id="disableButtonGradient" visible="false" color="#000000" opacity="0.5"/>
</children>

</component>
