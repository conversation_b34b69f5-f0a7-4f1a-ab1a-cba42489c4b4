' YOUBORA
' ----------------------------
sub InitYoubora(forced = true)
  debug = true ' no comentar, se usa abajo
  print ghLogHead("YB");"Utils/TrackingServices/InitYoubora -- START."
  userRegion = ghGetRegistry("region")
  ybConfig = ghGetChild(m.global, "youbora." + userRegion)
  ybConfig = invalid ' APGADO POR AHORA !!!!!!!!!!!!!!!!
  if ybConfig = invalid then
    print ghLogHead("YB");"Utils/TrackingServices/InitYoubora -- NO CONFIG."
  else
    ybEnabled = ghGetChild(ybConfig, "tracking.enabled")
    ' ybEnabled = true ' HARDCO PRENDIDO
    ybHost = ghGetChild(ybConfig, "tracking.host")
    ybAccountCode = ghGetChild(ybConfig, "tracking.accountcode")
    if debug then
      print "+++++++++++++++++++++++++++++++++++++++"
      print userRegion
      print "+++++++++++++++++++++++++++++++++++++++"
      a = ghGetChild(m.global, "youbora." + userRegion)
      print a.tracking
      print "+++++++++++++++++++++++++++++++++++++++"
    end if
    ' switch de APA
    if ybEnabled = invalid or ybEnabled = false then
      print ghLogHead("YB");"Utils/TrackingServices/InitYoubora -- DISABLED."
      m.global.youbora = invalid
    else
      print ghLogHead("YB");"Utils/TrackingServices/InitYoubora -- ENABLED."
      if forced or m.global.YB_Plugin = invalid then ' plugin creation
        m.global.addField("YB_Plugin", "node", false)
        m.global.YB_Plugin = CreateObject("roSGNode", "GHYoubora")
        m.global.YB_Plugin.debug = debug
      end if
      if m.global.YB_Plugin <> invalid then ' plugin config
        if ybHost <> invalid then
          m.global.YB_Plugin.host = ybHost ' especifico para manejo de host.
        end if
        m.global.YB_Plugin.options = {
          "accountCode": ybAccountCode 'Change for your accountcode
          ' "user": "Gustavo_Ripoll_DevAccount"
          "expectAds": false
          "content": {}
        }
        m.global.YB_Plugin.control = "RUN"
        m.global.YB_Plugin.session = {
          ev: "start"
          sc: "VideoScene"
          dim: {}
        }
      end if
    end if
  end if
  print ghLogHead("YB");"Utils/TrackingServices/InitYoubora -- END."
end sub

function ghYBGetHost()
  return ""
end function

' ANALYTICS
' ----------------------------
' sub InitAnalytics()
'   debug = true
'   print ghLogHead("GA");"MainScene -- init."
'   ' creación
'   if m.global.GA_plugin = invalid then
'     m.global.addField("GA_plugin", "node", false)
'     m.global.GA_plugin = CreateObject("roSGNode", "Roku_Analytics:AnalyticsNode")
'     if debug then m.global.GA_plugin.debug = true
'   end if
'   ' inicialización
'   m.global.GA_plugin.init = {
'     google: {
'       trackingID: "*********" ' "G-SDSFWQYGL2"
'       defaultParams: { ' For convenience, this allows developers to define a set of parameters and values that will be sent with every omniture call
'         "gVersion": "DePrueba",
'         "gOrigin": "Goose"
'       }
'       ' These values will be populated automatically by the component but can be overridden if desired
'       ' an: "app_name"
'       ' av: "app_version"
'       ' cid: "client_id"
'       ' sr: "screen_resolution" 'must be expressed as WWWWxHHHH
'     }
'   }
' end sub
