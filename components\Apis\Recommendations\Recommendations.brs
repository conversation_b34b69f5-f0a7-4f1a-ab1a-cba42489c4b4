sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/content/recommendations"
  m.api.query.Append({
    "group_id": m.top.group_id,
    "quantity": "10",
    "order_id": "ASC",
    "order_way": "ASC",
    "is_only_provider": m.top.provider.toStr(),
    "filterlist": getFilter(ghGetRegistry("region")),
    "region": ghGetRegistry("region")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api.query
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

function getFilter(region = "mexico")
  filters = m.global.filter_list

  return ghGetChild(filters, region + ".filterlist")
end function



sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub