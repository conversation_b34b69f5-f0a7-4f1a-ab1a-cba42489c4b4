' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' m.top.debug = true
  ' seteos generales
  ' m.top.focusable = true
  ' componentes
  m.background = m.top.findNode("background")
  m.label = m.top.findNode("label")
  ' m.label.font = ghGetComponentFont("GHTag")
  ' m.label.setFocus(true)
  ' Refresh()
end function

'
' FIELDS
' -----------------------------
sub updateFieldText()
  if m.top.text <> invalid then
    m.label.text = m.top.text
    recalcLabelSizeAndPadding()
  end if
end sub

sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub

sub updateFieldHorizAlign()
  ' if m.top.debug then print ghLogHead();"updateFieldHorizAlign -- "
  m.label.horizAlign = m.top.horizAlign
end sub
sub updateFieldVertAlign()
  ' if m.top.debug then print ghLogHead();"updateFieldVertAlign -- "
  m.label.vertAlign = m.top.vertAlign
end sub

' sub updateFieldFocusMap()
'   if m.top.focusMap <> invalid then m.background.uri = m.top.focusMap
' end sub

' utils ------------
sub Refresh()
  recalcLabelSizeAndPadding()
  recalcColors()
end sub
sub recalcLabelSizeAndPadding()
  ' if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding *** ";m.top.id;" ***"

  ' paddingVert = ghXtoAbstract(val(m.top.vertPadding))
  ' paddingHoriz = ghXtoAbstract(val(m.top.horizPadding))
  paddingVert = ghXtoAbstract(m.top.vertPadding)
  paddingHoriz = ghXtoAbstract(m.top.horizPadding)
  labelRect = m.label.boundingRect()

  m.background.height = ghXtoAbstract(labelRect.height) + (2 * paddingVert)
  m.background.width = ghXtoAbstract(labelRect.width) + (2 * paddingHoriz)

  ' if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding -- pw=";paddingHoriz;" ph=";paddingVert
  ' if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding -- ";m.top.text;" "; FormatJson(labelRect);" ";m.label.lineSpacing
  ' if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding -- back w=";m.background.width;" h=";m.background.height

  calcPositions()

end sub

sub calcPositions()
  if m.top.debug then print ghLogHead();"calcPositions *** "
  valign = m.top.vertAlign
  halign = m.top.horizAlign
  paddingVert = ghXtoAbstract(m.top.vertPadding)
  paddingHoriz = ghXtoAbstract(m.top.horizPadding)

  backRect = m.background.boundingRect()
  ' if m.top.debug then print ghLogHead();"calcPositions bounding [";m.top.text;"] "; FormatJson(backRect)
  backWidth = backRect.width
  backHeight = backRect.height

  if halign = "left" then ' left
    backHT = 0
  else if halign = "center" then ' center
    backHT = 0 - (backWidth / 2)
  else ' right
    backHT = 0 - (backWidth)
  end if
  lablHT = backHT + paddingHoriz

  if valign = "top" then ' top
    backVT = 0
  else if valign = "center" then ' center
    backVT = 0 - (backHeight / 2)
  else ' bottom
    backVT = 0 - (backHeight)
  end if
  lablVT = backVT + paddingVert

  m.background.translation = [backHT, backVT]
  m.label.translation = ghVal2Trans(lablHT, lablVT)
  ' if m.top.debug then print ghLogHead();"calcPositions back=", m.background.translation[0];" "; m.background.translation[1];" "; m.background.width;" "; m.background.height
  ' if m.top.debug then print ghLogHead();"calcPositions label=", m.label.translation[0];" "; m.label.translation[1];" "; m.label.width;" "; m.label.height
end sub


sub recalcColors()
  ' if m.top.debug then print ghLogHead();"recalcColors -- ", m.top.color, m.top.backColor
  m.background.blendColor = m.top.backColor
  m.label.color = m.top.color
end sub

' END FILE ------------------