' info de la home, parsear diferente a la info de content/data
sub handleData(event)
  data = event.GetData()

  m.logger.debug("Entrando a la vcard con informacion en data")

  genres = []
  for each aItem in ghGetChild(data, "extendedcommon.genres.genre", [])
    genres.push(aItem.desc)
  end for

  duration = ghGetChild(data, "duration")
  if duration <> invalid then
    duration = Left(duration, 5) + " min"
  end if

  result = {
    vistime: ghGetChild(data, "vistime")
    serie_id: ghGetChild(data, "is_series", false)
    season: ghGetChild(data, "season_number", "")
    episodenumber: ghGetChild(data, "episode_number", "")
    rating: ghGet<PERSON>hild(data, "rating_code", "")
    year: ghGetChild(data, "year", "")
    fondo: ghGetChild(data, "image_base_horizontal", "pkg:/images/mkh_back.jpg")
    title: ghGet<PERSON>hild(data, "title", "")
    description: ghGet<PERSON>hild(data, "description_large", "")
    isDeepLinking: ghGetChild(data, "deeplinking", false)
    duration: duration
    CATEGORIES: genres
  }

  ' play automatico
  ' continuar viendo(unico que tiene vistime ?),
  if result.vistime <> invalid then
    m.autoPlay = true
  end if

  ' mando info para que se imprima en pantalla

  ' m.logger.debug("Vcard result contenido", result)
  m.logger.debug("Vcard result contenido")
  m.top.contenido = result

  m.proveedor_code = ghGetChild(data, "proveedor_code", "")

  m.id = data.groupId
  ' para saber si es serie o pelicula, season o episode con deeplinking
  m.contentType = ghGetChild(data, "contentType", 1)
  ' series busco ultimo capitulo visto
  if m.contentType = 2 then
    getSeenLast(m.id)
  else
    getContent(m.id)
  end if

  m.isDeepLinking = ghGetChild(data, "deeplinking", false)

  ' ===== deepLinking =====
  if m.isDeepLinking then
    m.logger.debug("entro como deepLinking", { data: data })

    m.autoPlayTrailer = ghGetChild(data, "trailer", false)

    m.callback = ghGetChild(data, "callback", invalid)
    m.callbackParams = ghGetChild(data, "callbackParams", invalid)

    ' para que si es un llamado con callback no hago el auto play
    if ghGetChild(data, "callback") = invalid
      m.autoPlay = ghGetChild(data, "idplayable", false)
    end if

    if m.contentType = 3 then
      m.showEpisodes = true
    end if
  end if
end sub

' cuando hago login y me mantengo en la misma pantalla
' tengo que recargar la info de la vcard
' y ejecutar la funcion callback que viene despues de hacer login
sub changeAuth(event)
  data = event.getRoSGNode()

  m.logger.debug("changeAuth", data)

  ' por ejemplo llamara a switchFavorite, con el parametro true o false
  useCallback(ghGetChild(data, "fncCallback"), ghGetChild(data, "params"))

  ' como estoy cambiando todo el menu
  ' seteo el menu seleccionado en vacio, para buscar por apa el menu seleccionado por default
  m.global.setFields({ navSelect: "" })

  ' reset del menu y de la home
  m.top.reloadHome = true
  m.menu.refresh = true

  ' cargo informacion de la vcard
  if m.contentType = 2 then
    getSeenLast(m.id)
  else
    getContent(m.id)
  end if
end sub

' LASTSEEN
' ------------------------------------
sub getSeenLast(id)
  apiSeenLast = ghCallApi("SeenLast", "seenlastHandleOk", "seenlastHandleError", false)
  apiSeenLast.group_id = id
  apiSeenLast.control = "run"
end sub
sub seenlastHandleOk(event)
  data = event.GetData()

  if ghGetChild(data, "episode.id", invalid) <> invalid then
    getContent(data.episode.id)
  end if
end sub
sub seenlastHandleError() ' event
  getContent(m.id)
end sub

sub getOffers(id)
  offers = ghCallApi("PbiLite", "offersHandle", "offersHandleError", false)
  offers.group_id = id
  offers.control = "run"
end sub

sub contentSerie(id)
  apiContentSerie = ghCallApi("ContentSerie", "handleContentSerie", "handleErrorNothing", false)
  apiContentSerie.group_id = id
  apiContentSerie.control = "run"
end sub

' chargeContentSerie, cuando cambio de capitulo, recargo misma vcard
' no necesito volver a obtener la info de la content/serie
sub getContent(id, chargeContentSerie = true)
  ' cuando cargo la vcard de cero, tengo que volver a enviar el analytics
  m.analytic_enviado = false

  m.top.loading = true

  m.top.favorite = false
  m.id = id

  ' TODO, por ahora despues de la content/data
  ' getOffers(m.id)

  if chargeContentSerie = true then
    if m.contentType = 2 or m.contentType = 3 or m.contentType = 4 then
      contentSerie(id)
    end if
  end if

  apiContentData = ghCallApi("ContentData" + m.versionSuffix, "handleContentData", "handleContentDataError", false)
  apiContentData.group_id = id
  apiContentData.control = "run"

  apiFavorites = ghCallApi("GetFavorites", "handleFavorites", "handleErrorNothing", false)
  apiFavorites.control = "run"

  apiBookmark = ghCallApi("Bookmark", "handleBookmark", "handleErrorNothing", false)
  apiBookmark.group_id = id
  apiBookmark.control = "run"
end sub

sub handleFavorites(event)
  data = event.GetData()

  favorite = false

  items = ghGetChild(data, "groups", [])
  cantidad = items.count()
  for i = 0 to cantidad - 1
    item = ghGetChild(items, "#" + i.toStr())
    ' print "vcardlogic -- ";i;" ";ghGetChild(item, "id");" = ";m.id
    if ghGetChild(item, "id") = m.id then
      ' print "LA ENCONTRE!!! ";m.id
      favorite = true
    end if
  end for

  if favorite = true then
    m.top.favorite = true
  end if
end sub

sub handleBookmark(event)
  data = event.GetData()

  if m.top.debug then print ghLogHead();"handleBookmark = vistime = ";data; "::";ghGetChild(data, "groups.#0.vistime", invalid)

  m.top.vistime = ghGetChild(data, "groups.#0.vistime", invalid)

  ' refresco el vistime del episodio
  if ghGetChild(data, "groups.#0", invalid) <> invalid then
    episodeArray = []
    episodeArray.push(ghGetChild(data, "groups.#0"))
    changeEpisodeVistime(episodeArray)
  end if

  ' m.top.vistime = { duration: { seconds: 50 }, last: { seconds: 40 } }
end sub

sub handleRecommendations(event)
  data = event.GetData()

  chargeRecommentations(ghGetChild(data, "groups", []))
end sub

sub handleErrorNothing() ' event
end sub

function handleContentData(event as object)
  data = event.GetRoSGNode()

  ' TODO hacer los llamdos asincronos y de alguna manera saber si la content/data termino para mandar el play automatico
  ' despues de la content/data porque si llega antes intenta reproducir y no tengo la informacion para mostrar en el player
  getOffers(m.id)

  content = ghGetChild(data, "content", invalid)
  if content <> invalid then
    m.dataContent = content

    ' llamado a recommendations con provider ( true si es distinto a amco )
    provider = false
    info = ghGetChild(content, "group.common.extendedcommon.media.proveedor.codigo")
    if info <> "amco" then
      provider = true
    end if

    apiRecommendations = ghCallApi("Recommendations" + m.versionSuffix, "handleRecommendations", "handleErrorNothing", false)
    apiRecommendations.provider = provider
    apiRecommendations.group_id = m.id
    apiRecommendations.control = "run"

    if ghGetChild(content, "group") <> invalid then
      getTalents(content.group)
    end if

    ' fix highlight sin is_serie
    ' si no la detecte como serie
    if m.contentType <> 2 and m.contentType <> 3 and m.contentType <> 4 then
      contentSerie(ghGetChild(content, "group.common.id"))
    end if

    dataFinal = parseDataContent(ghGetChild(content, "group.common"))

    m.proveedor_code = info
    m.top.contenido = dataFinal
  end if
end function

function handleContentSerie(event as object)
  data = event.GetData()

  ' creo que solo esta dentro de los capitulos
  serieTitle = invalid
  if data <> invalid then
    seasonArray = CreateObject("roAssociativeArray")

    for each season in data.seasons
      episodeArray = []
      for each episode in season.episodes
        image = episode.image_still
        if image = invalid then
          image = episode.image_small
        end if

        if serieTitle = invalid then
          serieTitle = episode.title
        end if

        episodeNode = {
          ContentType: 3,
          id: episode.id,
          serieTitle: episode.title,
          title: episode.title_episode,
          Description: episode.description_large,
          year: episode.year,
          rating: episode.rating_code,
          image_medium: image + "?size&imwidth=448",
          image_small: image + "?size&imwidth=448",
          episode_number: ghGetChild(episode, "episode_number", ""),
          season_number: ghGetChild(season, "number", ""),
          is_series: true
        }

        episodeArray.Push(episodeNode)
      end for
      seasonArray[ghGetChild(season, "number")] = {
        serieTitle: serieTitle,
        year: ghGetChild(m.top.contenido, "year")
        categories: ghGetChild(m.top.contenido, "categories")
        rating: ghGetChild(m.top.contenido, "rating")
        title: ghGetChild(season, "title")
        number: ghGetChild(season, "number")
        image_medium: ghGetChild(season, "image_medium") + "?size&imwidth=268",
        image_small: ghGetChild(season, "image_small") + "?size&imwidth=268",
        firstEpisode: ghGetChild(season, "first_episode", "")
        id: ghGetChild(season, "first_episode", "")
        episodes: episodeArray
      }
    end for

    m.top.seasons = seasonArray
  end if
end function

sub handleContentDataError() ' error
  m.logger.debug("handleContentDataError")

  showError(true)
end sub

sub offersHandle(event as object)
  offers = event.GetData()

  offersTest = {
    listButtons: {
      button: [
        {
          "banner": "banner_cv_mensual",
          "bannerpromo": "cv_mensual_subscrition_argentina_wp1_amcogate",
          "bonus": "bonus_cv_mensual",
          "buy": "buy_cv_mensual",
          "contentPurchased": "1",
          "currency": "$",
          "family": "clarovideo",
          "frequency": 1,
          "gateway": "default",
          "includes": "includes_cv_mensual",
          "key": "Telmexmexico_Subscription_SVOD_30d",
          "linkworkflowstart": "/services/payway/v1/workflowstart?object_type=A&offer_id=14326887&suscription_id=980748&device_category=stb&device_manufacturer=roku&device_model=generic&device_type=generic&region=argentina&user_id=73156552",
          "offerid": "14326887",
          "oneofferdesc": "SUSCRIBITE AHORA",
          "oneofferdesckey": "offer_button_desc_subscription_cv_mensual",
          "oneoffertype": "subscrition",
          "paymentmethod": {
            "gateway": "amcogate",
            "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
          },
          "periodicity": "month",
          "price": "89",
          "price_before_tax": invalid,
          "product_id": "980748",
          "producttype": "CV_MENSUAL",
          "purchasable": true,
          "purchasechecked": "1",
          "purchaseid": "1734525361",
          "style": "style_clarovideo",
          "waspurchased": "1"
        }
      ],
    },
    "playButton": {
      "waspurchased": "1",
      "contentPurchased": "1",
      "purchasechecked": "1",
      "price": "89",
      "price_before_tax": invalid,
      "offerid": "14326887",
      "currency": "$",
      "paymentmethod": {
        "gateway": "rokugate",
        "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
      },
      "purchasable": true,
      "product_id": "980748",
      "purchaseid": "1734525361",
      "oneoffertype": "subscrition",
      "oneofferdesckey": "offer_button_desc_subscription_cv_mensual",
      "gateway": "default",
      "oneofferdesc": "SUSCRIBITE AHORA",
      "producttype": "CV_MENSUAL",
      "key": "Telmexmexico_Subscription_SVOD_30d",
      "bannerpromo": "cv_mensual_subscrition_argentina_wp1_amcogate",
      "idabono": "980748",
      "expirationonstore": "720",
      "expirationonstoredownload": "720",
      "objetotipo": "A",
      "visible": "1",
      "advertisement": {
        "adstierenabled": "0"
      },
    }
  }

  visible = ghGetChild(offers, "playButton.visible", "0")

  m.isRokuPay = false
  if visible = "1" and ghGetChild(offers, "playButton.paymentmethod.gateway", "") = "rokugate" then
    m.isRokuPay = true
  end if

  ' para la purchasebuttoninfo
  m.top.buybuttons = ghGetChild(offers, "listButtons")
  m.top.accessCode = ghGetChild(offers, "accessCode")
  m.tipo_abono_contenido = ghGetChild(offers, "playButton.key")

  if visible = "1"
    m.top.payway = ghGetChild(offers, "playButton.payway_token", "")
  else
    m.top.payway = ""
  end if

  'freemium
  visibleAds = ghGetChild(offers, "playFreemiumButton.visible", "0")
  m.enableAds = false
  if visibleAds = "1" then
    m.enableAds = true
  end if

  if visible = "0" then
    visible = visibleAds
    m.top.payway = ghGetChild(offers, "playFreemiumButton.payway_token", "")
  end if

  ' mostrar info no disponible
  showContentUnavailable = false
  if m.top.buybuttons = invalid and visible <> "1" then
    showContentUnavailable = true
  end if

  ' obtener botones
  getButtons(showContentUnavailable)

  continueCharge() ' GOOSE!!! PRUEBA!!!! -- bajando tiempos

  verifySubscription()
end sub

sub continueCharge()
  m.logger.debug("continuo charge....")

  'abrir seasons popup
  if m.showEpisodes = true then
    m.showEpisodes = false
    ' openSeasons()
    ' para que el foco vaya al carousel de episodios
    m.focusId = "episodes"
  end if
  'si tiene autoPlay
  if m.autoPlay = true and (m.top.payway <> "" or (m.autoPlayTrailer <> invalid and m.autoPlayTrailer = true)) then
    trailer = m.autoPlayTrailer
    m.autoPlayTrailer = false
    reproducirVideo(trailer)
    m.top.loading = false
  else
    m.logger.debug("sacando loading...")

    m.top.loading = false
  end if

  if m.callback <> invalid then
    m.logger.debug("llamando a callback", { callback: m.callback, params: m.callbackParams })
    useCallback(m.callback, m.callbackParams)
  end if
end sub

sub offersHandleError() ' event as object
  m.logger.debug("offersHandleError")

  m.top.payway = ""
  getButtons() ' obtener botones
  showError(false, "Error al cargar datos de compra")
  m.top.loading = false
end sub

sub showError(salir = true, message = "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (VCL-01)", title = "Hubo un error inesperado", accept = "ACEPTAR")
  ' para no abrir ma de un msg de error
  if m.errorVisible = invalid then
    m.errorVisible = true
    ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
    ScrGenericMessage.SetFields({
      title: title,
      message: message,
      accept: accept,
    })
    if salir = true then
      ScrGenericMessage.ObserveField("wasClosed", "OnExitToHome")
    else
      ScrGenericMessage.ObserveField("wasClosed", "OnExitError")
    end if

    m.top.routerChild = {
      page: ScrGenericMessage,
      fields: {}
    }
  end if
end sub

sub OnExitError() ' event
  m.errorVisible = invalid
  m.top.loading = false
end sub

sub getButtons(showContentUnavailable = false)
  buttons = []

  if m.top.payway <> "" then
    btnTitle = ghTranslate("vcard_access_option_button_play", "Reproducir")
    if m.contentType = 2 or m.contentType = 3 or m.contentType = 4 then
      season = ghGetChild(m.dataContent, "group.common.extendedcommon.media.episode.season")
      episode = ghGetChild(m.dataContent, "group.common.extendedcommon.media.episode.number")
      if season = invalid or episode = invalid then
        btnTitle = ghTranslate("vcard_access_option_button_play", "Reproducir")
      else
        btnTitle = ghTranslate("vcard_access_option_button_play", "Reproducir") + " T" + season + " | " + "E" + episode
      end if
    end if

    buttons.push({
      title: btnTitle,
      id: "play",
      selBackColor: "#9B0F0F",
      backColor: "#9B0F0F",
      HDLISTITEMICONSELECTEDURL: ghGetAssetByMode("play_icon"),
      HDLISTITEMICONURL: ghGetAssetByMode("play_icon"),
    })
  end if

  buySeason = false
  if m.top.payway = "" and m.top.buybuttons <> invalid then
    m.buyButtons = m.top.buybuttons.button
    m.accessCode = m.top.accessCode
    print ghLogHead();"handlePlayOffers -- DrawingBuyButtons"
    for b = 0 to m.buyButtons.Count() - 1
      buyB = m.buyButtons[b]
      buyPurchased = buyB.waspurchased
      if type(buyPurchased) <> "roString" then
        buyPurchased = StrI(buyPurchased)
      end if

      buttonColor = "#477F9B"
      if buyB.producttype = "CV_EPISODEBUY" then
        buttonColor = "#40336F"
      end if
      if buyB.producttype = "CV_SEASONBUY" then
        buttonColor = "#8065DF"
        buySeason = true
      end if

      if buyB.oneoffertype = "download_buy" then
        buttons.push({
          title: buyB.oneofferdesc
          price: buyB.currency + " " + buyB.price
          id: "buy" + buyB.offerid,
          selBackColor: buttonColor,
          backColor: buttonColor,
        })
      end if
      'if buyB.producttype = "CV_EPISODEBUY" then
      '  buttons.push({
      '    title: buyB.oneofferdesc
      '    price: buyB.currency + buyB.price
      '    id: "buy" + buyB.offerid,
      '    selBackColor: "#477F9B",
      '    backColor: "#282828",
      '  })
      'end if
      'if buyB.producttype = "CV_SEASONBUY" then
      '  buttons.push({
      '    title: buyB.oneofferdesc
      '    price: buyB.currency + buyB.price
      '    id: "buy" + buyB.offerid,
      '    selBackColor: "#8065DF",
      '    backColor: "#8065DF",
      '  })
      'end if
      if buyB.oneoffertype = "rent" or buyB.oneoffertype = "download_rent" then
        buttons.push({
          title: buyB.oneofferdesc
          price: buyB.currency + " " + buyB.price
          id: "buy" + buyB.offerid,
          selBackColor: "#8A4CA7",
          backColor: "#8A4CA7",
        })
      end if

      keyProveedor = ghGetChild(m.top.buyButtons.button[b], "oneofferdesckey")

      if buyB.oneoffertype = "subscrition" then
        buttons.push({
          title: ghTranslate(keyProveedor, ""),
          price: buyB.currency + " " + buyB.price
          id: "buy" + buyB.offerid,
          selBackColor: "#4C6FA7",
          backColor: "#4C6FA7",
        })
      end if
    end for
  end if
  ' ---------------------------------------------

  contentData = m.top.contenido
  if m.contentType = 2 or m.contentType = 3 or m.contentType = 4 or ghGetChild(contentData, "serie_id") <> invalid then
    buttons.push({
      title: ghTranslate("vcard_access_option_button_selectSeason", "Seleccionar temporada"), 'falta key de apa
      id: "temporadas",
      selBackColor: "#282828",
      backColor: "#282828",
      HDLISTITEMICONSELECTEDURL: ghGetAssetByMode("seasons_icon"),
      HDLISTITEMICONURL: ghGetAssetByMode("seasons_icon"),
    })
  end if

  hasPreview = ghGetChild(m.dataContent, "group.common.extendedcommon.media.haspreview", false)
  ' el dato puede ser un string
  if (type(hasPreview) = "String" or type(hasPreview) = "roString")
    if hasPreview = "true" then
      hasPreview = true
    else
      hasPreview = false
    end if
  end if

  if hasPreview = true then
    buttons.push({
      title: ghTranslate("vcard_access_option_button_trailer", "Ver trailer"),
      id: "trailer",
      selBackColor: "#282828",
      backColor: "#282828",
      HDLISTITEMICONSELECTEDURL: ghGetAssetByMode("trailer_icon"),
      HDLISTITEMICONURL: ghGetAssetByMode("trailer_icon")
    })
  end if

  buttons.push({
    title: ghTranslate("vcard_access_option_button_addFavoritiesList", "Agregar a Mi lista"),
    id: "favorite",
    selBackColor: "#282828",
    backColor: "#282828",
    HDLISTITEMICONSELECTEDURL: ghGetAssetByMode("milista_add_icon"),
    HDLISTITEMICONURL: ghGetAssetByMode("milista_add_icon"),
  })

  ' ver si va el msj o boton, etc
  ' msj No disponible para adquirir
  cant = buttons.count()

  'mensaje de compra de temporadas
  if buySeason = true then
    info4 = m.top.FindNode("info4")
    info4.text = ghTranslate("cv_seasonbuy_access_additionalInfo_label", "*La compra de una temporada completa incluye todos los capítulos de la misma.")
    info4.font = ghGetFont(15, "regular")
    info4.visible = true
  end if

  info3 = m.top.FindNode("info3")
  if showContentUnavailable = true then
    info3.text = ghTranslate("vcard_content_unavailable", "Adquiere este contenido desde la web o app de Claro video. Una vez adquirido podrás disfrutarlo en tu Roku.")
    info3.font = ghGetFont(18, "regular")

    if cant = 2 then
      info3.translation = [280, 415]
    else if cant = 3 then
      info3.translation = [380, 415]
    else if cant = 4 then
      info3.translation = [480, 415]
    end if

    info3.visible = true
  end if

  handleButtons(buttons)
end sub

sub reproducirVideo(trailer = false)
  setLoading(true)

  if trailer = true then
    sendActionToAnalytic("see trailer")
    setLoading(false)
    sendPlay(true)
  else
    m.continuoPlay = true
    verifySubscription()
  end if

end sub

sub continuoPlay()
  ' max = ghGetChild(m.top.vistime, "duration.seconds", -1)
  ' curr = ghGetChild(m.top.vistime, "last.seconds")
  ' porc = curr / max * 100

  ' si el porcentaje es menor al 5% entonces sigo con la reproduccion
  ' if porc < 5 then
  sendActionToAnalytic("reproduce")
  setLoading(false)

  if m.isDeepLinking = true or ghGetChild(m.top.vistime, "last.seconds", -1) < 5 then
    sendPlay(false)
  else
    resume = CreateObject("roSGNode", "GHResume")
    resume.ObserveField("wasClosed", "handleResumeSeasons")
    m.top.routerChild = {
      page: resume,
      fields: {}
    }
  end if

end sub

sub verifySubscription()
  m.logger.debug("verifySubscription", { isRokuPay: m.isRokuPay, payway: m.top.payway })

  ' if (m.isRokuPay = true or m.top.payway = "") then
  m.logger.debug("verifySubscription entro")

  isCanceled = false
  if m.top.payway = "" then
    isCanceled = true
  end if

  m.global.RPAY.UnobserveField("cmdResult")
  m.global.RPAY.ObserveField("cmdResult", "subscriptionResult")
  m.global.RPAY.cmd = {
    cmd: "verifySuscription"
    data: {
      isCanceled: isCanceled,
      offers: ghGetChild(m.top, "buybuttons.button", [])
    }
  }
  ' else
  '   m.logger.debug("verifySubscription else")
  '   continueCharge()
  ' end if

end sub

sub subscriptionResult(event)
  data = event.getData()
  m.logger.debug("subscriptionResult -- resultado de verificar suscripciones", { data: data, continuoPlay: m.continuoPlay })

  ' no vengo del boton play
  ' y soy la verificacion
  ' y tiene alguna subscripcion ligada
  if data.verify = true and m.continuoPlay <> true then
    m.logger.debug("vengo de verificacion", { vengoDePlay: m.continuoPlay })

    if data.status_subscription = "Canceled" then ' GOOSE!!! PRUEBA!!!! -- bajando tiempos
      m.global.RPAY.cmd = {
        cmd: "testScreenCanceled"
        data: {
          producttype: data.producttype
        }
      }
    else
      ' continueCharge()   ' GOOSE!!! PRUEBA!!!! -- bajando tiempos
      m.logger.debug("subscriptionResult -- continuaria la carga de la vcard", { data: data })
    end if

    return
  end if

  m.continuoPlay = false

  ' no encontro suscripcion, vengo con null
  if data.verify = true and data.status_subscription = invalid then
    m.logger.debug("vengo de verificacion y no tengo status")
    continuoPlay()
  else if data.verify = true then
    m.logger.debug("vengo de verificacion y soy play")

    if data.status_subscription = "InGrace" then
      m.logger.debug("vengo de verificacion y soy play con status InGrace")
      m.global.RPAY.cmd = {
        cmd: "testScreenOngrace"
        data: {
          producttype: data.producttype
        }
      }

    else if data.status_subscription = "OnHold" then
      m.logger.debug("vengo de verificacion y soy play con status OnHold")
      m.global.RPAY.cmd = {
        cmd: "testScreenSuspended"
        data: {
          producttype: data.producttype
        }
      }
    else
      m.logger.debug("vengo de verificacion y soy play con status active ( != InGrace and != onHold)")
      ' estatus active
      continuoPlay()
    end if

  else

    m.logger.debug("no vengo de verificacion", { data: data })

    ' vengo del boton ok de una de las pantallas ( inGraces, onHold )
    if data.status = "OK" then
      m.logger.debug("soy boton ok")

      ' para saber de donde vengo al hacer ok de doRecovery
      m.statusDoRecovery = data.status_subscription

      request = {
        command: "DoRecovery"
      }
      m.store = CreateObject("roSGNode", "ChannelStore")
      m.store.observeField("requestStatus", "RokuDoRecoveryReturn")
      m.store.request = request
      return
    else
      m.logger.debug("soy boton cancel")
      ' vengo del boton cancelar de unas de las pantallas
      ' m.buyB = data.offer
      ' openBuyView()

      ' si soy boton de la patalla cancelado, entonces estoy cargando vcard
      if data.status_subscription = "Canceled" then
        m.logger.debug("soy boton cancel, status canceled ")
        ' continueCharge()

        ' si vengo de pantalla onHold no continuo con el play
      else if data.status_subscription = "InGrace" then
        m.logger.debug("soy boton cancel, status InGrace")
        continuoPlay()
      else
        m.logger.debug("soy boton cancel, status OnHold")
        setLoading(false)
      end if
    end if
  end if
end sub

sub RokuDoRecoveryReturn(event)
  data = event.getData()

  setLoading(false)

  if data = invalid
    if m.statusDoRecovery = "InGrace"
      continuoPlay()
    end if

    print "Invalid requestStatus"
    print "DoRecovery failed"

  else if data.status <> 1
    if m.statusDoRecovery = "InGrace"
      continuoPlay()
    end if

    print "DoRecovery failed: status:", data.status

  else
    m.logger.debug("onDoRecovery ejecutado")

    m.global.RPAY.cmd = {
      cmd: "getPurchases"
      data: {}
    }

    if m.statusDoRecovery <> invalid then
      ' cancelar, recargar vcard
      ' onHold, mandar a play

      if m.statusDoRecovery = "Canceled"
        getContent(m.id)
      else if m.statusDoRecovery = "OnHold"
        continuoPlay()
      else if m.statusDoRecovery = "InGrace"
        continuoPlay()
      end if
    end if
  end if

  ' print ghGetChild(data, "result")
  ' print ghGetChild(data, "result.recoveryProducts")

  ' cant = ghGetChild(data, "result.recoveryProducts", []).Count()
  ' for p = 0 to cant - 1
  '   child = data.getChild(p)

  '   print child
  ' end for
end sub

sub handleResumeSeasons(event)
  root = event.GetRoSGNode()

  if root.selected = "" then
    ' si dio back no hago nada
    m.top.loading = false
  else if root.selected = "reanudar" then
    sendPlay(false)
  else
    sendPlay(false, true)
  end if
end sub

sub sendPlay(trailer = false, inicio = false)
  ' desactivo el autoplay
  m.autoplay = false
  ' para al salir y dar play muestre popup continuar o desde inicio
  m.isDeepLinking = false

  ' guardo el id, para poner foco cuando vuelvo a esta pantalla
  child = m.top.findNode("botonera")
  m.focusId = ghGetChild(child, "id")
  child.focus = false

  m.top.loading = true

  ' cuando cierro el player tengo que saber si lo que se vio fue un trailer
  ' para recargar la levelUser
  m.playTrailer = trailer

  content = m.top.contenido
  m.contentVideo = content

  iniciarReproduccion(m.dataContent, m.top.payway, trailer, inicio, m.top.seasons, m.enableAds, invalid, m.tipo_abono_contenido, m.top.favorite)
end sub

sub handleVideoChange(event)
  data = event.getData()

  groupId = ghGetChild(data, "id")
  if (groupId <> invalid and groupId <> m.id) then
    getContent(groupId)
  else
    m.top.loading = false
  end if
end sub

sub changeInfoVideo(event)
  data = event.getData()

  content = m.contentVideo
  content.favorited = data

  m.player.SetFields({
    info: content
  })
end sub

sub handleBuyClose(event)
  video = event.GetRoSGNode()

  ' si salio por algun error
  if video.errorMsg <> invalid and video.errorMsg <> "" then
    m.logger.debug("handleBuyClose error")

    error = ghGetChild(video, "errorMsg")
    ' disparar pantalla con msg de error
    showError(false, error)
  end if

  refresh = ghGetChild(video, "refresh", false)

  m.logger.debug("handleBuyClose", { refresh: refresh })
  if refresh = true then
    OnBuySuccess(event)
  end if
end sub

sub handleCloseVideo(event)
  m.logger.debug("handleCloseVideo")

  m.video = invalid

  m.top.UnobserveField("favorite")

  video = event.GetRoSGNode()

  if m.playTrailer = false then
    ' para recargar home levelUser ( carouseles de recien vistos etc)
    handleReload()
  end if

  groupId = ghGetChild(video, "newContent.groupId")
  if (groupId <> invalid and groupId <> m.id) then
    m.logger.debug("cargando nuevo content -- groupId: ", { groupId: groupId })
    getContent(groupId)
  else
    ' last touch para despues refrescar barra de avance
    ghCallApi("LastTouch", "refreshBookmark", "refreshBookmark")
  end if
end sub

sub refreshBookmark()
  m.logger.debug("actualizando barra de avance, sacando el loading")

  apiBookmark = ghCallApi("Bookmark", "handleBookmark", "handleErrorNothing", false)
  apiBookmark.group_id = m.id
  apiBookmark.control = "run"

  m.top.loading = false
end sub

sub handleCloseVideoOld(event)
  m.top.UnobserveField("favorite")

  video = event.GetRoSGNode()

  if m.playTrailer = false then
    ' para recargar home levelUser ( carouseles de recien vistos etc)
    handleReload()
  end if

  groupId = ghGetChild(video, "newContent.groupId")
  if (groupId <> invalid and groupId <> m.id) then
    getContent(groupId)
  else
    ' actualizo la barra de avance
    apiBookmark = ghCallApi("Bookmark", "handleBookmark", "handleErrorNothing", false)
    apiBookmark.group_id = m.id
    apiBookmark.control = "run"

    m.top.loading = false
  end if
end sub

' ---------------------------------
' COMPRAS
' ---------------------------------
sub OnBuySuccess(event)
  scr = event.getRoSGNode()
  ' recargar botones
  m.top.loading = true
  m.autoPlay = true
  getOffers(m.id)
  if scr.routerChild.page.value.option = "BACK" or scr.routerChild.page.value.option = "CANCEL"
    m.autoPlay = false
  end if
end sub

sub switchFavorite(event)
  data = event.getRoSGNode()
  add = ghGetChild(data, "params.#0")

  if add = invalid then
    add = true
    if m.top.favorite = true
      add = false
    end if
  end if

  m.logger.debug("switchFavorite -- add: ", { add: add })

  if add = true then
    sendActionToAnalytic("add to my list")
  else
    sendActionToAnalytic("remove from my list")
  end if

  apiFavorite = ghCallApi("Favorite", "handleFavorite", "handleRecommendationsError", false)
  apiFavorite.group_id = m.id
  apiFavorite.add = add
  apiFavorite.control = "run"
end sub

sub handleFavorite() ' event
  value = true
  if m.top.favorite = true then
    value = false
  end if

  ' recargo levelUser, asi agrega o quita en carousel mi lista
  handleReload()

  setFavorited(value)
end sub

sub openSeasons()
  item = {
    season: m.top.contenido.season,
    episodenumber: m.top.contenido.episodenumber,
  }
  sendActionToAnalytic("seasons")
  sendSelectContentAnalytic()

  seasons = CreateObject("roSGNode", "Seasons")
  seasons.ObserveField("wasClosed", "handleCloseSeasons")
  m.top.routerChild = {
    page: seasons,
    fields: {
      data: m.top.seasons
      title: m.top.title
      item: item
    }
  }
end sub

sub handeButtonSelected(event)
  child = event.GetData()

  value = child

  print ghLogHead();"OnButtonSelected -- value=";value

  if value = "play" then
    m.global.trailerSet = "play"
    reproducirVideo()
  else if value = "trailer"
    m.global.trailerSet = "trailer"
    reproducirVideo(true)
  else if value = "temporadas"
    if m.top.seasons = invalid then
      showError(false, "Error al cargar temporadas")
    else
      openSeasons()
    end if
  else if value = "favorite"
    favoriteParams = true
    if m.top.favorite = true then
      favoriteParams = false
    end if
    ghCheckAuthentication("switchFavorite", "changeAuth", [favoriteParams])

  else if Left(value, 3) = "buy"
    ' Goose! BUY
    ' ---------------------------------------------
    print ghLogHead();"OnButtonSelected -- BUY: ";value;" ";Mid(value, 4)
    ' print ghLogHead();"OnButtonSelected -- CONTENIDO: ";m.top.contenido
    ' result = {}
    for b = 0 to m.buybuttons.Count() - 1 ' busco la data del boton
      m.buyB = m.buyButtons[b]
      if Mid(value, 4) = m.buyB.offerid then
        openBuyView()
        'NavCheckoutFieldsEntry()

        exit for
      end if
    end for
    ' ---------------------------------------------
  end if
end sub

sub openBuyView()
  sendFullAnalytic(ghGetChild(m.buyB, "price", 0), m.buyB.oneofferdesc)


  ' ----------------------
  ' BuyView V2
  ' buyView = CreateObject("roSGNode", "BuyViewLite")
  buyView = CreateObject("roSGNode", "BuyViewV2")
  ' ----------------------
  buyView.id = "BuyView"
  ' crear otro que no capture error
  buyView.ObserveField("wasClosed", "handleBuyClose")
  m.top.routerChild = {
    page: buyView,
    fields: {
      buyB: m.buyB
      accessCode: m.accessCode
      data: m.top.contenido
      contentId: m.id
      content_name: ghGetChild(m.top.contenido, "title", "")
      content_type: getContentTypeString()
      content_category: ghConvertToStringAndJoin(ghGetChild(m.top.contenido, "categories", []), ", ")
    }
  }
end sub


sub NavCheckoutFieldsEntry()
  print "navigate to NavCheckoutFieldsEntry"
  CheckoutFieldsEntry = CreateObject("roSGNode", "CheckoutFieldsEntry")
  CheckoutFieldsEntry.id = "CheckoutFieldsEntry"
  m.top.routerChild = {
    page: CheckoutFieldsEntry,
    fields: {
      accessCode: ""
      data: ""
      screenType: ""
      checkoutFieldType: "promogate"
    }
  }
end sub

sub handleCloseSeasons(event)
  data = event.GetRoSGNode()

  if data.group_id <> invalid and data.group_id <> "" then
    getContent(data.group_id)
  end if
end sub


sub handleReload() ' event
  ' si las nuevas vcard abiertas cambia el reloadhome,
  ' aviso en cascada hasta llegar a la primer vcard
  m.top.reloadHomeUser = true
end sub

function getContentTypeString() as string
  contentData = m.top.contenido
  if m.contentType = 2 or m.contentType = 3 or m.contentType = 4 or ghGetChild(contentData, "serie_id") <> invalid then
    return "series"
  else
    return "movie"
  end if
end function
