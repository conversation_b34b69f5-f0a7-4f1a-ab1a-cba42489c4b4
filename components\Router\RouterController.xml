<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->


<component name="RouterController" extends="Group" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd">

  <script type="text/brightscript" uri="RouterController.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- @Public -->
    <!--  holds the reference to view that is currently shown. Can be used for checking in onkeyEvent -->
    <field id="currentView" type="node" />
    <!-- @Public -->
    <!-- If developer set this flag channel closes when press back or set close=true on last view -->
    <field id="allowCloseChannelOnLastView" type="boolean" value="true" alwaysNotify="true" />
    <!-- @Public -->
    <!-- If developer set this flag the last View will be closed and developer can open another in wasClosed callback  -->
    <field id="allowCloseLastViewOnBack" type="boolean" value="true" alwaysNotify="true" />
    <!-- @Public -->
    <!-- @WRITE-ONLY. Adds new stack assuming given value as new stack ID and makes it active. -->
    <field id="addStack" type="string" value="" alwaysNotify="true" />
    <!-- @Public -->
    <!--WRITE-ONLY. Accepts stack ID. -->
    <field id="removeStack" type="string" value="" alwaysNotify="true" />
    <!-- @Public -->
    <!--WRITE-ONLY. Accepts stack ID. -->
    <field id="selectStack" type="string" value="" alwaysNotify="true" />
    <!-- @Public -->
    <!--READ-ONLY. ID of the active stack.-->
    <field id="activeStack" type="string" value=""/>
    <!-- @Public -->
    <!-- Function that has to be called when you want to add view to view stack, and set focus to view -->
    <function name="show" />
    <function name="closeAllViews" />
    <function name="replace" />
    <!-- View manager is a reference to node that handles all View stack functionality -->
    <field id="ViewManager" type="node" />
    <!-- internal managemend -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- <ButtonBar id="buttonBar" /> -->
    <!-- <WidgetLayer id="WidgetManager"/> -->
  </children>

</component>
