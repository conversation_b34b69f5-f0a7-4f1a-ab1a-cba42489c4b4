<?xml version="1.0" encoding="utf-8" ?>

<component name="GHToast" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHToast.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz entrada -->
    <field id="time" type="int" value="12" onChange="onTimeChange" />
    <field id="title" type="string" value="***" onChange="onTitleChange" />
    <field id="divisor" type="string" value=" | " alias="divisorText.text" />
    <field id="body" type="string" value="***" onChange="onBodyChange" />
    <field id="backgroundImage" type="string" value="pkg:/images/toast_back_gradient.9.png" alias="background.uri"/>
    <field id="iconImage" type="string" value="" onChange="onIconChange" />
    <field id="contentOffset" type="array" value="[25,15]" onChange="Redraw"/>
    <field id="alignement" type="string" value="center" alias="texts.horizAlignment" />
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <field id="wasClosed" type="boolean" value="false" alwaysNotify="true" />
  </interface>

  <children>
    <Poster id="background" translation="[0,0]" />
    <LayoutGroup id="texts" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" itemSpacings="[0]">
      <Poster id="icon" />
      <Rectangle id="separator" color="0xFFFFFF00" height="10" width="30" visible="true" />
      <Label id="titleText" focusable="false" color="0xFFFFFF" visible="false" />
      <Label id="divisorText" focusable="false" color="0xFFFFFF" visible="false" />
      <Label id="bodyText" focusable="false" color="0xFFFFFF" visible="false" />
    </LayoutGroup>
  </children>

</component>
