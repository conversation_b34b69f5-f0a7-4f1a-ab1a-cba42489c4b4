# vCard

![](pantallas/vcards/2.png)





## Interface

Interfaz de entrada

```xml
<field id="contenido" type="assocarray" />
```

Interfaz de deeplink

```xml
<field id="mediaType" type="string" value="" />
<field id="groupId" type="string" value="" />
```

interfaz de salida

```xml
<field id="jumpTo" type="string" />
```

interfaz interna

```xml
<field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
<field id="debug" type="boolean" value="false" />
```





## Children

![](graficos/vcard-02.png)



- [MenuComponent](component.menucomponent.md)
- [TSelComponent](component.tselcomponent.md)
- GHTag
- GHProgressBar
- GHButtonImg
- GHRowGrid



---

