' ---------------------------------------------
' MenuComponent
' ---------------------------------------------

sub Init()
  m.haveChannel = false

  ' m.top.debug = false
  m.menu = m.top.findNode("menu")
  m.menu.ObserveField("value", "OnMenuSelected")
  m.menu.ObserveField("backSelected", "onBackMenu")
  m.menuBack = m.top.findNode("menuBack")
  m.menuBack.uri = ghGetAsset("", "pkg:/images/TopNavBarBG.png")
  m.logo = m.top.findNode("logo")
  m.logo.uri = ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
  ' ------------------------------------------------
  m.options = []

  nav = m.global.nav ' NAV -----

  if m.top.debug then
    print ghLogHead();"-----------------------------"
    print m.global.nav.items
    print ghLogHead();"-----------------------------"
  end if

  for each item in ghGetChild(nav, "items", [])
    ' Behaviour
    appBehaviourString = ghGetChild(item, "app_behaviour", "")

    appBehaviour = {}
    if appBehaviourString <> invalid or appBehaviourString <> "" then
      ' borro palabra app_beahviour: por si viene mal, para que no falle el parseo
      appBehaviourString = appBehaviourString.Replace("app_beahviour:", "")
      appBehaviour = ParseJson(appBehaviourString)
    end if
    showNode = ghGetChild(appBehaviour, "node_config.show_node", true)
    layout = ghGetChild(appBehaviour, "layout", "")

    if showNode = true then
      if layout = "guidechannels" then
        m.haveChannel = true

        m.options.Push({
          optId: item.code,
          optLabel: {
            component: "GHOption",
            props: { text: item.text }
          }
          optAction: {
            actionType: "function",
            actionName: "MnuLive"
          },
          noSelection: true ' para que no quede seleccionado el menu
        })
      else
        m.options.Push({
          optId: item.code,
          optLabel: {
            component: "GHOption",
            props: { text: item.text }
          }
          optAction: {
            actionType: "level",
            actionName: item.code
          }
        })
      end if
    end if
  end for

  ' m.options.Push({
  '   optId: "subscripciones",
  '   optLabel: {
  '     component: "GHOption",
  '     props: { text: "subscripciones" }
  '   }
  '   optAction: {
  '     actionType: "function",
  '     actionName: "MnuProfile"
  '   }
  ' })


  ' if m.haveChannel = false then
  '   m.options.unshift({ ' Live -----
  '     optId: "live",
  '     optLabel: {
  '       component: "GHOption",
  '       props: { text: "Live" }
  '     }
  '     optAction: {
  '       actionType: "function",
  '       actionName: "MnuLive"
  '     },
  '   })
  ' end if

  m.options.unshift({ ' Search -----
    optId: "search",
    optLabel: {
      component: "GHOptionImg",
      props: {
        height: 36
        width: 57
        imageURI: ghGetAssetByMode("search_icon", "pkg:/images/lupa.png")
        imageHeight: 24
        imageWidth: 24
        imageVertAlign: "center"
        imageHorizAlign: "center"
        color: "0xFFFFFF80"
        selColor: "0xFFFFFFFF"
      }
    }
    optAction: {
      actionType: "function",
      actionName: "MnuSearch"
    }
  })

  m.options.Push({ ' Salida -----
    optId: "logout",
    optLabel: {
      component: "GHOption",
      props: {
        ' width: 80
        text: "Salir"'"Salida"
      }
    }
    optAction: {
      actionType: "function",
      actionName: "MnuLogout"
    },
  })
  buildOptions()
  ' ------------------------------------------------
  ' seteo menu por defecto / nodo por defecto viene en apa
  node = ghGetChild(m.global.nodes, ghGetRegistry("region") + ".susc.default_node")
  ' si encuentro el nodo por defecto de apa, selecciono estemos
  ' si no se encuentra selecciono el primero que no sea el search
  firstItem = invalid
  nodoApa = false
  ' nodo APA
  cant = m.options.count()
  for i = 0 to cant - 1
    nodo = m.options[i]
    if nodo.optId = node then
      nodoApa = true
    end if
    if firstItem = invalid and nodo.optId <> "search" then
      firstItem = nodo
    end if
  end for
  if nodoApa = false then
    node = firstItem.optId
  end if
  ' Global
  if m.global.navSelect <> invalid and m.global.navSelect <> "" then
    node = m.global.navSelect
  end if
  ' Selected
  m.top.menuSelected = node
end sub

' OPTIONS
' -----------------------------
sub buildOptions()
  for opNum = 0 to m.options.Count() - 1
    opcion = m.options[opNum]

    if m.top.debug then print ghLogHead();"buildOptions > ";opNum, opcion.optId

    op = CreateObject("roSGNode", opcion.optLabel.component)
    op.setFields({
      id: opcion.optId,
      value: opcion.optId
    })
    op.setFields(opcion.optLabel.props)
    m.menu.appendChild(op)
  end for
  ' REACOMODAMIENTO MENU ANCHO Y POSICION
  ' ---------------------------------------
  menuPadding = 5 ' padding entre fondo y opciones
  ancho = m.menu.boundingRect().width ' ancho del menu
  anchoPad = ancho + (2 * menuPadding) ' ancho del menu + padding
  izquierda = m.logo.translation[0] + 30 ' margen del logo
  resto = 1280 - izquierda ' lo que queda a la derecha del logo / 1280=pantalla
  padDinamico = ((resto - ancho) / 2) ' agregado por ancho de menu
  m.menuBack.setFields({
    "width": anchoPad
    "translation": [
      izquierda + padDinamico - menuPadding, ' x
      m.menuBack.translation[1] ' y
    ]
  })
  m.menu.setFields({
    "translation": [
      izquierda + padDinamico, ' x
      m.menu.translation[1] ' y
    ]
  })
  ' ---------------------------------------

end sub

' EVENTS
' -----------------------------
sub updateFieldFocus(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- data=";data
  m.menu.focus = data
  if m.top.debug then print ghLogHead();"updateFieldFocus -- listo!"
end sub
sub menuSelected(event)
  data = event.getData()
  if data <> invalid then
    m.menu.selected = data
  end if
end sub
sub onBackMenu() ' event
  ' data = event.getData()
  m.top.backSelected = true
end sub
sub OnMenuSelected(event)
  data = event.getData()
  opcion = getOption(data)

  ' no selecciono el menu, si tiene el parametro noSelection = true
  ' si es false, le digo al menu que seleccionar
  if ghGetChild(opcion, "noSelection", false) <> true then
    m.global.setFields({
      navSelect: ghGetChild(opcion, "optId")
    })

    m.menu.selected = m.global.navSelect
  end if

  if opcion <> invalid then
    accion = ghGetChild(opcion, "optAction")

    if m.top.debug then print ghLogHead();"OnMenuSelected -- ";ghGetChild(opcion, "optLabel")

    if accion.actionType = "level" then ' niveles -----
      if m.top.debug then print ghLogHead();"OnMenuAction jumping to level ", ghGetChild(accion, "actionName")
      if m.top.hasField("routerReset") then
        m.top.routerReset = {
          page: "HomePage",
          fields: {
            nodo: ghGetChild(opcion, "optId")
          }
        }
      end if
    else if ghGetChild(accion, "actionType") = "function" then ' funciones especiales -----
      if m.top.debug then print ghLogHead();"OnMenuAction running", ghGetChild(accion, "actionType")
      if ghGetChild(accion, "actionName") = "MnuSearch" then
        MnuSearch()
      else if ghGetChild(accion, "actionName") = "MnuVivo" then
        ' MnuVivo()
      else if ghGetChild(accion, "actionName") = "MnuLive" then
        MnuLive()
      else if ghGetChild(accion, "actionName") = "MnuPrueba" then
        ' MnuPrueba()
      else if ghGetChild(accion, "actionName") = "MnuLogout" then
        MnuLogout()
      else if ghGetChild(accion, "actionName") = "MnuProfile" then
        MnuSubscriptions()
      end if
    else ' la deribo
      m.top.action = accion
    end if
  end if
  ' end if
end sub
function getOption(name)
  result = invalid
  for opNum = 0 to m.options.Count() - 1
    opcion = m.options[opNum]
    if opcion.optId = name then
      return opcion
    end if
  end for
  return result
end function
