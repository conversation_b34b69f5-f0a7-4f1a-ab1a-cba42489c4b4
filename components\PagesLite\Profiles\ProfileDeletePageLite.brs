' HOMEpage
'

sub Init()
  m.top.debug = true

  LogClear()
  m.logger.debug("Init")

  ' general de pantalla
  m.map = {
    "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid },
    "error": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }

  m.logo = m.top.findNode("logo")
  m.title = m.top.findNode("title")

  m.avatar = m.top.findNode("avatar")
  m.firstname = m.top.findNode("firstname")
  m.message = m.top.findNode("message")

  m.botonera = m.top.findNode("botonera")
  m.delete = m.top.findNode("delete")
  m.cancel = m.top.findNode("cancel")

  m.loading = m.top.findNode("loading")

  drawComponents() ' parámetros de los componentes
end sub
sub drawComponents()
  m.logger.debug("drawComponents.")
  ' logo
  m.logo.setFields({
    translation: [64, 32]
    width: 123
    height: 24
    loadDisplayMode: "scaleToFit"
    uri: ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
  })
  ' title
  m.title.setFields({
    text: ghTranslate("deleteProfile_access_title_label", "Eliminar Perfil", {})
    font: ghGetFont(32, "bold")
    horizAlign: "center"
    translation: [0, 80]
    width: 1280
    height: 70
  })
  ' --FORMULARIO---------------------
  ' avatar
  m.avatar.setFields({
    translation: [560, 140]
    width: "165"
    height: "165"
    uri: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar01.png"
  })
  ' name
  m.firstname.setFields({
    text: ghTranslate("xxxxname", "Nombre", {})
    horizAlign: "center"
    translation: [0, 320]
    font: ghGetFont(21, "regular")
    width: 1280
    height: 70
  })
  ' name
  m.message.setFields({
    text: ghReplaceStr(ghTranslate("deleteProfile_access_description_label", "Este perfil será eliminado y la información de" + chr(10) + "Mis contenidos y actividad no podrá ser recuperada."), "{br}", chr(10)) ' la Key tiene que tener el salto de línea dónde lo quieran
    horizAlign: "center"
    translation: [0, 400]
    font: ghGetFont(20, "regular")
    'width: 1280
    height: 70
    lineSpacing: 0
    wrap: true
  })
  ' botonera
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.setFields({
    layout: "map"
    map: {
      "delete": { "up": invalid, "right": invalid, "down": "cancel", "left": invalid }
      "cancel": { "up": "delete", "right": invalid, "down": invalid, "left": invalid }
    }
  })
  'delete
  m.delete.setFields({
    value: "delete"
    text: ghTranslate("deleteProfile_access_option_button_delete", "ELIMINAR PERFIL", {})
    translation: [480, 490]
    width: 344
    height: 72
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    selBackColor: "#981C15"
    focusColor: "0xFFFFFF"
    backColor: "#4B1512"
  })
  ' cancel
  m.cancel.setFields({
    value: "cancel"
    text: ghTranslate("deleteProfile_access_option_button_cancel", "CANCELAR", {})
    translation: [480, 556]
    width: 344
    height: 72
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    backcolor: "#2E303D"
    selBackColor: "#2E303D"
    focusColor: "0xFFFFFF"
  })
end sub

' ----------------------------
' DATA
' ----------------------------
sub onProfile(event)
  data = event.getData()
  m.logger.debug("onProfile data=", { data: data })
  refresh()
end sub
sub refresh()
  data = m.top.profile
  m.logger.debug("refresh =", { data: data })
  if data <> invalid then ' -------------
    ' campos
    m.avatar.uri = data.user_image
    m.firstname.text = data.firstname
  end if
  m.logger.debug("refresh to ", { mode: m.mode })
end sub
' ----------------------------
' EVENTOS
' ----------------------------
sub onWasShown() ' event
  m.logger.debug("onWasShown.")
  turnFocusTo("botonera")
end sub
' ----------------------------
' ACTIONS
' ----------------------------
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if child.selected then
    child.selected = false ' lo primero es apagarme!
    opcion = child.value
    if opcion = "cancel" then
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      m.top.result = "cancel"
      BackTo()
    else if opcion = "delete" then
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      deleteProfile()
      m.top.close = true
    end if
  end if
end sub

sub BackTo() ' event vuelta a la landing
  m.logger.debug("BackTo")
  m.top.focus = false
  ' m.top.wasClosed = true
  m.top.close = true
end sub
sub BackFromError() ' vuelvo desde error
  turnFocusTo("botonera")
end sub

' ----------------------------
' EDIT
' ----------------------------
sub deleteProfile()
  m.logger.debug("deleteProfile.")
  apiEdit = ghCallApi("ProfileDeleteLite", "deleteProfileOk", "deleteProfileFail", false)
  apiEdit.setFields(gatherCurrentData())
  apiEdit.control = "run"
end sub
sub deleteProfileOk(event)
  data = event.getData()
  m.logger.debug("deleteProfileOk [OK] data=", { data: data })
  m.top.result = "deleted"
  BackTo()
end sub
sub deleteProfileFail(event)
  data = event.getData()
  m.logger.debug("deleteProfileFail [FAIL] data=", { data: data })
  print " "
  print " "
  print "*****************************************"
  print "*****************************************"
  print "ERROR"
  print data
  print "*****************************************"
  print "*****************************************"
  print " "
  print " "
  m.top.result = "failed"
  BackTo()
end sub
' ----------------------------
' COMUNES
' ----------------------------
function gatherCurrentData()
  data = {
    "gamification_id": m.top.profile.gamification_id
    "user_id": m.top.profile.partnerUserId
    ' "firstname": m.name.value
    ' "user_image": m.top.profile.user_image
    ' "is_kids": m.kid.checked
  }
  m.logger.debug("gatherCurrentData data=", { data: data })
  return data
end function
' ----------------------------
' KEYS
' ----------------------------
function onKeyEvent(key, press)
  m.logger.debug("onKeyEvent", { key: key, press: press })
  handled = false
  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
      handled = true
      m.logger.debug("onKeyEvent -- focusedChild ", { focus: m.top.focusedChild.id })
    else
      m.logger.debug("onKeyEvent -- BACK!")
      m.top.result = "cancel"
      BackTo()
    end if
  end if
  return handled
end function
