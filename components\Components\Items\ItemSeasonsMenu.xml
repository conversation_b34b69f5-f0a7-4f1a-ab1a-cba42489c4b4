<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemSeasonsMenu" extends="Group">
  <script type="text/brightscript" uri="ItemSeasonsMenu.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" />
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Rectangle id="background" translation="[0,0]" width="184" height="48" color="#28292F" visible="true">
      <Label id="label" translation="[37.5,14]" text="" width="120" height="25" color="0x000000" visible="true"/>
    </Rectangle>
  </children>

</component>