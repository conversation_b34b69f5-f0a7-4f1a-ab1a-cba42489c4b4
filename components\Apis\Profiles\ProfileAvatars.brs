' https://mfwkweb-api.clarovideo.net
' /services/user/profile/avatars
' ?
' device_id=web&
' device_category=web&
' device_model=web&
' device_type=web&
' device_so=Chrome&
' format=json&
' device_manufacturer=generic&
' authpn=webclient&
' authpt=tfg1h3j4k6fd7&
' api_version=v5.92&
' region=mexico&
' HKS=web60806ec5a3fe2&
' user_id=42781937

sub DataInit()
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/profile/avatars"
  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user"),
    ' manifest
    "appversion": ghGetAppVersion(), ' &appversion=1.0.0
    "region": ghGetRegistry("region"),
    "api_version": ghGetChild(m.global.config, "api.version.ProfileAvatar", m.global.config.api.versions.default),
    'config.mfwk.api_version ' &api_version=v5.86
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  content = {}
  ' response = ghGetChild(res, "response", [])

  if res.errors = invalid then ' ================= OK
    ' social = ghGetChild(response, "socialNetworks", [])
    ' ' ----------------------------------------------------

  else ' ==================================================== FALLO

    if m.top.debug then print ghLogHead();"errors = ";res.errors
    if m.top.debug then print ghLogHead();"errors.error = ";res.errors.error
    err = res.errors.error
    msgErrors = ""
    for e = 0 to err.Count() - 1
      msgErrors += err[e] + "> "
      if m.top.debug then print ghLogHead();"errors = ";err[e]
    end for

    content.addReplace("isSocialOk", false)
    content.error = true
    content.error_code = msgErrors

    ' global
    ' ----------------------------------------------------
    ghDeleteSectionRegistry("social") ' no deberia

  end if

  ' ----------------------------------------------------
  if m.top.debug then
    print "++++++++++++++++++++++++++++++++++++++ social"
    print "++++++++++++++++++++++++++++++++++++++"
    print ghListSectionData("social")
    print "++++++++++++++++++++++++++++++++++++++"
    print "++++++++++++++++++++++++++++++++++++++ content"
    print content
    print "++++++++++++++++++++++++++++++++++++++"
  end if
  ' ----------------------------------------------------

  m.top.content = content
end sub