function ConvertAssociativeArrayToJsonString(assocArray as object) as string
  jsonString = "{"
  first = true
  for each key in assocArray
    if not first
      jsonString = jsonString + ","
    else
      first = false
    end if
    jsonString = jsonString + """" + key + """" + ":"
    value = assocArray[key]
    jsonString = jsonString + ConvertValueToJsonString(value)
  end for
  jsonString = jsonString + "}"
  return jsonString
end function

function ConvertValueToJsonString(value as dynamic) as string
  if Type(value) = "roAssociativeArray"
    return ConvertAssociativeArrayToJsonString(value)
  else if Type(value) = "roArray"
    return ConvertArrayToJsonString(value)
  else if Type(value) = "roString"
    return """" + value + """"
  else if Type(value) = "roBoolean"
    return value.ToStr().ToLower()
  else if Type(value) = "roDouble" or Type(value) = "roFloat" or Type(value) = "roInt" or Type(value) = "roLongInteger"
    return value.ToStr()
  else
    return """" + value.ToStr() + """"
  end if
end function

function ConvertArrayToJsonString(array as object) as string
  jsonString = "["
  first = true
  for each item in array
    if not first
      jsonString = jsonString + ","
    else
      first = false
    end if
    jsonString = jsonString + ConvertValueToJsonString(item)
  end for
  jsonString = jsonString + "]"
  return jsonString
end function