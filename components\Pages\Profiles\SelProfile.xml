<?xml version="1.0" encoding="utf-8" ?>

<component name="SelProfile" extends="Page">
  <script type="text/brightscript" uri="SelProfile.brs" />
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" /> -->
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" /> -->
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Screen.brs" /> -->

  <interface>
    <!-- interfaz de entrada -->
    <field id="contenido" type="assocarray" />
    <!-- deeplink -->
    <field id="mediaType" type="string" value="" />
    <field id="groupId" type="string" value="" />
    <!-- interfaz de salida -->
    <!-- <field id="jumpTo" type="string" /> -->
    <!-- interfaz interna -->
    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" /> -->
    <!-- <field id="debug" type="boolean" value="false" /> -->
  </interface>

  <children>
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" uri="http://cdn.ripoll.ar/roku/landingbg_HD.png" />
    <Label id="title" focusable="false" translation="[272,200]" width="696" height="56" horizAlign = "center" text="vCard" />

    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton value="boton1" id="btn1" width= "368" height= "72" text="btn1" translation="[464,344]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
      <GHButton value="boton2" id="btn2" width= "368" height= "72" text="btn2" translation="[464,416]" backcolor="#6C57A0" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#6C57A0" focusColor="0xFFFFFFFF" />
    </GHButtonGroup>

  </children>

</component>
