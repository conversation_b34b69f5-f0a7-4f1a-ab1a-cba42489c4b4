sub DataInit()
  m.top.debug = false
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/search/predictive"
  m.api.query.Append({
    "node": m.top.node
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"),
    "filterlist": ghGetChild(m.global.filter_list, ghGetRegistry("region") + ".filterlist", ""),
    "value": m.top.query
  })
  m.api.query.Append(ghGetChild(m.global, "search.search_full_quantity", {}))
  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- query="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then print ghLogHead();"Body = ";raw

  if res.errors <> invalid then
    if m.top.debug then print "** ERROR **********************"
    m.top.error = res.errors
    return
  end if

  'todo = ghGetChild(res, "response")
  todo = { "resultados": BuildContent(res) }

  if m.top.debug then
    print ghLogHead();"CONTENT = ";todo
    print ghLogHead();"CONTENT.resultados = ";todo.resultados
  end if
  m.top.content = todo
end sub
' **********************************
' Procesamiento especifico
' **********************************
function BuildContent(res)
  if m.top.debug then
    print "**************************************************"
    print "*[BUSQUEDA]***************************************"
    print "**************************************************"
    print m.global.search
    print m.global.search.search_full_quantity
    print m.global.search.search_predictive_quantity
    print m.global.search.searchPriority
    print "**************************************************"
    print res.msg
    print "**************************************************"
    ' print res.entry
    print res.response
    print res.response.prediction
    print res.response.suggest
    print "**************************************************"
    print "**************************************************"
    print "**************************************************"
  end if
  ' VARS
  content = CreateObject("roSGNode", "GHContent")
  priority = m.global.search.searchPriority
  ' priority = [
  '   "suggest"
  '   "movies_movie"
  '   "series_serie"
  '   ' "foxsports_movie"
  '   ' "foxsports_serie"
  '   ' "foxv3_movie"
  '   ' "foxv3_serie"
  '   "hbo_movies_movie"
  '   "hbo_series_serie"
  '   ' "noggin_movie"
  '   "noggin_series_serie"
  '   "picardia_movies_movie"
  '   ' "picardia_serie"
  '   "edye_movies_movie"
  '   ' "edye_serie"
  '   ' "indycar_movie"
  '   ' "indycar_serie"
  '   "paramount_movies_movie"
  '   "paramount_series_serie"
  '   "stingraykaraoke_movies_movie"
  '   ' "stingraykaraoke_serie"
  '   "stingrayqello_movies_movie"
  '   ' "stingrayqello_serie"
  '   ' "atresplayer_movie"
  '   ' "atresplayer_serie"
  '   ' "live_channels"
  '   ' "foxsports_live_channel"
  '   ' "foxv3_live_channel"
  '   ' "hbo_live_channel"
  '   ' "indycar_live_channel"
  '   ' "edye_live_channel"
  '   ' "cvperu_live_channel"
  '   ' "cvcolombia_live_channel"
  '   ' "atresplayer_live_channel"
  '   ' "nbatv_movie"
  '   ' "nbatv_serie"
  '   ' "nbatv_live_channel"
  '   ' "epgs_now"
  '   ' "epgs_future"
  '   ' "epgs_past"
  '   ' "talents"
  '   ' "genres"
  ' ]
  if m.top.debug then
    print "**************************************************"
    print "*[VARS]*******************************************"
    print "**************************************************"
    print "content=", content
    print "priority=", priority
    print "**************************************************"
    print "**************************************************"
    print "*[PROC]*******************************************"
    print "**************************************************"
  end if
  for each pri in priority
    if m.top.debug then print "----------------------------------------------------"
    path = getArrayPath(pri) ' path en el array del resultado
    ' if m.top.debug then
    if m.top.debug then print ">>> nombre=";pri;" path=";path
    if pri = "suggest" then ' el unico que no esta en prediction
      data = buildSuggest(ghGetChild(res.response, path, []))
    else
      data = ghGetChild(res.response, "prediction." + path, [])
    end if
    ' print data
    ' print data[0]
    if m.top.debug then print "TENGO ";data.count();" datos en ";pri
    if data.count() > 0 then
      roContent = CreateObject("roSGNode", "GHContent")
      roContent.id = pri ' nombre
      roContent.title = ghTranslate("search_title_" + pri, "Search_" + pri)
      ' items
      for each item in data
        if m.top.debug then print ">> ";item.title, item.proveedor_code
        itContent = CreateObject("roSGNode", "GHContent")
        itContent.id = item.id ' nombre
        itContent.TITLE = item.title
        itContent.data = item
        roContent.appendChild(itContent)
      end for
      ' agrego
      content.appendChild(roContent)
    end if
    if m.top.debug then print "----------------------------------------------------"
  end for
  if m.top.debug then print "**************************************************"
  return content
end function
function getArrayPath(nombre)
  return ghReplaceStr(nombre, "_", ".")
end function
function buildSuggest(data) ' saco el common que trae el suggest
  res = []
  for each d in data
    res.push(ghGetChild(d, "common"))
  end for
  return res
end function

