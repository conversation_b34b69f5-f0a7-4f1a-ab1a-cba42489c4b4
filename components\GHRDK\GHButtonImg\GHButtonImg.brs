' GHButton
'
' by<PERSON><PERSON>e(2020) <EMAIL>

function init()
  ' m.top.debug = true
  m.top.focusable = true
  ' componentes
  m.fondo = m.top.findNode("fondo")
  m.ico = m.top.findNode("ico")
  m.icoO = m.top.findNode("icoO")
  m.foco = m.top.findNode("foco")
  m.label = m.top.findNode("label")
  ' dibujo
  Recalculate()
  if m.top.debug then print ghLogHead();"Init **"
end function

sub refresh(event)
  if m.top.debug then
    print " "
    print " * * * ";"field [";event.getField();"]"
    print " "
  end if
  Recalculate(event.getField())
end sub

sub Recalculate(field = "")

  ' ancho
  if field = "width" then
    m.fondo.width = m.top.width
    m.label.width = m.top.width
    if m.top.debug then print ghLogHead();"refresh ** width ";m.fondo.width;"/";m.label.width
  end if
  if field = "height" then
    m.fondo.height = m.top.height
    if m.top.debug then print ghLogHead();"refresh ** height ";m.fondo.height;
  end if

  ' ico
  m.ico.setFields({
    width: m.top.iconWidth
    height: m.top.iconHeight
  })
  if m.top.debug then print ghLogHead();"refresh ** ico ";m.ico.width;"/";m.ico.height
  icoX = (m.top.width - m.ico.width) / 2
  icoY = (m.foco.height - m.ico.height) / 2
  m.ico.translation = [icoX, icoY]
  if m.top.debug then print ghLogHead();"refresh ** icoPOS ";icoX;"/";icoY

  ' ico OVER
  if m.icoO.uri <> "" then
    m.icoO.setFields({
      width: m.top.iconWidthOver
      height: m.top.iconHeightOver
    })
    if m.top.debug then print ghLogHead();"refresh ** ico ";m.ico.width;"/";m.ico.height
    icoX = (m.top.width - m.icoO.width) / 2
    icoY = (m.foco.height - m.icoO.height) / 2
    m.icoO.translation = [icoX, icoY]
    if m.top.debug then print ghLogHead();"refresh ** icoPOS ";icoX;"/";icoY
  end if

  ' foco
  m.foco.setFields({
    width: m.top.focoWidth
    height: m.top.focoHeight
  })
  if m.top.debug then print ghLogHead();"refresh ** foco ";m.foco.width;"/";m.foco.height
  ' label
  m.label.setFields({
    horizAlign: "center"
    font: ghGetComponentFont("GHButtonDesc")
    lineSpacing: 0
    translation: [0, m.top.focoHeight + m.top.textOffset]
  })
  if m.top.debug then print ghLogHead();"refresh ** label ";m.label.translation[1]
  focoX = (m.top.width - m.foco.width) / 2
  focoY = 0
  m.foco.translation = [focoX, focoY]
  if m.top.debug then print ghLogHead();"refresh ** focoPOS ";focoX;"/";focoY

end sub

' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function
sub updateFieldFocus(event)
  over = event.getData()
  if over then
    if m.icoO.uri <> "" then
      m.icoO.visible = true
      m.ico.visible = false
    end if
    m.foco.visible = true
  else
    if m.icoO.uri <> "" then
      m.ico.visible = true
      m.icoO.visible = false
    end if
    m.foco.visible = false
  end if

  if m.top.focus then
    m.top.setFocus(true)
    m.label.color = "#FFFFFF"
  else
    m.label.color = "#BBBBBB"
  end if

  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub

' FIELDS
' -----------------------------
' sub updateFieldText()
'   if m.top.text <> invalid then
'     m.label.text = m.top.text
'   end if
'   if m.top.buttonText <> invalid then
'     m.bLabel.text = m.top.buttonText
'   end if
'   Refresh()
' end sub
' sub updateFieldWrap()
'   Refresh()
' end sub
' sub updateFieldTranslation()
'   if m.top.translation <> invalid then m.background.translation = m.top.translation
' end sub
' sub updateFieldWidth()
'   if m.top.width <> invalid then
'     m.background.width = m.top.width
'     recalcLabelSizeAndPadding()
'   end if
' end sub
' sub updateFieldHeight()
'   if m.top.height <> invalid then
'     m.background.height = m.top.height
'     recalcLabelSizeAndPadding()
'   end if
' end sub
' sub updateFieldHorizAlign()
'   if m.top.horizAlign <> invalid then m.label.horizAlign = m.top.horizAlign
' end sub
' sub updateFieldVertAlign()
'   if m.top.vertAlign <> invalid then m.label.vertAlign = m.top.vertAlign
' end sub
' sub updateFieldBackColor()
'   if m.top.backColor <> invalid then m.background.color = m.top.backColor
' end sub
' sub updateFieldColor()
'   if m.top.color <> invalid then m.label.color = m.top.color
' end sub
' sub updateFieldFocusMap()
'   if m.top.focusMap <> invalid then m.border.uri = m.top.focusMap
' end sub
' sub updateFieldPadding()
'   if m.top.padding <> invalid then recalcLabelSizeAndPadding()
' end sub
' sub updateFieldBorder()
'   if m.top.border <> invalid then recalcLabelSizeAndPadding()
' end sub
' ' utils ------------
' sub Refresh()
'   recalcLabelSizeAndPadding()
'   recalcColors()
' end sub
' sub recalcLabelSizeAndPadding()
'   width = ghXtoAbstract(val(m.top.width))
'   height = ghYtoAbstract(val(m.top.height))
'   paddingFocus = ghXtoAbstract(val(m.top.focusPadding))
'   padding = ghXtoAbstract(val(m.top.padding))
'   paddingFocusSize = paddingFocus * 2
'   paddingSize = padding * 2

'   ' borde select
'   m.border.translation = "[0,0]" ' el unico que va en la misma posicion
'   m.border.height = height
'   m.border.width = width
'   ' fondo del boton
'   m.background.translation = ghVal2Trans(paddingFocus, paddingFocus)
'   m.background.height = height - paddingFocusSize
'   m.background.width = width - paddingFocusSize
'   ' icono del boton / ! esta adentro del fondo
'   x = ((m.background.width - m.ico.width) / 2)
'   y = ((m.background.height - m.ico.height) / 2)
'   m.ico.translation = [x, y]
'   ' textto abajo
'   m.label.translation = ghVal2Trans(paddingFocus + padding, height)
'   m.label.width = width - paddingFocusSize - paddingSize

'   if m.top.wrap then
'     m.label.wrap = m.top.wrap
'     m.label.vertAlign = "top"
'     m.label.height = invalid
'     m.label.lineSpacing = 0
'   end if

'   ' boton con precio o texto, sin icono
'   if m.top.mode <> "icon" then
'     m.ico.visible = false
'     m.bLabel.visible = true
'     m.bLabel.translation = [0, 0]
'     m.bLabel.width = m.background.width
'     m.bLabel.height = m.background.height
'     m.bLabel.vertAlign = "center"
'     m.bLabel.horizAlign = "center"
'     if m.top.wrap then
'       m.bLabel.wrap = m.top.wrap
'       m.bLabel.lineSpacing = 0
'     end if
'   else
'     m.ico.visible = true
'     m.bLabel.visible = false
'   end if

'   ' if m.top.debug then
'   print ghLogHead();"> recalcLabelSizeAndPadding -- ";m.label.text
'   print ghLogHead();"border> ";m.border.translation, m.border.width, m.border.height
'   print ghLogHead();"background> ";m.background.translation, m.background.width, m.background.height
'   print ghLogHead();"label> ";m.label.translation, m.label.width, m.label.height
'   ' end if

'   ' d = m.top.boundingrec()
'   ' print ":: ";d
'   ' rd = m.top.findNode("deb")
'   ' rd.width = d.width
'   ' rd.height = d.width

' end sub

' sub drawIco()
'   m.ico.uri = m.top.icon
'   m.ico.blendColor = m.top.iconColor
' end sub
' END FILE ------------------

sub debugEvent()
  ' data = event.GetData()
  ' obj = event.getRoSGNode()
  ' print ghLogHead("DEBUG");"debugEvent -- [";obj.id;"] =>";data
end sub


