sub DataInit()
  m.top.debug = false
  ' nowTimestamp = date.AsSeconds()

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/cms/leveluser"

  typeNav = ghGetChild(m.global, "config.nav", "nav")
  m.api.query.Append({
    ' no usa lasttouch
    ' "lasttouch": nowTimestamp.toStr(), ' ghGetRegistry("lasttouch_seen", "user"),
    ' "lasttouch": ghGetRegistry("lasttouch_seen", "user"),
    "node": m.top.node
    "region": ghGetRegistry("region"),
    "user_hash": ghGetRegistry("session_userhash", "user"),
    "type": typeNav
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if

  if ghGetChild(res, "errors") <> invalid then
    if m.top.debug then print "** ERROR **********************"
    m.top.error = ghGetChild(res, "errors")
    return
  end if
  if m.top.debug then print "---------------------------------------"
  result = []
  cintas = ghGetChild(res, "response.modules.module")
  for each cinta in cintas
    obCinta = CreateObject("roSGNode", "GHContent")
    if m.top.debug then print ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>";
    if m.top.debug then print cinta.name
    obCinta.id = cinta.name
    comps = ghGetChild(cinta, "components.component")
    for each comp in comps
      if m.top.debug then print "---------------------------------------"
      if m.top.debug then print comp.name;" type=";comp.type

      if comp.name = "header" then
        obCinta.title = comp.properties.large
      else if comp.name = "carrousel" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
          LongOKType: ghGetChild(comp, "properties.type")
        })

        if m.top.debug then
          for each prop in comp.properties.Items()
            print prop.key;" = ";prop.value
          end for
        end if
      else
        if m.top.debug then
          for each prop in comp.properties.Items()
            print prop.key;" = ";prop.value
          end for
        end if
      end if
    end for

    ' ------------------------------
    ' children vacios
    for v = 1 to 10
      vacio = CreateObject("roSGNode", "GHContent")
      vacio.title = "" ' "[vacio]"
      vacio.hdposterurl = "pkg:/images/loading.png"
      obCinta.appendChild(vacio)
    end for
    ' result.appendChild(obCinta)
    result.push(obCinta)
    if m.top.debug then print ":: > ";cinta.name
  end for
  if m.top.debug then print "---------------------------------------"
  m.top.content = { cintas: result }
end sub



