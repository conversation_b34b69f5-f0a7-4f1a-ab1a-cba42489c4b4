' ProfileDeleteLite
' https://app.swaggerhub.com/apis/ClaroVideo/profileDelete/1.0.0#/Microframework/post_services_user_v1_profile_delete
' -----------------------

sub DataInit()
  ' m.top.debug = true
  m.logger.debug("DataInit **")

  m.api.method = "POST"
  m.api.url = m.config.mfwk.host + "/services/user/v1/profile/delete"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user_id
  m.api.query.delete("HKS") ' sin user id

  m.api.query.Append({
    "gamification_id": m.top.gamification_id
    "user_id": m.top.user_id
    "region": ghGetRegistry("region")
  })
  m.api.body = ghArray2Query({
    "user_token": ghGetRegistry("user_token", "user")
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' manejo de errores
  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("ProcessData -- ERROR ", { error: errors })
    res.raw = raw
    m.top.error = res
    return
  end if

  data = res.data ' raiz de datos

  ' actualizo lasttouch
  if data.lasttouch?.profile <> invalid then
    ghSetRegistry("lasttouch_profile", ghGetChild(data, "lasttouch.profile", ""), "user")
  end if

  ' respuesta
  m.top.content = {
    data: data
  }
end sub
