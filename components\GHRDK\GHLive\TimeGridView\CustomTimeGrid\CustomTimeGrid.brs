' CustomTimeGrid is used to know if user press down right or left remote button
' and does not release it

sub Init()
  ' m.top.timeBarTwoLineLayout = true ' dos lineas en la hora
  ' m.top.timeBarMessageText = "GOO" ' mensajito hora en el item
  buildKeyTimer() ' timer long OK ---
  m.top.observeField("programSelected", "OnProgramSelected")
  m.selected = 0
end sub

sub OnProgramSelected(event)
  data = event.getData()
  m.selected = data
end sub

function OnKeyEvent(key as string, press as boolean) as boolean
  handled = false
  m.top.isScrolling = press and (key = "right" or key = "left")

  if press then
    m.ktimer.control = "start"
    m.kkey = key
    m.kpress = true
  else
    if m.kpress then
      m.ktimer.control = "stop"
      triggerKey()
    end if
  end if

  return handled
end function

sub buildKeyTimer()
  m.kkey = invalid
  m.kpress = false
  m.kTimer = CreateObject("roSGNode", "Timer")
  m.kTimer.duration = 1
  m.kTimer.repeat = false
  m.kTimer.ObserveField("fire", "triggerKeyTimer")
end sub

sub triggerKeyTimer()
  m.kkey = "long_" + m.kkey
  triggerKey()
end sub

sub triggerKey()
  m.kpress = false ' fuera del timer de teclado

  if m.kkey = "long_OK" then
    m.top.cmd = {
      action: "long_OK",
      item: m.selected,
    }
  else if m.kkey = "OK"
    m.top.cmd = {
      action: "OK",
      item: m.selected,
    }
  end if
end sub