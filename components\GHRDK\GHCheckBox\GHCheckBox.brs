' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes
  m.border = m.top.findNode("border")
  m.tilde = m.top.findNode("tilde")
  m.background = m.top.findNode("background")
  ' label
  m.label = m.top.findNode("label")
  m.label.font = ghGetComponentFont("GHCheckBox")
  m.border.uri = ghGetImageByMode("focus01.9.png")
  refresh()
  ' foco
  m.label.setFocus(true)
end function

'
' EVENTS
' -----------------------------

sub updateFieldSelected() ' event
  if m.top.debug then print ghLogHead();"updateFieldSelected.";m.top.selected
end sub

sub updateFieldChecked(event)
  if m.top.debug then print ghLogHead();"updateFieldChecked. ";m.top.checked
  m.tilde.visible = event.getData()
  refresh()
end sub


function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.selected = true
      m.top.checked = not m.top.checked
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function
sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.setFocus(true)
  end if
  Refresh()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub



' END FILE ------------------