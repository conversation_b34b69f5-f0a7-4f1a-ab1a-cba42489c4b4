sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/payway/akmpbi"
  m.api.query.Append({
    "api_version": ghGetChild(m.global.config, "api.version.Purchase", m.global.config.api.versions.default),
    "object_type": "A",
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })  

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- headers="m.api.headers
    print ghLogHead();"DataInit -- query="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if res = invalid
    m.top.error = ghErrorNetwork(m.api.name, m.api.url, raw)
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub