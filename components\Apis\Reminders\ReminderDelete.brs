sub DataInit()
  m.api.url = m.config.mfwk.host + "/servicesuser/reminder/delete"

  m.api.query.Append({
    "user_hash": ghGetRegistry("session_userhash", "user")
    "region": ghGetRegistry("region"),
    "reminder_id": m.top.reminder_id
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' resultado = {
  '   reminders: response
  '   cant: response.Count()
  ' }
  ' print "****************************"
  ' print "****************************"
  ' print resultado
  ' print "****************************"
  ' print "****************************"

  m.top.content = res

end sub