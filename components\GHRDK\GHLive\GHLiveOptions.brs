sub Init()
  if m.top.debug then print ghLogHead();"Init ***"

  m.map = {
    "options": {
      "up": invalid,
      "right": invalid,
      "down": invalid,
      "left": invalid
    }
  }

  m.card = m.top.findNode("card")
  m.tag = m.top.findNode("tag")
  m.channelImage = m.top.findNode("channelImage")
  m.channelTitle = m.top.findNode("channelTitle")

  m.panelOptions = m.top.findNode("options")
  m.panelOptions.ObserveField("itemSelected", "onItemSelected")
  m.panelOptions.ObserveField("itemFocused", "onItemFocused")

  m.top.ObserveField("visible", "onPanelVisibleChange")

  setFormats()

  initInfo()
end sub

sub setFormats()
  ' ==== panal de numero y imagen del canal ====
  m.card.setFields({
    layoutDirection: "horiz"
    horizAlignment: "left"
    vertAlignment: "top"
    itemSpacings: 0
    translation: [5, 15]
  })

  m.channelImage.setFields({
    uri: ghGetAsset("mini_epg_channel_default", "pkg:/images/Placeholder_tv envivo.png") 'acá va la key
    loadDisplayMode: "scaleToFit"
  })

  m.channelTitle.setFields({
    width: 50
    height: 50
    horizAlign: "center"
    vertAlign: "center"
    text: ""
    text: "106"
    font: ghGetFont(24, "regular")
    color: "#FFFFFF"
  })
  ' =====

  ' ==== Panel Left ====
  m.top.findNode("panelLeft").setFields({
    translation: [0, 0]
    width: 1920
    height: 720
    color: "#000000"
  })
  '  ==== Panel Right ====
  m.top.findNode("panelRight").setFields({
    translation: [880, 0]
    width: 401
    height: 720
    color: "#282828"
  })

  m.top.findNode("langLabel").setFields({
    translation: [16, 70]
    text: ghTranslate("", "OPCIONES DEL PROGRAMA")
    color: "#999999"
    font: ghGetFont(21, "regular")
  })

  ' opciones
  m.panelOptions.setFields({
    translation: [00, 140]
    itemComponentName: "GHLiveOptionsItem"
    numColumns: 1
    numRows: 10
    color: "#FFFFFF"
    itemSize: [500, 30]
    itemSpacing: [0, 50]
    drawFocusFeedback: false
    vertFocusAnimationStyle: "floatingFocus"
  })

end sub

sub isCurrentEvent(program)
  ' hora actual
  curTime = CreateObject("roDateTime").AsSeconds()

  if program <> invalid then
    tIni = program.playstart
    tEnd = program.playstart + program.playduration

    if curTime >= tIni and curTime <= tEnd then ' es el actual
      print "ahora"
      if ghGetDisplayMode() = "FHD" then
        m.tag.uri = ghGetAsset("", "pkg:/images/tag_ya_emitido.png")
      else
        m.tag.uri = ghGetAsset("", "pkg:/images/tv_ahora.png")
      end if
      ' m.infoSeason.text = gh Translate("languaje_screen_label_temporada", "Temp.") + " " + ghGetChild(data, "season")
    else
      print "mastarde"
      if ghGetDisplayMode() = "FHD" then
        m.tag.uri = ghGetAsset("", "pkg:/images/tag_mas_tarde.png")
      else
        m.tag.uri = ghGetAsset("", "pkg:/images/tv_mas_tarde.png")
      end if
    end if
  end if
end sub

sub onChannelUpdate(event)
  data = event.getData()

  m.channelImage.uri = data.image
  m.channelTitle.text = data.number
end sub

function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press

  handled = true

  if m.top.visible = true then
    if press then
      if key <> "back" then
        turnFocusTo(guessFocusTo(key))
        handled = true
        if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
      else
        if m.top.debug then print ghLogHead();"onKeyEvent -- BACK!"
        m.top.visible = false
        handled = true
      end if
    end if
  end if
  return handled
end function

function guessFocusTo(direction)
  focusTo = invalid
  current = getCurrentFocus()

  if current <> invalid then
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    else
      focusTo = current
    end if
  end if

  return focusTo
end function

sub turnFocusTo(id)
  current = getCurrentFocus()

  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if

    if m.top.findNode(id).focus <> invalid then
      m.top.findNode(id).focus = true
      m.top.findNode(id).setFocus(true)
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub

function getCurrentFocus()
  current = invalid

  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if

  return current
end function

sub onPanelVisibleChange(event)
  data = event.getData()

  if data then ' entro
    m.panelOptions.visible = true
    m.panelOptions.jumpToItem = 0
    m.panelOptions.setFocus(true)
  else ' salgo ------------------
    m.panelOptions.visible = false
    m.panelOptions.setFocus(false)
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub

sub onItemSelected(event)
  data = event.getData()

  if data <> invalid then
    m.top.selected = m.top.options[data]
    m.top.visible = false

    ' hombre muerto
    m.top.keypressed = "ok"
  end if
end sub

sub onOptionsUpdate(event)
  ls = event.getData()

  options = createObject("RoSGNode", "GHContent")

  for each l in ls
    option = createObject("RoSGNode", "GHContent")
    option.data = l
    options.appendChild(option)
  end for

  m.panelOptions.content = options
end sub

' sub onProgramUpdate(event)
'   data = event.getData()

'   isCurrentEvent(data)

'   m.title.text = ghGetChild(data, "title", "")
'   m.time.text = ghFormatDuration(ghGetChild(data, "duration", invalid))
'   m.desc.text = ghGetChild(data, "description", "")

'   if ghGetChild(data, "serie_id") <> invalid then
'     m.infoSeason.text = gh Translate("languaje_screen_label_temporada", "Temp.") + " " + ghGetChild(data, "season")
'     m.infoEpisode.text = gh Translate("languaje_screen_label_episodio", "Ep.") + " " + ghGetChild(data, "episodenumber")
'     m.infoEpisodeTitle.text = ghGetChild(data, "titleEpisode")
'     m.infoSerie.visible = true
'   else
'     m.infoSerie.visible = false
'   end if
' end sub

sub initInfo()
  m.titleLabel = m.top.findNode("title")
  m.titleLabel.color = "#FFFFFF"
  m.titleLabel.font = ghGetFont(43, "bold")

  m.descriptionLabel = m.top.findNode("description")
  m.descriptionLabel.color = "#FFFFFF"
  m.descriptionLabel.font = ghGetFont(21, "bold")

  m.layoutTalents = m.top.findNode("layoutTalents")
  m.layoutGenre = m.top.findNode("layoutGenre")
end sub

sub onProgramUpdate(event)
  content = event.getData()

  isCurrentEvent(content)

  m.titleLabel.text = ""
  m.descriptionLabel.text = ""

  cant = m.layoutTalents.getChildCount()
  m.layoutTalents.removeChildrenIndex(cant, 0)

  cant = m.layoutGenre.getChildCount()
  m.layoutGenre.removeChildrenIndex(cant, 0)

  if content <> invalid then
    item = content.item
    titleLabelText = content.title

    if content.releaseDate <> invalid and Len(content.releaseDate) > 0 then
      titleLabelText += " | " + content.releaseDate
    end if

    if content.StarRating <> invalid and content.StarRating > 0 then
      titleLabelText += " | " + generateStarsRating(content.StarRating)
    end if

    if content.item <> invalid then
      empiezaArray = ghSplit(content.item.date_begin, " ")
      empieza = empiezaArray[0]

      if Len (empieza) > 1 then
        empieza = empiezaArray[1]
      end if

      terminaArray = ghSplit(content.item.date_end, " ")
      termina = terminaArray[0]

      if Len (termina) > 1 then
        termina = terminaArray[1]
      end if

      m.titleLabel.text = titleLabelText
      m.descriptionLabel.text = content.description
      m.talent = item.talent
      m.rating = item.parental_rating
      m.director = item.ext_director
      m.country = item.ext_country
      m.genre = item.dvb_content
      m.year = item.ext_year

      'setRole(ghTranslate("", "Director:"), ghGetChild(m, "director", ""))
      'setRole(ghTranslate("", "Protagonistas:"), ghGetChild(m, "talent", ""))
      'setRole(ghTranslate("", "País:"), ghGetChild(m, "country", ""))

      if ghGetChild(m, "genre", "") <> "" then
        createChild({
          type: "label",
          props: {
            text: ghGetChild(m, "genre", "")
            font: ghGetFont(18, "regular")
          }
        }, m.layoutGenre)
      end if

      if ghGetChild(m, "year", "") <> "" then
        createChild({
          type: "label",
          props: {
            text: ghGetChild(m, "year", "")
            font: ghGetFont(18, "regular")
          }
        }, m.layoutGenre)
      end if

      if ghGetChild(m, "rating", "") <> "" then
        createChild({
          type: "GHTag",
          props: {
            font: ghGetFont(16, "regular")
            backMap: ghGetImageByMode("2px_back.9.png")
            color: "0xFFFFFFFF"
            text: ghGetRatingLabel(ghGetChild(m, "rating", ""))
            horizPadding: 10
            vertPadding: 6
          }
        }, m.layoutGenre)
      end if

      createChild({
        type: "label",
        props: {
          text: ghFormatDurationEpg(empieza) + " a " + ghFormatDurationEpg(termina)
          font: ghGetFont(18, "regular")
        }
      }, m.layoutGenre)

      if ghGetChild(content, "item.duration", "") <> "" then
        createChild({
          type: "label",
          props: {
            text: ghFormatDuration(ghGetChild(content.item, "duration", invalid))
            font: ghGetFont(18, "bold")
          }
        }, m.layoutGenre)
      end if

    end if
  end if

  ' CONFIGURACIÓN PARA EL LAYOUT DE TALENTOS
  setRole({
    text: ghTranslate("", "Director:")
    font: ghGetFont(17, "bold")
  }, {
    text: ghGetChild(m, "director", "")
    font: ghGetFont(17, "regular")
  })
  setRole({
    text: ghTranslate("", "Protagonistas: ")
    font: ghGetFont(17, "bold")
  }, {
    text: ghGetChild(m, "talent", "")
    font: ghGetFont(17, "regular")
    width: 345
  })
  setRole({
    text: ghTranslate("", "País: ")
    font: ghGetFont(17, "bold")
  }, {
    text: ghGetChild(m, "country", "")
    font: ghGetFont(17, "regular")
  })

end sub

sub setRole(propsLabel, propsText)
  if propsLabel.text <> "" and propsText.text <> "" then
    group = CreateObject("roSGNode", "LayoutGroup")
    group.setFields({ layoutDirection: "horiz", vertAlignment: "center", itemSpacings: "[4]" })

    'en negrita
    prompt = CreateObject("roSGNode", "label")
    prompt.setFields(propsLabel)

    ' en normal
    text = CreateObject("roSGNode", "label")
    text.setFields(propsText)

    group.appendChild(prompt)
    group.appendChild(text)
    m.layoutTalents.appendChild(group)
  end if
end sub

sub createChild(object, layout)
  op = CreateObject("roSGNode", object.type)
  op.setFields(object.props)
  layout.appendChild(op)
end sub

function generateStarsRating(starRating as integer, maxRating = 100 as integer, maxCount = 5 as integer) as string
  stars = {
    empty: chr(9734)
    full: chr(9733)
  }

  result = ""
  pointsPerStar = maxRating / maxCount
  fullCount = starRating / pointsPerStar

  if fullCount - int(fullCount) > 0.5 then
    fullCount = int(fullCount) + 1
  else
    fullCount = int(fullCount)
  end if

  emptyCount = maxCount - fullCount

  while fullCount > 0
    result += stars.full
    fullCount--
  end while

  while emptyCount > 0
    result += stars.empty
    emptyCount--
  end while

  return result
end function

sub onItemFocused()
  if m.top.debug then print ghLogHead();"onItemFocused ** !!!!!!!"
  m.top.keypressed = "x"
end sub