<series id="{content provider id}">
  <titles>
    <title language="en">sample series title</title>
  </titles>
  <images>
    <!--Series images must be 360x270. If language is not provided, en is used.-->
    <image language="en" width="360" height="270">
      <url>http://yourdomain.com/path_to_image/image_name.png</url>
    </image>
  </images>
  <descriptions>
    <description language="en" length="60">This is a description</description>
  </descriptions>
  <originalAirDate>1999-01-01</originalAirDate>
  <!--When including cast or crew, birthdate, while optional, is a preferred value to have so we can try to match against our existing database-->
  <crew>
    <person>
      <firstName>FirstName</firstName>
      <middleName>MiddleName</middleName>
      <lastName>LastName</lastName>
      <image>http://www.yourdomain.com/pathtoimage/image_name.png</image>      <!-- size:270x360 -->
      <role>Director</role>
      <birthDate>1999-01-01</birthDate>
      <deathDate>1999-01-01</deathDate>
    </person>
  </crew>
  <cast>
    <person>
      <firstName>FirstName2</firstName>
      <middleName>MiddleName2</middleName>
      <lastName>LastName2</lastName>
      <image>http://www.yourdomain.com/pathtoimage/imagename.png</image>      <!-- size:270x360 -->
      <role>Voice</role>
      <birthDate>1999-01-01</birthDate>
      <deathDate>1999-01-01</deathDate>
    </person>
  </cast>
  <seasons>
    <season>
      <seasonNumber>1</seasonNumber>
      <images>
        <!--Series images must be 360x270. If language is not provided, en is used.-->
        <image language="en" width="360" height="270">
          <url>http://yourdomain.com/image_path/image_name.png</url>
        </image>
      </images>
      <episodes>
        <episode id="{content provider id}">
          <titles>
            <title language="en">sample series title</title>
          </titles>
          <images>
            <!--Series images must be 360x270. If language is not provided, en is used.-->
            <image language="en" width="360" height="270">
              <url>http://yourdomain.com/image_path/image_name.png</url>
            </image>
          </images>
          <descriptions>
            <description language="en" length="60">
                  Sample description is here.
            </description>
          </descriptions>
          <episodeNumber>1</episodeNumber>
          <airDate>1999-01-01</airDate>
          <!--When including cast or crew, birthdate, while optional, is a preferred value to have so we can try to match against our existing database-->
          <crew>
            <person>
              <firstName>FirstName</firstName>
              <middleName>MiddleName</middleName>
              <lastName>LastName</lastName>
              <image>http://www.yourdomain.com/pathtoimage/image_name.png</image>              <!-- size:270x360 -->
              <role>Director</role>
              <birthDate>1999-01-01</birthDate>
              <deathDate>1999-01-01</deathDate>
            </person>
          </crew>
          <cast>
            <person>
              <firstName>FirstName2</firstName>
              <middleName>MiddleName2</middleName>
              <lastName>LastName2</lastName>
              <image>http://www.yourdomain.com/pathtoimage/imagename.png</image>              <!-- size:270x360 -->
              <role>Voice</role>
              <birthDate>1999-01-01</birthDate>
              <deathDate>1999-01-01</deathDate>
            </person>
          </cast>
          <ratings>
            <rating>
              <source>Motion Picture Association of America</source>
              <rating>G</rating>
            </rating>
          </ratings>
          <keywords>
            <keyword>
              <type>Mood</type>
              <word>happy</word>
            </keyword>
          </keywords>
          <genres>
            <genre>comedy</genre>
          </genres>
          <videos>
            <video>
              <region>us</region>
              <playId>sample-play-id-for-us-videos</playId>
              <viewOptions>
                <option>
                  <price>1.99</price>
                  <currency>USD</currency>
                  <quality>SD</quality>
                  <license>rental</license>
                </option>
                <option>
                  <quality>SD</quality>
                  <license>subscription</license>
                </option>
              </viewOptions>
            </video>
            <video>
              <region>ca</region>
              <playId>sample-play-id-for-ca-videos</playId>
              <viewOptions>
                <option>
                  <price>1.99</price>
                  <currency>CAD</currency>
                  <quality>SD</quality>
                  <license>rental</license>
                </option>
                <option>
                  <quality>SD</quality>
                  <license>subscription</license>
                </option>
              </viewOptions>
            </video>
            <video>
              <region>gb</region>
              <playId>sample-play-id-for-gb-videos</playId>
              <viewOptions>
                <option>
                  <price>1.99</price>
                  <currency>GBP</currency>
                  <quality>SD</quality>
                  <license>rental</license>
                </option>
                <option>
                  <quality>SD</quality>
                  <license>subscription</license>
                </option>
              </viewOptions>
            </video>
          </videos>
        </episode>
      </episodes>
    </season>
  </seasons>
</series>
