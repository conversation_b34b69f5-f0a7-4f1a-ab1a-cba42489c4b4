' ExperienceService
' DOC https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4062019617/BRF-11673+Todos+OTT+e+IPTV+Claro+video+con+publicidad
' API https://app.swaggerhub.com/apis-docs/ClaroVideo/getuserexperience/1.0.0
' ---------------------------

' DEFINICION
' ------------------
sub Init()
  ' m.top.debug = true
  ' m.test = 1 ' -- solo para pruebas
  m.background = m.top.findNode("background")
  m.panel = m.top.findNode("panel")
  ' vars
  m.enabled = false
  m.paused = false
  m.refreshHours = invalid ' por defecto
  m.refreshHoursCount = 0 ' cuenta de horas de refresco
  ' internos
  m.currUserExperience = "" ' experiencia actual - comparacion
  m.prevUserExperience = "" ' experiencia previa - comparacion
  m.iguales = false ' es igual a la anterior

  drawDebugScreen()
  ShouldStartService()
end sub
sub ShouldStartService()
  print ghLogHead("EXP");"ShouldStartService -- config=";FormatJson(m.global.cv_advertising_config)
  m.refreshHours = m.global.cv_advertising_config?.experience_hour_refresh
  print ghLogHead("EXP");"ShouldStartService -- refreshHours=";m.refreshHours
  if m.refreshHours <> invalid then
    m.enabled = true
  else
    m.enabled = false
  end if
  print ghLogHead("EXP");"ShouldStartService -- enable=";m.enabled
end sub
' EVENTOS
' ------------------
sub onTick()
  if m.top.debug then print ghLogHead("EXP");"onTick -- enabled=";m.enabled;" paused=";m.paused
  if m.enabled then ' habilitacion por config
    m.refreshHoursCount += 1
    if m.refreshHoursCount >= m.refreshHours then
      'accion
      if m.top.debug then print ghLogHead("EXP");"onTick -- RUN! hours=";m.refreshHours;" actual=";m.refreshHoursCount
      if not m.paused then ' pausa por player
        m.prevUserExperience = m.global.nav?.type
        if m.prevUserExperience = invalid then m.prevUserExperience = "" ' no puede ser invalido, sino vacio
        if m.top.debug then print ghLogHead("EXP");"onTick -- prevUserExperience=";m.prevUserExperience
        GetUserExperience()
        m.refreshHoursCount = 0
      end if
    else
      if m.top.debug then print ghLogHead("EXP");"onTick -- NOT NOW hours=";m.refreshHours;" actual=";m.refreshHoursCount
    end if
    panelRefresh()
  end if
end sub
sub onDebugTurn(event)
  data = event.getData()
  print ghLogHead("SERVICE");"onDebugTurn -- ";data
  m.top.findNode("display").visible = data
end sub
sub onPauseTurn(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("EXP");"onPauseTurn -- pause=";data
  m.paused = data
  panelRefresh()
end sub
' API CALL
' ------------------
sub GetUserExperience()
  if m.top.debug then print ghLogHead("EXP");"GetUserExperience >>"
  ghCallApi("NavLite", "GetUserExperienceOK", "GetUserExperienceFAIL") ' API Profile
  ' m.apiGetUserExperience = ghCallApi("GetUserExperienceLite", "GetUserExperienceOK", "GetUserExperienceFAIL") ' API getuserexperience
end sub
sub GetUserExperienceOK()
  m.currUserExperience = m.global.nav?.type
  if m.top.debug then print ghLogHead("EXP");"GetUserExperienceOK -- curr=";m.currUserExperience
  CompareExperiences()
end sub
sub GetUserExperienceFAIL(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("EXP");"GetUserExperienceFAIL -- data=";data
end sub
' COMPARACIONES
' ------------------
sub CompareExperiences()
  ' m.currUserExperience = "GOOSE" ' hardco para pruebas -- fuerza el reload
  if m.currUserExperience = m.prevUserExperience then
    m.iguales = true
    print ghLogHead("EXP");"CompareExperiences ** IGUAL"
    ' m.test = m.test + 1 ' -- solo para pruebas
    ' if m.test > 2 then m.top.pause = true ' -- solo para pruebas
  else
    print ghLogHead("EXP");"CompareExperiences ** CAMBIO!"
    m.iguales = false
    doReload()
  end if
  panelRefresh()
end sub
sub doReload()
  m.paused = true ' me freno solo
  scene = m.top.getScene()
  print ghLogHead("EXP");"doReload ** changed experience."
  scene.callFunc("warmReboot", { logout: true, tag: "ExperienceService >> changed." })
end sub
' DEBUG
' ------------------
sub drawDebugScreen()
  translation = [650, 10]
  height = 350
  width = 600
  m.background.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    color: "#b0ffc5"
    opacity: 0.5
  })
  m.panel.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    infoText: "EXP ** UserExperience **" '	string	none	READ_WRITE	The text to be displayed in the label.
    textColor: "0x00CCFF" '		color	0xFFFFFFFF	READ_WRITE	The color of the text displayed in the label.
    bulletText: ["***"] '		array of strings	none	READ_WRITE	List of strings preceded by a bullet (for example, ["Bullet 1","Bullet 2"]).
    infoText2: "Experience." '		string	none	READ_WRITE	A second text string that can be offset from the first.
    infoText2Color: "#FF0000" '		color	0xFFFFFFFF	READ_WRITE	Specifies the infoText2 string color
    infoText2BottomAlign: true '		boolean	false	READ_WRITE	Specifies whether the infoText2 string is vertically aligned to the bottom of the info pane
  })
end sub
sub panelRefresh()
  ' status
  enabled = "false"
  if m.enabled then enabled = "true"
  iguales = ""
  if m.iguales then iguales = " *IGUALES*"
  paused = ""
  if m.paused then paused = " [PAUSED]"

  m.panel.infoText2 = "[STATUS] enabled=" + enabled + iguales + paused
  log = []
  log.push("hours=" + StrI(m.refreshHours) + " actual=" + StrI(m.refreshHoursCount))
  log.push("Prev >> " + m.prevUserExperience)
  log.push("Curr >> " + m.currUserExperience)
  m.panel.bulletText = log
end sub
