' GHToast
' by<PERSON>oose(20203 <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = false
  ' objects
  m.background = m.top.findNode("background")
  m.icon = m.top.findNode("icon")
  m.separator = m.top.findNode("separator")
  m.texts = m.top.findNode("texts")
  m.title = m.top.findNode("titleText")
  m.divisor = m.top.findNode("divisorText")
  m.body = m.top.findNode("bodyText")
  ' events
  m.top.ObserveField("visible", "onVisibleChange")
  ' controls
  InitialDraw()
  buildVTimer()
  if m.top.debug then print ghLogHead("WIDGET");"Init "
end function
sub InitialDraw()
  if m.top.debug then print ghLogHead("WIDGET");"InitialDraw "
  m.title.setFields({
    visible: false
    font: ghGetComponentFont("GHErrorTitle")
  })
  m.body.setFields({
    visible: false
    font: ghGetComponentFont("GHErrorMessage")
  })
  m.icon.visible = false
  m.icon.loadSync = true
  if m.top.debug then print ghLogHead("WIDGET");"InitialDraw rect.. ";m.texts.boundingRect()
end sub
sub buildVTimer()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer --"
  m.VTimer = CreateObject("roSGNode", "Timer")
  m.VTimer.duration = m.top.time
  m.VTimer.repeat = false
  m.VTimer.ObserveField("fire", "VTimerTrigger")
  m.VTimer.control = "start"
  if m.top.debug then print ghLogHead("WIDGET");"buildKeyTimer -end- "
end sub
sub VTimerTrigger()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer -- GO!"
  m.top.wasClosed = true ' importante para limpiar memoria
  m.top.visible = false
end sub
' EVENTS
' -----------------------------
sub onVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onVisibleChange -- ";data
  if data then m.VTimer.control = "start"
end sub
sub onTimeChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onTimeChange -- ";data
  m.VTimer.control = "stop"
  m.VTimer.duration = data
  m.VTimer.control = "start"
end sub
sub onTitleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onTitleChange -- ";data
  m.title.setFields({
    text: data
    visible: (data <> "***")
  })
  Redraw()
end sub
sub onBodyChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onBodyChange -- ";data
  m.body.setFields({
    text: data
    visible: (data <> "***")
  })
  Redraw()
end sub
sub onIconChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onIconChange -- ";data
  if data <> invalid then
    m.icon.uri = data
    m.icon.visible = true
    m.separator.setFields({
      width: 20
      visible: true
    })
  else
    m.icon.visible = false
    m.separator.setFields({
      width: 0
      visible: false
    })
  end if
  Redraw()
end sub
' DRAW
' -----------------------------
sub Redraw()
  if m.top.debug then print ghLogHead("WIDGET");"Redraw -- GO! alignement="m.top.alignement

  m.divisor.visible = (m.title.visible and m.body.visible)

  m.texts.translation = [0, 0]
  m.texts.horizAlignment = m.top.alignement
  rec = m.texts.boundingRect()

  bW = rec.width + (m.top.contentOffset[0] * 2)
  bH = rec.height + (m.top.contentOffset[1] * 2)
  bX = rec.x - m.top.contentOffset[0]
  bY = rec.y - m.top.contentOffset[1]
  m.background.setFields({ width: bW, height: bH, translation: [bX, bY] })

  if m.top.debug then
    print ghLogHead("WIDGET");"Redraw BoundRec=";" ";rec.x;" ";rec.y;" ";rec.width;" ";rec.height
    print ghLogHead("WIDGET");"Redraw Background=";bX;" ";bY;" ";bW;" ";bH
  end if
end sub
' END FILE
