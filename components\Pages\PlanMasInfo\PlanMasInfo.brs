function init()
  '   ghCallApi("Purchase", "getSubscriptions", "handleError")
  ' en caso de no existir la api hay que agregarla en la carpeta apis


  ' TEXTOS
  m.texto0 = m.top.findNode("texto0")
  m.texto0.font = ghGetFont(20, "medium")
  m.texto0.text = "Las mejores series y películas en tu suscripción y en todos tus dispositivos." 'falta key
  m.description = m.top.findNode("descriptionText")
  m.logoAddon = m.top.findNode("logoAddon")


  'texto Promo 1
  m.texto1 = m.top.findNode("texto1")
  m.texto1.font = ghGetFont(19, "regular")
  m.texto1.text = "Si eres cliente Infinitum, ¡Ya tienes Claro video Catálogo incluido en tu plan!"
  m.logoPromo1 = m.top.findNode("logoPromo1")
  'texto Promo 2
  m.texto2 = m.top.findNode("texto2")
  m.texto2.font = ghGetFont(19, "regular")
  m.texto2.text = "Si tienes Plan Telcel Max 3000 o superior, ¡Ya tienes Claro video Catálogo incluido en tu plan!"
  m.logoPromo2 = m.top.findNode("logoPromo2")

  m.texto3 = m.top.findNode("texto3")
  m.texto3.font = ghGetFont(21, "bold")
  m.texto3.text = "ESTIMADO USUARIO"

  m.texto4 = m.top.findNode("texto4")
  m.texto4.font = ghGetFont(19, "regular")
  m.texto4.text = "Estás a un paso de disfrutar tu contenido seleccionado. Entra a clarovideo.com para finalizar tu proceso de compra. "

  m.texto5 = m.top.findNode("texto5")
  m.texto5.font = ghGetFont(19, "bold")
  m.texto5.text = "Disfruta 30 días sin costo"
  m.texto5.color = "#00A9FF"

  m.texto6 = m.top.findNode("texto6")
  m.texto6.font = ghGetFont(19, "regular")
  m.texto6.text = ", después:"



  'IMAGENES
  m.imagen0 = m.top.findNode("imagen0")
  ' m.imagen0.uri= ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png") 'FALTA KEY, si no es un asset va a ser un get child
  m.imagen1 = m.top.findNode("imagen1")
   'm.imagen1.uri= ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png") 'FALTA KEY, si no es un asset va a ser un get child
  m.imagen2 = m.top.findNode("imagen2")
   'm.imagen2.uri= ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png") 'FALTA KEY, si no es un asset va a ser un get child
  m.imagen3 = m.top.findNode("imagen3")
   'm.imagen3.uri= ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png") 'FALTA KEY, si no es un asset va a ser un get child
  m.imagen4 = m.top.findNode("imagen4")
   'm.imagen4.uri= ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png") 'FALTA KEY, si no es un asset va a ser un get child




  ''BOTONES
  m.buttonOK = m.top.findNode("ok")
  m.buttonOK.text = ghTranslate("contentToDelete_modal_option_button_deleteRecord", "ELIMINAR")
  m.buttonCancel = m.top.findNode("cancel")
  m.buttonCancel.text = ghTranslate("contentToDelete_modal_option_button_cancel", "CANCELAR")
  ''+ chr(10)
  '' buttons
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "ButtonSelected")
  m.botonera.ObserveField("backSelected", "BackSelected")
  m.botonera.map = {
    "ok": { "up": invalid, "right": invalid, "down": "cancel", "left": invalid },
    "cancel": { "up": "ok", "right": invalid, "down": invalid, "left": invalid },
  }
  '
  ghFocusJumpTo("botonera")
end function

sub updateFieldFocus() ' event
 if m.top.focus then
   ghFocusJumpTo("botonera")
 else
   m.top.close = true
 end if
end sub

sub handleContent() ' event
  m.title.text = "HOLAAAAAAAAAAAAAAAA" 'falta key
  m.description.text = m.top.description
  m.image.uri = ghGetChild(m.top, "image", "pkg:/images/loading_horizontal.png")
  m.info.text = ghTranslate("contentToDelete_modal_description_label", "¿Seguro que deseas eliminar ") + chr(10) + m.top.title + ghTranslate("contentToDelete_modal_descriptionEnd_label", "?") 'falta key
end sub

sub ButtonSelected(event)
 child = event.getRoSGNode()

 if child.value = "OK" then
   m.top.selected = m.top.group_id
   m.top.close = true
 else
   m.top.close = true
 end if
end sub
sub BackSelected(event)
  data = event.getData()

  m.top.close = true
end sub