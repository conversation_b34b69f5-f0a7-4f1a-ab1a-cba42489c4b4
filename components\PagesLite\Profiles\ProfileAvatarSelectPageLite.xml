<?xml version="1.0" encoding="utf-8" ?>

<component name="ProfileAvatarSelectPageLite" extends="Page">

  <script type="text/brightscript" uri="ProfileAvatarSelectPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />

  <interface>
    <field id="profile" type="assocarray" onChange="onProfile" />
    <field id="result" type="assocarray" value="" alwaysNotify="true" />
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />

    <Poster id="logo" />
    <Label id="title" />
    <Label id="userName" translation="[970,120]" width="161" horizAlign= "center" />
    <Poster id="avatar" />

    <GHRowList id="theGrid" />

    <GHLoading id="loading" visible="false" />
  </children>

</component>
