sub callMonthTypeGrid()
    monthTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    monthTypeGrid.ObserveField("selected", "setMonthTypeField")
    monthTypeGrid.id = "monthTypeGrid"
    m.top.routerChild = {
        page: monthTypeGrid,
        fields: {
            gridType: "monthType"
            gridData: ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"]
        }
    }
end sub

sub callYearTypeGrid()
    yearTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    yearTypeGrid.ObserveField("selected", "setYearTypeField")
    yearTypeGrid.id = "yearTypeGrid"
    m.top.routerChild = {
        page: yearTypeGrid,
        fields: {
            gridType: "yearType"
            gridData: ["2025", "2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036"]
        }
    }
end sub

sub callEstadoButtonTypeGrid()
    estadoTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    estadoTypeGrid.ObserveField("selected", "setEstadoTypeField")
    estadoTypeGrid.id = "estadoTypeGrid"
    m.top.routerChild = {
        page: estadoTypeGrid,
        fields: {
            gridType: "estadoType"
            gridData: ["Aguascalientes", "Baja California", "Baja California Sur", "Campeche", "Chihuahua", "Chiapas", "Ciudad de México", "Coahuila", "Coahuila", "Durango", "Guanajuato", "Guerrero", "Hidalgo", "Jalisco", "México", "Michoacán", "Morelos", "Nayarit", "Nuevo León", "Oaxaca", "Puebla", "Querétaro", "Quintana Roo", "San Luis Potosí", "Sinaloa", "Sonora", "Tabasco", "Tamaulipas", "Tlaxcala", "Veracruz", "Yucatán", "Zacatecas"]
        }
    }
end sub

sub setMonthTypeField(event)
    data = event.getData()
    print "setMonthTypeField:" data
    m.top.findNode("monthButton").value = data
end sub

sub setYearTypeField(event)
    data = event.getData()
    print "setYearTypeField:" data
    m.top.findNode("yearButton").value = data
end sub


sub setEstadoTypeField(event)
    data = event.getData()
    print "setEstadoTypeField:" data
    m.top.findNode("estadoButton").value = data
end sub
