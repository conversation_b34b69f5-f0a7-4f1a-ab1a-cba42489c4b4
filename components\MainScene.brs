sub Init()
  ' para test
  ' ghDeleteSectionRegistry()
  ' ghDeleteSectionRegistry("user")

  m.logger = CreateLogger()
  m.debug = true

  m.logger.debug("mainScene init", { iniciado: m.iniciado })

  if m.iniciado = invalid then
    m.iniciado = false
  end if

  m.top.backgroundUri = "pkg:/images/splash_HD.png"
  m.top.backgroundColor = "#121212"

  m.top.ObserveField("focusedChild", "GlobalFocusChanged") ' Cambio de Foco Global

  jumpStart()
end sub

sub jumpStart()
  m.logger.debug("jumpStart")

  ' diferenciar hks de startHeaderInfo y de login
  ' si no tiene hks-login, es que no esta logueado
  ' entonces busco en la nube
  HKS = ghGetRegistry("HKS-login")
  m.logger.debug("HKS en registry: ", { hks: HKS })

  if HKS = invalid or HKS = "" then
    m.logger.debug("buscando HKS en nube")

    m.store = CreateObject("roSGNode", "ChannelStore")
    m.store.ObserveField("channelCred", "onGetChannelCredHks")
    m.store.command = "getChannelCred"
  else
    m.apiStartHeaderInfo = ghCallApi("StartHeaderInfo", "startHeaderInfoOk", "startHeaderInfoError")
  end if
end sub

sub onGetChannelCredHks() as void
  json = parsejson(ghGetChild(m.store, "channelCred.json", "{}"))
  HKS = ghGetChild(json, "channel_data")

  m.logger.debug("hks en nube: ", { hks: HKS })
  if HKS <> invalid and HKS <> "" and HKS <> "logout" then
    m.logger.debug("guardando HKS en registry")
    ghSetRegistry("HKS", HKS)
  end if
  m.apiStartHeaderInfo = ghCallApi("StartHeaderInfo", "startHeaderInfoOk", "startHeaderInfoError")
end sub

sub startHeaderInfoError()
  m.logger.error("Error en la llamada a API StartHeaderInfo")

  ErrorInicializacion("XSTH00")
end sub

sub startHeaderInfoOk()
  m.logger.debug("startHeaderInfoOk")

  ' guardo ip para enviar al startClassic y startLite
  m.ipaddr = ghGetChild(m.apiStartHeaderInfo, "content.ipaddr")

  m.apiApa = ghCallApi("Apa", "modeDivider", "apaError")
end sub

sub apaError()
  m.logger.error("Error en la llamada a API Apa")

  ErrorInicializacion("XAPA00")
end sub

function getModelClasicOrLite(forceClassic = false)
  m.logger.debug("getModelClasicOrLite ", { region: ghGetRegistry("region") })

  m.versionSuffix = ""
  model = "classic"

  di = CreateObject("roDeviceInfo")
  m.logger.debug("DeviceInfo", { clientId: di.GetChannelClientId() })

  if forceClassic = true then
    return model
  end if

  'no subir activar modo publicidad
  adConfig = ghGetChild(m.global, "cv_advertising_config", { enable: false })

  m.logger.debug("adConfig: ", { config: adConfig })

  if ghGetChild(adConfig, "enable", false) = true then
    model = "lite"

    ' para usar al llamar a las api o pantallas
    m.versionSuffix = "Lite"
  end if

  return model
end function

sub modeDivider()
  m.logger.debug("modeDivider")

  ' print ghListSectionData()
  ' print ghListSectionData("user")

  ' para test enviar true para forzar el classic
  model = getModelClasicOrLite(false)

  m.logger.debug("model: ", { model: model })

  RegisterServiceManager(model)
  m.global.model = model

  ' guardo el idioma-subtitulo del usuario en el registry
  ghCallApi("ProfileAud")

  if model = "lite" then
    lite_jumpStart(m.ipaddr)
  else
    classic_jumpStart(m.ipaddr)
  end if
end sub

sub ErrorInicializacion(code = "GEN")
  m.logger.debug("ErrorInicializacion")

  setLoading(false)

  title = "Hubo un error inesperado"
  message = "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (" + code + ")"
  accept = "ACEPTAR"
  ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
  ScrGenericMessage.ObserveField("wasClosed", "GenericMessage_Return")
  ScrGenericMessage.SetFields({
    title: title,
    message: message,
    accept: accept,
  })
  m.top.ComponentController.CallFunc("show", { view: ScrGenericMessage })
  ScrGenericMessage.focus = true

  ' borro session si dio error al iniciar
  ghDeleteSectionRegistry()
  ghDeleteSectionRegistry("user")
end sub

sub GenericMessage_Return()
  ' salir de la app
  m.top.GetScene().exitChannel = true
end sub

sub GlobalFocusChanged(event)
  Child = event.getRoSGNode().focusedChild

  haymas = true
  profundidad = 0
  while haymas
    if Child <> invalid then
      nuevoChild = Child.focusedChild ' miro para abajo
      if nuevoChild.isSameNode(Child) then ' si es el mismo, corto
        haymas = false
      else
        Child = nuevoChild
        haymas = true
      end if
      profundidad += 1
      if profundidad > 20 then haymas = false ' valvula de escape
    else
      haymas = false
    end if
  end while

  m.global.currFocus = Child
end sub

function warmReboot(params)
  m.logger.debug("warmReboot", { params: params })

  setLoading(true)

  ' guardo params para el deepLink
  if ghGetChild(params, "dl") <> invalid then
    m.logger.debug("DL: ", { dl: params.dl })

    ghSetRegistry("arguments", FormatJson(params.dl), "DL")

    m.top.launch_args = params.dl
  end if

  ' variables que se usan en jumpStartLite
  m.skipHome = ghGetChild(params, "skipHome", false)
  m.skipProfile = ghGetChild(params, "sameProfile")
  m.wrNode = ghGetChild(params, "node_id")

  if m.skipHome = false then
    ' cerrar todas las pantallas
    m.top.ComponentController.callFunc("closeAllViews", {})
  end if

  m.logout = ghGetChild(params, "logout")
  hks = ghGetRegistry("HKS")

  ' actualizo el hks de la nube, espero respuesta para seguir
  m.store = CreateObject("roSGNode", "ChannelStore")
  m.store.ObserveField("storeChannelCredDataStatus", "onStoreChannelCredData")
  m.store.ObserveField("channelCred", "getHksRokuOk")

  ' si esta haciendo logout borro el hks de la nube
  if m.logout <> invalid then hks = "logout"
  m.store.channelCredData = hks
  m.store.command = "storeChannelCredData"
end function

sub getHksRokuOk()
  ' cuando llega de un logout, tengo que llamar a la startHeaderInfo
  if m.logout <> invalid then
    m.logger.debug("warmreboot para logout: ", { logout: m.logout })

    jumpStart()
  else
    modeDivider()
  end if
end sub