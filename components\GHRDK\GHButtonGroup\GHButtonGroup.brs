' GHButtonGroup

function Init()
  m.top.debug = true
  m.logger = createLogger()

  m.top.valueFocused = m.top.foco

  if m.top.debug then print ghLogHead();"init -- getChildCount ";m.top.getChildCount()
end function

function onKeyEvent(key, press) as boolean
  handled = m.top.handleKey

  if m.top.enabled then
    if press then
      if m.top.debug then print ghLogHead();"ghButton onKeyEvent -- key=";key

      if key = "back" then
        if m.top.backSelEnable = true then
          m.top.backSelected = true
          return true
        end if

        return false
      end if

      getChilds()

      focusTo = guessFocusTo(key)
      if focusTo <> invalid then
        handleFocus(focusTo)
      else
        if isValidExit(key) then
          handled = false
        else
          if m.top.handleKey then
            handled = true
          end if
        end if
      end if

    end if
  end if

  return handled
end function

function guessFocusTo(direction)
  focusTo = invalid

  if m.top.layout = "map" then
    focusTo = guessFocusToByMap(direction)
  else if m.top.layout = "childs" then
    focusTo = guessFocusToByChilds(direction)
  end if

  if m.top.debug then print ghLogHead();"guessFocusTo -- type ";m.top.layout;" to ";focusTo

  return focusTo
end function

function guessFocusToByMap(direction, focused = invalid)
  if focused = invalid then focused = m.top.valueFocused

  if m.top.debug then print ghLogHead();"guessFocusToByMap focused=";focused;" -- direction=";direction

  if m.top.map[focused] <> invalid then
    focusTo = m.top.map[focused][direction]
    ' FALTA TESTEAR ESTO!!!
    ' if m.top.findNode(focusTo).visible = false then
    '   print "***************************** NO esta ";focusTo
    '   focusTo = guessFocusToByMap(direction, focusTo) ' sigo de largo en la misma direccion
    ' end if
  else
    focusTo = m.top.valueFocused
  end if

  return focusTo
end function

function isValidExit(key)
  valid = false
  if key = "up" then
    if m.top.exitUp then valid = true
  else if key = "down" then
    if m.top.exitDown then valid = true
  else if key = "left" then
    if m.top.exitLeft then valid = true
  else if key = "right" then
    if m.top.exitRight then valid = true
    ' else if key = "back" then
    '   if m.top.exitBack then valid = true
  end if

  if m.top.debug then print ghLogHead();"isValidExit -- ";valid

  return valid
end function

function guessFocusToByChilds(direction)
  focusTo = invalid

  if m.top.orientation = "vertical" then
    dPrev = "up"
    dNext = "down"
  else if m.top.orientation = "horizontal" then
    dPrev = "left"
    dNext = "right"
  end if

  m.logger.debug("init guessFocusToByChilds", { direction: direction, selectedId: m.top.valueFocused })

  if direction = dPrev then
    focusTo = getPrevVisibleChild(m.childs, m.top.valueFocused)
  else if direction = dNext then
    focusTo = getNextVisibleChild(m.childs, m.top.valueFocused)
  end if

  m.logger.debug("end guessFocusToByChilds", { focusTo: focusTo })

  return focusTo
end function

function getNextVisibleChild(childs, selectedId)
  foundSelected = false

  for each child in childs
    if foundSelected and child.visible then
      return ghGetChild(child, "id")
    end if

    if child.id = selectedId then
      foundSelected = true
    end if
  end for

  return invalid
end function

function getPrevVisibleChild(childs, selectedId)
  childPrevVisible = invalid

  for each child in childs
    if child.id = selectedId then
      return ghGetChild(childPrevVisible, "id")
    end if

    if child.visible then
      childPrevVisible = child
    end if
  end for

  return invalid
end function

sub getChilds()
  m.childs = []

  for i = 0 to m.top.getChildCount() - 1
    m.childs.push(m.top.getChild(i))
  end for
end sub

sub updateFieldFocus(event)
  data = event.getData()

  m.logger.debug("updateFieldFocus", { data: data })

  if data then
    ' busco primer elemento visible, para darle foco
    firstChildVisible = invalid
    for i = 0 to m.top.getChildCount() - 1
      child = m.top.getChild(i)
      if child.visible then
        firstChildVisible = child.id
        exit for
      end if
    end for

    m.top.valueFocused = firstChildVisible

    handleFocus(m.top.valueFocused)

    getChilds()

    for i = 0 to m.childs.Count() - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
      child.ObserveField("selected", "onChildSelected")
    end for
  else
    handleFocusOut(m.top.valueFocused)

    tot = m.top.getChildCount()
    for i = 0 to tot - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
    end for
  end if
end sub

sub onChildSelected(event)
  child = event.getRoSGNode()

  m.top.value = child.value
end sub

sub handleFocus(id)
  idNode = m.top.findNode(id)

  if idNode <> invalid then
    idNodePrev = m.top.findNode(m.top.valueFocused)
    if m.top.valueFocused <> id then
      idNodePrev.focus = false
    end if

    idNode.focus = true

    m.top.valueFocused = id
  end if
end sub

sub handleFocusOut(id)
  idNodePrev = m.top.findNode(id)
  if idNodePrev <> invalid then
    idNodePrev.focus = false
  end if
end sub

sub updateValue(event)
  data = event.getRoSGNode()
  if m.top.value = invalid or m.top.value = "" then
    m.top.valueFocused = data.valueFocused
  end if

  m.top.selected = true
end sub

sub updateFoco()
  m.top.valueFocused = m.top.foco
end sub
