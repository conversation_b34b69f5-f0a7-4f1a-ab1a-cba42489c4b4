'LogoutLite
'https://app.swaggerhub.com/apis/ClaroVideo/Logout/1.0.0#/Logout/get_user_v1_ott_logout
sub DataInit()
  m.top.debug = true

  print "+++++++++++++++++++++++++++++++++++++++++"
  print ghListSectionData()
  print ghListSectionData("user")
  print "+++++++++++++++++++++++++++++++++++++++++"

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/v1/ott/logout"

  m.api.query.delete("api_version") ' sin api version

  m.api.query.Append({
    '  "HKS": ghGetRegistry("HKS")
    "region": ghGetRegistry("region"),
    '  "user_id": ghGetRegistry("user_id", "user")
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })

end sub

sub ProcessData(res, raw)
  response = res.response

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ghSetRegistry("isLoggedIn", "false")
  ghDeleteSectionRegistry()
  ghDeleteSectionRegistry("user")

  ' avatar del usuario
  m.global.profile_img = invalid

  m.top.content = { status: "OK" }

  if m.top.debug then print ghLogHead();"Body = ";response
end sub