<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLiveOptions" extends="Group">
  <script type="text/brightscript" uri="GHLiveOptions.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="channel" type="assocarray" onChange="onChannelUpdate" alwaysNotify="true" />
    <field id="program" type="node" onChange="onProgramUpdate" alwaysNotify="true" />

    <field id="options" type="array" onChange="onOptionsUpdate" />
    <field id="selected" type="assocarray" alwaysNotify="true" />

    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="panelLeft">
      <LayoutGroup itemSpacings="[15]" translation="[80,182]">
        <Label id="title" font="font:MediumBoldSystemFont" wrap="false" maxLines="1" />
        <Poster id="tag"/>
        <LayoutGroup id="layoutGenre" translation="[10,10]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[22]" visible="true" />
        <Label id="description" lineSpacing="2" maxLines="2" wrap="true" width="716" height="60"/>
        <LayoutGroup id="layoutTalents" translation="[10,10]" itemSpacings="[15]" layoutDirection="horiz" vertAlignment="center" visible="true" />
      </LayoutGroup>
    </Rectangle>

    <Rectangle id="panelRight">
      <LayoutGroup id="card" layoutDirection="horiz" itemSpacings="[0]" vertAlignment="center">
        <Label id="channelTitle" text="title" />
        <Poster id="channelImage" height="50" width="60"/>
      </LayoutGroup>

      <Label id="langLabel" height= "24" width="272"/>
      <MarkupGrid id="options" />
    </Rectangle>
  </children>

</component>
