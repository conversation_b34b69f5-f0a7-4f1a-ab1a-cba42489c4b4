<?xml version="1.0" encoding="utf-8" ?>

<component name="Plan" extends="Page" >

  <script type="text/brightscript" uri="Plan.brs"/>

  <interface>
    <field id="offer_id" type="string" /> 
  </interface>

  <children>
    <Rectangle id="background" color="#121212" translation="[0,0]" width="1280" height="720"/>

    <Label id="titleText" focusable="false" color="0xFFFFFF" translation="[0,64]" wrap="true" vertAlign="center" horizAlign="center" width="1280" height="72" />
    <Label id="descriptionText" focusable="false" color="0xFFFFFF" translation="[350,100]" wrap="true" horizAlign="center" width="480" text="descripcion de la pelicula" visible="false" />
    <Poster id="imagePoster"  width= "316" height="178" translation="[480,167]" />

    <Label id="info" focusable="false" color="0xFFFFFF" translation="[0,400]" wrap="true" vertAlign="center" horizAlign="center" width="1280" text="" />
<!--
    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton id="ok" value="OK" text="ELIMINAR" width="344" height="72" translation="[460,512]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" focusColor="0xFFFFFF" selBackColor="#981C15" />
      <GHButton id="cancel" value="CANCEL" text="CANCELAR" width="344" height="72" translation="[460,592]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#2E303D" focusColor="0xFFFFFF" selBackColor="#2E303D" />
    </GHButtonGroup> --> 
  </children> 

</component>
