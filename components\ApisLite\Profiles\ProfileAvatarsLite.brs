' ProfileAvatarsLite
' https://app.swaggerhub.com/apis/ClaroVideo/ProfileRead/1.0.0
' -----------------------

sub DataInit()
  ' m.top.debug = true
  m.logger.debug("DataInit **")

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/profile/avatars/collection"
  ' m.api.query.delete("api_version") ' sin api version
  ' m.api.query.delete("user_id") ' sin user id
  ' m.api.query.delete("HKS") ' sin user id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    region: ghGetRegistry("region")
    ' app_key ??? no es mandatorio, reemplaza url??

    ' "user_token": ghGetRegistry("user_token", "user")
    ' "lasttouch": ghGetRegistry("lasttouch_profile", "user")
  })

  m.logger.debug("DataInit -- api=", { api: m.api, params: m.api.query })
end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  m.logger.debug("ProcessData -- res=", res)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' manejo de errores
  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("ProcessData -- ERROR ", { error: errors })
    res.raw = raw
    m.top.error = res
    return
  end if

  data = res.response?.avatars ' raiz de datos
  if data <> invalid then
    grilla = []
    rowCount = data.count()
    for r = 0 to rowCount - 1
      ro = data[r]
      if ro.collection <> invalid then
        row = {}
        row.title = ro.name
        row.items = []
        itemCount = ro.collection.Count()
        for i = 0 to itemCount - 1
          it = ro.collection[i]
          cont = {}
          cont.id = it.id
          cont.title = it.name
          cont.data = it
          row.items.push(cont)
        end for
        grilla.push(row)
      end if
    end for
  end if

  ' respuesta
  m.top.content = {
    rowcount: rowCount,
    content: grilla
  }
end sub
