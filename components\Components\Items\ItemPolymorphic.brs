'
' Carrousellomastop
' tooncharacter
' premiumchannel
' Navchilds
'

sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init"

  ' DEFAULTS !!!
  m.defaultItemComponent = "CarrouselVertical" ' "ItemComponent"
  m.polyChild = invalid
  ' -----
end sub

sub rowListHasFocus(event)
  data = event.getData()

  if m.polyChild <> invalid then
    m.polyChild.setFields({
      "rowListHasFocus": data
    })
  end if
end sub
sub focusPercent(event)
  data = event.getData()

  m.polyChild.setFields({
    "focusPercent": data
  })
end sub
sub rowHasFocus(event)
  data = event.getData()

  if m.polyChild <> invalid then
    m.polyChild.setFields({
      "rowHasFocus": data
    })
  end if
end sub
sub itemHasFocus(event)
  data = event.getData()

  if m.polyChild <> invalid then
    m.polyChild.setFields({
      "itemHasFocus": data
    })
  end if
end sub

sub itemContentChanged() ' event
  data = ghGetChild(m.top.itemContent, "data") ' este funciona mejor
  m.rowType = ghGetChild(data, "rowtype", "std")

  if m.top.debug then print ">> rowtype >>", m.rowType, getComponentName(m.rowType)

  m.polyChild = CreateObject("roSGNode", getComponentName(m.rowType)) ' fabricon un child

  if m.polyChild = invalid then
    print ghLogHead();"itemContentChanged [[ERROR]] ";m.rowType;" no existe."
    if m.top.debug then print ">>", m.defaultItemComponent, getComponentName(m.defaultItemComponent)
    m.polyChild = CreateObject("roSGNode", getComponentName(m.defaultItemComponent))
  end if

  ' lo configuro
  m.polyChild.setFields({
    "debug": m.top.debug ' prendo o apago todo
    "itemContent": m.top.itemContent
    "index": m.top.index
    "rowIndex": m.top.rowIndex
    "wdith": m.top.width
    "height": m.top.height
    "rowListHasFocus": m.top.rowListHasFocus
    "rowHasFocus": m.top.rowHasFocus
  })

  m.top.replaceChildren([m.polyChild], 0) ' lo reemplazo
end sub

function getComponentName(name) as string
  return "Item" + UCase(Left(name, 1)) + LCase(Mid(name, 2))
end function
