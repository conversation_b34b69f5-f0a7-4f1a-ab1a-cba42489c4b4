' LANDINGpage
'

sub Init()
  m.top.debug = true
  m.logger.debug("Init.")

  m.map = {
    "menu": { "up": invalid, "right": invalid, "down": "botonera", "left": invalid }
    "botonera": { "up": "menu", "right": invalid, "down": invalid, "left": invalid }
  }

  ' fondo
  m.fondo = m.top.findNode("fondo")
  m.fondo.setFields({
    height: 1280
    width: 1920
    translation: [0, 0]
    color: "#191919"
  })
  ' imagen
  m.imagen = m.top.findNode("imagen")
  m.imagen.setFields({
    height: 233
    width: 200
    translation: [219, 250]
    uri: ghGetAsset("menu_access_icon_avatars", "")
  })
  ' menu
  m.menu = m.top.findNode("menu")
  ' m.menu.ObserveField("backSelected", "OnExitToHome")

  ' buttons
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnBackSelected")
  m.botonera.setFields({
    exitUp: true
    layout: "childs"
    orientation: "vertical"
    translation: [637, 168]
    horizAlignment: "left"
    backSelEnable: true
    ' debug: true
  })
  ' -----

  adddBotones()
end sub

sub onWasShown() ' event
  m.top.routerAddRouterTo = m.menu ' habilito al menu a usar router
end sub

sub adddBotones()

  LogClear()
  print "============================="
  print "============================="
  print "============================="
  print m.global.profiles_current
  print "============================="
  print "============================="
  print "============================="

  m.logger.debug("buildBotonera")
  ' build botonera
  m.blabla = addMenuButton({
    id: "verPerfiles"
    value: "verPerfiles"
    text: ghTranslate("ver_perfiles", "Ver perfiles")
    rightLabelContent: ">"
    padding: 16
  })
  m.otro = addMenuButton({
    id: "closeSesion"
    value: "closeSesion"
    text: ghTranslate("cerrar_session", "Cerrar sesión")
    rightLabelContent: ">"
    padding: 16
  })
end sub
function addMenuButton(campos)
  m.logger.debug("addMenuButton text=", { text: campos.text })
  btn = CreateObject("roSGNode", "GHButton")
  btn.setFields({
    width: 528
    height: 70
    selBackColor: "#2E303D"
    backColor: "#2E303D"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    font: ghGetFont(26, "regular")
    ' vertAlign:"left" ' no queda muy bien
    ' horizAlign:"left" ' no queda muy bien
  })
  btn.setFields(campos)
  m.botonera.appendChild(btn)
  return btn
end function

' EVENTS
' -----------------------
sub updateFieldFocus(event) ' donde me paro cuando arranca la pantalla
  data = event.getData()
  m.logger.debug("updateFieldFocus focus=", { data: data })
  turnFocusTo("botonera")
end sub
sub OnButtonSelected()
  m.logger.debug("onAction selected", { value: m.botonera.value })
  boton = m.botonera.value
  if boton = "verPerfiles" then
    doProfileSelect()
  else if boton = "closeSesion" then
    doLogout()
  end if
end sub
sub OnBackSelected(event)
  data = event.getData()
  m.logger.debug("onAction", { data: data, value: m.botonera.value })
  doProfileSelectBack()
end sub

' OPCIONES
' -------------------
' logout
sub doLogout()
  m.logger.debug("OPCION:: doLogout.")
  m.global.setFields({ navSelect: "" }) ' para que al loguear de nuevo no tenga un menu seleccionado
  ghCallApi("LogoutLite", "doLogoutBack", "doLogoutBack")
end sub
sub doLogoutBack()
  m.logger.debug("OPCION:: doLogoutBack.")

  if m.top.hasField("routerReset") then
    GA4Event("screen_view", {
      screen_class: "/login"
      screen_name: "logout",
    })
  end if

  scene = m.top.getScene()
  scene.callFunc("warmReboot", { logout: true })
end sub
' elegir perfil
sub doProfileSelect()
  m.logger.debug("OPCION:: doProfileSelect.")
  m.ProfSel = CreateObject("RoSGNode", "ProfileSelectPageLite")
  m.ProfSel.ObserveField("wasClosed", "doProfileSelectBack")
  m.ProfSel.id = "ProfileSelectPageLite"
  m.top.routerChild = { page: m.ProfSel } ' modo router
end sub
sub doProfileSelectBack()
  m.logger.debug("OPCION:: doProfileSelectBack.")
  m.top.close = true
  m.global.navSelect = invalid ' limpio para que vaya a la home
  m.top.routerReset = {
    page: "HomePageLite",
    fields: { nodo: invalid }' limpio para que recargue la home
  }
end sub


' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
  end if
  return handled
end function
function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function


