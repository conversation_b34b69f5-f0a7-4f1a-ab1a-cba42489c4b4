sub DataInit()
  m.top.debug = false
  m.top.showCurl = false

  m.api.name = "logs"

  m.api.query.delete("HKS")
  m.api.query.delete("api_version")
  m.api.query.delete("authpn")
  m.api.query.delete("authpt")
  m.api.query.delete("device_category")
  m.api.query.delete("device_manufacturer")
  m.api.query.delete("device_model")
  m.api.query.delete("device_type")
  m.api.query.delete("format")
  m.api.query.delete("user_id")
  m.api.headers.delete("partition")

  m.api.method = "POST"
  m.api.url = "https://log-api.newrelic.com/log/v1"

  m.api.body = FormatJson({
    env: ghGetChild(m.global, "config.env")
    level: m.top.type,
    user: ghGetRegistry("email", "user"),
    region: ghGetRegistry("region"),
    HKS: ghGetRegistry("HKS"),
    device_id: m.global.device_id,
    file: m.top.file,
    message: m.top.description,
    context: m.top.data,
  })

  m.api.headers.addReplace("Content-Type", "application/json")
  m.api.headers.addReplace("Api-Key", "99620e949f2a6f008e8f1a6a9558dcf7FFFFNRAL")
end sub

sub ProcessData(res, raw)
  if raw = "true" then
    m.top.content = { result: "OK" }
  else
    m.top.error = { result: "FAILED" }
  end if
end sub