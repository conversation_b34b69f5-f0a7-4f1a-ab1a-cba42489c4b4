sub Init()
  ' number de carrusel que se esta cambiando
  ' para que el press false no pise el press up
  m.cambiandoFoco = 0
  m.moviendoFoco = false

  ' para pulsaciones largas
  m.selected = {}
  m.kkey = ""
  m.kpress = false

  m.press = false

  m.top.ObserveField("rowItemFocused", "OnRowItemFocused")' row/item
  m.top.ObserveField("rowItemSelected", "OnRowItemSelected") ' row/item

  ' desaparezco el foco al mantener pulsado las teclas de avance
  ' evito que se muestre el foco circular en elementos cuadrados
  m.top.fadeFocusFeedbackWhenAutoScrolling = true

  buildKeyTimer()
end sub
sub updateFieldFocus(event)
  data = event.getData()

  if data = false then
    m.moviendoFoco = false
  end if

  m.top.setFocus(data)

  if m.top.debug then print ghLogHead(); "updateFieldFocus to "; data
end sub
sub OnRowItemFocused(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"OnRowItemFocused: ";data

  ' seteo dos veces para que se acomode el foco
  ' algo raro pasaba al pasar del menu al carousel en premium donde el foco es distinto
  m.top.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  m.top.focusBitmapUri = ghGetChild(m.top.focusBitmapUris, "#" + data[0].Tostr(), ghGetImageByMode("4px_Focus.9.png"))

  if m.press = false then
    m.top.position = data
  end if
end sub
sub OnRowItemSelected(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"OnRowItemSelected: row=";data[0];" col=";data[1]
  row = m.top.content.getChild(data[0])
  item = row.getChild(data[1])
  if m.top.debug then print ghLogHead();"OnRowItemSelected itemData=";item

  m.selected = {
    carouselId: row.id
    carouseTitle: row.title
    longOkType: item.longOkType
    position: data,
    data: item
  }

  ' m.top.value = {
  '   position: data,
  '   data: item
  ' }
end sub

function onKeyEvent(key, press) as boolean
  handled = false

  m.press = press

  if press then
    ' si mantengo pulsado el boton OK,
    ' oculto cartel flotante (mi lista, continuar viendo) en home
    m.global.WIDGET.run = { cmd: "remove", id: "LongOkMensaje" }

    ' para detectar las pulsaciones largas
    m.ktimer.control = "start"
    m.kkey = key
    m.kpress = true
    ' ===================================
  else
    ' para las pulsaciones largas
    if m.kpress then
      if m.ktimer.control <> "stop" then
        m.ktimer.control = "stop"
        triggerKey()
      end if
    end if
  end if

  ' si no esta configurado ( por ejemplo el search )
  if m.top.focusBitmapUris = invalid then
    return handled
  end if

  ' solo si estoy en una cinta con varios carouseles (row)
  if m.top.numRows > 1 then
    ' obtengo fila actual, puede ser un %, capturo numero entero y decimales para hacer calculo
    currPositionGral = m.top.currFocusRow

    pospunto = Instr(1, currPositionGral.Tostr(), ".")

    currPositionDec = 0
    if (pospunto > 0) then
      currPositionDec = ("0." + mid(currPositionGral.toStr(), pospunto + 1).Tostr()).toFloat() ' obtengo decimales
    end if

    currPosition = mid(currPositionGral.toStr(), 0).toInt()
    if pospunto > 0 then
      currPosition = mid(currPositionGral.toStr(), 0, pospunto).toInt()
    end if

    if press then
      ' para evitar cuando venis del menu al corousel
      m.moviendoFoco = true

      ' cambiar el foco, segun el elemento superior o inferior
      if key = "up" then
        if currPositionDec > 0.80 then
          currPosition = currPosition + 1
        end if

        ' para que no haga nada en el key up, si el current es el mismo que estoy configurando
        m.cambiandoFoco = currPosition - 1
        currPosition = currPosition - 1
        if currPosition >= 0 and currPosition <= (m.top.focusBitmapUris.Count() - 1) then
          m.top.focusBitmapUri = ghgetChild(m.top.focusBitmapUris, "#" + currPosition.Tostr(), ghGetImageByMode("4px_Focus.9.png"))
        end if
      else if key = "down" then
        ' si los decimales son mayor a 5 sumo uno a currPostion, ejemplo 1.65 asi toma como elemento 2
        if currPositionDec > 0.50 then
          currPosition = currPosition + 1
        end if

        m.cambiandoFoco = currPosition
        if currPosition >= 0 and currPosition <= (m.top.focusBitmapUris.Count() - 1) then
          m.top.focusBitmapUri = ghGetChild(m.top.focusBitmapUris, "#" + currPosition.Tostr(), ghGetImageByMode("4px_Focus.9.png"))
        end if
      end if
    else
      ' para evitar cuando venis del menu al corousel
      if m.moviendoFoco = true then
        if m.cambiandoFoco <> currPosition
          if key = "up" then
            ' si los decimales es menor a 2, entonces ya esta en el carousel de arriba
            if currPositionDec > 0 and currPositionDec < 0.2 then
              currPosition = currPosition - 1
            end if

            ' si se apreta y suelta muy rapido, el current es el mismo que cuando entro en press = false
            ' y entra aca al ser distinto el current con el m.cambiandoFoco -1 del press = false
            if currPositionDec <> 0 or currPosition <> m.cambiandoFoco + 1 then
              if currPosition >= 0 and currPosition <= (m.top.focusBitmapUris.Count() - 1) then
                m.top.focusBitmapUri = ghgetChild(m.top.focusBitmapUris, "#" + currPosition.Tostr(), ghGetImageByMode("4px_Focus.9.png"))
              end if
            end if

          else if key = "down" then
            currPosition = currPosition + 1

            ' si los decimales son mayor a 94 en realidad ya esta abajo
            if currPositionDec > 0.94 then
              currPosition = currPosition + 1
            end if

            if currPosition >= 0 and currPosition <= (m.top.focusBitmapUris.Count() - 1) then
              m.top.focusBitmapUri = ghgetChild(m.top.focusBitmapUris, "#" + currPosition.Tostr(), ghGetImageByMode("4px_Focus.9.png"))
            end if
          end if
        end if
      end if
    end if
  end if

  return handled
end function

sub buildKeyTimer()
  if m.top.debug then print ghLogHead();"buildKeyTimer -ini- "

  m.kkey = invalid
  m.kpress = false
  m.kTimer = CreateObject("roSGNode", "Timer")
  m.kTimer.duration = 1
  m.kTimer.repeat = false
  m.kTimer.ObserveField("fire", "triggerKeyTimer")

  if m.top.debug then print ghLogHead();"buildKeyTimer -end- "
end sub

sub triggerKeyTimer()
  m.kkey = "long_" + m.kkey

  triggerKey()
end sub

sub triggerKey()
  if m.selected <> invalid then
    if m.kkey = "long_OK" then
      m.selected.pressLong = true
      m.top.value = m.selected
    else if m.kkey = "OK" then 
      m.selected.pressLong = false
      m.top.value = m.selected
    end if
  end if
end sub
