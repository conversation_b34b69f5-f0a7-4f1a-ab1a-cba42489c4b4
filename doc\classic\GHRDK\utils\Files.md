# Files.brs

[toc]

## loadJsonFile

Carga un archivo de tipo Json y convierte su contenido en un objeto



<u>Definición:</u>

```basic
function loadJsonFile(url) as object
```



<u>Parámetros</u>

| Nombre | Tipo   | Default | Descripción                                                  |
| ------ | ------ | ------- | ------------------------------------------------------------ |
| url    | string |         | ubicación del archivo a levantar, en el formato `pkg:/<dir>/<file>` |

---

## ghArray2Query

Convierte un objeto en un string de formato query con el formato `&key=valor`. 

Opcionalmente se puede definir que no se incluya el `?` al principio del query



<u>Definición:</u>

```basic
function ghArray2Query(items, headChar = "?")
```



<u>Parámetros:</u>

| Nombre   | Tipo   | Default | Descripción                               |
| -------- | ------ | ------- | ----------------------------------------- |
| items    | string |         | objeto tipo clave--valor.                 |
| headChar | string | ?       | caracter a incluir al principio del query |

---