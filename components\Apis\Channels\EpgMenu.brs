sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/epg/menu"
  m.api.query.Append({
    "region": ghGetRegistry("region")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

function getNodoId(region = "mexico")
  config = m.global.tvConfig

  return ghGetChild(config, region + ".node_id", ghGetChild(config, "default.node_id"))
end function

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  nodo = ghGetChild(response, "nodes.#0.id")

  ' nodo = ""
  ' for i = 0 to ghGetChild(response, "nodes", []).count() - 1
  '   item = ghGetChild(response, "nodes.#" + i.toStr())

  '   if item.code = "tv_todos_not_demo" then
  '     nodo = item.id
  '     exit for
  '   end if
  ' end for

  ' nodo = getNodoId(ghGetRegistry("region"))
  version = m.global.epg.version
  m.global.setFields({
    epg: {
      version: version
      nodoId: nodo
    }
  })

  m.top.content = { nodo: nodo }
end sub