# Router

# Page

---

### routerClose

Cierra la página actual y vuelve a mostrar la anterior

_Known Bugs: Tiene problemas con el foco a la vuelta_

#### Ejemplo

```javascript
m.top.routerClose = true;
```

---

### routerChild

Abre una nueva ventana que se pushea al stack.
No quita la anterior.

#### Ejemplo

Estoy en la `LandingPage` y quiero abrir la `LoginPage`, pero que si el usuario presiona back, vuelta aquí.

```javascript
m.top.routerChild = {
  page: "LoginPage",
};
```

---

### routerJump

Abre una nueva ventana que pisa a la actual dentro del stack.
Quita la anterior.

#### Ejemplo

Estoy en la `LoginPage`, vengo desde la `LandingPage`.
Desde allí **salto** a la `RegisterPage`, pero si el usuario presiona back, quiero que vuelva a la `LandingPage`.

```javascript
m.top.routerJump = {
  page: "RegisterPage",
};
```

---

### routerReset

Limpia el stack y pone como primera página la que se le indica.

#### Ejemplo

Estoy en la `LoginPage`, el usuario se logea con exito. Desde alli **reseteo** el stack y pongo como base la `HomePage`. Si estando parado allí el usuario apreta back, salgo de la aplicación.

```javascript
m.top.routerChild = {
  page: "LoginPage",
};
```

---
