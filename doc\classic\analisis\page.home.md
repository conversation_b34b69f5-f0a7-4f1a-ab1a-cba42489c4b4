# Home

![](pantallas/levels/0.png)





## Interface

Interfaz para DeepLinking

```
dlMediaType string
dlContentId string
dlMode string
```

Interfaz para cambios de página

```xml
<field id="jumpTo" type="string" />
  - "page_vcard" : salta a la página de vcard, usa el groupId como parámetro.
  ? "search" : salta a la página del search, sin parámetros
  ? "profiles" : salta a la página de profiles, sin parámetros
<field id="contenido" type="assocarray" />
```

Interfaz para vCard

```
groupId string
```

interfaz interna

```xml
<field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
<field id="debug" type="boolean" value="true" />
```





## Children

![](graficos/home-01.png)

- [MenuComponent](component.menucomponent.md)
- [LevelComponent](component.levelcomponent.md)





---

