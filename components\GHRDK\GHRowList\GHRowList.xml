<?xml version="1.0" encoding="utf-8" ?>

<component name="GHRowList" extends="RowList">
  <script type="text/brightscript" uri="GHRowList.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfase salida -->
    <field id="position" type="intarray" alwaysNotify="true" />
    <field id="focusBitmapUris" type="array" />
    <field id="value" type="assocarray" />

    <!-- interfase interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <field id="debug" type="boolean" value="false" />
  </interface>

</component>