' ProfileUpdate
' https://app.swaggerhub.com/apis/ClaroVideo/profileUpdate/1.0.0#/Microframework/post_services_user_v1_profile_update
' -----------------------

sub DataInit()
  ' m.top.debug = true
  m.logger.debug("DataInit **")

  ' curl --location 'https://mfwkstbroku-api.clarovideo.net/services/user/v1/profile/update?gamification_id=663e4eaeb0c5465c92588ae3&user_id=53541952&authpn=roku&authpt=IdbIIWeFzYdy&device_category=stb&device_model=generic&device_type=generic&device_name=generic&device_manufacturer=generic&device_id=666&device_so=roku&format=json&region=mexico' \
  ' --header 'partition: claromexhomolog' \
  ' --header 'Cookie: PHPSESSID=6644ff2181e0a' \
  ' --form 'firstname="KIDSaa22"' \
  ' --form 'user_image="https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar01.png?**********"' \
  ' --form 'user_token="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ELZunONFFwzC-PPuqz-Ji03svtx4ydUahgh9Z_vSe68"' \
  ' --form 'is_kids="true"'

  m.api.method = "POST"
  m.api.url = m.config.mfwk.host + "/services/user/v1/profile/update"
  ' m.api.url = m.config.mfwk.host + "/services/user/profile/update"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user_id
  m.api.query.delete("HKS") ' sin user id

  m.api.query.Append({
    "gamification_id": m.top.gamification_id
    "user_id": m.top.user_id
    "region": ghGetRegistry("region")
  })
  m.api.body = ghArray2Query({
    "user_token": ghGetRegistry("user_token", "user")
    "firstname": m.top.firstname
    "user_image": m.top.user_image
    "is_kids": m.top.is_kids
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw= ", { raw: raw })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  m.logger.debug("ProcessData -- res=", { res: res })
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' manejo de errores
  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("ProcessData -- ERROR ", { error: errors })
    res.raw = raw
    m.top.error = res
    return
  end if

  data = res.data ' raiz de datos

  ' actualizo lasttouch
  if data.lasttouch?.profile <> invalid then
    ghSetRegistry("lasttouch_profile", ghGetChild(data, "lasttouch.profile", ""), "user")
  end if

  ' respuesta
  m.top.content = {
    data: data
  }
end sub
