sub playContentAD(enableAD = true, postroll = false)
	content = m.top.content

	m.logger.debug("play player de publicidad (se inicia siempre)")

	if content <> invalid then
		m.video.content = content
		m.video.visible = false

		m.PlayerTask = CreateObject("roSGNode", "PlayerTaskLite")
		m.PlayerTask.observeField("state", "taskStateChanged")
		m.PlayerTask.enableAD = enableAD
		m.PlayerTask.postroll = postroll
		m.PlayerTask.video = m.video
		m.PlayerTask.control = "RUN"
	else
		m.logger.warn("Intento de reproducir sin contenido disponible")
	end if
end sub

sub taskStateChanged(event as object)
	state = event.GetData()

	m.logger.debug("Estado del reproductor de publicidad cambiado a: " + state)

	if state = "done" or state = "stop"
		updateState("done")
	end if
end sub

sub nextEpisode(offers, content)
	m.logger.debug("nextEpisode offers: ", { offers: offers, content: content })

	visible = ghGetChild(offers, "playButton.visible", "0")
	payway = ghGetChild(offers, "playButton.payway_token", "")

	visibleAds = ghGetChild(offers, "playFreemiumButton.visible", "0")
	enableAds = false
	if visibleAds = "1"
		enableAds = true
	end if

	if visible = "0" then
		payway = ghGetChild(offers, "playFreemiumButton.payway_token", "")
	end if

	m.logger.debug("Cargando el siguiente episodio - ads: ", { enableAds: enableAds })

	iniciarReproduccion(content, payway, false, true, m.top.seasons, enableAds, m.top, ghGetChild(offers, "playButton.key"))
end sub

sub getOffers(group_id, onlyPrepare = false)
	m.onlyPrepare = onlyPrepare

	m.nextEpisodeOffer = invalid

	m.logger.debug("Solicitando offers para el group_id: " + group_id)

	m.apiOffers = ghCallApi("PbiLite", "getOfferOk", "getOfferError")
	m.apiOffers.group_id = group_id
	m.apiOffers.control = "run"
end sub

sub getOfferError()
	updateState("error", {
		message: "Se produjo un error al cargar el siguiente episodio, PBI",
	})
end sub

sub getOfferOk(event)
	data = event.getData()
	root = event.getRoSGNode()

	m.logger.debug("Offers recibidas para el siguiente episodio", { data: data })
	m.nextEpisodeOffer = data

	playable = ghGetChild(data, "playButton.visible", "0")

	if playable = "0" then
		m.logger.debug("No lo tiene adquirido, revisando si es free")

		playable = ghGetChild(data, "playFreemiumButton.visible", "0")
	end if

	if playable <> "0" then
		m.logger.debug("El siguiente episodio es reproducible")
		m.nextEpisodePlay = true
	else
		m.logger.debug("El siguiente episodio no es reproducible")
		m.nextEpisodePlay = false
	end if

	getContentData(root.group_id)
end sub

sub getContentData(groupId)
	apiContentData = ghCallApi("ContentData" + m.versionSuffix, "handleContentData", "handleContentDataError", false)
	apiContentData.group_id = groupId
	apiContentData.control = "run"
end sub

sub handleContentData(event)
	data = event.getData()

	m.logger.debug("Datos de contenido recibidos para cargar el siguiente episodio", { data: data })

	m.nextContentData = data

	' si voy a mostrar la endCard, solo tengo que preparar y no cambiar
	if m.onlyPrepare = false
		m.changeEpisode = true
		updateState("contentDataCharged")
	end if
end sub

sub handleContentDataError()
	updateState("error", {
		message: "Se produjo un error al cargar el siguiente episodio",
	})
end sub

sub setEndCard()
	next_group = ghGetChild(m.top, "content.next_group.common")

	if next_group = invalid ' no tengo proximo, soy peli
		m.logger.debug("Configurando end card para película, no hay proximo grupo.")
		m.trk.hasEndCard = false
		return
	end if

	if next_group.id <> ghGetChild(m.endCard.common, "id") then
		m.logger.debug("Configurando end card para serie con proximo group_id: " + next_group.id)

		m.trk.hasEndCard = true
		btnAutoTime = (ghGetChild(m.top, "content.rollingcreditstime", "").toInt() * -1) - 1

		if btnAutoTime > 10 then btnAutoTime = 10
		next_group.timerButtonTime = btnAutoTime
		m.endCard.common = next_group
	end if
end sub

