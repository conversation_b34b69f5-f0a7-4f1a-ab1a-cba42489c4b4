<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLivePanelMiniEPG" extends="Group">
  <script type="text/brightscript" uri="GHLivePanelMiniEPG.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="jumpTo" type="integer" onChange="onJumpTo" />
    <field id="channels" type="array" onChange="onChannelsUpdate" />
    <field id="events" type="node" onChange="onEventsUpdate" />
    <field id="available" type="boolean" value="true" />
    <!-- interfaz de salida -->
    <field id="cmd" type="assocarray" alwaysNotify="true" />
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
    <field id="keypressed" type="string" alwaysNotify="true"/>
  </interface>

  <children>
    <Poster id="pBackground" translation="[0,-550]" width="1280" height="720" uri="pkg:/images/gradientMiniEpg.png"/>
    <Poster id="leftArrow" translation="[36,56]" uri="pkg:/images/flechasMiniEpg/ic_chevron_left.png" />
    <Poster id="upArrow" translation="[154,-15]" uri="pkg:/images/flechasMiniEpg/ic_chevron_up.png" />
    <Poster id="rightArrow" translation="[1218,56]" uri="pkg:/images/flechasMiniEpg/ic_chevron_right.png" />
    <Poster id="downArrow" translation="[154,130]" uri="pkg:/images/flechasMiniEpg/ic_chevron_down.png" />
    <Poster id="msgBack" visible="false" translation="[50,130]" uri="pkg:/images/pill_gray.9.png" />
    <Label id="msgText" visible="false" />
    <!-- datos -->
    <GHLivePanelMiniEPGChannel id="channel" />
    <GHLivePanelMiniEPGItem id="current" />
    <GHLivePanelMiniEPGItem id="later" />
    <GHLoading id="loading" />
  </children>

</component>