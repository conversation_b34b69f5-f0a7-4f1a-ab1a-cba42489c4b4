sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init"

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  ' m.info = m.top.findNode("info")
  m.progress = m.top.findNode("progress")
  ' m.moreInfo = m.top.findNode("moreInfo")
  m.infoCarrusel = m.top.findNode("infoCarrusel")
  m.logoAddon = m.top.findNode("logoAddon")
  'm.logoAddon.uri= ghGetAsset("HD_plan_selector_logo_atresplayer", "") ' cuando suban as FHD, reempazarlas por el ghGetAssetByMode
  ' print m.logoAddon.uri
  m.price = m.top.findNode("price")
  m.price.font = ghGetFont(17, "bold")
  m.currency = m.top.findNode("currency")
  m.currency.font = ghGetFont(10, "regular")
  m.iva = m.top.findNode("iva")
  m.iva.font = ghGetFont(12, "regular")
  m.rectangulo = m.top.findNode("msgBack")
  m.suscInfo = m.top.findNode("suscInfo")
  m.suscInfo.font = ghGetFont(9, "bold")
end sub

sub itemContentChanged(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"itemContentChanged -- ";data

  data = ghGetChild(m.top.itemContent, "data")
  ' visible = ghGetChild(data, "waspurchased")

  m.logoAddon.uri = ghGetAssetByMode("plan_selector_logo_" + ghGetChild(data, "family", ""), "") ' cuando suban as FHD, reempazarlas por el ghGetAssetByMode
  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 165
  m.itemPoster.height = 394
  m.itemPoster.uri = ghGetAsset(ghGetChild(data, "banner", ""))
  m.rectangulo.blendColor = ghTranslate(ghGetChild(data, "style", ""), "#9A75DE") 'colores mal cargados

  m.price.text = ghGetChild(data, "currency", "") + ghGetChild(data, "price", "")'falta dato de la api
  m.currency.text = ghTranslate("subscription_periodicity_month_label_1", "") + getTraslate(ghGetChild(data, "periodicity", ""))
  m.iva.text = ghTranslate("subscription_costTaxIcluded_complement_label_1", "IVA incluido") 'falta dato de la api
  m.suscInfo.text = ghTranslate(ghGetChild(data, "oneofferdesckey", ""), "SUSCRIBIRME MENSUAL") 'falta key

  drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height)
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)
  initTimer()

  ' if data <> invalid and data.rowtype = "planselector" then

  'Plan-selector nombre del plan
  'm.title.setFields({
  '  ' width: m.itemPoster.width - 20
  '  text: ghGetChild(data, "title")
  '  ' text: "titulo de la pelicula largo para ver como queda"
  '  font: ghGetFont(20, "medium")
  '  translation: [0, 160]
  '  color: "0xFFFFFF50"
  '  horizAlign: "center"
  '  vertAlign: "top"
  '})
  ' m.moreInfo.translation = [0, 0]
  ' m.moreInfo.width = 165
  ' m.moreInfo.height = 32

  ' m.info.setFields({
  ' text: ghGetChild(data, "title")
  '   text: gh Translate("", "¿QUÉ INCLUYE?")
  '   font: ghGetFont(22.5, "bold")
  '   translation: [0, 0]
  '   color: "#FFFFFF"
  '   width: 205
  '   horizAlign: "center"
  '   vertAlign: "center"
  ' })


  ' m.info.setFields({
  ' text: ghGetChild(data, "title")
  '   text: ghTranslate("", "¿QUÉ INCLUYE?")
  '   font: ghGetFont(22.5, "bold")
  '   translation: [0, 0]
  '   color: "#FFFFFF"
  '   width: 205
  '   horizAlign: "center"
  '   vertAlign: "center"
  ' })

  ' m.moreInfo.setFields({
  '   ' text: ghGetChild(data, "title")
  '   text: ghTranslate("", "Presiona y sostén OK")
  '   font: ghGetFont(15, "regular")
  '   translation: [0, 0]
  '   color: "#FFFFFF"
  '   horizAlign: "center"
  '   vertAlign: "center"
  ' })
  ' end if
end sub


sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")

  if data <> invalid then
    if data.rowtype = "planselector" then
      if m.top.rowHasFocus = true then
        ' if m.top.focusPercent = 1
        if m.top.itemHasFocus = 1
          m.title.setFields({
            color: "0xFFFFFF"
            width: 185
          })
          ' m.info.visible = true
          ' m.moreInfo.visible = true
          m.infoCarrusel.visible = true
        else
          m.title.setFields({
            color: "0xFFFFFF50"
            width: m.itemPoster.width - 20
          })
          ' m.info.visible = false
          ' m.moreInfo.visible = false
          m.infoCarrusel.visible = false
        end if
      else
        m.title.setFields({
          color: "0xFFFFFF50"
          width: m.itemPoster.width - 20
        })
        ' m.info.visible = false
        ' m.moreInfo.visible = false
        m.infoCarrusel.visible = false
      end if
    end if
  end if
end sub

function getTraslate(text as string)
  ' newValue = ""
  return ghReplaceStr(ghReplaceStr(text, "month", "mes"), "year", "año")
end function