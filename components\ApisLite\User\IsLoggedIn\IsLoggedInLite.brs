' IsLoggedInd
' https://app.swaggerhub.com/apis/ClaroVideo/Isloggedin/1.0.0
' -----------------------

sub DataInit()
  ' m.top.debug = true
  try
    m.api.method = "GET"
    m.api.url = m.config.mfwk.host + "/services/user/v1/ott/isloggedin"
    m.api.query.delete("api_version") ' sin api version
    m.api.query.Append({
      region: ghGetRegistry("region")
    })
    'hardco -----
    ' ghSetRegistry("region", "mexico")
    ' m.top.ipaddr = "***************,44908"
    ' m.top.ipaddr = "***************,44908"
    'hardco -----
    ' iptelmex
    if m.top.ipaddr <> "" then
      region = ghGetRegistry("region")
      ipaddr = m.top.ipaddr

      ipAddrArr = ghSplit(ipaddr, ",") ' fix ipaddr con puerto
      if ipAddrArr.count() > 1 then ipaddr = ipAddrArr[0]
      ipAddrArr = ghSplit(ipaddr, ":") ' por las dudas
      if ipAddrArr.count() > 1 then ipaddr = ipAddrArr[0]
      m.api.query.Append({
        ipaddr: ipaddr
        "region": region
      })
      m.logger.debug("DataInit -- IpTelmex -- ", { ip: ipaddr, region: region })
    end if
    m.logger.debug("DataInit -- api=", { api: m.api, params: m.api.query })
  catch error
    ghErrorDumpToLog("user/isloggedin", {}, error)
    ghErrorDump(error)
    m.top.content = ghErrorGetJson(error)
    m.top.content.addReplace("isLoggedIn", false)
  end try
end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw= ", { raw: raw })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  result = {}
  isLogged = false

  if res = invalid then
    result.addReplace("isLoggedIn", isLogged)
    m.top.content = result
    return
  end if

  err = res.errors
  response = res.data

  if err <> invalid then
    if ghGetChild (err, "code") = "USR_USR_00001" then ' no esta logeado
      print ghLogHead();"ProcessData -- USR_USR_00001 ERROR = ";err
      ghSetRegistry("isLoggedIn", "false")
      ghSetRegistry("user_token", response.user_token, "user")
    else
      print ghLogHead();"ProcessData -- UNKNOWN ERROR = ";err
    end if
    m.top.content = res
  else ' logeado
    m.logger.debug("ProcessData -- response=", { response: response })

    if type(response) = "roAssociativeArray" then
      ' puedo tomar que si tiene user_session está logeado ?
      if type(response.user_session) = "String" then
        if response.user_session <> invalid then
          isLogged = true
          ' Levanto los datos del usuario
          ' user
          ghSetRegistry("isLoggedIn", "true")
          ghSetRegistry("region", response.region)
          ' subRegion
          subRegion = response.subregion
          if subRegion = invalid then subRegion = ""
          ghSetRegistry("subregion", subRegion, "user")
          ' ----------------------------------------------------
          ghSetRegistry("user_id", response.user_id, "user")
          ghSetRegistry("parent_id", ghGetChild(response,"parent_id"), "user") 

          ghSetRegistry("session_userhash", response.session_userhash, "user")
          ghSetRegistry("lasttouch_seen", ghGetChild(response, "lasttouch.seen", ""), "user")
          ghSetRegistry("lasttouch_favorited", ghGetChild(response, "lasttouch.favorited", ""), "user")
          ghSetRegistry("lasttouch_profile", ghGetChild(response, "lasttouch.profile", ""), "user")
          ghSetRegistry("user_token", response.user_token, "user")
          ghSetRegistry("user_session", response.user_session, "user")
          ' ghSetRegistry("language", response.language, "user")
          ghSetRegistry("username", response.username, "user")
          ghSetRegistry("firstname", response.firstname, "user")
          ghSetRegistry("lastname", response.lastname, "user")
          ghSetRegistry("email", response.email, "user")
          ghSetRegistry("region", response.region, "user")
          ghSetRegistry("country_code", response.country_code, "user")
          ' ghSetRegistry("HKS", response.session_stringvalue)
          ' ----------------------------------------------------
        end if
      end if
    end if

    ' REGISTRY
    if isLogged then ghSetRegistry("isLoggedIn", "true") else ghSetRegistry("isLoggedIn", "false")

    ' apis separadas ( v1/superhighlight, v1/profile )
    ' m.global.paywayProfile = ghGetChild(response, "paywayProfile", {}) ' para Youbora
    ' m.global.superhighlight = ghGetChild(response, "superhighlight", []) ' SUPERHIGHLIGHT

    updateGlobalArray("lasttouch", ghGetChild(response, "lasttouch", "")) ' LASTTOUCH

    ' DEVUELVO
    result.addReplace("isLoggedIn", isLogged)
    m.logger.debug("ACCEPTED-TERMS (isloggedin) >> ", { acceptedTerms: ghGetChild(response, "accepted_terms") })
    result.addReplace("accepted_terms", ghGetChild(response, "accepted_terms", 1))
    if m.top.debug then print "result=";result
    m.top.content = result
  end if
end sub
