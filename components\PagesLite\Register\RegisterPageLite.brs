' HOMEpage
'

sub Init()
  m.top.debug = true
  m.focusId = invalid ' para terminos y condiciones
  ' theme
  m.top.getScene().updateTheme = m.global.config.theme

  ' general de pantalla
  m.map = {
    "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid },
    "error": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }

  ' texts
  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(40, "regular")
  m.title.text = ghTranslate("register_access_title_label", "Regístrate", {})
  ' descrip
  m.descrip = m.top.findNode("descrip")
  m.descrip.font = ghGetFont(28, "regular")
  m.descrip.text = ghTranslate("register_access_description_label", "¿Cuál es tu correo electrónico?", {})
  ' user
  m.user = m.top.findNode("user")
  m.user.placeholder = ghTranslate("register_access_placeHolder_textfield", "Correo electrónico", {})
  getRokuUserEmail("gotRokuUserEmail")
  ' pass
  m.pass = m.top.findNode("pass")
  m.pass.placeholder = ghTranslate("register_password_placeHolder_textfield", "Contraseña", {})
  m.pass.title = ghTranslate("register_password_description_label", "Elige una contraseña", {})
  m.pass.message = ghTranslate("password_tooltip_title_label", "Entre 6 y 10 caracteres, may., minúsc., un número y un caracter especial (!, @, #), sin secuencias. ", {})
  ' register
  m.register = m.top.findNode("register")
  m.register.text = ghTranslate("register_access_option_button_next", "SIGUIENTE", {})
  ' cancel
  m.cancel = m.top.findNode("cancel")
  m.cancel.text = ghTranslate("register_access_option_button_cancel", "CANCELAR", {})
  ' terminos
  m.terminos = m.top.findNode("terminos")
  m.terminos.text = ghTranslate("register_access_option_button_termsConditions", "Ver Términos y Condiciones", {})
  m.terminos.font = ghGetFont(16, "regular")
  ' tologin
  m.toLogin = m.top.findNode("toLogin")
  m.toLogin.text = ghTranslate("register_access_option_button_login", "         ¿Ya tienes cuenta?                             Inicia sesión", {})
  m.toLogin.font = ghGetFont(16, "regular")
  m.toLogin.wrap = true
  ' check
  m.check = m.top.findNode("check")
  m.check.text = ghTranslate("register_access_termsConditions_label", "Acepto los términos y condiciones", {})
  ' loading
  m.loading = m.top.findNode("loading")
  ' botonera
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.map = {
    "user": { "up": "toLogin", "right": invalid, "down": "pass", "left": invalid },
    "pass": { "up": "user", "right": invalid, "down": "check", "left": invalid },
    "check": { "up": "pass", "right": "terminos", "down": "register", "left": invalid },
    "terminos": { "up": "pass", "right": invalid, "down": "register", "left": "check" }
    "register": { "up": "check", "right": invalid, "down": "cancel", "left": invalid }
    "cancel": { "up": "register", "right": invalid, "down": "toLogin", "left": invalid }
    "toLogin": { "up": "cancel", "right": invalid, "down": "user", "left": invalid }
  }
  ' error
  m.error = m.top.findNode("error")
  m.error.ObserveField("wasClosed", "BackFromError")

  ' ---------------para testeos----------------
  ' m.user.value = "<EMAIL>" ' para rokugate
  ' m.pass.value = "Abcd!12345"
end sub

' EVENTOS
' ----------------------------
sub onWasShown() ' event
  m.top.signalBeacon("AppDialogInitiate")

  if m.top.debug then print ghLogHead();"onWasShown -- init - ";m.top.focus
  if m.top.debug then print ghLogHead();"onWasShown -- end - ";m.top.focus

  if m.top.loading <> true then
    if m.focusId <> invalid then
      turnFocusTo(m.focusId)
      m.focusId = invalid
    else
      turnFocusTo("botonera")
    end if
  end if
  GA4Event("screen_view", {
    screen_name: "sing up",
    screen_class: "/sing up"
    user_type:getUserTypeGA4(true),
  })
end sub
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if child.selected then
    child.selected = false ' lo primero es apagarme!
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value

    if child.value = "registracion" then ' registracion
      if m.check.checked then
        FlujoRegistracion()
      else ' no acepto terminos y condiciones
        m.error.title = "Error"
        m.error.descrip = ghTranslate("password_tooltip_checkTermsConditions_label_validation", "Debes aceptar los términos y condiciones")
        turnFocusTo("error")
      end if
    else if child.value = "TerminosPage" then
      m.focusId = "terminos"
      m.top.routerChild = {
        page: "TerminosPage",
        fields: {
          "titleField": "tyc_overhang_text"
          "textField": "tyc_full_text_plano"
        }
      }
    else if child.value = "LoginPage" then
      m.top.signalBeacon("AppDialogComplete")
      m.top.routerJump = { page: "LoginPageLite" }
    else if child.value = "Back" then
      BackTo()
    end if
  end if
end sub
sub updateFieldFocus()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- init"
  turnFocusTo("botonera")
  if m.top.debug then print ghLogHead();"updateFieldFocus -- end."
end sub

sub BackTo() ' event vuelta a la landing
  if m.top.debug then print ghLogHead();"BackTo."
  m.top.signalBeacon("AppDialogComplete")
  m.top.routerClose = true
end sub
sub BackFromError() ' vuelvo desde error
  turnFocusTo("botonera")
end sub

' FLUJO de registracion
' ----------------------------
sub FlujoRegistracion()
  ghTurnLoading(true, m.loading, m.botonera)
  ' Register ------------
  if m.top.debug then print ghLogHead();">>> Registracion --- [FlujoRegistracion]"
  m.apiRegister = ghCallApi("Register", "FlujoRegistracion_Ok", "FlujoRegistracion_Error", false)
  m.apiRegister.username = m.top.findNode("user").value
  m.apiRegister.password = m.top.findNode("pass").value
  m.apiRegister.control = "run"
  ' ----------------------------
end sub
sub FlujoRegistracion_Ok() ' event as object
  ' res = event.GetData()
  GA4Event("sign_up", {
    'method: m.user.value,  
    method: "email",  
    user_id: ghGetRegistry("user_id", "user"),
    user_type: getUserTypeGA4(true),
    country: ghGetRegistry("country_code", "user"),
    screen_name: "sign up",
    screen_class: "/sign-up"
  })
  m.apiPushSession = ghCallApi("PushSession", "saliDelApi", "saliDelApi") ' API PushSession
  ghTurnLoading(false, m.loading, m.botonera)
  m.top.signalBeacon("AppDialogComplete")
  ' m.top.jumpTo = "page_home"
  ' m.top.wasClosed = true

  Notify_Roku("Roku_Authenticated")
  'GA4Event("register", {
  '  method: "email"
  '  screen_name: "register",
  '  screen_class: "/login"
  '})

  print " "
  print "%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^"
  print "SALTO AL WARMREBOOT!"
  print "%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^%^"
  print " "
  scene = m.top.getScene()
  scene.callFunc("warmReboot", { model: "lite" })

  ' m.top.routerReset = {
  '   page: "HomePageLite",
  '   fields: { nodo: "" }
  ' }
end sub
sub FlujoRegistracion_Error(event as object) ' flujo con error
  res = event.GetData()

  ghTurnLoading(false, m.loading, m.botonera)
  m.error.title = res.error_code
  m.error.descrip = res.error_msg
  turnFocusTo("error")
end sub
sub saliDelApi() ' event volvi del pushSession
  if m.top.debug then print "*******************************************"
end sub


' ROKU
' ----------------------------
sub gotRokuUserEmail() ' data
  if m.store.userData <> invalid then
    if m.store.userData.email <> invalid then
      m.user.value = m.store.userData.email
    end if
  end if
end sub

' DEFAULTS a optimizar
' ----------------------------
function onKeyEvent(key, press)
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
  end if
  return handled
end function
function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function

