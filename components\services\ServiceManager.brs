' GHButton
' by<PERSON><PERSON>e(2020) <EMAIL>

function Init()
  ' m.top.debug = true
  m.top.focusable = false

  m.services = m.top.findNode("services")
  m.panel = m.top.findNode("panel")
  m.loop = {}

  TimerInit()
  debugDisplayInit()

  m.loading = m.top.findNode("loading")

  if m.top.debug then print ghLogHead("SERVICE");"Init **"
end function
sub debugDisplayInit()
  m.top.findNode("background").setFields({
    color: "0x00559988"
    translation: [10, 10]
    width: 600
    height: 500
    visible: true
  })
  m.panel.setFields({
    translation: [10, 10]
    width: 600
    height: 700
    visible: true
  })
end sub

' TIMER LOOP
' ------------------------------
sub TimerInit()
  if m.top.debug then print ghLogHead("SERVICE");"TimerInit **"
  m.timer = CreateObject("roSGNode", "Timer")
  m.timer.ObserveField("fire", "TimerTick")
  m.timer.repeat = true
  m.timer.duration = 15
  m.timer.control = "start"
  ' arranco directamente...
  TimerTick()
end sub
sub TimerTick()
  if m.top.debug then print ghLogHead("SERVICE");"TimerTick - GO !!"
  ' fecha
  date = CreateObject("roDateTime")
  date.ToLocalTime()
  ' show debug
  if m.top.debug then
    tick = "SERVICE -- cant:" + Str(m.loop.Count()) + " cada" + Str(m.timer.duration) + "s"
    tick += chr(10) + date.ToISOString()
    tick += chr(10) + "-----"
    tick += chr(10) + "Loops>"
  end if
  for each item in m.loop
    if m.loop[item].started then
      m.loop[item].current++
      if m.top.debug then tick += chr(10) + "[" + item + "] "
      if m.loop[item].current >= m.loop[item].to then
        m.loop[item].obj.setField("tick", true) ' ---- GO!
        if m.top.debug then tick += "RUN! "
        m.loop[item].current = 0
      end if
      if m.top.debug then tick += str(m.loop[item].current) + " de " + str(m.loop[item].to)
    end if
  end for
  if m.top.debug then
    tick += chr(10) + "-----"
    m.panel.text = tick
    print ghLogHead("SERVICE");"TimerTick >> " + tick
  end if
end sub

' MANAGER
' -----------------------------
sub onManagerShutdown()
  if m.top.debug then print ghLogHead("SERVICE"); "onManagerShutdown -- SHUTTING DOWN!"
  ' for each serv in m.loop
  '   print ":>> ";serv
  '   print "    ";m.loop[serv]
  '   print " "
  ' end for
  for each serv in m.loop ' Accion!!
    serviceRemove(serv)
  end for
  if m.top.debug then print ghLogHead("SERVICE"); "onManagerShutdown -- READY."
end sub

' SERVICES
' -----------------------------
sub onServiceAdd(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onServiceAdd ";data.id
  if data.component <> invalid then
    loop = ghGetChild(data, "loop", 1)
    globalName = ghGetChild(data, "global")
    m.loop[data.id] = {
      config: data,
      current: 0,
      obj: invalid,
      started: true
      to: loop
      global: globalName
    }
    serviceStart(data.id)
  else
    if m.top.debug then print ghLogHead("SERVICE");"onServiceAdd -- Error: parametros invalidos: ";data
  end if
end sub
sub onServiceRemove(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onServiceRemove ";data
  ' TODO
end sub
sub onServiceStart(event)
  name = event.getData()
  print name
  serviceStart(name)
end sub
sub onServiceStop(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onServiceStop ";data
  serviceStop(data)
end sub
sub onServiceRestart(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onServiceRestart ";data
  serviceStop(data)
  serviceStart(data)
end sub
' COMMANDS
' -----------------------------
sub serviceStart(name)
  if m.top.debug then print ghLogHead("SERVICE");"serviceStart ";name
  data = m.loop[name].config
  if data <> invalid then
    obj = CreateObject("roSGNode", data.component) ' creo objeto
    if data.debug <> invalid then
      obj.debug = data.debug
    end if
    if data.id <> invalid then
      obj.id = data.id
    else
      obj.id = data.component
    end if
    if data.initialTick <> invalid then
      print ghLogHead("SERVICE");"serviceStart -- ARRANCO CON TICK!!!"
      obj.tick = true
    end if
    m.services.appendChild(obj) ' agrego a childs
    m.loop[name].obj = obj ' agrego a array
    m.loop[name].started = true
    ' global
    if data.global <> invalid then ' si se pide inscribir
      if m.global[data.global] = invalid then ' si no existe
        m.global.addField(data.global, "node", false)
      end if
      m.global[data.global] = obj ' lo piso
    end if
  end if
end sub
sub serviceStop(name)
  m.services.removeChild(m.loop[name].obj) ' sacarla de los children
  m.loop[name].started = false ' apagado
  m.loop[name].obj = invalid ' mato objeto
  globalName = m.loop[name].global ' mato global
  if globalName <> invalid then m.global.removeField(globalName)
  if m.top.debug then print ghLogHead("SERVICE");"serviceStop ";name;" stopped."
end sub
sub serviceRemove(name)
  serviceStop(name) ' apago
  m.loop.Delete(name) ' elimino
  if m.top.debug then print ghLogHead("SERVICE");"serviceRemove ";name;" removed."
end sub
' LOADING
' -----------------------------
sub onLoadingChange(event)
  data = event.getData()


  print ghLogHead("LOADING");"onLoadingChange calling new status=";data
  m.top.findNode("loadingPanel").visible = data

  ' ' timer para actualizar ui
  ' m.tmrSeguir = CreateObject("roSGNode", "Timer")

  ' print ghLogHead("LOADING");"onLoadingChange calling new status=";data
  ' if data then
  '   m.tmrSeguir.ObserveField("fire", "loadingON")
  ' else
  '   m.tmrSeguir.ObserveField("fire", "loadingOFF")
  ' end if

  ' m.tmrSeguir.repeat = false ' una vez
  ' m.tmrSeguir.duration = 0.1 ' 1 sec
  ' m.tmrSeguir.control = "start"
end sub

' sub loadingON()
'   print ghLogHead("LOADING");"onLoadingChange calling new status ON"
'   print "*** ";m.top.findNode("loadingPanel").visible
'   m.top.findNode("loadingPanel").visible = true
'   print "*** ";m.top.findNode("loadingPanel").visible
' end sub
' sub loadingOFF()
'   print ghLogHead("LOADING");"onLoadingChange calling new status OFF"
'   print "*** ";m.top.findNode("loadingPanel").visible
'   m.top.findNode("loadingPanel").visible = false
'   print "*** ";m.top.findNode("loadingPanel").visible
' end sub


' EVENTS
' -----------------------------
sub onDebugTurn(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onDebugTurn -- ";data
  m.top.findNode("display").setField("visible", data)
end sub
'
' END FILE ------------------