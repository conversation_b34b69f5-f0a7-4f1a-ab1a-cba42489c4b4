sub init()
    ' m.top.debug = true

    m.logger.debug("Init, Configurando player y observadores")

    CreateVideoNode()

    m.loading = m.top.findNode("loading")

    m.endCard = m.top.findNode("theEndCard")
    m.endCard.ObserveField("value", "onEndCardValue")

    m.top.ObserveField("cerrarPlayer", "onCerrarPlayer")

    m.nextEpisodePlay = false
    m.episodeCharged = false

    m.hombreMuerto = m.top.findNode("hombreMuerto")
    m.hombreMuerto.ObserveField("isShowing", "onShowHombreMuerto")
    m.hombreMuerto.ObserveField("cmdSalir", "onSalidaHombreMuerto")
    ' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
    ' m.hombreMuerto.playerType = "vod"
end sub

sub onShowHombreMuerto(event)
    data = event.getData()
    if m.video <> invalid then
        if data = true then
            m.video.control = "pause"
        else
            m.video.control = "resume"
        end if
    end if
end sub

sub onSalidaHombreMuerto(event)
    root = event.getRoSGNode()

    m.logger.debug("Hombre muerto! Salida", { pressKey: root.pressKey })

    ' si presiona el boton continuo en la vcard
    ' si cierra automaticamente vuelvo a la home
    if root.pressKey = true then
        exitPlayer()
    else
        exitPlayer()
        m.top.routerReset = {
            page: "HomePage",
            fields: {
                nodo: ""
            }
        }
    end if
end sub

sub handleLanguages(event)
    data = event.getData()
    m.video.languages = data
end sub

sub onCerrarPlayer()
    m.hombreMuerto.shutDown = true
    updateState("done")
end sub

sub CreateVideoNode() as object
    m.logger.debug("video (m.video) creado y configurado")

    m.video = m.top.findNode("theVideo")
    m.video.id = "video"
    m.video.width = "1280"
    m.video.height = "720"
    m.video.translation = "[0,0]"
    m.video.enableUI = "false"
    m.video.disableScreenSaver = false
    m.video.notificationInterval = 1

    m.video.ObserveField("onkey", "onKeyPanel")
    m.video.ObserveField("selected", "onPanelSelected")
    m.video.ObserveField("favorited", "onFavoritedHandle")
    m.video.ObserveField("onOmitirIntro", "onOmitirIntro")
    m.video.ObserveFieldScoped("position", "OnPositionChanged")
    ' m.video.ObserveFieldScoped("duration", "OnDurationChanged")
    m.video.ObserveFieldScoped("state", "OnVideoStateChanged")
    m.video.ObserveFieldScoped("streamInfo", "OnBitrateChanged")
end sub

sub onKeyPanel(event)
    key = event.getData()

    if key <> "" then
        updateState("onkey", { key: key })
    end if
end sub

sub onOmitirIntro(event)
    data = event.getData()

    if data = true then
        updateState("hideIntro", { position: m.trk.oi.jumpTo })
    end if
end sub

' ============
' observer de m.video
' ============
sub onPanelSelected(event)
    ' cambio de idioma o episodio
    data = event.getData()

    if data <> invalid then
        if data.content_id = invalid then
            m.logger.debug("Cambio de episodio")
            updateState("episodeChange", data)
        else
            m.logger.debug("Cambio de idioma")
            updateState("languageChange", data)
        end if
    end if
end sub

sub onFavoritedHandle(event)
    ' para avisar a la vcard de que cambió el favorito
    data = event.getData()

    m.top.favorited = data
end sub

sub OnPositionChanged(event as object)
    ' duration = m.video.duration
    position = event.GetData()

    m.logger.debug("Posicion del video cambiada a: ", { position: position, duration: m.video.duration })
    updateState("positionChange")
end sub

sub OnBitrateChanged(event as object)
    data = event.GetData()

    m.logger.debug("Cambio en la informacion de bitrate. Datos: ", { data: data })
    updateState("qualitychange")
end sub

sub OnVideoStateChanged(event as object)
    state = event.getData()

    m.logger.debug("Estado del video (m.video) cambiado a: ", { state: state })
    updateState(state)
end sub
' ============
' ============


' ============
' observer de PlayerAD
' ============
sub controlChanged(event)
    control = event.getData()

    m.logger.debug("m.top.control (ej: inicio y cambio de contenido) cambiado a: " + control)

    updateState(control)
end sub

sub handleSeasons(event)
    data = event.GetData()

    if data <> invalid and m.video <> invalid then
        m.logger.debug("Informacion de temporadas recibida")
        m.video.seasons = data
    end if
end sub

sub handleInfo(event)
    data = event.GetData()

    if data <> invalid and m.video <> invalid then
        m.logger.debug("Informacion del contenido recibida", {
            id: ghGetChild(data, "id")
            title: ghGetChild(data, "title")
            serie_id: ghGetChild(data, "serie_id")
            content_id: ghGetChild(data, "content_id"),
        })
        m.video.info = data
    end if
end sub

sub onTrackInfoChange(event)
    data = event.getData()

    m.logger.debug("Informacion de track info actualizada.")

    updateState("trackInfoChange", data)
end sub
' ===============
' ===============


' observer de theEndCard
sub onEndCardValue()
    if m.endCard.value <> invalid and m.endCard.value <> "" then
        if m.endCard.value = "CANCEL" then
            m.logger.debug("Se canceló el próximo episodio")
        else
            m.logger.debug("Se seleccionó el próximo episodio", { common: ghGetChild(m.top, "content.next_group.common") })

            m.nextEpisodeShouldChange = {
                ' content: ghGetChild(m.top, "content.next_group"),
                content: m.nextContentData,
                offer: m.nextEpisodeOffer,
                isComplete: true
            }

            updateState("endCardFinish")

            ' updateState("chargeNextEpisode", {
            '     content: ghGetChild(m.top, "content.next_group.common"),
            '     offer: m.nextEpisodeOffer,
            '     isComplete: true
            ' })

        end if

        m.video.setFocus(true)
    end if
end sub

function onKeyEvent(key as string, press as boolean) as boolean
    m.logger.debug("Evento de tecla: " + key + ", presionado: " + press.toStr())

    ' si es back, mando estado para salir
    ' retorno true porque es controlado
    if press and key = "back" then
        updateState("done")
        return true
    end if

    m.hombreMuerto.keyReset = true

    return false
end function