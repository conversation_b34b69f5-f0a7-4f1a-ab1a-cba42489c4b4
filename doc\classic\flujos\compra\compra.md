# Flujo de compra

## Flujo general

![Flujo de compra](gen-02.png)



## Estados

![Estados](mach-02.png)



## BuyObject

```json
m.buy= {
  state: "",
  status: "ok|process|fail"
  buypin: {
    status: "ok|process|fail"
  },
  start: {
    status: "ok|process|fail"
    methods: [{},{}]
  },
	select: {
    status: "ok|process|fail"
    selected: {}
  },
	options: {
    status: "ok|process|fail"
    state: "proc1|proc2|etc"
    proc1: {}
		proc2: {}
  },
	confirm: {
    status: "ok|process|fail"
  }
}
```



## BuyMachine









## Pantallas

vCard



ScrPinInput

ScrMethodSelect

ScrPromoInput

ScrCVVInput



ScrBuyOk

ScrBuyError

