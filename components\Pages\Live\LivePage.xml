<?xml version="1.0" encoding="UTF-8"?>

<component name="LivePage" extends="Page">

  <!-- WIDEVINE -->
  <script type="text/brightscript" uri="pkg:/source/GHRDK/WideVineUtils.brs" />

  <script type="text/brightscript" uri="LivePage.brs" />
  <script type="text/brightscript" uri="LivePageLogic.brs" />
  <script type="text/brightscript" uri="VideoLivePlayerLogic.brs" />
  <script type="text/brightscript" uri="Tracker.brs" />

  <interface>
    <field id="group_id" type="string" value="" />

    <field id="channels" type="array" onChange="handleChannels" />
    <field id="events" type="assocarray" onChange="handleEvents" />

    <field id="state" type="string" value="stopped" />
    <field id="loading" type="boolean" alwaysNotify="true" onChange="onLoadingChange" value="true" />
    <field id="reloadHome" type="boolean" alwaysNotify="true" />
  </interface>

  <children>
    <GHLive id="theVideo" visible="true"/>
    <GHLoading id="spinner" visible="true" />
    <Label id="message" visible="false" height="700" width="1260" />
    <Poster id="pBackground"  visible="false" uri="pkg:/images/gradientMiniEpg.png"/>
    <GHHombreMuerto id="hombreMuerto"/>
  </children>

</component>
