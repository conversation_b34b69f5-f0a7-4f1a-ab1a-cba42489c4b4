<?xml version="1.0" encoding="utf-8" ?>

<component name="GHHombreMuerto" extends="Group">
  <script type="text/brightscript" uri="GHHombreMuerto.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24 -->
    <!-- <field id="videoObj" type="node" /> -->
    <!-- <field id="playerType" type="string" value="" onChange="onPlayerType"/> -->

    <!-- interfaz entrada -->
    <field id="isShowing" type="boolean" value="false"/>
    <field id="isSerie" type="boolean" value="false"/>
    <field id="keyReset" type="boolean" value="false" alwaysNotify="true" onChange="onKeyReset"/>
    <field id="newEpisode" type="boolean" value="false" alwaysNotify="true" onChange="onNewEpisode"/>
    <field id="shutDown" type="boolean" value="false" alwaysNotify="true" onChange="doShutDown" />
    <field id="pressKey" type="boolean" value="false" alwaysNotify="true" />
    <!-- interfaz salida -->
    <field id="cmdSalir" type="boolean" value="false" alwaysNotify="true" />
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
    <field id="focus" type="boolean" value="false" onChange="onUpdateFieldFocus" />
  </interface>

  <children>
    <Group id="pantalla" visible="false">
      <Rectangle id="background" />
      <Poster id="logo"/>
      <Label id="title"/>
      <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
        <GHButton id="btnContinuar"/>
        <GHButton id="btnRegresar" />
      </GHButtonGroup>
    </Group>
  </children>

</component>
