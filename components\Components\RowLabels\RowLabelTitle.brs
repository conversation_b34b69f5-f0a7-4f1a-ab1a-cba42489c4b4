function init()
  if m.top.debug then print ghLogHead();"Init ***"
  m.top.layoutDirection = "horiz"
  m.top.horizAlignment = "left"
  m.top.itemSpacings = [10, 0]
  draw() ' dibujo inicial, antes de los datos
end function
sub draw()
  if m.top.debug then print ghLogHead();"draw *** init ***"
  m.contentTitle = drawString("") ' , "#FFFFFF"
  m.contentTitle.font = ghGetFont(21, "regular")
  drawString(" ", "")
  if m.top.debug then print ghLogHead();"draw *** end. ***"
end sub
sub refresh(event)
  data = event.getData()
  m.contentTitle.text = ghDecodeHTML(data.title)
end sub
function drawString(text, id = "") ' color = "#FFFFFF", font = invalid,
  cadena = CreateObject("roSGNode", "Label")
  cadena.setFields({
    id: id
    text: text
  })
  m.top.appendChild(cadena)
  return cadena
end function
