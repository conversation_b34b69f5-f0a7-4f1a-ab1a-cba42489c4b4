sub DataInit()
  ' m.top.debug = false
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/cms/level"

  typeNav = ghGetChild(m.global, "config.nav", "nav")
  m.api.query.Append({
    "node": m.top.node
    "region": ghGetRegistry("region"),
    "type": typeNav
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if

  if res.errors <> invalid then
    if m.top.debug then print ghLogHead();"ProcessData -- ERROR(!)"
    m.top.error = res.errors
    return
  end if

  if m.top.debug then print ghLogHead();"ProcessData -----v "
  result = []
  cintas = ghGetChild(res, "response.modules.module")

  tipoNivel = ghGetChild(cintas, "#0.type")
  if tipoNivel <> "listadoinfinito" then
    tipoNivel = "cintas"
  end if

  for each cinta in cintas
    obCinta = CreateObject("roSGNode", "GHContent")
    obCinta.id = cinta.name
    obCinta.title = ""
    ghUtils_ForceSetFields(obCinta, {
      visible: true
    })

    comps = ghGetChild(cinta, "components.component")
    for each comp in comps
      if comp.name = "header" then
        obCinta.title = ghDecodeHTML(comp.properties.large)
      else if comp.name = "carrousel" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
        })
      else
      end if
    end for

    ' ------------------------------
    ' children vacios
    for v = 1 to 5
      ob = CreateObject("roSGNode", "GHContent")
      ob.data = { ' datos por defecto
        "rowtype": obCinta.data.type,
        "order": v + 1
      }
      obCinta.appendChild(ob)
    end for

    ' result.appendChild(obCinta)
    result.push(obCinta)
    if m.top.debug then print ":: > ";cinta.name, "(";result.Count();")"
    if result.Count() = 1 then
      navCinta = injectNavRow()
      if navCinta <> invalid
        if m.top.debug then
          print ghLogHead();"ProcessData -- navCinta", navCinta;
          print ghLogHead();"ProcessData -- navCinta.getChild(0)", navCinta.getChild(0)
          print ghLogHead();"ProcessData -- navCinta.getChild(0).data", navCinta.getChild(0).data
        end if
        result.push(navCinta)
      end if
    end if
  end for
  if m.top.debug then print ghLogHead();"ProcessData -----^ "

  ' aca cierro todo
  m.top.content = {
    tipo: tipoNivel
    cintas: result
  }
end sub

' #########################################################
' NAV
' #########################################################
function injectNavRow()
  navCinta = invalid

  if m.top.debug then print ghLogHead();"injectNavRow -- Ini [";m.top.node;"]"

  childs = getNodeChilds(m.top.node)

  if m.top.debug then print "*****************************"
  if m.top.debug then print "La magia del NAV -- count: ";childs.Count()

  if childs.Count() > 0 then
    navCinta = CreateObject("roSGNode", "GHContent")
    navCinta.id = "navchilds"
    ghUtils_ForceSetFields(navCinta, {
      visible: true
      "type": "navchilds"
      "oldType": "navchilds"
    })
    navCinta.title = ""
    navCinta.data = {
      "name": "navData",
      "properties": { "byuser": "false" },
      "type": "navchilds"
    }
    ' ------------------------------
    for v = 0 to childs.Count() - 1
      nav = childs[v]
      ob = CreateObject("roSGNode", "GHContent")
      ob.data = { ' datos por defecto
        "rowtype": "navchilds",
        "order": v + 1,
        "title": nav.text
        "data": nav
      }
      navCinta.appendChild(ob)
    end for
    if m.top.debug then print "-----------------------------"
    for v = 1 to 5
      if m.top.debug then print "== > ";navCinta.getChild(v)
    end for
  end if
  if m.top.debug then print "*****************************"
  if m.top.debug then print ghLogHead();"injectNavRow -- End"
  return navCinta
end function

function getNodeChilds(code)
  childs = []

  nav = m.global.nav
  for each item in nav.childs
    if item.code = code and item.childs <> invalid then
      childs = item.childs

      if m.top.debug print item.code;" >  ";childs.Count()
    end if
  end for

  return childs
end function