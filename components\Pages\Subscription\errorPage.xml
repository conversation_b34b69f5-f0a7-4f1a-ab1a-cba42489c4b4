<?xml version="1.0" encoding="utf-8"?>
<!-- <component name="LoginPage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="errorPage" extends="Page">
	<script type="text/brightscript" uri="errorPage.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<interface>
	</interface>
	<children>
		<Rectangle id="background" width="1280" height="720" color="0x000000FF" />
		<poster id="title" uri="pkg:/images/HD/logoClaro.png" translation="[68, 32]" width="132" height="28" />
		<poster id="errorTitle" uri="pkg:/images/FHD/peligro.png" translation="[585, 160]" width="60" height="60" />
		<!-- No se puda completar la accion -->
		<Label id="description0" focusable="false" text="Hubo un error inesperado" translation="[510, 280]" width="500" wrap="true" color="0xFFFFFF" font="font:SmallSystemFont" />
		<Label id="description" focusable="false" horizAlign="center" vertAlign="center" text="Por el momento no pudimos completar la acción. Por favor, intenta más tarde." translation="[400, 330]" width="450" wrap="true" color="0xFFFFFF" font="font:SmallSystemFont" />
		<GHButtonGroup id="b01" layout="childs" orientation="vertical">
			<GHButton id="subscribeButton" text="ACEPTAR" translation="[450, 430]" width="368" height="72" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" focusable="true" />
		</GHButtonGroup>
		<!-- <GHError id="error" /> -->
		<!-- <GHLoading id="loading" visible="false" /> -->
	</children>
</component>
