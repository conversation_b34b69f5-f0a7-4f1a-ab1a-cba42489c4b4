' -----------------------------
' GHMenu
' -----------------------------

' Specifies the name of an XML component to render titles in place of the row label.
' This component must extend from Group.
' If this component defines a "content" field, it will be set to the row's content.
' If this component defines a "rowCounterVertAlign" field (possible values are "top", "center", and "bottom"),
' the row counter's vertical alignment is respective of the row title component.

function init()
  if m.top.debug then print ghLogHead();"Init ***"
  ' seteos generales del layout
  m.top.layoutDirection = "horiz"
  m.top.horizAlignment = "left"
  m.top.itemSpacings = [10, 0]
  draw() ' dibujo inicial, antes de los datos
end function

sub draw()
  if m.top.debug then print ghLogHead();"draw *** init ***"
  ' --------------------------
  m.contentTitle = drawString("") ' , "#FFFFFF"
  m.contentTitle.font = ghGetFont(21.33, "medium")
  m.contentTemporada = drawString("") ' , "#FFFFFF"
  m.contentTemporada.font = ghGetFont(21.33, "medium")
  m.contentEpisodes = drawString("...") ' , "#FFFFFF"
  m.contentEpisodes.font = ghGetFont(21.33, "medium")
  m.contentEpisodes.opacity = "0.5"
  drawString(" ", "")
  m.episodeText = drawString(ghTranslate("vcard_access_abbreviationEpisode_label", " episodio")) ' , "#FFFFFF"
  m.episodeText.font = ghGetFont(21.33, "medium")
  m.episodeText.opacity = "0.5"
  ' --------------------------
  if m.top.debug then print ghLogHead();"draw *** end. ***"
end sub
' cuendo me llegan los datos...
sub refresh(event)
  data = event.getData()
  if data <> invalid then
    if data.title <> invalid and data.title <> "" then
      info = ParseJson(data.TITLE)
      ' cargo campos
      if info <> invalid then ' por las dudas
        m.contentTitle.text = ghGetChild(info, "title", "*")
        m.contentTemporada.text = ghGetChild(info, "temporada", "*")

        m.contentEpisodes.text = ghGetChild(info, "cantidad", "0")
        m.episodeText.text = ghTranslate("vcard_access_abbreviationEpisode_label", " episodio")
        ' ESPISODIOS
        if val(m.contentEpisodes.text) > 1 then
          m.episodeText.text = m.episodeText.text + "s"
        end if
      end if
    end if
  end if
end sub

' UTILIDADES
' -----------------------------------------------
function drawIcono(uri, width, height, id = "")
  if m.top.debug then print ghLogHead();"drawIcono -- ";width;" ";height;" ";uri
  ico = CreateObject("roSGNode", "Poster")
  ico.setFields({
    id: id
    uri: uri
    width: width
    height: height
  })
  m.top.appendChild(ico)
  return ico
end function
function drawString(text, id = "") ' color = "#FFFFFF", font = invalid,
  cadena = CreateObject("roSGNode", "Label")
  cadena.setFields({
    id: id
    text: text
  })
  m.top.appendChild(cadena)
  return cadena
end function
function drawRectangle(color, width, height, id = "")
  if m.top.debug then print ghLogHead();"drawRectangle -- ";color;" ";width;" ";height
  rect = CreateObject("roSGNode", "Rectangle")
  rect.setFields({
    id: id
    color: color
    width: width
    height: height
  })
  m.top.appendChild(rect)
  return rect
end function

'drawIcono("pkg:/images/Registered.png", 20, 20)
'drawRectangle("#00FFA6", 100, 20)
