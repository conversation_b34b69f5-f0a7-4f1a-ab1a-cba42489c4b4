

'
' Roku_Authenticated
' ----------------------------
sub Notify_Roku(eventMsg, rsgScreen = invalid as object)
  if false then
    print "Notify_Roku > ";eventMsg
    print "Notify_Roku > ";rsgScreen
  end if
  ' get the global node
  if type(m.top) = "roSGNode" ' was called from a component script
    globalNode = m.global
  else ' must pass roSGScreen when calling from main() thread
    globalNode = rsgScreen.getGlobalNode()
  end if

  ' get the Roku Analytics Component Library used for RED
  RAC = globalNode.roku_event_dispatcher
  if RAC = invalid then
    RAC = createObject("roSGNode", "Roku_Analytics:AnalyticsNode")
    RAC.debug = true ' for verbose output to BrightScript console, optional
    RAC.init = { RED: {} } ' activate RED as a provider
    globalNode.addFields({ roku_event_dispatcher: RAC })
  end if

  ' dispatch an event to Roku
  RAC.trackEvent = { RED: { eventName: "Roku_Authenticated" } }
end sub

'
' USERDATA
' -----------------------------
sub getRokuUserEmail(callback = "")
  m.store = CreateObject("roSGNode", "ChannelStore")
  m.store.requesteduserdata = "email, firstname, lastname"
  m.store.ObserveField("userdata", callback)
  m.store.command = "getUserData"
end sub
'
' Payment
' -----------------------------
function ghRokupayBuildOrder(code, qty = 1)
  order = CreateObject("roSGNode", "ContentNode")
  item = order.createChild("ContentNode")
  item.addFields({ "code": code, "qty": qty })
  return order

  ' item = CreateObject("roSGNode", "ContentNode")
  ' item.addField("code", "string", false)
  ' item.addField("quantity", "string", false)
  ' item.code = code
  ' item.quantity = qty
  ' return item
end function
function ghRokupayDoOrder(order, callback = "ghRokupayDoOrderCallback")
  print " "
  print " "
  print "*************************************"
  print "ORDER"
  print order
  print "*************************************"
  print " "
  print " "
  store = CreateObject("roSGNode", "ChannelStore")
  ' store.FakeServer(true)
  store.ObserveField("orderStatus", callback)
  store.order = order
  store.command = "doOrder"
  return store

  ' superOrder = CreateObject("roSGNode", "ContentNode")

  ' nOrder = CreateObject("roSGNode", "ContentNode")
  ' nOrder.addField("code", "string", false)
  ' nOrder.addField("quantity", "string", false)
  ' nOrder.code = "AR70004153"
  ' nOrder.quantity = 1

  ' superOrder.appendChild(nOrder)

  ' store = CreateObject("roSGNode", "ChannelStore")
  ' store.ObserveField("orderStatus", callback)
  ' ' store.order = nOrder
  ' store.order = superOrder
  ' store.command = "doOrder"

  ' a = 10
  return store

end function
sub ghRokupayDoOrderCallback(event)
  ' ghDumpEvent(event)
  data = event.getData()
  status = ghGetChild(data, "status", -20)
  print
  print "-----------------------------------"
  print "VOLVI DE LA COMPRA"
  print "-----------------------------------"

  ' STATUS
  print
  print "-----------------------------------"
  print "STATUS ";status
  if status > 0 then
    if status = 1 then
      print "SUCCESS !!!"
    else if status = 2 then
      print "INTERRUPTED !!!"
    else
      print "GOOSE PANIC !!! ";status, data
    end if
  else
    if status = -1 then
      print "HTTP Error/Timeout"
    else if status = -2 then
      print "Timeout"
    else if status = -3 then
      print "Unknown Error"
    else if status = -4 then
      print "Invalid request"
    else
      print "GOOSE PANIC !!! ";status, data
    end if
  end if
  print "id = [";ghGetChild(data, "id", "---");"]"
  print "Mensaje = [";ghGetChild(data, "statusMessage", "sin mensaje");"]"

  print
  print "-----------------------------------"
  print "USER"
  print m.store.userData

  print "-----------------------------------"
  print "-----------------------------------"
  print "-----------------------------------"
  print
end sub

function onStoreChannelCredData() as void
  print "onStoreChannelCredData"
  if (m.store.storeChannelCredDataStatus <> invalid)
    print "- response: " m.store.storeChannelCredDataStatus.response
    print "- status: " m.store.storeChannelCredDataStatus.status
  end if

  m.store.command = "getChannelCred"
end function