' GHLivePanel
' --------------------------

sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init ***"

  m.top.ObserveField("visible", "onPanelVisibleChange")
  ' big mag
  m.map = {
    "languages": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }
  ' Panel Left
  ' -----------------------------
  m.panL = m.top.findNode("panelLeft")
  m.infoSeason = m.top.findNode("infoSeason")
  m.infoEpisode = m.top.findNode("infoEpisode")
  m.infoEpisodeTitle = m.top.findNode("infoEpisodeTitle")
  m.panL.setFields({
    translation: [0, 0]
    width: 1080
    height: 1920
    color: "#000000"
  })
  m.title = m.top.findNode("title")
  m.title.setFields({
    translation: [81, 187]
    font: ghGetFont(43, "regular")
    width: 664
    height: 55
  })
  m.time = m.top.findNode("time")
  m.time.setFields({
    translation: [50, 100]
    font: ghGetFont(21, "regular")
    width: 500
    height: 32
  })
  m.desc = m.top.findNode("description")
  m.desc.setFields({
    translation: [81, 340]
    font: ghGetFont(21, "regular")
    width: 728
    height: 112
  })
  ' -----------------------------
  ' Panel Right
  ' -----------------------------
  m.panR = m.top.findNode("panelRight")
  m.panR.setFields({
    translation: [880, 0]
    width: 401
    height: 720
    color: "#282828"
  })
  m.langLabel = m.top.findNode("langLabel")
  m.langLabel.setFields({
    translation: [50, 100]
    text: ghTranslate("", "IDIOMA")
    color: "#999999"
    font: ghGetFont(21, "regular")
  })
  m.languages = m.top.findNode("languages")
  m.languages.setFields({
    translation: [00, 190]
    itemComponentName: "GHLiveLangItem"
    numColumns: 1
    numRows: 10
    color: "#FFFFFF"
    itemSize: [500, 30]
    itemSpacing: [0, 50]
    drawFocusFeedback: false
    vertFocusAnimationStyle: "floatingFocus"
  })
  m.languages.ObserveField("itemSelected", "onItemSelected")
  ' -----------------------------
end sub
' EVENTS
' -----------------------------
sub onPanelVisibleChange(event)
  data = event.getData()
  if data then ' entro----
    position = 0
    for each l in m.top.langData
      if l.is_current then
        exit for
      end if
      position = position + 1
    end for
    m.languages.jumpToItem = position

    m.languages.setFocus(true)
  else ' salgo ------------------
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  if m.top.visible = true then
    if press then
      if key <> "back" then
        turnFocusTo(guessFocusTo(key))
        handled = true
        if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
      else
        handled = true
        ' m.top.focus = false
        m.top.visible = false
      end if
    end if
  end if
  return handled
end function
function guessFocusTo(direction)
  focusTo = invalid
  current = getCurrentFocus()
  if current <> invalid then
    ' a donde voy?
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    else
      focusTo = current
    end if
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
      m.top.findNode(id).setFocus(true)
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  ' if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function
sub onInfoUpdate(event)
  data = event.getData()
  m.title.text = ghGetChild(data, "title", "")
  m.time.setFields({
    font: ghGetFont(24, "regular")
    text: ghFormatDuration(ghGetChild(data, "duration", invalid))
    translation: [80, 292]
  })
  m.desc.text = ghGetChild(data, "description", "")
  if ghGetChild(data, "serie_id") <> invalid then
    m.infoSeason.setFields({
      font: ghGetFont(24, "regular")
      text: ghTranslate("Temporada", "Temporada") + " " + ghGetChild(data, "season")
    })
    m.infoEpisode.setFields({
      font: ghGetFont(16, "regular")
      text: ghTranslate("Episodio", "Episodio") + " " + ghGetChild(data, "episodenumber")
    })
    m.infoEpisodeTitle.setFields({
      font: ghGetFont(16, "bold")
      text: ghGetChild(data, "titleEpisode")
    })

    m.top.findNode("infoSerie").visible = true
  else
    m.top.findNode("infoSerie").visible = false
  end if
end sub
sub onLangDataUpdate(event)
  if m.top.debug then print ghLogHead();"onLangDataUpdate ***"
  ls = event.getData()
  langs = createObject("RoSGNode", "GHContent")
  for each l in ls
    ' print ghLogHead("LANGUAGES");l
    lang = createObject("RoSGNode", "GHContent")
    lang.data = l
    langs.appendChild(lang)
  end for
  m.languages.content = langs
end sub
sub onItemSelected(event)
  data = event.getData()
  if data <> invalid then
    print ghLogHead();"onItemSelected >>----> ";m.languages.content.getChild(data)
    print ghLogHead();"onItemSelected >>----> ";data
    data = ghGetChild(m.languages.content.getChild(data), "data")
    m.top.selected = data
  end if
end sub
' Logica Macabra
' --------------------------------------
function getLenguajesCompleto(langs, audios, subs)
  ' m.top.debug = true
  if m.top.debug then LMDump("Logica Macabra")
  arrSalida = {}
  ' ---------------------
  ' languages
  for each l in langs
    if m.top.debug then print ">>>>> ";l
    l.modo = "SA"
    arrSalida[l.option_id] = l
  end for
  ' ---------------------
  ' audios
  audioOriginal = invalid
  for each a in audios
    lang = getLanguage("A", a)
    if lang <> invalid and lang <> "D-" then
      if lang = "O-" then
        lang = getOriginal(arrSalida) ' busco el original
        audioOriginal = a
      end if
      ' revisar
      if arrSalida[lang] <> invalid then
        arrSalida[lang].modo = "MA"
        arrSalida[lang].trackType = "audio"
        arrSalida[lang].track = a
      end if
    end if
  end for
  ' ---------------------
  ' subtitle
  for each a in subs
    lang = getLanguage("S", a)
    if lang <> invalid then
      arrSalida[lang].modo = "MA"
      arrSalida[lang].trackType = "subtitle"
      arrSalida[lang].track = a
      arrSalida[lang].audioOriginal = audioOriginal
    end if
  end for
  if m.top.debug then
    print "#####################################"
    for each item in arrSalida
      print ghLogHead();item;" --> ";arrSalida[item]
      print ghLogHead();item;" T --> ";arrSalida[item].track
    end for
    print "#####################################"
  end if
  m.top.debug = false
  return arrSalida
end function
function getLanguage(mode, ori) ' obtiene el lenguaje apropiado de acuerdo al formato
  lang = ori.Language
  ' para contenido hbo viene language = audioORI
  if ori.name <> invalid then
    langR = Right(UCase(ori.name), 3)
    if langR = "ORI" then
      lang = "ORI"
    end if
  end if
  result = ""
  if mode = "A" then
    result = getLanguage3to2("D-", lang)
  else if mode = "S" then
    result = getLanguage3to2("S-", lang)
  else if mode = "L" then
    result = ori.option_id
  end if
  return result
end function
function getLanguage3to2(prefix, l3) ' convierte de un formato de lenguaje al otro
  lang = Left(UCase(l3), 2)
  if Left(lang, 1) = "O" then ' si es el original
    lang = "O-"
  else if lang = "PO" ' porque no se ponen de acuerdo
    lang = prefix + "PT"
  else if lang = "SP" ' porque no se ponen de acuerdo
    lang = prefix + "ES"
  else
    lang = prefix + lang ' agrego el prefijo
  end if
  return lang
end function
function getOriginal(langs) ' encuentra el idioma original en languages
  for each l in langs
    if Left(l, 1) = "O" then
      return l ' es el que busco
    end if
  end for
  return invalid
end function
sub LMDump(msg) ' dump inicial de datos
  print
  print
  print
  print "======================================================================"
  print "======================================================================"
  print "[ ";msg
  print "======================================================================"
  print "availableAudioTracks"
  for each item in m.top.availableAudioTracks
    print getLanguage("A", item), item
  end for
  print "======================================================================"
  print "avaiblableSubtitleTracks"
  for each item in m.top.availableSubtitleTracks
    print getLanguage("S", item), item
  end for
  print "======================================================================"
  print "content.languages"
  for each item in m.top.content.languages
    print getLanguage("L", item), item
  end for
  print "======================================================================"
  print "======================================================================"
  print "======================================================================"
end sub
' END OF FILE