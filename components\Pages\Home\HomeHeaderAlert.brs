sub Init()
  ' Set fonts and translated text
  m.logger = CreateLogger()
  m.ribbonText = m.top.findNode("ribbonText")
  m.ribbonText.font = ghGetFont(21, "regular")
  m.ribbonText.text = "¿Quieres ver Claro fuera de casa? Elige OK para completar tus datos"

  ' Get button references
  m.btnOk = m.top.findNode("btnOk")
  m.btnOk.font = ghGetFont(19, "regular")

  m.btnCerrar = m.top.findNode("btnCerrar")
  m.btnCerrar.font = ghGetFont(19, "regular")
  ' Set up button observers
  m.btnOk.ObserveField("buttonSelected", "OnOkSelected")
  m.btnCerrar.ObserveField("buttonSelected", "OnCerrarSelected")

  ' Set initial visibility
  m.top.visible = true
  ' Navigation map for smooth focus
  m.map = {
    "btnOk": { "right": "btnCerrar", "left": invalid, "up": invalid, "down": invalid },
    "btnCerrar": { "left": "btnOk", "right": invalid, "up": invalid, "down": invalid }
  }
end sub

sub updateFieldFocus(event)
  data = event.getData()
  if data then
    turnFocusTo("btnOk")
  end if
end sub

sub OnOkSelected(event)
  if event.getRoSGNode().buttonSelected then
    m.top.routerChild = { page: "DataCompletionPage" }
  end if
end sub

sub OnCerrarSelected(event)
  if event.getRoSGNode().buttonSelected then
    m.top.visible = false
  end if
end sub

sub onWasShown()
  m.top.routerAddRouterTo = m.menu ' habilito al menu a usar router
  m.showHome = true
end sub

function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "left" or key = "right" then
      changeFocusBasedOnKey(key)
      handled = true
      ' else if key = "down" or key = "up" then
      '   handled = false
    else if key = "back" then
      m.top.visible = false
      handled = false
    end if
  end if
  return handled
end function