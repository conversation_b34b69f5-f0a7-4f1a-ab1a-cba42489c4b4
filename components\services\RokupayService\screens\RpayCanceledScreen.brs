sub Init()
  m.map = { "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid, "default": true } }

  m.botonera= m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "ButtonSelected")
  m.botonera.ObserveField("backSelected", "BackSelected")

  ghSetBackground(ghGetAssetByMode("landing_access_background", "")) 'de momento faltaría tener el key y nombre de la imagen

  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(40, "bold")
  m.title.text = ghTranslate("Alerta_SubscriptionState_TextoTitulo_Canceled", "Suscripción suspendida")

  m.descrip1 = m.top.findNode("descrip1")
  m.descrip1.font = ghGetFont(22, "regular") 'revisar el tema del tamaño en pantalla, el invision pide 24px pero se corta
  m.descrip1.text = ghTranslate("Alerta_SubscriptionState_TextoDescripcion1_Canceled", "No se pudo renovar tu suscripción.") 

  m.descrip2 = m.top.findNode("descrip2")
  m.descrip2.font = ghGetFont(22, "regular") 'revisar el tema del tamaño en pantalla, el invision pide 24px pero se corta
  m.descrip2.text = ghTranslate("Alerta_SubscriptionState_TextoDescripcion2_Canceled", "Para evitar que sea cancelada, regulariza tu pago en Roku Pay.") 

  m.btnRegister = m.top.findNode("btnRegister")
  m.btnRegister.text = ghTranslate("Alerta_SubscriptionState_TextoBotonPrimario_Canceled", "ACEPTAR", {})

  m.textoAyuda = m.top.findNode("textoAyuda")
  m.textoAyuda.font = ghGetFont(22, "medium")
  m.textoAyuda.text = ghTranslate("Alerta_SubscriptionState_TextoAyuda1_Canceled", "¿Necesitas ayuda?")

  m.textoCallCenter = m.top.findNode("textoCallCenter")
  m.textoCallCenter.font = ghGetFont(22, "regular")
  m.textoCallCenter.text = ghTranslate("Alerta_SubscriptionState_TextoAyuda2_Canceled", "Por favor comunícate al 01 800 000 000")

  m.iconoWarning = m.top.findNode("iconoWarning")
  m.iconoWarning.uri = ghGetAsset("Alerta_Icono", ghGetImageByMode("peligro.png"))
end sub

sub updateFieldFocus()
  turnFocusTo("botonera")
end sub

function onKeyEvent(key, press) as boolean
  handled = false

  if press then
    m.logger.debug(["keyEvent: ", key])
    if key <> "back" then
      changeFocusBasedOnKey(key)

      handled = true
    end if
  end if

  return handled
end function

sub ButtonSelected(event)
  child = event.getRoSGNode()

  if child.selected then
    m.top.value = m.botonera.value

    m.top.wasClosed = true
  end if
end sub

sub BackSelected(event)
  child = event.getRoSGNode()

  if child.backSelected then
    m.top.value = ""

    m.top.wasClosed = true
  end if
end sub