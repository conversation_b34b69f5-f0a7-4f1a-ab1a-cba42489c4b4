# Mensajes

## Roku

### #1
hola <PERSON>, te mando los comentarios de Felix:

They'll need to set these headers in the drmHttpAgent of the video node. They need to create an roHttpAgent object, add the headers there, then set the video node's drmHttpAgent to that newly created roHttpAgent object. Some docs about this. There's a caveat though (at least in the past there was one, I'm sure it's still present to this day): if they set a drmHttpAgent to their video node, they must also set an roHttpAgent to the video node itself. Otherwise, it won't work. I'll send a piece of sample code over that kinda explains what they need to do (sent it to Vudu the other day).

Do you know what exactly they want to add to the body? Is it for the license request? Is it to the license url endpoint? Are they trying to modify the license challenge?

y aquí va el sample code mencionado en el punto 1:

```
// add stream specific headers/cookies/certs to this http agent, if needed
httpAgent = createObject("roHttpAgent")
httpAgent.AddHeader("videoSpecificHeader", "XXX")
m.video.setHttpAgent(httpAgent)

// add license specific headers/cookies/certs to this http agent, if needed
drmHttpAgent = CreateObject("roHttpAgent") 
drmHttpAgent.AddHeader("drmLicenseSpecificHeader", "XXX") 
m.video.drmHttpAgent = drmHttpAgent

content.drmParams = {
  keySystem: "Widevine"
  licenseServerUrl: url
}

m.video.content = content
```

### #2


Hola Sebastián, te vuelvo a molestar.

We´re sending the headers as you told us, but still not working. We get:

```
====================================
ERROR PLAYER <VIDEO>
[PLAYER] video.errorCode == -6
[PLAYER] video.errorMsg == Protected content license error.
====================================
[PLAYER] video.licenseStatus == <Component: roAssociativeArray> =
{
    duration: 0
    keysystem: "widevine"
    response: "????
0
 B74EAD5EF5426A940100000000000000??\??B?U; ?("
    status: **********
}
====================================
```

And we suspected that´s because we were not sending the certificate (but not sure).

At that point I made a function to get it as I show you..

```
function getCertificate(url)

  pathname = "tmp:/certificate.der"

  print " "
  print " "
  print "###!###!###!###!###!###!###!###!###!###!###!###!"
  print "url=";url
  print "pathname=";pathname
  print "###!###!###!###!###!###!###!###!###!###!###!###!"

  newXfer = CreateObject("roUrlTransfer")
  newXfer.SetCertificatesFile("common:/certs/ca-bundle.crt")
  newXfer.AddHeader("X-Roku-Reserved-Dev-Id", "")
  newXfer.InitClientCertificates()
  newXfer.SetUrl(url)

  result = newXfer.GetToFile(pathname)
  print "###!###!###!###!###!###!###!###!###!###!###!###!"
  print type(result), "[";result;"]"
  print "###!###!###!###!###!###!###!###!###!###!###!###!"

  fs = CreateObject("roFileSystem")
  if fs.Exists(pathname) then
    print "ITs THERE! -- ";pathname
    fileContent = CreateObject("roByteArray")
    readResult = fileContent.ReadFile(pathname)
    fs.Delete(pathname)
    print "READ RESULT=";readResult
    print "BYTES READ=";fileContent.Count()
    output = fileContent.ToAsciiString()
  else
    print "NOT FOUND! -- ";pathname
    output = ""
  end if

  print "###!###!###!###!###!###!###!###!###!###!###!###!"
  print " "
  print " "

  return output
end function
```

with an apparent good result..

```
###!###!###!###!###!###!###!###!###!###!###!###!
url=https://widevine-claroargentina-vod.clarovideo.net/licenser/getcertificate
pathname=tmp:/certificate.der
###!###!###!###!###!###!###!###!###!###!###!###!
###!###!###!###!###!###!###!###!###!###!###!###!
Integer         [ 200]
###!###!###!###!###!###!###!###!###!###!###!###!
ITs THERE! -- tmp:/certificate.der
READ RESULT =true
BYTES READ = 700
###!###!###!###!###!###!###!###!###!###!###!###!
```

Last thing I do in the function is to convert the roByteArray .ToAsciiString()
because in documentation says it should be a String,
IS THAT CORRECT?

'Cause if that's the case, we're having another problem.
And I'm not sure which can it be.



Felix Mejia
  18:29
I don't think you need to use roFileSystem to convert to a byte array. roUrlTransfer.GetToFile() should download the file to whatever path you specify (unless it fails). So when you need to use the cert(?) file for the drmHttpAgent, it will look something like:
pathname = "tmp:/certificate.der"

```basic
print " "
print " "
print "###!###!###!###!###!###!###!###!###!###!###!###!"
print "url=";url
print "pathname=";pathname
print "###!###!###!###!###!###!###!###!###!###!###!###!"

newXfer = CreateObject("roUrlTransfer")
newXfer.SetCertificatesFile("common:/certs/ca-bundle.crt")
newXfer.AddHeader("X-Roku-Reserved-Dev-Id", "")
newXfer.InitClientCertificates()
newXfer.SetUrl(url)

result = newXfer.GetToFile(pathname)
print "###!###!###!###!###!###!###!###!###!###!###!###!"
print type(result), "[";result;"]"
print "###!###!###!###!###!###!###!###!###!###!###!###!"

if result = 200
  drmHttpAgent = createObject("roHttpAgent")
  drmHttpAgent.setCertificatesFile(pathname)
  m.video.drmHttpAgent = drmHttpAgent
end if
```

If you still have issues, can you send over a sample that demonstrates the issue?


### #4

Hi Felix!
What you told me work great! Thansk! 
Now to the next problem.... sorry.
DRM seems ok, but I get an error like

```text
====================================
ERROR PLAYER <VIDEO>
[PLAYER] video.errorCode == -5
[PLAYER] video.errorMsg == no valid bitrates
====================================
[PLAYER] video.licenseStatus == invalid
====================================
```

and, just before that y get an event from streamInfo with this data (not sure if that helps)

{
    isResume: false
    isUnderrun: false
    measuredBitrate: 3219456
    streamBitrate: 128000
    streamUrl: "https://arclarovideo.akamaized.net/multimediav81/plataforma_vod/MP4/201802/WMP4H20688MTDS_full/WMP4H20688MTDS_full_WV_DASH.ism/.mpd"
}

The guy from BackEnd tells me that the bitrates in the manifest are: 300000, 600000, 900000, 1200000, 1500000, 1800000 y 2100000

Any ideas?

==============================================================





## Gabriel Polverini

### #1

[Ayer 19:24] Gabriel Ernesto Polverini
ahi Gus en la doc de Roku dice

[Ayer 19:24] Gabriel Ernesto Polverini

Passing custom HTTP headers to licensing requests
Developers looking to pass custom HTTP headers with a licensing request can now set those headers using the ifHttpAgent interface methods on the Video node.

[Ayer 19:29] Gabriel Ernesto Polverini
creería que viene por acá la cosa

[Ayer 19:29] Gabriel Ernesto Polverini

	drmHttpAgent for handling DRM key/license requests separately
	Since Roku OS 9.3, you can create a separate agent to handle DRM key and license requests, apart from other types of requests.
	Once you have created your agent, you can set the Video node's drmHttpAgent field directly to designate that the special agent is to supersede any currently-set agent in the case of DRM key and license requests. The drmHttpAgent field must be configured before setting the content in the Video node.

```
' Configure the DRM HttpAgent before setting content in the Video node
 httpAgent = CreateObject("roHttpAgent")
 httpAgent.AddHeader("DRM-Specific-1", "weqweqweqweqweqweqeqeqeqeqwe")
 httpAgent.AddHeader("DRM-Specific-2", "fgfgfgfgfgfgfgfgfg")
 httpAgent.AddHeader("DRM-Specific-3", "zxzxzxzxxzxzxzxzxzx")
 m.video.drmHttpAgent = httpAgent    
 m.video.content = videocontent
```

	If drmHttpAgent is not set (the default), uri fetches for video involving the DRM URLs (serializationURL, licenseServerURL, licenseRenewURL) of ContentMetaData will use the video's regular HttpAgent. However, if the drmHttpAgent is set, the agent cited in the field will be used for those fetches instead.



### #2


-------------------

```
source = {
          dash: media.video_url,
          drm: {
              widevine: {
                  LA_URL: media.server_url,
                  serverCertificate: cert,
                  headers: {
                      'custom-data': JSON.stringify({
                          token: JSON.parse(media.challenge).token,
                          device_id: entry.device_id
                          }),
                      'content-type': 'application/octet-stream'
                  },
              },
          },
      };
```



## Cristopher Carmona / Edwin Sanchez

### #1

[17:15] Cristopher Hernandez Carmona

https://bitmovin.com/demos/drm

https://developer.bitmovin.com/encoding/docs/how-to-create-widevine-drm-protected-content

DRM Secure Stream Test using HTML5 Video Player | Bitmovin
Test DRM protected streams with Bitmovin's Video Player using live code | Including MSE Support ✅ and EME Support 

mira gus, seguimos esas docs para hacer las pruebas de widevine en bitmovin

el lunes tendras tiempo de hacer una llamada con edwin que hizo ese desarrollo para que platiques con el y te explique lo que hizo de nuestro lado?

podriamos correr el codigo para ir viendo el paso a paso


https://cdn.bitmovin.com/content/assets/art-of-motion_drm/mpds/11331.mpd
https://cwip-shaka-proxy.appspot.com/no_auth

[13:29] Edwin Eduardo Sanchez Smith
https://shaka-player-demo.appspot.com/demo/#audiolang=es-ES;textlang=es-ES;uilang=es-ES;panel=HOME;build=uncompiled

<img src="img/charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48%20(1).jpg" alt="charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48 (1)" style="zoom:50%;" />

<img src="img/charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48%20(2).jpg" alt="charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48 (2)" style="zoom:50%;" />

<img src="img/charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48%20(3).jpg" alt="charla-bitmovin-65f53131-605a-4e31-973d-73c05e850e48 (3)" style="zoom:50%;" />

