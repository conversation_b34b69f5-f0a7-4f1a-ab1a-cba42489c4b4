sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/epg/channel"

  ' fecha desde
  date = CreateObject("roDateTime")
  date.ToLocalTime()

  year = date.GetYear()
  month = date.GetMonth()
  day = date.GetDayOfMonth()
  hour = date.GetHours()

  if hour < 10 then
    hour = "0" + hour.Tostr()
  end if
  if month < 10 then
    month = "0" + month.Tostr()
  end if
  if day < 10 then
    day = "0" + day.Tostr()
  end if

  ' fecha hasta
  date02 = CreateObject("roDateTime")
  addSegundos = 24 * 60 * 60 ' sumar segundo al dia de hoy, para obtener
  date02.FromSeconds(date.AsSeconds() + addSegundos)

  year02 = date02.GetYear()
  month02 = date02.GetMonth()
  day02 = date02.GetDayOfMonth()
  hour02 = date02.GetHours()

  if hour02 < 10 then
    hour02 = "0" + hour02.Tostr()
  end if
  if month02 < 10 then
    month02 = "0" + month02.Tostr()
  end if
  if day02 < 10 then
    day02 = "0" + day02.Tostr()
  end if

  datefrom = year.Tostr() + month.tostr() + day.toStr() + hour.Tostr() + "0000"
  dateto = year02.Tostr() + month02.Tostr() + day02.Tostr() + hour02.Tostr() + "0000"

  epg = ghGetChild(m.global, "epg", {})
  m.api.query.Append({
    "region": ghGetRegistry("region")
    "date_from": datefrom
    "date_to": dateto
    ' "date_from": "20220427100000"
    ' "date_to": "20220428170000"
    "quantity": "500"
    "epg_version": epg.version
    "node_id": epg.nodoId
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  channels = ghGetChild(response, "channels", [])

  result = setGrid(channels)

  m.global.setFields({
    channelProvider: { result: result }
  })

  m.top.content = { result: result }
end sub

function setGrid(data)
  rootChildren = CreateObject("roSGNode", "ContentNode")
  for i = 0 to data.Count() - 1
    item = data[i]
    events = data[i].events

    channel = getEvents(item.group_id, item.name, item.number, item.image, events, i, item.group.common.proveedor_code)

    rootChildren.appendChild(channel)
  end for

  return rootChildren
end function

function getEvents(id, title, number, image, events, position, proveedor_code)
  result = CreateObject("roSGNode", "ContentNode")
  result.id = id
  result.title = title
  ghUtils_ForceSetFields(result, {
    number: number
    group_id: id
    channelPosition: position
    proveedor_code: proveedor_code
  })
  result.HDSMALLICONURL = image

  for each program in events
    programNode = CreateObject("roSGNode", "ContentNode")

    if program.name <> invalid and program.name <> ""
      programNode.title = program.name
    else
      programNode.title = "---"
    end if
    programNode.description = program.description
    programNode.playStart = program.unix_begin
    programNode.playDuration = program.unix_end - program.unix_begin
    ghUtils_ForceSetFields(programNode, {
      channelPosition: position
      item: program
    })

    result.appendChild(programNode)
  end for

  return result
end function