# Proyecto



-  [HN: Historias de negocio](hn.md) 
-   [HT: Historias técnicas](ht.md) 



-  [Información temática](tematico.md) 
-  [Du<PERSON>](dudas.md) 



## Investigación

- [<PERSON>st<PERSON><PERSON> de xml (VAST 4.3)](files\VAST_4.3.pdf) 



## Info

#### JIRA

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4062019617/BRF-11673+Todos+OTT+e+IPTV+Claro+video+con+publicidad

https://dlatvarg.atlassian.net/browse/BRF-11673

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4062019617/BRF-11673+Todos+OTT+e+IPTV+Claro+video+con+publicidad



#### MVPs

https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4055859225/BRF-11673+-+FE+Todos+OTT+e+IPTV+Claro+video+con+publicidad



#### APA

Llaves Editoriales y de Configuración https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4083712100/Llaves+Editoriales+y+de+Configuraci+n



#### APIs

Diagramas https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4068179999/Diagramas+de+Flujos+de+APIs

Swagger https://app.swaggerhub.com/home

Contratos https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4084532017/Contratos+de+APIs

Estandar de llamado de api https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4023354145/Est+ndar+Contrato+de+API.



#### Deadline

HT025 : Deadline

1. Los dispositivos en los que se tienen complicaciones técnicas y no estaría lista la integración para la primer Fase son:
   1. Roku
   2. Chromecast
2. En una siguiente fase se deberán contemplar.



#### AFES

Cómo levantar AFE https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/3878780999/Proceso+para+la+creaci+n+de+Tareas+-+AFE



#### Roku

Roku Advertising Framework overview https://developer.roku.com/es-mx/docs/developer-program/advertising/roku-advertising-framework.md

Integrating the Roku Advertising Framework https://developer.roku.com/es-mx/docs/developer-program/advertising/integrating-roku-advertising-framework.md

API reference https://developer.roku.com/es-mx/docs/developer-program/advertising/raf-api.md#configuration

Displaying video ads https://developer.roku.com/es-mx/videos/courses/rsg/video-ads.md

Video advertisements https://developer.roku.com/es-mx/docs/features/monetization/video-advertisements.md

Implementing client-side ad stitching https://developer.roku.com/es-mx/docs/developer-program/advertising/csas.md

SGDEX https://github.com/rokudev/SceneGraphDeveloperExtensions

Dudas https://community.roku.com/t5/Roku-Developer-Program/RAF-2-0-Native-integration-of-ads-in-RSG-apps/td-p/449383

Ejemplo bueno https://github.com/rokudev/RAF4RSG-sample

Ejemplo no usamos https://github.com/rokudev/samples/tree/master/advertising/CSASAdSample



#### Google

https://developers.google.com/interactive-media-ads/docs/sdks/html5/client-side/tags



Deprecado:

https://developers.google.com/interactive-media-ads

https://support.google.com/adsense/answer/6054303?hl=es&ref_topic=1706004&sjid=9342198415822253528-NC

https://github.com/googleads/googleads-ima-roku-dai



#### APIS Disponibes

https://docs.google.com/spreadsheets/d/1yNrDke6vVpMcCyOrvgvTSwA96uC8DeoFiCOQusk7FUQ/edit#gid=622436651

