' LevelListComponent
' -----------------------------
sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init "

  ' theme
  m.top.getScene().updateTheme = m.global.config.theme

  ' -- Filtros ---------------------------------
  if m.top.debug then print ghLogHead();"Init -- setting filters."

  m.theFilters = m.top.findNode("theFilters")
  m.theFilters.setFields({
    "exitUp": true
    "exitDown": true
    "layout": "childs"
    "orientation": "horizontal"
  })
  m.theFilters.ObserveField("value", "OnFiltersChanged")
  ' m.currFilter = "320-DESC"
  m.currFilter = "200-DESC"

  ' -- Lista   ---------------------------------
  m.theList = m.top.findNode("theList")
  m.theList.ObserveField("value", "OnCardSelect")
  m.theList.ObserveField("content", "refresh")

  m.title = m.top.findNode("title")

  m.spinner = m.top.findNode("spinner")
  ' -- -----------------------------------------

  ' -- MAPA DE COMPONENTES -------------------------
  m.map = {
    "theFilters": { "up": "menu", "right": invalid, "down": "theList", "left": invalid },
    "theList": { "up": "theFilters", "right": invalid, "down": invalid, "left": invalid },
  }
end sub
sub OnTitleChange(event)
  title = event.getData()

  if title = invalid or title = "" then
    m.title.visible = false
  else
    m.title.text = m.top.title
    m.title.visible = true
  end if
end sub
' EVENTS
' -----------------------------
sub updateFieldFocus(event)
  data = event.getData()

  if data then
    turnFocusTo("theList")
  end if
end sub
sub OnCardSelect(event)
  data = event.getData()

  m.top.findNode("theList").focus = false ' apago, para que al volver tome foco ( sino se pierde )

  m.top.contenido = ghGetChild(data, "data.data")
end sub
sub OnFiltersChanged(event)
  data = event.getData()

  if m.top.debug print ghLogHead();"OnFiltersChanged ";data

  m.currFilter = data
  m.theFilters.activeCode = data

  changeLoading(true)

  LoadData()
end sub
sub LoadData()
  if m.top.debug then print ghLogHead();"LoadData >> api."

  m.cType = LCase(m.cinta.data.type)

  apiRow = ghCallApi("Row" + m.versionSuffix, "RowOK", "RowERROR", false)
  apiRow.id = m.cinta.id ' uso el numero de child a la vuelta
  apiRow.rowType = ghGetChild(m.cinta, "data.type", "desconocido")
  apiRow.url = ghGetChild(m.cinta, "data.properties.url")
  apiRow.currFilter = m.currFilter
  apiRow.currFilterTitle = getCurrentFilter(m.currFilter)

  ord = getOrdereData(m.currFilter)
  apiRow.order_id = ord.id
  apiRow.order_way = ord.way
  apiRow.control = "run"
end sub

function getCurrentFilter(filter)
  label = ""
  ' Recorrer el array de objetos
  for each item in m.theoptions
    if item.code = filter then
      label = item.label
    end if
  end for
  return label
end function

sub UpdateLevel(event)
  if m.top.debug then print ghLogHead();"UpdateLevel -- -------------------------------------------"
  cintas = event.getData()
  if cintas <> invalid then
    ' Filters
    ' -----------------------------------------
    ordenamientos = ghGetChild(cintas[0], "data.properties.ordenamiento", [])
    ' array para poder hacer un sort por algun campo
    m.theOptions = []
    for o = 0 to ordenamientos.Count() - 1
      ord = ordenamientos[o]
      ordData = getOrdereData(ord.order)
      ' if m.top.debug then print ghLogHead();"UpdateLevel - order - ";ord.order;" -- ";ord.label;" > ";ordData
      ' m.theOptions[ord.order] = {
      m.theOptions.push({
        "code": ord.order
        "label": Ucase(ghDecodeHTML(ord.label))
        "order_id": ordData.id ' order_id
        "order_way": ordData.way ' order_way
        "order": o
      })
    end for
    m.theOptions.sortBy("order")

    m.theFilters.options = m.theOptions

    m.currFilter = ghGetChild(m.theOptions, "#0.code", "200-DESC")
    m.theFilters.activeCode = m.currFilter

    if m.top.debug then print ghLogHead();"Init -- filters setted."
    ' Data
    ' -----------------------------------------
    m.cinta = cintas[0]
    if m.top.debug then
      print "-----------------------------------------"
      print "cantCintas=";cintas.count()
      print "cinta=";m.cinta
      print "data=";m.cinta.data
      print "properties=";m.cinta.data.properties
      print "-----------------------------------------"
    end if
    ' -DATOS----------------------------------------
    LoadData()
    ' -----------------------------------------
  else
    print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    print "GOOSE PANIC: LevelComponent: FALTA PANTALLA DE ERROR!!!!"
    print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
    print
    print
  end if
end sub
' ROWS
' ------------------------------
sub RowOk(event)
  data = event.getData()
  cinta = ghGetChild(data, "cinta")
  if m.top.debug then print ghLogHead();"RowOk << ";cinta.getChildCount();" resultados."

  ' si tiene mas de 4 cards, lo divido en mas carouseles
  result = []
  cont = 1
  carousel = CreateObject("roSGNode", "GHContent")

  cant = cinta.getChildCount()
  for i = 0 to cant - 1
    item = cinta.getChild(0) ' siempre el primero
    carousel.appendChild(item)

    if (i = cant - 1) or cont = 4 then
      cont = 0

      result.push(carousel)
      carousel = CreateObject("roSGNode", "GHContent")
    end if

    cont = cont + 1
  end for

  ' hago el gridSetup con todos las cards
  m.cinta_result = result
  GridSetup()
end sub
sub RowERROR(event)
  data = event.getData()
  print ghLogHead();"RowERROR -- removing row id=";ghGetChild(data, "id")
  print ghLogHead();"RowERROR -- DATA ";data
  id = ghGetChild(data, "id")
  if id = invalid then
    id = ghGetChild(data, "payload.id") ' error de red
  end if

  if id <> invalid then
    removeRowLive(id)
  end if

  changeLoading(false)
end sub
' ROW MANAGEMENT
' ------------------------------
sub updateRowLive(id, data)
  row = getRowIdById(id)
  ' if m.top.debug then print ghLogHead();"updateRowLive (id=";id;", row=";row;")"
  if row <> invalid then
    prev = m.theList.content.getChild(row)
    data.title = prev.title
    chs = m.theList.content
    chs.replaceChild(data, row)
    m.theList.content.update(chs)
  end if
end sub
sub removeRowLive(id)
  row = getRowById(id)
  if row <> invalid then
    print ghLogHead();"removeRowLive -- borrando ";row.id
    chs = m.theList.content
    chs.removeChild(row)
    m.theList.content = chs
  end if
end sub
function getRowById(id)
  if m.theList.content <> invalid then
    cant = m.theList.content.getChildCount()
    for i = 0 to cant - 1
      r = m.theList.content.getChild(i)
      if r.id = id then
        return r
      end if
    end for
  end if
  return invalid
end function
function getRowIdById(id)
  if m.theList.content <> invalid then
    cant = m.theList.content.getChildCount()
    for i = 0 to cant - 1
      r = m.theList.content.getChild(i)
      if r.id = id then
        return i
      end if
    end for
  end if
  return invalid
end function
' SETUP
' ------------------------------
sub GridSetup()
  content = CreateObject("roSGNode", "GHContent")
  ' content.Update({ children: [m.cinta] }, true)
  content.Update({ children: m.cinta_result }, true)

  if m.top.debug then print ghLogHead();"GridSetup."
  ' manejo global
  m.theList.translation = [-130, 120]
  m.theList.itemComponentName = "ItemPolymorphic"
  m.theList.numRows = 3
  m.theList.itemSize = [1580, 210]
  m.theList.itemSpacing = [0, 0]
  m.theList.rowLabelOffset = [0, 15]
  m.theList.showRowLabel = [true]
  m.theList.focusXOffset = [1]
  m.theList.rowFocusAnimationStyle = "fixedFocusWrap"
  m.theList.rowCounterRightOffset = 0
  m.theList.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  ' content
  m.theList.content = content
  m.theList.jumpToRowItem = [0, 0] ' inicialmente al principio
  ' visible
  m.theList.visible = true
end sub
function refresh() ' event
  if m.top.debug then print ghLogHead();"refresh !!"
  curPos = m.theList.position ' posicion
  m.theList.setFields({
    rowItemSize: [[274, 154]]
    rowItemSpacing: [[21, 0]]
    rowHeights: [154]
    rowSpacings: [70]
    focusXOffset: [200]
    rowLabelOffset: [[200, 10]]
    showRowLabel: [true] ' boolean
    showRowCounter: [false] ' b
  })
  m.theList.jumpToRowItem = curPos ' posicion
  changeLoading(false)
end function
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press

  handled = false
  if press then
    if key <> "back" then
      exist = turnFocusTo(guessFocusTo(key))
      if key = "up" and exist = false then
        handled = false
      else
        handled = true
      end if
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
  end if

  return handled
end function
function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current] <> invalid and m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
function turnFocusTo(id)
  current = getCurrentFocus()
  exist = false
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if

    if m.top.findNode(id) <> invalid then
      if m.top.findNode(id).focus <> invalid then
        if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
        m.top.findNode(id).focus = true
        exist = true
      end if
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
  return exist
end function
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function
' UTILS
' -----------------------------
function getOrdereData(order)
  divPos = Instr(1, order, "-")
  orderData = {
    "id": left(order, divPos - 1)
    "way": mid(order, divPos + 1)
  }
  return orderData
end function
sub changeLoading(loading)
  if loading = true then
    m.spinner.visible = true
  else
    m.spinner.visible = false

    turnFocusTo("theList")
  end if
end sub