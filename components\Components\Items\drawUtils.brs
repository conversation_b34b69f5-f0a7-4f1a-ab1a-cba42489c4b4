' ----------------------
' CHAPITAS
' ----------------------
sub initTimer()
  ' print "*";
  m.tmrChapitas = CreateObject("roSGNode", "Timer")
  m.tmrChapitas.ObserveField("fire", "doChapita")
  m.tmrChapitas.setFields({
    repeat: false ' chequea varias veces...
    duration: 1 ' 0.1 ' 1 ' 1 sec
    control: "start"
  })
end sub
sub doChapita()
  ' print ".";
  drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)
end sub

sub drawChapitas(totX, totY, offsetX = 0, padX = 5, padY = 5, itemYpad = 3)
  ' print ghLogHead()"drawChapitas -- INIT"

  m.thePanels = m.top.findNode("thePanels")
  ' arrChapitas = []

  ' Live o VOD
  live_enabled = ghGetChild(m.top.itemContent, "data.live_enabled")
  live_type = ghGetChild(m.top.itemContent, "data.live_type")
  if live_enabled = "1" and live_type = "1" then
    liveOVod = "live"
  else
    liveOVod = "vod"
  end if

  proveedor = ghGetChild(m.top.itemContent, "data.proveedor_code", invalid) ' proveedor_name -- doc dice code
  ' print ghLogHead()"drawChapitas -- PROVEEDOR=";proveedor

  if proveedor <> invalid then
    m.thePanels.setFields({
      width: totX
      height: totY
      padX: padX
      padY: padY
      padItem: itemYpad
      offsetX: offsetX
    })

    ' -amco-----------------------------------
    if proveedor = "amco" then proveedor = "default"

    chProv = getChapitas(proveedor, liveOVod)
    if chProv.Count() > 0 then
      ' arrChapitas.Append(chProv)
      ' drawArrChapitas(arrChapitas)
      drawArrChapitas(chProv)
    end if
  end if

  ' --------------------------------------------
  ' print ghLogHead()"drawChapitas -- Listo!"
end sub
function getChapitas(proveedor, liveOVod)
  ' print ghLogHead();"P ";proveedor

  chProvs = []
  contador = 0

  if proveedor <> invalid then ' me pasaron algo

    ' --------------------------
    ' mode = m.global.nav?.typeChapitas
    ' if mode = invalid then ' estoy en classic
    ' print ghLogHead();"getChapitasProveedor -- Sali vacio por Jose."
    ' return []
    mode = "susc" ' fuerzo a susc, lo unico que hay en classic
    ' end if
    ' --------------------------
    tipos = ghGetChild(m.top.itemContent, "data.format_types") ' est/ppe
    if tipos <> invalid then
      arrTipos = ghSplit(tipos, ",")
      ' proveedores = {}
      ' if m.global.model = "lite" then
      '   ' print ghLogHead();"getChapitasAmco -- *** MODO LITE"
      '   if m.global.providersAds <> invalid then
      '     if m.global.providersAds[mode] <> invalid then
      '       if m.global.providersAds[mode][proveedor] <> invalid then
      '         proveedores = m.global.providersAds[mode][proveedor]
      '       end if
      '     end if
      '   end if
      ' else
      ' print ghLogHead();"getChapitasAmco -- *** MODO CLASSIC"

      ' if m.global.providers <> invalid then
      '   if m.global.providers[mode] <> invalid then
      '     if m.global.providers[mode][proveedor] <> invalid then
      '       proveedores = m.global.providers[mode][proveedor]
      '     end if
      '   end if
      ' end if
      proveedores = ghGetChild(m.global.providers, mode + "." + proveedor) ' mas rapido
      ' end if

      if proveedores <> invalid then
        for each tipo in arrTipos
          if tipo <> "download" then ' ROKU NO TIENE DOWNLOAD
            chapitas = invalid
            if proveedores[tipo] <> invalid then
              chapitas = proveedores[tipo][liveOVod]
              if chapitas <> invalid then
                ' print ghLogHead();"getChapitasProveedor -- tipo:";tipo;" path:";path;" >> ";chapitas.count()
                ' print ghLogHead();"getChapitasProveedor -- ";proveedor;".";tipo;".";liveOVod;" = ";chapitas.count()
                for each chapita in chapitas
                  if chapita <> invalid then
                    contador++ ' solo para ids
                    ch = {
                      id: "chAmco" + contador.Tostr()
                      type: chapita.type
                      gravity: chapita.gravity
                      data: chapita
                      component: invalid
                    }
                    ch.component = getChapita(ch)
                    chProvs.push(ch) ' agregado
                  end if
                end for
              end if
            end if
          end if
        end for
      end if
    end if
  end if
  return chProvs
end function
' COMPONENTES
function getChapita(props)
  ' chap = invalid
  if props.type = "image" then 'chapita con imagen
    chap = getImgChapita(props)
  else 'chapita con texto
    chap = getTxtChapita(props)
  end if
  return chap
end function
function getImgChapita(props) ' genera una chapita con imagen
  ch = CreateObject("roSGNode", "Poster")
  ch.setFields({
    id: props.id
    uri: ghGetAsset(ghGetChild(props, "data.url", ""), "")
  })
  return ch
end function
function getTxtChapita(props) ' genera una chapita con texto
  ch = CreateObject("roSGNode", "GHTagSimple")
  ch.id = props.id
  lblCode = ghGetChild(props, "data.text", "")
  text = ghTranslate(lblCode, lblCode)
  ch.setFields({
    font: ghGetFont(15) ' font: ghGetFont(props.textSize) -- no sirve lo que viene
    text: text ' si no esta, el codigo
    backMap: "pkg://images/back_chapita.9.png"
    color: ghGetChild(props, "data.textColor", "0xFFFFFF")
    backColor: ghGetChild(props, "data.backgroundColor", "0xFF0000FF")
  })
  return ch
end function
' DIBUJAR
sub drawArrChapitas(arrChapitas)
  ' -dibujo-------------------------------------
  if arrChapitas.Count() > 0
    panL = m.thePanels.findNode("leftPanel")
    panR = m.thePanels.findNode("rightPanel")
    ' LIMPIO
    if panL.getChildCount() > 0 then panL.removeChildrenIndex(panL.getChildCount(), 0)
    if panR.getChildCount() > 0 then panR.removeChildrenIndex(panR.getChildCount(), 0)
    ' DIBUJO
    for i = 0 to arrChapitas.Count() - 1 ' por cada chapita
      ch = arrChapitas[i]
      if ch.gravity = "left" then
        panL.appendChild(ch.component)
      else
        panR.appendChild(ch.component)
      end if
    end for
    ' --------------------------------------------
  end if
end sub

' ----------------------
' TITLE
' ----------------------
sub drawTitle(totX, totY, padX = 5, padY = 5)
  proveedor = ghGetChild(m.top.itemContent, "data.proveedor_code", invalid)
  liveType = ghGetChild(m.top.itemContent, "data.live_type", invalid)
  if liveType = "1" then return
  if proveedor <> invalid then ' si tengo proveedor
    provsTitle = m.global.ItemTitle
    if provsTitle <> invalid then ' si tengo la key de apa
      provsReg = provsTitle.LookupCI(ghGetRegistry("region")) ' x region
      if provsReg = invalid then ' si no tengo la region
        provsReg = provsTitle.LookupCI("default") ' region default
      end if
      if provsReg <> invalid then ' si tengo algo
        if Instr(1, provsReg, proveedor) > 0 then ' si el proveedor esta en la lista.
          prog = m.top.findNode("progress") ' Tengo en cuenta la barra de progreso si existe
          if prog <> invalid then ' tengo progress
            if prog.visible then ' esta prendido
              progBR = prog.boundingRect()
              totY = progBR.y - padY ' tengo menos alto
              padX = progBR.x ' me alineo con el progress
            end if
          end if
          m.title.setFields({
            width: totX - (padX * 2)
            height: totY - (padY * 2)
            text: m.top.itemContent.data.title
            font: ghGetFont(16, "bold")
            translation: [padX, padY]
            color: "0xFFFFFF"
            wrap: "true"
            lineSpacing: "0"
            vertAlign: "bottom"
            visible: true
          })
        end if
      end if
    end if
  end if
end sub
' ----------------------
' PROGESS
' ----------------------
sub drawProgress()
  vistime = ghGetChild(m.top.itemContent, "data.vistime", invalid)
  if vistime <> invalid then
    max = ghGetChild(vistime, "duration.seconds", -1)
    curr = ghGetChild(vistime, "last.seconds")
    porc = curr / max * 100
    if m.top.debug then print ghLogHead();"drawProgress -- ";m.top.itemContent.title;" ";curr;" ";max;" ";porc;"%"
    m.progress.width = m.itemPoster.width - 30
    m.progress.translation = [15, m.itemPoster.height - 22]
    m.progress.padding = 4
    m.progress.height = 12
    m.progress.value = porc
    m.progress.visible = true
  else
    m.progress.visible = false
  end if
end sub
