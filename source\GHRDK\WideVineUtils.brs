' ----------------------------
' Utilidades WIDEVINE
' ----------------------------

' FUNCIONES DE DETECCION
' ----------------------------

' Detecta si el dispositivo tienen WideVine L1
function ghDrmHaveL1Widevine() as boolean
  ' print " "
  ' print "---- !!! HARDCO SMOOTH (())"
  ' print " "
  ' return false

  res = false
  try
    di = CreateObject("roDeviceInfo")
    diDrm = di.GetDrmInfoEx()
    secLevel = diDrm?.Widevine?.securitylevel
    if secLevel <> invalid then
      if Val(secLevel) >= 1 then
        res = true
      end if
    end if

  catch Error
    print "ERROR: ";Error
  end try

  print "WideVineUtils -- ghDrmHaveL1Widevine = ";res

  return res
end function

function getEncodesSupported()
  if m.global.encodesAppSupported <> invalid then
    return m.global.encodesAppSupported
  end if

  hayWideVine = ghDrmHaveL1Widevine()

  encodesAppSupported = ["smooth_streaming_ma", "smooth_streaming"]

  if hayWideVine then
    encodesAppSupported = ["dashwv_ma", "dashwv", "smooth_streaming_ma", "smooth_streaming"]
  else
    m.logger.debug("No soporta Widevine")
  end if

  m.logger.debug("getEncodesSupported >> ", { encodes: encodesAppSupported })

  m.global.encodesAppSupported = encodesAppSupported

  return encodesAppSupported
end function

' FUNCIONES DE DRM
' ----------------------------

' Genera un HttpAgent Vacio
function buildEmptyHttpAgent()
  httpAgent = createObject("roHttpAgent")
  ' httpAgent.AddHeader("videoSpecificHeader", "XXX")
  ' m.video.setHttpAgent(httpAgent)
  return httpAgent
end function

' Genera un HttpAgent para DRM con lectura de certificados y headers
function buildDrmHttpAgent(datos)
  print "%%%%%%%%%%%%% buildDrmHttpAgent %%%%%%%%%%%%%%%%%%%%%"
  ' datos.customdata.token = "9d9079795cf46ed6e3cd95fd52a99103" ' HARDCO TEST
  ' if m.top.debug then
  print ghLogHead();"buildDrmHttpAgent -- datos=";datos
  print ghLogHead();"buildDrmHttpAgent -- datos.headers=";datos.headers
  ' end if

  ' creo el objeto -----
  drmHttpAgent = CreateObject("roHttpAgent")

  ' headers -----
  ' headers = {
  '   "custom-data": FormatJson(datos.customdata)
  '   "content-type": datos.contentType
  ' }
  headers = datos.headers
  print ghLogHead();"buildDrmHttpAgent -- HEADERS>> ";headers

  drmHttpAgent.SetHeaders(headers)

  print ghLogHead();"buildDrmHttpAgent -- result=";drmHttpAgent

  return drmHttpAgent
end function








' ------------------------
' PARA TEST
' ------------------------
' certificate_url -> https://widevinetest.clarovideo.net/v2/licenser/getcertificate
' token -> 9d9079795cf46ed6e3cd95fd52a99103
' server_url -> https://widevinetest.clarovideo.net/v2/licenser/getlicense




' ------------------------
' VERSION VIEJA
' ------------------------
' function getCertificate(url)
'   pathname = "tmp:/certificate.der"
'   print " "
'   print " "
'   print "###!###!###!###!###!###!###!###!###!###!###!###!"
'   print "url=";url
'   print "pathname=";pathname
'   print "###!###!###!###!###!###!###!###!###!###!###!###!"
'   newXfer = CreateObject("roUrlTransfer")
'   newXfer.SetCertificatesFile("common:/certs/ca-bundle.crt")
'   newXfer.AddHeader("X-Roku-Reserved-Dev-Id", "")
'   newXfer.InitClientCertificates()
'   newXfer.SetUrl(url)
'   result = newXfer.GetToFile(pathname)
'   print "###!###!###!###!###!###!###!###!###!###!###!###!"
'   print type(result), "[";result;"]"
'   print "###!###!###!###!###!###!###!###!###!###!###!###!"
'   fs = CreateObject("roFileSystem")
'   if fs.Exists(pathname) then
'     print "ITs THERE! -- ";pathname
'     fileContent = CreateObject("roByteArray")
'     readResult = fileContent.ReadFile(pathname)
'     fs.Delete(pathname)
'     print "READ RESULT=";readResult
'     print "BYTES READ=";fileContent.Count()
'     output = fileContent.ToAsciiString()
'   else
'     print "NOT FOUND! -- ";pathname
'     output = ""
'   end if
'   print "###!###!###!###!###!###!###!###!###!###!###!###!"
'   print " "
'   print " "
'   return output
' end function

' certificate -----
' serviceCertUrl = ghGetChild(datos, "serviceCert")
' ' serviceCertUrl = "https://widevinetest.clarovideo.net/v2/licenser/getcertificate" ' HARDCO TEST
' if serviceCertUrl <> invalid then

'   pathname = "tmp:/certificate.der"
'   if m.top.debug then print ghLogHead();"buildDrmHttpAgent -- TENGO CERTIFICADO -- ";serviceCertUrl;" >> ";pathname

'   newXfer = CreateObject("roUrlTransfer")
'   newXfer.SetCertificatesFile("common:/certs/ca-bundle.crt")
'   newXfer.AddHeader("X-Roku-Reserved-Dev-Id", "")
'   newXfer.InitClientCertificates()
'   newXfer.SetUrl(serviceCertUrl)

'   result = newXfer.GetToFile(pathname)
'   if m.top.debug then print ghLogHead();"buildDrmHttpAgent GetToFile-result=";result, "(";type(result);")"
'   if result = 200
'     if m.top.debug then print ghLogHead();"buildDrmHttpAgent -- DESCARGUE EL CERTIFICADO :";pathname
'     drmHttpAgent.setCertificatesFile(pathname)
'     ' m.video.drmHttpAgent = drmHttpAgent
'   else
'     if m.top.debug then print ghLogHead();"buildDrmHttpAgent -- FALLO LA CARGA DEL CERTIFICADO"
'   end if
' else
'   if m.top.debug then print ghLogHead();"buildDrmHttpAgent -- SIN CERTIFICADO (!!) :";serviceCertUrl
' end if



