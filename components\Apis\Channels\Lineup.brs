sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/epg/lineup"

  epg = ghGetChild(m.global, "epg", {})
  m.api.query.Append({
    "region": ghGetRegistry("region")
    "epg_version": epg.version
    "node_id": epg.nodoId
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  channels = ghGetChild(response, "channels", [])
  for i = 0 to channels.Count() - 1
    channel = channels[i]
    channels[i].label_large = channel.name
    channels[i].channelPosition = i
  end for

  m.global.setFields({
    channels: { list: channels }
  })

  m.top.content = response
end sub