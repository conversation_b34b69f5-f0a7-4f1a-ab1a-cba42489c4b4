# Briefs 2023

## TV en vivo [analisis1]

*Funcionalidad básica de TV en vivo.*

Status:  **TODO[CHEQUEO/REVISIÓN]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-8435

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3583739083/BRF-8435+Todos+OTT+Roku+MVP+con+funcionalidades+de+TV+en+vivo



Insumos Roku ????

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3565944899/Insumos+Roku



### HUs

#### HU001 - Desarrollo del MVP de TV en vivo

1. El presente MVP para el dispositivo Roku:
   1. **INCLUIR** las siguientes funcionalidades:
      1. Habilitar nodo de TV en vivo **HU002**
      2. Reproducción de canales en vivo - Player **HU003,HU004**
      3. Full EPG **HU008, HU009**
      4. Selector de canales **HU010, HU011**
      5. Desbloqueo de visualización de canales **HU012**
      6. Pantallas de error en la reproducción de canales
         - Canal no contratado **HU005**
         - Canal Caído **HU007**
         - Falla de servicio de TV en vivo **HU006**
   2. **No incluirá** las siguientes funcionalidades:
      1. Transacciones de canales
      2. Mini EPG
      3. Bloqueo y desbloqueo de canales
      4. Filtrado de canales por categoría
      5. Favoritear canales
      6. Menú de opciones de canal
      7. Menú de idiomas
      8. Grabaciones
      9. Timeshift

#### HU002 - Habilitar nodo de TV en vivo

- El nodo debe estar disponible en dispositivos Roku en todas las operaciones.
- El nombre del nodo debe ser: **TV en vivo**
  - El nodo se debe ubicar como parte del primer nivel del árbol de navegación.
  - La posición del nodo en el árbol de navegación debe ser después del nodo **Series**
- Cuando el usuario seleccione el nodo **TV en vivo**:
  - Si es la primera vez que el usuario ingresa al nodo, se le debe dirigir al canal default configurado para cada región. **HU003**
  - Si el usuario ya cuenta con un historial de reproducción, se le debe dirigir al último canal visto. **HU004**
  - Si el usuario no cuenta con derechos de reproducción del canal, se le debe mostrar una pantalla de notificación. **HU005**
  - Si se presenta un error en el servicio de carga de la EPG, se debe mostrar al usuario una pantalla de alerta. **HU006**
  - Si el canal al que el usuario desea acceder no está disponible, se debe mostrar una pantalla de notificación de canal caído. **HU007**

#### HU003 - Reproducción de canal default 

- Cuando el usuario seleccione el nodo de TV en vivo y sea la primera vez que accede a esa opción, se le debe dirigir al player del canal default establecido por la operación.

#### HU004 - Reproducción del último canal visto

- Cuando el usuario seleccione nuevamente el nodo de TV en vivo:

  - Si el usuario tiene permisos de reproducción del último canal visualizado entonces se debe dirigir al player del canal.

  - Si el usuario no tiene permisos de reproducción sobre el último canal visualizado se debe mostrar la pantalla de notificación.

#### HU005 - Pantalla de notificación- Canal no contratado

- Cuando el usuario acceda a un canal no contratado, la plataforma debe mostrar un mensaje de notificación para comunicar al usuario las acciones a tomar.
- La pantalla debe ser una modal y debe incluir los siguientes elementos:
  - **Titulo**: Transacción no disponible
  - **Texto informativo**:
    Adquiere este contenido desde la web o app de Claro video. Una vez adquirido podrás disfrutarlo en tu Roku.
  - **Botón**: ACEPTAR
    1. Cuando el usuario seleccione esta opción, se debe cerrar la pantalla modal y  mostrar la pantalla que detonó el flujo.
- La comunicación debe ser configurada por operación. 
  - Actualmente se debe aplicar para todas las operaciones el texto indicado en el ***criterio 2.***

#### HU006 - Pantalla de alerta- Falla del servicio de TV en vivo

- Cuando el servicio que carga la EPG falle,  la plataforma debe mostrar un mensaje de alerta para comunicar al usuario las acciones a tomar.
- La pantalla debe ser una modal y debe incluir los siguientes elementos:
  - Titulo: Hubo un error inesperado
  - Texto informativo: 
    Por el momento no pudimos completar la acción.
    Por favor, intenta más tarde.
  - Texto botón: OK
    1. Cuando el usuario seleccione esta opción, se le debe cerrar la pantalla modal y  mostrar la pantalla que detonó el flujo.

#### HU007 - Pantalla de notificación- Canal caído

1. Si el usuario accede a un canal que se encuentra caído, la plataforma debe mostrar un mensaje de alerta para comunicar al usuario que el canal no está disponible.
2. La pantalla debe ser una modal y debe incluir los siguientes elementos:
   1. Icono de alerta amarillo
   2. Titulo: 
      El canal {*@nombre del canal número de canal*} no está disponible por ahora
      - Ejemplo: El canal {HBO 199} no está disponible por ahora
   3. Texto informativo: 
      Continúa viendo tu programación favorita probando con otro canal.
3. El usuario debe poder navegar entre canales.
4. La comunicación debe ser configurada por operación. 

#### HU008 - Acceso a full EPG

- Para acceder a la full EPG el usuario debe estar visualizando un canal y seleccionar la tecla Down del control remoto. HU009

#### HU009 - Full EPG (Guía de programación)

- La full EPG debe cumplir con los siguientes criterios *con base al rediseño de la plataforma*:
  - Se debe visualizar sobre el player del canal.
  - La reproducción debe mantenerse en background.
  - Debe contener los siguientes elementos con sus respectivos datos:
    - Metadata del evento
    - Lista de canales con:
      - Logo del canal
      - Nombre del canal
      - Programación de eventos
      - Línea de tiempo.
  - Si un canal está bloqueado debe visualizarse en la Full EPG con el icono del Candado cerrado.
- La full EPG debe ocultarse cuando el usuario:
  - Selecciona la tecla Back del control remoto ó
  - Selecciona un canal.

#### HU010 - Acceso a mosaico de canales

- Para acceder al mosaico de canales el usuario debe estar visualizando un canal y seleccionar la tecla *Up* del control remoto. HU011

#### HU011 - Mosaico de canales

- El mosaico de canales debe cumplir con los siguientes criterios *con base al rediseño de la plataforma*:
  - Se debe visualizar sobre el player del canal.
  - La reproducción debe mantenerse en background.
  - Debe contener los siguientes elementos:
    - Título: Canales
    - Deben listarse los canales en orden ascendente de izquierda a derecha de acuerdo al número de canal en la EPG.
    - Por cada canal:
      - Número de canal
      - Logo de canal
      - Si el canal está bloqueado debe visualizarse el icono del Candado cerrado.
- El mosaico de canales debe ocultarse cuando el usuario:
  - Selecciona la tecla Back del control remoto ó
  - Selecciona un canal.

#### HU012 - Desbloqueo de visualización de canal

- Cuando el usuario acceda a un canal bloqueado la plataforma debe permitir el desbloqueo para su visualización.
- El usuario debe ingresar correctamente el PIN de seguridad configurado en su cuenta de Claro video para desbloquear la visualización del canal. HU013
- Una vez desbloqueado el canal el usuario podrá visualizar sus eventos siempre y cuando no cambie de canal.
- Si el usuario cambia de canal y posterior accede nuevamente al canal bloqueado debe iniciarse el flujo de desbloqueo de visualización.

#### HU013 - Ingresar PIN de seguridad

- La pantalla modal de Ingreso de PIN de seguridad debe visualizarse cuando el usuario selecciona en la EPG un canal bloqueado.
- Al visualizarse la modal de Ingreso de PIN de seguridad **NO** se debe iniciar en background la reproducción del canal bloqueado, tanto imagen como sonido.
- La pantalla de Ingreso de PIN de seguridad debe cumplir con los siguientes elementos *con base al rediseño de la plataforma*:
  - Titulo: Canal bloqueado
  - Texto informativo: 
    Ingresa tu pin de seguridad para desbloquear este canal.
  - Cajas de texto:
    1. Debe tener 6 campos numéricos
    2. Cuando el usuario ingrese el PIN de seguridad, los números deben visualizarse enmascarados.
    3. Debido a que hay usuarios que conservan su PIN de seguridad de 4 dígitos, se debe permitir al usuario continuar con el desbloqueo del canal a partir del ingreso del 4to al 6to dígito válido.
  - Opción para *visualizar/ocultar* los dígitos capturados.
  - Botón SIGUIENTE
    1. Cuando el usuario seleccione esta opción se debe verificar que el PIN capturado sea válido.
    2. Si el PIN capturado es válido entonces el canal debe desbloquearse para visualización.
    3. Si el PIN capturado es inválido entonces debe notificarse al usuario y el canal debe continuar bloqueado.
  - Botón CANCELAR
    - Cuando el usuario seleccione esta opción
      - La pantalla de Ingreso de PIN de seguridad debe cerrarse y dirigir al usuario a la pantalla que detonó el flujo.
      - El canal debe continuar bloqueado.
  - Opción *¿Olvidaste tu PIN de Seguridad?* 
    - Cuando el usuario seleccione esta opción la plataforma debe enviar el correo de *Recordatorio de PIN de seguridad* al correo configurado en la cuenta del usuario en la plataforma de Claro video, con la información y diseño solicitado en el rediseño de la plataforma.

#### HU010 - Insumos de diseño

- Revisar



### Action points

- [ ]   [GOOSE] Analizar.
- [ ]   [TALI/NACHO] Revisar si falta algún punto.
- [ ]   [NACHO] Generar la clave APA para canal default.
- [ ]   [JULI] Botón [ACEPTAR] o [OK], no pueden ser distintos... Mal UX. Si no, que me expliquen la diferencia a mí, no la entiendo.
- [ ]   [JULI] Revisar Assets / Armar lista de problemas.



## Youbora [análisis1]

*Metricas de Youbora*

Status: **[CHEQUEO/REVISIÓN]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-8761

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3624666450/BRF-8761+Todos+ROKU+Integrar+Youbora



### HUs

#### HU001 - Integrar Youbora

- Aplica únicamente para dispositivos Roku
- Se solicita integrar el plugin de Youbora en la aplicación
- Se debe utilizar las librerías de SceneGraph como Player
- Se comparte la liga y documentación del proveedor para la integración: https://documentation.npaw.com/integration-docs/docs/roku-scene-graph-raf-1



### Action points

- [ ]   [GOOSE] Analizar
- [ ]   [TALI/NACHO] Revisar si falta algún punto.
- [ ]   [NACHO] Pedir rechequeo.



## Player [análisis1]

*Player: controles y custom.*

Status: **TODO[1er ANÁLISIS]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-8613

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3626926211/BRF-8613+Todos+OTT+Roku+Player+VOD



### HUs

#### HU001- Player VOD

- El Player VOD se debe utilizar para Películas y Series VOD, así como sus traillers correspondientes.
- El Player VOD solo debe visualizarse si el usuario tiene permisos de reproducción.
- El Player VOD debe tener controles de reproducción, mismos que se detallan en otra historia de usuario (USER_02)
- Si el contenido nunca ha sido visualizado por el usuario, la reproducción debe realizarse desde el inicio.
- Si el contenido es gratuito y lo reproduce un usuario anónimo, la reproducción siempre debe realizarse desde el inicio.
- Si el contenido ya ha sido visualizado por el usuario, la reproducción debe iniciar desde el punto donde se interrrumpió la reproducción previa (bookmark)
- El bookmark de reproducción no aplica para los trailers.

#### HU002 - Control Player

- En cualquier contexto, si no existe ninguna acción sobre el player, en un lapso de 5 segundos la aplicación debe ocultar todos los elementos del Control Player, manteniendo la reproducción del contenido.
  - El tiempo debe empezar a contar desde que el usuario realiza una acción sobre el.
  - El tiempo debe ser configurable por cada operación.
- Para el dispositivo Roku, se debe mostrar el control player cuando el usuario oprima alguno de los siguientes botones del control remoto:
  - OK
  - UP

#### HU003 - Control Player: Título del contenido

- Para una Película únicamente debe mostrarse el título del contenido.
- La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). 
- Para un episodio de una serie, el título del contenido debe tener la siguiente información:
  - Nombre de la serie
  - Prefijo "Temp."
  - Número de temporada
  - Pipe |
  - Prefijo "Ep."
  - Número del episodio
  - Título del episodio
- El título del contenido VOD debe cumplir con las especificaciones indicadas en los insumos de diseño.

#### HU004 - Control Player: Barra de Progreso (progress bar)

- La Barra de progreso debe representar el tiempo de reproducción transcurrido y el tiempo que dura el programa.
- En la barra de progreso, el tiempo de reproducción transcurrido del contenido se debe representar con una barra de color rojo. Para el caso de ATV3 el color debe ser azul.
- En la barra de progreso, el tiempo de reproducción que dura el contenido se debe representar con una barra de color gris. Para el caso de ATV3 el color debe ser blanco.
- En la barra de progreso, el momento exacto de reproducción, es decir la posición, se debe representar con un pin en color rojo.
- De lado izquierdo de la barra de progreso se debe visualizar el tiempo de reproducción transcurrido, en el siguiente formato **hh:mm:ss**. Para ATV3 este punto no aplica.
- De lado derecho de la barra de progreso se debe visualizar el tiempo que dura el evento, en el siguiente formato **hh:mm:ss**.

#### HU005 - Navegar sobre la Progress Bar

- El pin y la barra que indica el progreso de la reproducción deben moverse en forma sincronizada con base a las acciones del usuario.
- La reproducción debe retroceder o avanzar con base a las acciones del usuario.
- El valor de la etiqueta que representa tiempo de reproducción transcurrido debe actualizarse con base a las acciones del usuario.
- Cuando el usuario navegue en la barra de progreso se debe mostrar sobre ella una imagen correspondiente a la escena del segundo selecionado (sprite). Véase USR_19.
- El pin, la barra que indica el progreso de la reproducción y el sprite deben moverse en forma sincronizada con base a las acciones del usuario.
- El Sprite debe corresponder a la escena del punto en el tiempo en que se posicione el usuario.
- Esta funcionalidad solo debe visualizarse para los contenidos que cuentan con el sprite generado.

#### HU006 - Control Player: Botón Atrasar (rewind)

- [RECHAZAR] - No cumple con las normativas de ROKU. Existe el botón en el control remoto.

#### HU007 - Control Player: Botón Pausar/Reproducir

- [RECHAZAR] - No cumple con las normativas de ROKU. Existe el botón en el control remoto.

#### HU008 - Control Player: Botón Adelantar (forward)

- [RECHAZAR] - No cumple con las normativas de ROKU. Existe el botón en el control remoto.

#### HU009 - Control Player: Botón Idiomas

- El botón "Idioma" siempre debe presentarse, sin importar las opciones de idioma con las que cuente el contenido.
  - Al seleccionar el botón "Idioma" se debe presentar el Panel de idioma.
  - Se cancela la HU041
- El panel de idioma debe visualizarse sobre el player con una transparencia que permita identificar la reproducción en background.
- El botón debe representarse con un icono correspondiente a Idioma y una etiqueta con el texto "Idioma". Tanto el icono como el texto deben ser personalizables (configurables) por región y dispositivo.
- El panel de idioma estará dividido en dos secciones: a) Panel Izquierdo que corresponderá a la metadata del contenido y b) Panel Derecho que corresponderá a las opciones de audio y subtitulos alternos.
- El panel de idioma debe tener la opción de salir/regresar.

#### HU010 - Panel Idioma: Metadata

- En orden de arriba hacia abajo se visualizará: 1) el título del contenido, 2) la duración del contenido y 3) la sinopsis del contenido.
- Para una Película el titulo corresponderá al nombre de la misma.
- Para un Episodio de una Serie, se debe mostrar el <Nombre de la Serie>, seguido del prefijo "Temp.", <Número de Temporada>, símbolo de pipe, prefijo "Ep.", <Número de Episodio> y <Título del capítulo>.
- La duración del contenido se presentará en el formato horas y minutos, ejemplo: **1h** **30min.**
- El texto correspondiente a la sinopsis del contenido debe mostrarse en formato alineado a la izquierda.
- La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). Véase USR_28.

#### HU011 - Panel de idioma (Single audio)

- La opción que debe aparecer seleccionada cuando se abre el panel, debe corresponder a "Idioma Original".
- El elemento seleccionado debe representarse con un arrow check.
- El usuario deberá presionar la opción Salir/Regresar para cerrar el Panel de idioma.
- Al mostrar el panel, la aplicación debe continuar con la reproducción del contenido.
- La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). Véase USR_28.
- El panel de idioma para cuando el contenido tenga una única opción de audio debe mostrar lo siguiente:
  - Título: "Idioma"
  - La opción de idioma original se debe mostrar de la siguiente forma:
    - "Ícono de idioma y el texto: Idioma Original"

#### HU012 - Panel de idioma (Múltiple audio)

- Al mostrar el panel, la aplicación debe continuar con la reproducción del contenido.
- La lista de audios y subtítulos debe ser dinámica con base a los disponibles para cada contenido.
- Cada opción de la lista debe ser seleccionable.
- La opción que debe aparecer seleccionada cuando se abre el panel, debe corresponder al audio de la reproducción que se esta ejecutando.
- Solo se puede seleccionar una opción a la vez.
- El elemento seleccionado debe representarse con un arrow check.
- Cuando el usuario seleccione una opción de audio o subtítulos, la aplicación debe reproducir automáticamente el contenido con la nueva configuración.
- El usuario deberá presionar la opción Salir/Regresar para cerrar el Panel de idioma.
- La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). Véase USR_28.
- El panel de idioma para cuando el contenido tenga múltiples opciones de idioma debe mostrar lo siguiente:
  - Título: "Idioma"
  - La opción de idioma original se debe mostrar de la siguiente forma:
    - "Ícono de idioma y el texto: Idioma Original"
  - Si el contenido cuenta con audios diferentes al original, entonces se debe presentar las opciones disponibles con el siguiente formato:
    - "Ícono de idioma y texto: Doblada al [Idioma]".
      - Ejemplo: "Doblada al Español"
  - Si el contenido cuenta con subtítulos, entonces se debe presentar las opciones disponibles con el siguiente formato:
    - "Ícono de subtítulo y texto: Subtitulada al [Idioma]".
      - Ejemplo: "Subtitulada al Español"

<img src="img/player_idiomas.png" alt="player_idiomas" style="zoom:50%;" />

#### HU013 - Panel Idioma: Opción Salir/Regresar

- Para el dispositivo Roku, cuando el usuario oprima el botón *Back* del control remoto se debe cerrar el Panel Idioma.

#### HU014 - Control Player: Botón Temporadas

- **[REVISAR FACTIBILIDAD]**

- La aplicación debe visualizar el carrusel de temporadas y episodios sin interrumpir la reproducción.
- Esta funcionalidad no estará disponible para ATV3. **(!!!)**
- El botón debe representarse con un icono y una etiqueta con el texto "Temporadas". Tanto el icono como el texto deben ser personalizables (configurables) por operación.
  1. Se elimina la HU050.
- El carrusel de temporadas y episodios debe tener controles para navegar entre temporadas y episodios, así como metadata de los mismos.
- Cuando se abra el carrusel el foco de la aplicación debe estar sobre la temporada a la que corresponde el episodio en cuestión.
- Cuando se abra este componente, el carrusel debe contener la lista de episodios de la temporada en cuestión.
- El carrusel de temporadas y episodios debe tener la opción de Cerrar.

#### HU015 - Carrusel de Temporadas y Episodios: Seleccionar Temporada

- **[REVISAR FACTIBILIDAD]**

- Cada elemento, de la lista de temporadas, debe representarse con el prefijo "Temporada" más el <número> de temporada correspondiente. Ejemplo: Temporada 8.
- La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). Véase USR_28.
- Siempre que seleccione una temporada diferente a la actual, la aplicación debe actualizar la lista de episodios.

#### HU016 - Navegación entre temporadas

- **[REVISAR FACTIBILIDAD]**

- La navegación del carrusel no debe ser cíclica.
- Se deben visualizar las temporadas definidas por dispositivo en el layout.
  - En el primer layout el carrusel se queda estático y el usuario puede mover el foco hasta la última posición de la derecha, a partir de esa posición si el usuario se mueve a la derecha el foco se queda estático y las temporadas se empiezan a recorrer de a uno a la izquierda.
- Si la temporada que está visualizando el usuario excede de las definidas en el layout entonces la lista de temporadas se debe recorrer un espacio a la izquierda, ocupando la última posición de la derecha para permitir la visualización completa de la temporada seleccionada.

#### HU017 - Carrusel de Episodios: Seleccionar Episodio

- **[REVISAR FACTIBILIDAD]**

- Al reproducir el nuevo episodio, la aplicación debe ocultar el carrusel y continuar con la reproducción.
- La aplicación debe reproducir el nuevo episodio siempre y cuando el usuario lo tenga adquirido.
- Si el usuario no tiene adquirido el nuevo episodio seleccionado y el contenido tiene un solo tipo de oferta, la aplicación deberá mostrar la "Pantalla checkout transacción". Si el usuario ya tiene asociado un método de pago, se debe mostrar la modal de adquisición de medio de pago asociado, en caso contrario se deberá visualizar la modal de adquisición para usuarios sin método de pago asociado. Se recomienda consultar los BRFs de transacciones: [BRF-5435_Rediseño Transacciones](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2327117866) 
- Si el usuario no tiene adquirido el nuevo episodio seleccionado y el contenido tiene más de un tipo de oferta, la aplicación debe abrir una modal con las ofertas disponibles. Véase USR_32.
- Si el usuario tiene activado el PIN de seguridad (parental), para este flujo en especifico, no debe solicitarlo, ya que previamente ya fue solicitado en la vCard al reproducir el capítulo inicial.

#### HU018 - Navegación entre episodios

- **[REVISAR FACTIBILIDAD]**

- La navegación del carrusel de episodios debe ser cíclica para la temporada seleccionada.

#### HU019 - Carrusel de Episodios: Metadata del Episodio

- **[REVISAR FACTIBILIDAD]**

- El poster de cada elemento debe corresponder al del Episodio en cuestión.
- El bookmar se debe representar con una progress bar, sobre el póster del episodio, en la parte inferior.
- Para la longitud máxima soportada para los textos véase USR_28. >> **quiero** que cuando se supere la longitud máxima soportada para cada unos de los textos, en los diferentes dispositivos, se usen puntos suspensivos (...). 

#### HU020 - Carrusel de Episodios: Opciones de oferta

- **[REVISAR FACTIBILIDAD]**
- La modal debe mostrarse cuando el usuario seleccione un contenido que no haya adquirido y tenga más de un tipo de oferta.
- La modal debe tener el encabezado"¿Como quieres adquirir este contenido?". Mismo que debe ser configurable por región y dispositivo.
- En la modal se debe mostrar el título en español y el título original del contenido. Ejemplo: Cómo conocí a tu madre (How I meet your mother).
- Para el caso de episodios de series, se visualizará el título de la serie en cuestión.
- Las opciones de adquisición dependerán de cada contenido y se representarán, cada una, con un botón, el cuál mostrará el tipo de adquisición y el precio.
- Cuando el usuario seleccione un botón de adquisición, la aplicación deberá mostrar la "Pantalla checkout transacción". Si el usuario ya tiene asociado un método de pago, se debe mostrar la modal de adquisición de medio de pago asociado, en caso contrario se deberá visualizar la modal de adquisición para usuarios sin método de pago asociado. 

#### HU021 - Carrusel de Temporadas y Episodios: Opción Cerrar

- **[RECHAZAR]**
- Se debe habilitar el botón ***CERRAR*** en el carrusel de temporadas y episodios con base a lo solicitado en el criterio 1 de la **USR_21** del [BRF-5405_Rediseño Player VOD](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2311717423). 
- El botón ***CERRAR*** debe tener las siguientes características:
  - Texto: CERRAR
    - El texto debe ser configurable por operación.
  - El botón debe cumplir con las especificaciones indicadas en los insumos de diseño.
  - El botón debe ser focuseable.

#### HU022 - Agregar a Mi Lista

- El player debe tener un botón que permita agregar el contenido que se está reproduciendo a "Mi Lista".
- Se debe presentar con el texto "Agregar a Mi Lista".
- Al presionar el botón, el contenido en reproducción se debe añadir al carrusel "Mi Lista" del nodo "Mis contenidos".
- Si el usuario agregó el contenido a "Mi Lista", entonces el player debe mostrar un botón que permita quitarlo.
- Se debe presentar con el texto "Quitar de Mi Lista".
- Al presionar el botón, el contenido en reproducción se debe eliminar del carrusel "Mi Lista" del nodo "Mis contenidos".

#### HU023 - Omitir Intro

- El botón debe tener el texto "OMITIR INTRO" y este debe ser configurable por región y dispositivo.
- Cuando el usuario seleccione el botón, la aplicación debe omitir la introducción y avanzar al inicio del contenido.
- Cuando se encuentre reproduciendo la introducción de un contenido sin presentarse el control player, el botón "Omitir intro" se debe mostrar en pantalla durante 7 segundos.
- Cuando se encuentre reproduciendo la introducción de un contenido y se presente el control player en pantalla, se debe mostrar también el botón de "Omitir intro".
  - Si se cumple el tiempo de inactividad de 5 segundos se debe cerrar el control player pero se debe mostrar el botón de "Omitir intro" por 7 segundos más siempre y cuando no haya terminado la intro.
- Cuando la introducción del contenido termina el botón “Omitir intro“ debe desaparecer.
- Si el usuario retrocede la reproducción y cae en la introducción del contenido, se debe presentar el botón de "Omitir intro" únicamente al mostrar el control player.

<img src="img/player_vod_omitirintro.png" alt="player_vod_omitirintro" style="zoom:50%;" />

#### HU024 - Control Player: Botón PIP

- **Quiero** que en el dispositivo Roku no se habilite el botón PIP en el control player **(??? OK!)**

#### HU025 - Control Player: Opción Salir

- Cuando el usuario seleccione el botón Salir, la aplicación debe parar la reproducción, cerrar el player y mostrar la ficha (vCard) del contenido VOD que se estaba reproduciendo. Debe quedar registrado el tiempo reproducido en el bookmark del usuario.

#### HU026 - Contenido con bookmark

- Aplica para películas y series.
- La modal se debe presentar sobre la experiencia de player.
- La modal debe contener las siguientes características:
  - Texto: “Continúa con tu reproducción“
    - Configurable por operación.
  - Botón “REANUDAR“ con esquinas redondas, fondo color rojo y texto color blanco.
    - Texto configurable por operación.
    - Al seleccionarlo se debe continuar con la reproducción desde el instante que indica el bookmark del contenido.
  - Botón “DESDE EL PRINCIPIO“ con esquinas redondas, fondo color gris y texto color blanco.
    - Texto configurable por operación.
    - Al seleccionarlo se debe iniciar desde el inicio la reproducción del contenido.
    - En caso de serie se debe reproducir desde el inicio del capitulo seleccionado.
- En caso de que no se tenga bookmark en el contenido seleccionado, se debe reproducir el contenido desde el inicio.
- Si el contenido requiere el ingreso de PIN se debe mostrar primero la modal para capturarlo.

#### HU027 - Modal opciones de reproducción

- **[ES LO MISMO QUE LA ANTERIOR]**

#### HU028 - Comunicación usuario anónimo: Contenido no gratuito

- **[DEPENDE DE IMPLEMENTACION DE ANONIMO]**

#### HU029 - Usuario anónimo: Regístrate

- **[DEPENDE DE IMPLEMENTACION DE ANONIMO]**

#### HU030 - Usuario anónimo: Iniciar sesión

- **[DEPENDE DE IMPLEMENTACION DE ANONIMO]**

#### HU031 - Usuario anónimo: Cancelar

- **[DEPENDE DE IMPLEMENTACION DE ANONIMO]**

#### HU032 - Longitud máxima de textos

- **[DAH!!]**
- **Quiero** que en el dispositivo Roku se considere la longitud máxima de caracteres con base al rediseño

#### HU033 - Métricas

- **[QUÉ ES AYM?]**
- El equipo de AYM deberá especificar las métricas a implementar por cada pantalla y flujo en el dispositivo Roku.

#### HU034 - Insumos de diseño: Player VOD



### Action points

- [ ]    [GOOSE] Analizar
- [ ]    [JULI] Revisar Assets / Armar lista de problemas.



## Buscador [análisis1]

*Evolución de buscador.*

Status: **TODO[2do ANÁLISIS]**



https://dlatvarg.atlassian.net/browse/BRF-8804

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3630071942/BRF-8804+Todos+OTT+Roku+Nodo+Buscador



### HUs

#### HU001 - Ubicación Nodo Buscador

- [x]  Primera posición
- [ ]  Revisar diseño

#### HU002 - Contenidos populares

- Se deben mostrar las búsquedas más populares con base en lo solicitado en los criterios 1, 2 y 3 de la USER03 del BRF-5395_Rediseño:Buscador 

<img src="img/busquedas_populares.png" alt="busquedas_populares" style="zoom:50%;" />

- Se deben mostrar las 10 búsquedas más populares tomadas de las métricas de los usuarios por operación y dispositivo.

- En las búsquedas populares se pueden mostrar:

  - Contenido VOD (unitarios y series)


  - Contenido en vivo (unitarios y series)


  - Talentos


  - Grabaciones


  - Canales lineales en vivo


- Se deben mostrar de acuerdo a lo que más consuma la operación.

#### HU003 - Cuadro de texto y búsqueda

- OK

#### HU004 - Mostrar contenidos

- Si el usuario selecciona el “Buscador” se debe mostrar en la “home del buscador” los contenidos que la aplicación “identifica” como los “Populares” para esa operación.
- Los contenidos “Populares” aparecen sin necesidad de que se haya ingresado ninguna palabra.
- Debe mostrarse un titulo (como por ejemplo “Búsquedas populares”), el cual debe ser configurable por operación. Este debe mostrarse en la parte superior de los contenidos mostrados.
- La aplicación debe identificar de forma “automática”, cuales son los contenidos que cumplen con el criterio de los contenidos con mayor cantidad de visualizaciones en un lapso de tiempo establecido por la operación (por ejemplo últimos 15 días)

#### HU005 - Contenidos VOD

<img src="img/resultado_buscador.png" alt="resultado_buscador"  />

#### HU006 - Eventos de TV

Cada uno de los contenidos deberá mostrar un layout superior y uno inferior.

El superior:

- Titulo del evento que coincide con la búsqueda.
- Timeline de tiempo de reproducción del evento: Solo se representa si el evento esta “En Vivo” y justamente indica el tiempo que lleva reproducido desde que inició el programa a la hora actual en el cual el usuario obtiene los resultados de búsqueda.

El inferior:

- Botón de Player: se debe representar con un “circulo” rojo y sobrepuesto el símbolo de “avanzar” en color blanco y centrado: 

  - este sólo se mostrará si el usuario lo puede reproducir. Ver USER09

  - Solo se mostrará si el evento está en “En Vivo” y “Ya Emitido”

  - Los eventos que se emiten “Mas Tarde” no presentan el botón de player USER13


- Titulo del evento.

- Número de canal en donde se visualiza el evento. El formato a representar los canales es XXX. En caso de que el canal no tenga los tres dígitos se debe completar con 0.

- |  (símbolo que separa el número del canal del Nombre)

- Nombre del canal.

- “icono” de TV (símbolo que represente una “Tv”)

- Texto que indique si el contenido se permite reproducir (por ejemplo “Ahora”), ya finalizo (por ejemplo “Ya emitido”) o se trasmitirá en un horario posterior (por ejemplo “Más tarde”).

  - El texto debe ser configurable por operación.


  - Debe mostrarse con un fondo de color, el cual debe ser configurable por operación, así como diferente por cada cada texto.


- Para eventos presentes se debe mostrar el Horario de transmisión: 

  - El formato debe ser HH:MM - HH:MM 
    Ejemplo: 15:45 - 16:45

- Para eventos pasados y futuros ver la HU025

<img src="img/buscador_evento_tv.png" alt="buscador_evento_tv"  />

#### HU007 - Icono de Player

Solo en los contenidos o eventos a los que tenga “derecho” se debe mostrar  el icono del Player.

#### HU008 - Icono de Player contenido con derechos de reproducción

- **[DENEGAR]  - OJO! Aparentemente no aplica en ROKU**
- Quiero que si el usuario selecciona el icono del “Player” en un contenido, se inicie la reproducción del contenido. 

- Si el contenido nunca ha sido reproducido por el usuario, debe iniciar desde el inicio de la reproducción.
- Si el contenido seleccionado ya ha sido reproducido por el usuario, la reproducción debe iniciar en el minuto:segundo en el que se “quedó” la reproducción.
  - Si el contenido es una serie, la reproducción debe iniciar en la temporada y capitulo ultimo visto, considerando el minuto:segundo a partir detuvo la reproducción.
- Si es un evento de TV “En Vivo” deberá ir al Player contenidos live. [Revisar contenidos “Ya Emitidos”]
- **Aplica para dispositivos Web, Android, iOS y W10**

#### HU009 - Contenidos VOD sin derechos de reproducción

- Si el usuario selecciona el poster de un contenido VOD sin derecho  en cualquier “parte” del mismo debe abrir la vcard.

#### HU010 - Evento de TV “Futuro”

- Se debe dirigir al usuario al ambiente de Tv en vivo cuando seleccione un evento que se emitirá “Mas Tarde”, mostrando el Menú de Opciones del programa futuro sobre la reproducción que sucede en vivo en ese mismo canal.

#### HU011 - Opciones del Programa

En el caso anterior...

<img src="img/buscador_a_opciones_vivo.png" alt="buscador_a_opciones_vivo" style="zoom: 25%;" />

#### HU012 - Botón Más info 

- [ ]  Revisar posibilidad de ”mantener apretado” en la cinta.

#### HU013 - Eventos de TV - Resultados de búsqueda

- Para los eventos de televisión, los resultados de búsqueda se deben obtener en una ventana de tiempo de 1 día atrás y 2 días adelante.
- Para eventos pasados en los que el canal no tenga timeshift, **no** se deben mostrar en los resultados de búsqueda.
- Los eventos grabados no entran dentro de la ventana de tiempo, es decir siempre se deben mostrar en los resultados.

#### HU014 - Mostrar estatus de grabación en resultados de búsqueda

- **[DENEGAR] no hay grabación en ROKU** 

#### HU015 - Mostrar fecha en resultados de TV

- Quiero que en dispositivos Roku los resultados de la búsqueda para eventos de TV pasados y futuros, se muestre la fecha de emisión

#### HU016 - Mostrar asset de recordatorio en evento de TV

- **[REVISAR]** que en dispositivos Roku los resultados de búsqueda para u evento de TV tiene un recordatorio activo, se muestre el asset relacionado

#### HU017 - Mostrar chapitas

- OK

#### HU018 - Canal sin derechos de reproducción

- Si la búsqueda arroja un evento que está dentro de un canal sin derecho de reproducción y el usuario lo selecciona, se debe mostrar la pantalla de contratación con base a lo descrito en el  en la 

  - Canal que pertenece a un add on. 

  - Canal que pertenece a uno o más paquetes de canales. 

  - Canal que se contrata desde Call center.

#### HU019 - Flujos de Eventos TV seleccionados

![buscador_evento_ahora](img/buscador_evento_ahora.png)

#### HU020 - Modal Player

Cuando ya empezó a verlo, mostrar la posibilidad de :

![vod_ya_iniciado](img/vod_ya_iniciado-1672776930869-7.png)

#### HU021 - Keyword sin resultados

<img src="img/busqueda_no_encontrada.png" alt="busqueda_no_encontrada" style="zoom:50%;" />

#### HU022 - Búsquedas para perfil Kids

- **[REVISAR]** que en dispositivos Roku al realizar una búsqueda desde un perfil Kids, únicamente se muestren resultados que estén dentro de las categorías permitidas de dicho perfil.

#### HU023 - Resultado de búsqueda no disponible para perfil kids

- **[RECHAZAR] Si se hace la búsqueda como kid, no se sabe si está en otro perfil**

#### HU024 - Keyword sin resultados (kids)

<img src="img/busqueda_kid_no_result.png" alt="busqueda_kid_no_result" style="zoom: 33%;" />

#### HU025 - Búsqueda de canal

1. Los resultados de búsqueda deben mostrar canales de televisión.
2. Los resultados de canales deben ser mostrados de acuerdo a la experiencia de cada dispositivo.
3. Los canales deben ser ordenados de manera ascendente con respecto al número del canal.
4. Por cada canal encontrado se debe mostrar:
   1. Logo de canal y barra de progreso.
   2. Asset de play
      1. Para AAF, ATV4 y COSHIP se debe mostrar sobre el logo en la esquina inferior derecha.
      2. Para Web, ADR, IOS, W10 y XBOX se debe mostrar a la derecha de la información del canal.
   3. Nombre del evento que esta actualmente en vivo.
   4. El número de canal seguido del nombre del canal, separado por el símbolo de barra vertical “ | “.
   5. El icono de “TV” seguido de la etiqueta “AHORA“.
   6. El horario del evento.
5. Cuando el usuario seleccione un canal:
   1. Contratado, se debe iniciar la reproducción del canal con el evento en vivo.
   2. No contratado, se debe iniciar el flujo de contratación correspondiente.

#### HU026 - Búsqueda de Talentos

1. Esta historia de usuario actualiza la  historia de usuario **HU030.**
2. Al realizar la búsqueda de Talentos, se debe visualizar debajo del nombre del Talento, el rol con que cuenta, dónde:
   1. Si el Talento no tiene rol, éste dato puede ir vacío en los resultados de la búsqueda, es decir únicamente se presentará la imagen y nombre del Talento.
   2. Si el Talento cuenta con 2 o más roles:
      1. Los roles se deben ordenar como se tiene actualmente en producción,
      2. Los roles se deben concatenar y mostrar separados por comas,
      3. La descripción de los roles puede extenderse a máximo dos líneas, si el Talento cuenta con muchos roles.
      4. Si los roles del Talento exceden las 2 líneas entonces se debe colocar tres puntos al final de la segunda línea para indicar al usuario que cuenta con más roles.
      5. Para cada dispositivo la longitud por línea se detalla en el freehand **(NO ESTA ROKU)**
   3. Los roles se deben mostrar en el idioma que fueron ingestados.
3. Si el usuario selecciona el poster de un Talento, se debe mostrar la pantalla descrita en la **HU038**.

#### HU027 - Pantalla de Talentos (filtros)

**PANTALLA DE TALENTOS !!!**

<img src="img/filtros_talentos.png" alt="filtros_talentos" style="zoom: 33%;" />

#### HU028 - Seleccionar “Back” en contenido VOD

- **[REVISAR]**

#### HU029 Seleccionar “Back” en contenido de TV

- **[NO CREO QUE SE PUEDA - FUERA DE CIRCUITO]**

#### HU030 Terminar de visualizar contenido VOD seleccionado desde el Fin Player

- **[REVISAR]**

#### HU031 - Insumos de diseño

- [JULI]



### Action points

- [ ]      [GOOSE] Analizar
- [ ]      [NACHO] Conseguir información de api de busquedas populares [HU002].
- [ ]      [JULI] Revisar Assets / Armar lista de problemas.
- [ ]      [JULI] Revisar si hay un asset de la ficha de talentos en resultado de buscador
- [ ]      [GOOSE] Revisar el API https://dlatvarg.atlassian.net/wiki/spaces/PLAYER/pages/3177119988/ranking+list



## Player en Vivo [analisis1]

*Controles de Player en Vivo.*

Status: **TODO[1er ANÁLISIS]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-8767

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3627319528/BRF-8767+Todos+OTT+Roku+TV+en+vivo+-+Control+Player



### HUs

#### HU001 - Funcionalidades de TV en vivo

- Transacciones de canales
- Mini EPG
- Más opciones (Mini y Full EPG)
- Bloqueo y desbloqueo de canales
- Filtrado de canales por categoría
- Favoritear canales
- Menú de opciones de canal
- Menú de idiomas
- **[RECHAZAR]** Grabaciones -- no aplica
- Timeshift

#### HU002 - Insumos de diseño: TV en vivo

- **[JULI]** Revisar

#### HU003 - Elementos en foco

1. El nuevo diseño del foco debe aplicarse en todas las secciones de la plataforma.
2. El diseño del foco debe contener las siguientes características:
   1. El foco debe envolver al elemento preseleccionado con un borde blanco
   2. El elemento preseleccionado debe tener un pequeño incremento
   3. Se debe de agregar un efecto de opacidad para los elementos no seleccionados

| Homes      | https://drive.google.com/file/d/11IHuVBl2YB_VbAKNX7IqUDVoFeUzo8I-/view |
| ---------- | ------------------------------------------------------------ |
| Carruseles | https://drive.google.com/file/d/1JATw1PQymLqhMPF1uTyhBDBBoVDMhGHE/view |
| Temporadas | https://drive.google.com/file/d/1BpKH2xsgX5v4DpUHlCUW7YXvGwPG8uE1/view |
| vCards     | https://drive.google.com/file/d/1pgmmq5PtcnAv9-gNaHgdu7nRy32k8cWu/view |

#### HU004 - Control Player 

1. El control player se debe mostrar:
   1. Cuando el usuario comience la reproducción de un evento en vivo.
   2. **[REVISAR]** Cuando el usuario oprima alguno de los siguientes botones del control remoto:
      1. OK
      2. UP
2. El control player debe tener los elementos indicados en la **HU020.**
3. En cualquier contexto, si no existe ninguna acción sobre el player, en un lapso de **X** segundos la aplicación debe ocultar todos los elementos del Control Player, manteniendo la reproducción del contenido.
   1. Inicialmente se puede configurar por 5 segundos, sin embargo este valor puede cambiarse de acuerdo a la necesidad de la operación.
   2. El tiempo de visualización del Control Player debe ser configurable por operación.
   3. El tiempo debe empezar a contar desde que el usuario realiza una acción sobre el Control Player.

#### HU005 - Elementos del Control Player

- - Número del Canal (**HU006**)
  - Logo del Canal (**HU007**)
  - Título del contenido (**HU008**)
  - Barra de progreso (**HU009** y **HU010**)
  - Etiqueta AHORA (**HU011**)
  - Botón Grabar (**HU012**)
  - Botón Atrasar (**HU013**)
  - Botón Pausar/Reproducir (**HU014**)
  - Botón Adelantar (**HU015**)
  - Botón Guía (**HU016**)
  - Botón Idiomas (**HU017**)
  - Botón SALIR (**HU018**)
  - Botón MÁS OPCIONES (**HU019**)
  - Botón MINI GUÍA (**HU020**)
  - Botón VER DESDE EL INICIO (**HU021**)
- Los elementos del control player deben cumplir con las especificaciones solicitadas en los insumos de diseño. 
  - La posición de los elementos del control player no debe tener afectación al agregar u ocultar botones del control player o de la botonera de opciones.

#### HU006 - Control Player: Número del Canal

- Ver HU005

#### HU007 - Control Player: Logo del Canal

- Ver HU005

#### HU008 - Control Player: Título del contenido

- Link a un documento, que linkea a otro documento, que tira error de carga (gracias)

#### HU009 - Control Player: Barra de progreso (Progress Bar)



#### HU010 - Navegar sobre la Progress Bar

**[REVISAR] Todo esto hay que hacerlo a mano!!**

- En la barra de progreso, el tiempo total de reproducción que dura el programa se debe representar con una barra de color gris obscuro.
- En la barra de progreso, el tiempo de reproducción transcurrido del programa, en tiempo real, se debe representar con una barra de color gris claro. (Aplica para escenarios de timeshift). También véase **HU054**
- En la barra de progreso, el tiempo de reproducción en el que se encuentra el usuario, se debe representar con una barra de color rojo. Para un evento en vivo, sin que el usuario haya retrasado la reproducción, esta barra estará sincronizada con el tiempo transcurrido del programa en tiempo real. 
- En la barra de progreso, el momento exacto de reproducción, es decir la posición en el tiempo, se debe representar con un pin en color rojo.
- De lado izquierdo de la barra de progreso se debe visualizar el tiempo de reproducción transcurrido, en el siguiente formato **hh:mm:ss**.
- De lado derecho de la barra de progreso se debe visualizar el tiempo que dura el evento, en el siguiente formato **hh:mm:ss**.
- En el extremo derecho de la barra de progreso, se debe mostrar la etiqueta "AHORA".
- La etiqueta "AHORA" siempre se muestra en eventos en vivo. Cuando la posición de la reproducción está alineada con la transmisión en vivo, el background de la etiqueta debe ser de color rosa, en caso contrario de color gris claro.
- Para ATV4 se debe indicar al usuario la funcionalidad Press&Hold para acceder al menú de opciones. Véase **HU064**.

#### HU011 - Control Player: Etiqueta AHORA

- Ver HU010

#### HU012 - Control Player: Botón Grabar

- **[RECHAZAR]**

#### HU013 - Control Player: Botón Retroceder (Rewind)

- **[REVISAR]**
- Ver HU010
- El tiempo definido para el botón retroceder es 10 segundos, sin embargo este tiempo debe ser personalizable (configurable) por región y dispositivo.
- El botón debe representarse con un icono correspondiente a backward, mismo que debe ser personalizable (configurable) por región y dispositivo.
- La visualización del botón Retroceder será dinámica con base a las reglas de timeshift.

#### HU014 - Control Player: Botón Pausar/Reproducir

- **[REVISAR]**
- Ver HU010
- Si la reproducción esta activa debe mostrarse el botón Pause.
- El botón debe representarse con un icono correspondiente a Play, cuando la reproducción este inactiva.
- El icono y texto del botón debe ser personalizables (configurables) por región y dispositivo.

#### HU015 - Control Player: Botón Avanzar (Forward)

- **[REVISAR]**
- Ver HU010
- El tiempo definido para el botón avanzar es 10 segundos, sin embargo este tiempo debe ser personalizable (configurable) por región y dispositivo.
- El botón debe representarse con un icono correspondiente a forward, mismo que debe  ser personalizable (configurable) por región y dispositivo.
- La visualización del botón Avanzar será dinámica con base a las reglas de timeshift.

#### HU016 - Control Player: Botón Guía

1. Cuando el usuario seleccione el botón Guía, la aplicación debe ocultar el control player y mostrar la Guía completa (Full EPG).
2. La reproducción del programa no se debe interrumpir.
3. La Guía de programación debe mantenerse visible hasta que el usuario decida cerrarla.
4. Para mayor detalle de la funcionalidad de la EPG véase: **HU027**

#### HU017 - Control Player: Botón Idiomas

- Se debe habilitar el botón **Idiomas** con las siguientes características:
  - Texto: Idiomas - El texto debe ser configurable por operación.
  - Cuando el usuario seleccione esta opción, la plataforma debe mostrar el panel de idiomas **(HU022)**.
  - El botón debe cumplir con las especificaciones indicadas en los insumos de diseño.

#### HU018 - Control Player: Botón SALIR

- **[RECHAZAR]** No aplica a ROKU

#### HU019 - Control Player: Botón MÁS OPCIONES

1. El botón Opciones se mostrará siempre en el control player.
2. La aplicación debe visualizar el panel de "Opciones de Programa y de Canal" sin interrumpir la reproducción.
3. El panel de "Opciones de Programa y de Canal" debe visualizarse sobre el player con una transparencia que permita identificar la reproducción en background.
4. El botón **MÁS OPCIONES** debe tener las siguientes características:
   1. Texto: MÁS OPCIONES - El texto debe ser configurable por operación.
   2. El botón debe cumplir con las especificaciones indicadas en los insumos de diseño.
   3. El botón debe ser focuseable.

#### HU020 - Control Player: Botón MINI GUÍA

- Se debe habilitar el botón **MINI GUÍA** en el control player con base en las siguientes características:
  - Texto: MINI GUÍA - El texto debe ser configurable por operación.
  - Cuando el usuario seleccione esta opción, la plataforma debe mostrar la MINI EPG.
  - El botón debe cumplir con las especificaciones indicadas en los insumos de diseño.
  - El botón debe ser focuseable.

#### HU021 - Control Player: Botón VER DESDE EL INICIO

- Se debe habilitar el botón **VER DESDE EL INICIO** en el control player, únicamente para los canales con Timeshift.  
- Si el usuario está visualizando un evento en vivo que ya inicio, se debe habilitar el botón **VER DESDE EL INICIO**.
  - Cuando el usuario seleccione esta opción, la reproducción del evento debe reproducirse desde el inicio y el texto del botón debe cambiar a **VER AHORA**.
- Si el usuario selecciona el botón **VER AHORA**, el evento debe reproducirse  en tiempo real.
- El botón debe cumplir con las especificaciones indicadas en los insumos de diseño.
- El botón debe ser focuseable.

#### HU022 - Panel de Idiomas

- Se debe habilitar el panel de idiomas con las siguientes características:
  - Cuando se visualice el panel de idioma la reproducción del contenido **no** se debe pausar.
  - El panel de idioma debe dividirse en dos secciones: 
    - Panel Izquierdo: que corresponderá a la metadata del contenido (**HU023**)
    - Panel Derecho: que corresponderá a las opciones de audio y subtítulos (**HU024**, **HU025**, **HU026** y **HU027**)
  - Cuando el usuario oprima el botón *Back* del control remoto se debe cerrar el Panel Idioma.

#### HU023 - Panel de Idiomas: Metadata

- Se debe visualizar la metadata del contenido en el panel de idioma con base en lo solicitado en la **HU022** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644)  y en la historia de usuario mencionada en la misma.
- En orden de arriba hacia abajo se visualizará: 1) el título del programa, 2) la etiqueta de estado, 3) El horario de transmisión, 4) la duración del programa y 5) la sinopsis del programa.
- La etiqueta de estado va a depender del momento en el que esté el evento, pasado, presente y futuro, según corresponda: "Ya emitido", "Ahora", "Más tarde". Además de color de background: Naranja, Rosa y Verde. Los textos y colores deben ser configurables por región y dispositivo.
- El horario de transmisión se presentará en el formato horas y minutos, ejemplo 16.00hs a 17.30hs
- La duración del programa se presentará en el formato horas y minutos, ejemplo: **1h** **30min.**
- El texto correspondiente a la sinopsis del programa debe mostrarse en formato alineado a la izquierda.
- El número y logo del canal se debe mostrar en la sección derecha.
- Para conocer la longitud máxima soportada para estos textos véase **HU065**.

#### HU024 - Panel de Idiomas: Opciones de audio alterno

- Se deben habilitar las opciones de audio alterno en el panel de idiomas con base en lo solicitado en la **HU023** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644) y en la historia de usuario mencionada en la misma.
- La sección derecha del panel debe mostrar la etiqueta "Elige el idioma del canal", el texto debe ser configurable.
- La lista de audios debe tener el titulo "AUDIO", mismo que será personalizable (configurable).
- La lista de audios será dinámica con base a los disponibles para cada canal.
- Cada opción de la lista de audios se representará con un icono y una etiqueta de texto que describa el idioma. Ambos deben ser configurables por región y dispositivo.
- Cada opción de la lista debe ser seleccionable.
- La opción que debe aparecer seleccionada, cuando se abre el panel, debe corresponder al audio de la reproducción que se esta ejecutando.
- Solo se puede seleccionar una opción a la vez. Véase **HU025**
- El elemento seleccionado debe representarse con un arrow check.

#### HU025 - Panel de Idiomas: Seleccionar audio alterno

- Se debe habilitar la selección de audio alterno en el panel de idioma con base en lo solicitado en los criterios **1** y **2** de la **HU025** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).
- Cuando el usuario seleccione una opción de audio, la aplicación debe reproducir automáticamente el nuevo audio. 
  1. Al seleccionar alguna configuración de audio el panel de opciones se debe mantener.
- El elemento seleccionado debe representarse con un arrow check.
- Para ATV4 y STV, el foco sobre un elemento de la lista debe representarse con un backgroud rojo, mientras que para Coship-9085 debe ser un outer-line blanco.

#### HU026 - Panel de Idiomas: Opciones de subtítulos alternos

- Se deben habilitar las opciones de subtítulos alternos en el panel de idiomas con base en lo solicitado en la **HU024** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644) y en la historia de usuario mencionada en la misma.
- La lista de subtítulos debe tener el titulo "SUBTÍTULOS", mismo que será personalizable (configurable) por región y dispositivo.
- La lista de subtítulos será dinámica con base a los disponibles para cada canal.
- Cada opción de la lista de subtítulos se representará con un icono y una etiqueta de texto que describa el idioma. Ambos deben ser configurables por región y dispositivo.
- A la lista de subtítulos se le debe agregar la opción "Desactivados", para desactivar los subtítulos. 
- Cada opción de la lista debe ser seleccionable.
- La opción que debe aparecer seleccionada, cuando se abre el panel, debe corresponder a los subtítulos de la reproducción que se esta ejecutando. En caso de que no este activo ningún subtítulo, la opción seleccionada debe ser "Desactivados".
- Solo se puede seleccionar una opción a la vez. Véase **HU026**
- El elemento seleccionado debe representarse con un arrow check.

#### HU027 - Panel de idioma: Seleccionar subtítulos alternos

- Se debe habilitar la selección de subtítulo alterno en el panel de idioma con base en lo solicitado en los criterios **1**, **2** y **4** de la **HU026** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).
- Cuando el usuario seleccione una opción de subtítulo, la aplicación debe reproducir automáticamente el nuevo subtítulo.
  1. Al seleccionar alguna configuración de subtítulos el panel de opciones se debe mantener.
- El elemento seleccionado debe representarse con un arrow check.
- Si el usuario selecciona la opción "Desactivados", la aplicación debe ocultar los subtítulos activos.

### Insumos

| **Freehand**                                     | https://amcomx.invisionapp.com/freehand/DEFFocus-Televisiones-znQfUNopT |      |
| ------------------------------------------------ | ------------------------------------------------------------ | ---- |
| **Sección 2 - Definición de focos por elemento** | https://amcomx.invisionapp.com/freehand/DEFFocus-Televisiones-znQfUNopT?zoomToItems=Y2w4YzZ3Z3N1MDAxbzJlNmtmYXI4bW45Mg%3D%3D |      |
| **Sección 3 - Focos por feature**                | https://amcomx.invisionapp.com/freehand/DEFFocus-Televisiones-znQfUNopT?zoomToItems=Y2w5MzFxMXkxMDAxeDJlN3dhc2VobzZvaA%3D%3D |      |
| **Specs**                                        | https://amcomx.invisionapp.com/overview/CV_AAF_FocoComportamiento-cl9fpyxpl07vn0150ayz4eanu/screens?sortBy=3&sortOrder=1&viewLayout=2](https://amcomx.invisionapp.com/overview/CV_AAF_FocoComportamiento-cl9fpyxpl07vn0150ayz4eanu/screens?sortBy=3&sortOrder=1&viewLayout=2) |      |
| **Sección 4: Movimiento de foco**                | https://amcomx.invisionapp.com/freehand/DEFFocus-Televisiones-znQfUNopT?zoomToItems=Y2w5NXYzcmR2MDA1dTJlN3diYzcwcWczdQ%3D%3D |      |


### Action points

- [ ]   [GOOSE] Analizar
- [ ]   [JULI] Revisar Assets / Armar lista de problemas.




## Timeshift [análisis1]

*Timeshift y grabación en Player en Vivo.*

Status: **TODO[1er ANÁLISIS]** timeshift NPVR TV VIVO(Las grabaciones no caben)



Brief: https://dlatvarg.atlassian.net/browse/BRF-8819

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3631775850/BRF-8767+Todos+OTT+Roku+TV+en+vivo+-+Timeshift+y+NPVR



### HUs

#### HU001 - Canal con Timeshift: Seleccionar Evento Pasado 

- **Quiero** que se habilite la selección y reproducción de un **evento pasado** con base al rediseño y con las funcionalidades requeridas

- Cuando el usuario seleccione un evento y finalizado la aplicación debe cerrar la EPG y reproducir el programa elegido.
- Solo debe reproducir el programa siempre y cuando el usuario tenga adquirido el Canal y tenga permisos timeshift. 
- **[POR QUÉ NO PIDE ESTE?]** Si Canal del evento en cuestión no tiene permisos de timeshift, la aplicación debe mostrar una alerta indicándolo. (**HU057**)
  **Quiero** que cuando seleccione un evento pasado sin permisos de timeshift, la aplicación me alerte que el canal no se puede reproducir

  - La aplicación no debe reproducir el canal seleccionado, debe continuar con la reproducción actual.


   - La alerta se mostrará en pantalla durante 12 segundos y desaparecerá automáticamente. El tiempo debe ser configurable por región y dispositivo.

   - El estado de la alerta se representará con una etiqueta con el texto "No disponible". Misma que será configurable por región y dispositivo.

   - La alerta debe mostrar la leyenda: "En este canal no puedes ver programas ya emitidos". Este texto debe ser configurable por región y dispositivo.



- **[ESTÁ DEPRECADA APARENTEMENTE]** Si el usuario no tiene adquirido el canal seleccionado, la aplicación debe iniciar el flujo transaccional del contenido. Véase en **USER05** del BRF: [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
- Si el canal esta protegido con el control parental, la aplicación debe solicitar el PIN de seguridad para permitir la reproducción. Véase **HU053**.
  1. El diseño de la interfaz de usuario debe corresponder a los siguientes bocetos: https://amcomx.invisionapp.com/freehand/document/apAkINxfW  
  2. La modal se muestra en un overlay.
  3. La reproducción actual no se debe interrumpir.
  4. La modal debe tener la opción para regresar a la pantalla anterior (Botón Cancelar).
  5. La modal debe presentar el texto: "Ingresa tu PIN de seguridad para acceder a este canal.", mismo que debe ser configurable por región y dispositivo.
  6. La modal debe tener 6 cajas de texto para capturar el PIN deseado. La cual solo permitirá ingresar números.
  7. Cuando el usuario ingrese el PIN, los números deben visualizarse enmascarados con un punto.
  8. La modal debe contar con un botón para mostrar y/o ocultar los números del PIN.
  9. Cuando Cuando el usuario ingrese el PIN, la aplicación debe activar el botón "Desbloquear".
  10. Cuando el usuario seleccione el botón Desbloquear, la aplicación debe cerrar la modal y reproducir el canal.
  11. Además la modal debe tener la opción para recuperar el PIN parental, botón "¿Olvidaste tu PIN de Seguridad?". El texto debe ser configurable por región y dispositivo.
  12. Cuando el usuario seleccione la opción "¿Olvidaste tu PIN de Seguridad?", la aplicación debe enviar un correo con el PIN parental. Cabe mencionar que el diseño de los correos electrónicos también debe actualizarse.
  13. Al presentarse la modal para ingresar PIN de seguridad descrita en la **HU053**, **NO** se debe presentar la reproducción del canal bloqueado, tanto imagen como sonido, hasta que el usuario ingrese su PIN correctamente y presione “Siguiente“. 
  14. Al presentarse esta modal no se debe poder cambiar de canal al hacer Channel Up o Channel Down con las teclas del RCU. 




#### HU002 - Control Player: Canal sin Timeshift

- **[NO SE ENTIENDE]**
- Aplica para canales con permisos de reproducción y sin Timeshift.
- Se deben habilitar los elementos del control player, con base en lo solicitado en las historias de usuario del [BRF-8767_Todos | OTT | Roku: TV en vivo - Control Player](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3627319528/BRF-8767+Todos+OTT+Roku+TV+en+vivo+-+Control+Player): 
  1. Número del Canal (**HU006**)
  2. Logo del Canal (**HU007**)
  3. Título del contenido (**HU008**)
  4. Barra de progreso (**HU009** y **HU010**)
  5. Etiqueta AHORA (**HU011**)
  6. Botón Guía (**HU016**)
  7. Botón Idiomas (**HU017**)
  8. Botón SALIR (**HU018**)
  9. Botón MÁS OPCIONES (**HU019**)
  10. Botón MINI GUÍA (**HU020**)
- Los elementos del control player deben cumplir con las especificaciones solicitadas en los insumos de diseño.
  1. La posición de los elementos del control player no debe tener afectación al agregar u ocultar botones del control player o de la botonera de opciones.



#### HU003 - Canal sin Timeshift: Seleccionar Evento Pasado 

- Aplica para canales con permisos de reproducción y sin Timeshift.
- Cuando un canal no cuente con Timeshift:
  1. La información del **evento pasado** debe mostrarse en color gris en la EPG.
  2. Cuando el usuario seleccione un **evento pasado**, se debe visualizar una alerta sobre la EPG.
- La alerta debe tener las siguientes características:
  1. **Texto:** No disponible | En este canal no puedes ver programas ya emitidos
     1. El texto debe ser configurable por operación.
  2. La alerta debe visualizarse durante **X** segundos, cumpliendo con las siguientes especificaciones:
     1. Inicialmente se puede configurar por 12 segundos, sin embargo este valor puede cambiarse de acuerdo a la necesidad de la operación.
     2. El tiempo de visualización de la alerta debe ser configurable por operación.
- Para el criterio 2 y 3, la alerta debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU004 - Canal sin Timeshift: Seleccionar evento en vivo

- Aplica para canales con permisos de reproducción y sin Timeshift.
- Cuando el usuario intente retroceder o avanzar la reproducción de un **evento en vivo** desde la barra de progreso:
  1. La reproducción del evento debe continuar, es decir, no se debe adelantar o retrasar.
  2. Se debe visualizar una alerta sobre la EPG.
- La alerta debe tener las siguientes características:
  1. **Texto:** Acción no disponible | Este canal no permite grabar ni avanzar o retroceder la reproducción
     1. El texto debe ser configurable por operación.
  2. La alerta debe visualizarse durante **X** segundos, cumpliendo con las siguientes especificaciones:
     1. Inicialmente se puede configurar por 12 segundos, sin embargo este valor puede cambiarse de acuerdo a la necesidad de la operación.
     2. El tiempo de visualización de la alerta debe ser configurable por operación.
- Para el criterio 3, la alerta debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU005 - Grabación de Evento Pasado 

- **[NO APLICA]**

#### HU006 - Grabación de Evento Pasado no disponible

- **[NO APLICA]**

#### HU007 - Grabación de Evento en vivo

- **[NO APLICA]**

#### HU008 - Grabación de Evento Futuro

- **[NO APLICA]**

#### HU009 - Eliminar grabación 

- **[NO APLICA]**

#### HU010 - Grabación de serie

- **[NO APLICA]**

#### HU011 - Opciones: Grabación de serie

- **[NO APLICA]**

#### HU012 - Grabación de serie completa

- **[NO APLICA]**

#### HU013 -  Grabación de serie desde episodio en vivo

- **[NO APLICA]**

#### HU014 -  Grabación de serie desde episodio pasado

- **[NO APLICA]**

#### HU015 -  Grabación de serie desde episodio futuro

- **[NO APLICA]**

#### HU016 - Grabación de episodio

- **[NO APLICA]**

#### HU017 - Grabación de episodio en vivo

- **[NO APLICA]**

#### HU018 - Grabación de episodio pasado

- **[NO APLICA]**

#### HU019 - Grabación de episodio futuro

- **[NO APLICA]**

#### HU020 - Espacio de almacenamiento insuficiente

- **[NO APLICA]**

#### HU021 - Panel de opciones: Cancelar grabación 

- **[NO APLICA]**

#### HU022 - Opciones: Cancelar grabación de serie

- **[NO APLICA]**

#### HU023 - Cancelar grabación de serie

- **[NO APLICA]**

#### HU024 - Cancelar grabación de serie desde episodio en vivo

- **[NO APLICA]**

#### HU025 - Cancelar grabación de serie desde episodio pasado

- **[NO APLICA]**

#### HU026 - Cancelar grabación de serie desde episodio futuro

- **[NO APLICA]**

#### HU027 - Cancelar grabación de episodio

- **[NO APLICA]**

#### HU028 - Cancelar grabación de episodio en vivo

- **[NO APLICA]**

#### HU029 - Cancelar grabación de episodio pasado

- **[NO APLICA]**

#### HU030 - Cancelar grabación de episodio futuro

- **[NO APLICA]**

#### HU031 - Insumos de diseño: TV en vivo




### Action points

- [ ]     [GOOSE] Analizar
- [ ]     [JULI] Revisar Assets / Armar lista de problemas.
- [ ]     [NACHO] Documentación de cómo funciona TimeShift (??)
- [ ]    [NACHO] Cómo sé que un canal tiene TimeShift (??)




## Epg: full y mini [análisis1]

*Funcionalidades y diseños en EPG full y mini.*

Status: **[1er ANÁLISIS]**



https://dlatvarg.atlassian.net/browse/BRF-8818

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3632070657/BRF-8818+Todos+OTT+Roku+TV+en+vivo+-+Mini+EPG+y+Full+EPG



### HUs

#### HU001 - Mini EPG

- Cuando el usuario seleccione el nodo de TV en vivo, se debe mostrar:
  1. El player con la reproducción del evento, aplicando las reglas del canal definidas en el primer MVP.
  2. La **Mini EPG** encendida durante **X** segundos, cumpliendo con las siguientes especificaciones:
     1. Inicialmente se puede configurar por 12 segundos, sin embargo este valor puede cambiarse de acuerdo a la necesidad de la operación.
     2. El tiempo de visualización de la **Mini EPG** debe ser configurable por operación.
     3. Si el usuario no realiza alguna acción, la **Mini EPG** debe desaparecer automáticamente.
     4. Si la **Mini EPG** está visible y el usuario selecciona el botón *Back* del control remoto, la aplicación debe ocultarla.
     5. La **Mini EPG** debe estar compuesta con los elementos indicados en la **HU005**.
  3. El mensaje de opciones visible, cumpliendo con las siguientes especificaciones:
     1. Texto: Presiona * para más opciones
        - El texto debe ser configurable por operación.
     2. Cuando el usuario oprima el botón * (asterisco) del control remoto, la plataforma debe comunicar las opciones disponibles.



#### HU002 - Mini EPG: Elementos

- Se deben habilitar los elementos de la Mini EPG con base en lo solicitado en las historias de usuario del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644):

  ##### Información del Canal (**HU060**)

  1. El número de canal debe visualizarse al extremo izquierdo de la Miní Guía.
  2. El logo del canal debe mostrarse al lado derecho del número de canal.
  3. El número y logo del canal deben corresponder a los eventos del lado derecho y viceversa, los eventos deben corresponder al canal.

  ##### Información eventos (**HU061**)

  1. La Miní EPG mostrará dos eventos por canal
  2. El póster debe corresponder al programa.
  3. La longitud máxima soportada para los textos dependerá del dispositivo, cuando este supere dicha longitud, se usaran puntos suspensivos (...). Véase **HU065**.
  4. El horario debe corresponder al programa. Y de indicar el tiempo de inicio y el tiempo de fin.
  5. El horario se debe representar en formato de horas y minutos. Ejemplo: 15.:30 -17:00
  6. La Etiqueta de estado va a depender del momento en el que esté el evento, pasado, presente o futuro, según corresponda: "Ya emitido" (color naranja), "Ahora"(color rosa), "Más tarde"(color verde). Los textos y color de etiqueta deben ser configurables por región y dispositivo.
  7. El icono de Recording solo debe mostrarse cuando el canal tiene permisos de Timeshift y NPVR.
  8. La Barra de Progreso solo deberá mostrarse para eventos en vivo (presentes).
  9. En la barra de progreso, el tiempo total de reproducción que dura el programa se debe representar con una barra de color gris claro.
  10. En la barra de progreso, el tiempo de reproducción transcurrido del programa, en tiempo real, se debe representar con una barra de color rojo.

  ##### Botones de navegación (**HU062**)

  1. La navegación hacía abajo debe mostrar los eventos del siguiente canal de la lista.
  2. La navegación hacia arriba debe mostrar los eventos del canal anterior de la lista.
  3. **[RECHAZAR]** La navegación hacia la derecha debe mostrar los siguientes eventos del canal.
  4. **[RECHAZAR]** La navegación hacia la izquierda debe mostrar los eventos anteriores del canal.
  5. **[RECHAZAR]** La aplicación debe mostrar los botones de navegación en los extremos de la Miní EPG, 
  6. Los botones de navegación (arriba, abajo, izquierda, derecha) se representarán con iconos en forma de chevrones, cada uno el en sentido respectivo. 
  7. La navegación también podrá realizarse con los botones del control remoto: Arrow Left Button, Arrow Right Button, Down Button, Up Button



#### HU003 - Mini EPG: Metadata del evento no disponible

1. Se deberá mostrar la leyenda “No disponible“ en los eventos que no se logre obtener su información.

Para la Mini EPG:

En el nombre del evento se deberá incluir el texto “No disponible“

- Horario: No se debe mostrar en caso de que no se pueda obtener.
- Progress bar: No se debe mostrar en caso de que no se pueda obtener.
- Shortcut de más opciones no se debe mostrar.

<img src="img/mini-01.png" alt="mini-01" style="zoom:50%;" />

Para la Full EPG:

La metadata del evento se debe de mostrar de la siguiente forma:

- Título del evento: “No disponible“
- Horario de transmisión: No se debe mostrar en caso de que no se pueda obtener.
- Duración: No se debe mostrar en caso de que no se pueda obtener.
- Sinopsis: No se debe mostrar en caso de que no se pueda obtener.
- No se debe visualizar una etiqueta adicional en caso de no obtenerse la metadatada de los 3 elementos mencionados arriba.

En grilla:

- Título del evento: “No disponible“
- Horario de transmisión. No se debe mostrar en caso de que no se pueda obtener.
- Shortcut de más opciones no se debe mostrar.



Resto de dispositivos:

- Se debe ocultar el horario y el botón contextual para sacar el panel más opciones (botón de 3 puntos) en la EPG.
- Se debe ocultar la Progress Bar, los botones “Pause/Play”, “Avanzar”, “Retroceder“, “Idioma“, “Grabar“ y “Opciones“.

<img src="img/epg-01.png" alt="epg-01" style="zoom:50%;" />



#### HU004 - Mini EPG: Categorías en la metadata del evento 

- **[REVISAR]** **Quiero** que en la metadata del evento se visualice la categoría asociada de acuerdo a la conversión de categorías definida en el rediseño de la plataforma

1. La aplicación debe continuar con la reproducción del contenido.
2. La modal debe presentar el título "Filtrar". Dicho texto debe ser configurable por región.
3. Las opciones de filtros (grupos) se representará con un botón cada una. El texto de cada botón será "Categorías", "Canales" y "Canales favoritos". Los textos deben ser configurables por región.
4. Para Coship-9085 y STV se deben mostrar las opciones Salir y Regresar como shortcuts, en la parte inferior de la pantalla. El shortcut Salir corresponderá al botón verde del control remoto y deberá tener la leyenda "SALIR". El shortcut Regresar corresponderá al botón Amarillo y al botón back del control remoto; deberá tener la leyenda "REGRESAR". Los textos de los shortcuts deben ser configurables por región. Estos elementos debes ser focuseables.
5. La opción Salir debe cerrar la pantalla y presentar el player.
6. La opción Regresar debe cerrar la pantalla y presentar la Full EPG.



#### HU005 - Mini EPG: Selección de un evento

1. **[REVISAR] El HU034 no corresponde con el título de esta historia de usuario**
2. Se debe habilitar la selección de un evento en la Mini EPG con base en lo solicitado en la historia de usuario **HU034** del [BRF-7520_Todos | OTT, IPTV | Rediseño TV en Vivo: Evolutivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3347284441).
3. Para el dispositivo Roku la selección de un evento debe realizarse con el botón *OK* del control remoto.



#### HU006 - Mini EPG: Más opciones

1. **[REFORMULAR]** generar wireframe de propuesta.
2. Cuando el usuario oprima el botón * (asterisco) del control remoto, la plataforma debe comunicar las opciones disponibles.
3. Se debe presentar una pantalla con los siguientes elementos:
   1. **Texto:** Opciones
   2. **Botón Opciones de programa**
      1. Cuando el usuario seleccione esta opción se debe mostrar el panel de opciones de programa (**HU018**).
   3. **Botón Guía completa**
      1. Cuando el usuario seleccione esta opción, se debe cerrar la pantalla y presentar el player con la Full EPG visible (**HU009**).
   4. **Botón Mostrar reproductor**
      1. Cuando el usuario seleccione esta opción, se debe cerrar la pantalla y presentar el player con el Control Player visible (**HU019**).
4. Cuando el usuario oprima el botón *Back* del control remoto, se debe cerrar la pantalla y presentar el player con la Mini EPG encendida.



#### HU007 - Mini EPG: Evento sin imagen

- Cuando un evento no tenga una imagen disponible, se debe visualizar en la Mini EPG el logo del canal al que pertenece el evento.



#### HU008 - Mini EPG: Reglas cuando falla la carga de la Mini EPG

- **[NO TIENE SENTIDO]**
- Esta historia de usuario sucede cuando el servicio que muestra la guía de programación falla.
- Se debe configurar un N número de intentos para la carga de la guía de programación.
  1. El número de intentos debe ser configurable por operación.
- Cuando el usuario seleccione una opción en la plataforma que lo dirija a TV en Vivo, se debe validar lo siguiente:
  1. Si el usuario ha visualizado con anterioridad un canal de TV en vivo con permisos de reproducción, se debe mostrar el player. **HU0138**
  2. Si el usuario ha visualizado con anterioridad un canal de TV en vivo pero no tiene permisos de reproducción, se le debe dirigir a la pantalla de la oferta del canal correspondiente. **HU131**
  3. Si el usuario no ha visualizado con anterioridad un canal de TV en vivo, la plataforma debe mostrar una alerta. **HU0139**
     - Si el usuario no ha visualizado con anterioridad un canal de TV en vivo, la plataforma debe dirigirlo al canal
       en la EPG que la operación tenga configurado por default.
- Se cancela **HU082**.



#### HU009 - Mini EPG: Player sin carga de la Mini EPG

- **[REPETIDO?]**
- Cuando no se puede cargar la Mini EPG después del número de intentos configurados para la operación, se debe visualizar:
  1. El player con la reproducción del evento que se esté transmitiendo en ese momento del último canal visualizado.
  2. La Mini EPG con los siguientes elementos:
     1. Número del canal
     2. Placeholder de canal en sustitución del logo del canal.
     3. Placeholder de evento en sustitución de la imagen de cada uno de los eventos.
     4. Texto ***No disponible*** en sustitución del título de cada uno de los eventos.



#### HU010 - Full EPG

- **[NO COINCIDE!]**
- Se debe habilitar la **Full EPG** con base en lo solicitado en las **HU008** y **HU009** del [BRF-8435_Todos | OTT | Roku: MVP con funcionalidades de TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3583739083/BRF-8435+Todos+OTT+Roku+MVP+con+funcionalidades+de+TV+en+vivo).

##### HU008 - Control Player: Barra de Progreso (Progress Bar)

##### HU009 - Control Player: Botón Retroceder (Rewind)



#### HU011 - Full EPG: Metadata del evento

1. **[REVISAR] FULL EPG no modificable.**
2. Se debe visualizar la **metadata del evento** en la **Full EPG** con base en lo solicitado en:
   1. La **HU029** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).
   2. La **HU002**, **HU003**, **HU004** y **HU053** del [BRF-7520_Todos | OTT, IPTV | Rediseño TV en Vivo: Evolutivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3347284441).
3. La **metadata del evento** debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU012 - Full EPG: Metadata del evento no disponible

- **[REPETIDO]**



#### HU013 - Full EPG: Agregar posters a los eventos

- **[REVISAR]**

##### HU011 - Control Player: Botón Pause/Play

1. Aplica para todos los dispositivos.
2. Los posters deben posicionarse del lado izquierdo de cada evento (rejilla), donde:
   1. **No** se debe incluir el poster en los eventos con duración menor a media hora.
   2. Los eventos con duración igual o mayor a media hora **si** deben incluir poster.
3. En caso de que no se tenga el poster del evento, se debe mostrar el logo del canal.
4. Se comparte imagen de referencia:

<img src="img/epg-2.png" alt="epg-2" style="zoom: 33%;" />





#### HU014 - Full EPG: Agregar reloj en la línea del tiempo

- **[REVISAR]**

- Aplica para todos los dispositivos.
- Se debe agregar un reloj en la parte superior de la barra de tiempo de la full EPG.
- El reloj debe visualizarse en formato de 24 horas.
  - El formato debe ser **hh.mm** con el texto adicional **hs.** sin espacio.
    Ejemplo: **15.50hs.**
- El reloj debe cumplir con el mismo color code de la etiqueta AHORA.
- El reloj debe indicar la hora actual.
- El reloj no modifica la funcionalidad actual de la barra de tiempo.



#### HU015 - Full EPG: Selección de un evento

1. Aplica para todos los dispositivos.

2. Si el usuario no tiene derechos de reproducción se debe visualizar la pantalla de Oferta de Contratación de canal correspondiente.

3. Si el usuario tiene derechos de reproducción, entonces:

   1. ***Evento presente***

      1. Si el usuario está visualizando un evento en vivo y selecciona en la mini o full EPG ese mismo evento, se debe abrir el panel de opciones del programa con la información del evento seleccionado.
      2. Cuando el usuario esté visualizando un evento pasado y selecciona un programa en vivo, se debe mostrar la reproducción del evento seleccionado.

   2. ***Evento futuro***

      1. Si el usuario selecciona en la mini o full EPG un evento futuro se debe abrir el panel de opciones del programa como se describe en la **HU0020**.

      <img src="img/epg-opciones-1673358570429-6.png" alt="epg-opciones" style="zoom:50%;" />

   3. ***Evento pasado***

      1. Cuando el usuario seleccione en la mini o full EPG un evento pasado:
         - Y tiene permisos de timeshift el canal, se debe reproducir el evento seleccionado.
         - Y el canal no tiene timeshift, se debe mostrar el panel de más opciones con la información del evento seleccionado.
         - Que en ese momento está visualizando, se debe mostrar el panel de opciones con la información del evento seleccionado.



#### HU016 - Full EPG: Más opciones

- **[DÓNDE ESTÁN LAS PANTALLAS?]**
- Cuando el usuario oprima el botón * (asterisco) del control remoto, la plataforma debe comunicar las opciones disponibles.
- Se debe presentar una pantalla con los siguientes elementos:
  1. **Texto:** Opciones
  2. **Botón Opciones del canal**
     1. Cuando el usuario seleccione esta opción se debe mostrar el panel de opciones del programa y canal.
  3. **Texto: Filtrar**
  4. **Botón Categorías**
     1. Cuando el usuario seleccione esta opción, se debe mostrar la pantalla de categorías (**HU015**).
  5. **Botón Canales**
     1. Cuando el usuario seleccione esta opción, se debe mostrar la pantalla de canales (**HU016**).
  6. **Botón Canales favoritos**
     1. Cuando el usuario seleccione esta opción, la Full EPG se debe actualizar (**HU017**).
- Cuando el usuario oprima el botón *Back* del control remoto, se debe cerrar la pantalla y presentar el player con la Full EPG visible.
- La pantalla de **más opciones** debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU017 - Full EPG: Filtro por Categorías

- Cuando el usuario seleccione el filtro de **Categorías** se debe visualizar la pantalla de categorías on base a lo solicitado en las **HU034** y **HU035** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).



##### HU034 - Guía de Programación: "Categorías"

**Quiero** que que cuando seleccione el botón "Categorías" de la EPG, la aplicación muestre una grilla de todas  las categorías de los canales disponibles para la región, en donde cada categoría estará representado por un póster

1. La aplicación debe cerrar la EPG, cuando el usuario seleccione el filtro.
2. La aplicación debe continuar con la reproducción del contenido.
3. La pantalla debe presentar el título "Selecciona una categoría", en la parte superior de la pantalla. Dicho texto debe ser configurable.
4. La pantalla debe presentar todos los categorías disponibles en la región. Mismas que serán configurables por región.
5. Cada elemento de la grilla debe ser un póster que representa a la categoría. El póster debe ser configurable.
6. Cada elemento de la grilla debe ser seleccionable (**HU035**). 
7. La pantalla debe tener las opciones de Salir y Regresar.
8. La opción Salir/Cerrar debe cerrar la pantalla y presentar el player.
9. **[WHATS? 3 flujos de salida?]** La opción Regresar debe cerrar la pantalla y presentar la Full EPG.



##### HU035 - Guía de Programación: Seleccionar categoría

**[WHATS? 2 EPG Distintas? y filtrada?]**

1. La aplicación debe cerrar la grilla de categorías y mostrar la Full EPG con el filtro aplicado.
2. La aplicación debe continuar con la reproducción del programa actual.
3. La EPG solo debe mostrar los canales relacionados a la categoría seleccionada. 
4. En la EPG se debe mostrar una etiqueta con el texto correspondiente a la categoría, ejemplo: "DEPORTES"



- Cuando el usuario oprima el botón *Back* del control remoto, se debe cerrar la pantalla de categorías.
- La pantalla de categorías debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU018 - Full EPG: Filtro por Canales

1. Cuando el usuario seleccione el filtro de **Canales** se debe visualizar el mosaico de canales on base a lo solicitado en las **HU010** y **HU011** del [BRF-8435_Todos | OTT | Roku: MVP con funcionalidades de TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3583739083/BRF-8435+Todos+OTT+Roku+MVP+con+funcionalidades+de+TV+en+vivo).



##### HU010 - Acceso a mosaico de canales

**Quiero** que el usuario pueda acceder al mosaico de canales desde las teclas de su control remoto



1. Para acceder al mosaico de canales el usuario debe estar visualizando un canal y seleccionar la tecla ***Up*** del control remoto. **HU011**



##### HU011 - Mosaico de canales

**Quiero** que se muestren los elementos del mosaico de canales con base al rediseño de la plataforma



1. El mosaico de canales debe cumplir con los siguientes criterios *con base al rediseño de la plataforma*:
   1. Se debe visualizar sobre el player del canal.
   2. La reproducción debe mantenerse en background.
   3. Debe contener los siguientes elementos:
      1. Título: Canales
      2. Deben listarse los canales en orden ascendente de izquierda a derecha de acuerdo al número de canal en la EPG.
      3. Por cada canal:
         - Número de canal
         - Logo de canal
         - Si el canal está bloqueado debe visualizarse el icono del Candado cerrado.
2. El mosaico de canales debe ocultarse cuando el usuario:
   1. Selecciona la tecla Back del control remoto ó
   2. Selecciona un canal.





1. Cuando el usuario oprima el botón *Back* del control remoto, se debe cerrar el mosaico de canales.
2. El mosaico de canales debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU019 - Full EPG: Filtro por Canales favoritos

- **Quiero** que se actualice la Full EPG cuando el usuario seleccione el filtro de **Canales favoritos**



##### HU036 - Guía de Programación: "Favoritos"

- La aplicación debe actualizar la Full EPG y mostrar únicamente la lista de canales favoritos del usuario.
- La aplicación debe continuar con la reproducción del programa actual.
- En la EPG se debe mostrar una etiqueta con el texto "FAVORITOS", misma que deber ser configurable por región



##### HU017 - Usuario sin canales favoritos

1. Si el usuario selecciona el shortcut o botón que se encuentra en la EPG para seleccionar los canales favoritos, se debe mostrar una alerta.
2. El texto de la alerta es: Aún no tienes canales favoritos | Agrega algunos desde las opciones de cada canal.
   1. El texto debe ser configurable por operación.
3. La alerta se debe mostrar sobre la EPG.
4. El tiempo en pantalla de la alerta debe ser igual que el establecido actualmente para las alertas de TV en vivo.
5. Se adjunta la siguiente imagen para mayor referencia:

<img src="img/sin-canal-favorito.png" alt="sin-canal-favorito" style="zoom:33%;" />





#### HU020 - Panel de opciones del programa y canal

1. Se debe habilitar el **panel de opciones del programa y canal** con los siguientes elementos:
   1. Metadata del evento (**HU021**)
   2. Opciones del Programa
      1. Grabar Programa (**HU022**)
      2. Programar Recordatorio (**HU023**)
   3. Opciones del Canal 
      1. Añadir Canal a favoritos (**HU024**)
      2. Cambiar Idioma del Canal (**HU025**)
      3. Bloquear Canal (**HU026**)
2. El panel de opciones del programa y canal debe cumplir con las especificaciones solicitadas en los insumos de diseño.



#### HU021 - Panel de opciones del programa y canal: Metadata del evento 

- **Quiero** que se visualice la **metadata del evento** en el panel de opciones del programa y canal



##### HU039 - Panel "Opciones de Programa y de Canal": Metadata

1. En orden de arriba hacia abajo se visualizará: 1) el título del programa, 2) la etiqueta de estado, 3) El horario de transmisión, 4) la duración del programa y 5) la sinopsis del programa.
2. La etiqueta de estado va a depender del momento en el que esté el evento, pasado, presente o futuro, según corresponda: "Ya emitido", "Ahora", "Más tarde". Los textos deben ser configurables por región y dispositivo.
3. Además la etiqueta de estado debe tener un color definido para cada tipo de evento, pasado, presente y futuro, según corresponda: Naranja, Rosa, Verde. Los colores deben ser configurables por región y dispositivo.
4. El horario de transmisión se presentará en el formato horas y minutos, ejemplo 16.00hs a 17.30hs
5. La duración del programa se presentará en el formato horas y minutos, ejemplo: **1h** **30min.**
6. El texto correspondiente a la sinopsis del programa debe mostrarse en formato alineado a la izquierda.
7. El número y logo del canal se debe mostrar en la sección derecha.
8. Para conocer la longitud máxima soportada de los textos véase **HU065** 



#### HU022 - Opciones del programa: Grabar Programa

- **[NO APLICA]**



#### HU023 - Opciones del programa: Programar Recordatorio

- **Quiero** que se habilite la opción **programar recordatorio** en el panel de opciones del programa y canal



##### HU047 - Pantalla Programar Recordatorio

- El diseño de la interfaz de usuario debe corresponder a los siguientes bocetos: https://amcomx.invisionapp.com/freehand/document/apAkINxfW 

- Cuando el usuario seleccione la opción de "Programar Recordatorio", la aplicación debe mostrar una confirmación indicando que el programa ha sido agregado a la lista de recordatorios.
- El mensaje de confirmación se mostrará encima del player durante 12 segundos y desaparecerá automáticamente. El tiempo debe ser configurable por región y dispositivo.
-  El estado de la confirmación se representará con un icono y una etiqueta con el texto "Recordatorio programado". Misma que será configurable por región y dispositivo.
- La confirmación debe mostrar la leyenda: "Te enviaremos una notificación cuando el programa comience". Este texto debe ser configurable por región y dispositivo.
- Una vez que el programa este dentro de la lista de recordatorios, 10 minutos antes del horario de comienzo de la transmisión de este, se le debe mostrar al usuario una alerta de recordatorio sobre la pagina o player en que se encuentre. Véase **HU067**.
- Cuando el usuario active el recordatorio para un programa, la opción del programa debe cambiar a "Quitar recordatorio". Y viceversa. 



#### HU024 - Opciones del programa: Añadir Canal a favoritos

- Cuando el usuario seleccione la opción "Añadir Canal a favoritos", la aplicación debe mostrar, sobre el player, una alerta de que el contenido ha sido agregado a favoritos.
- La alerta, del punto anterior, se mostrará encima del player durante 12 segundos y desaparecerá automáticamente. El tiempo debe ser configurable por región y dispositivo. 
- La alerta, del punto anterior, mostrará el estado del proceso y se representará con un icono y una etiqueta con el texto "Agregado a favoritos". Ambos elementos deben ser configurables por región y dispositivo.
- La alerta, del punto anterior, debe mostrar la leyenda: "El canal fue agregado a la sección "Mis contenidos.". Este texto debe ser configurable por región y dispositivo. 
- Para WEB, iOS, WIN10 y ADR, el texto "Mis contenidos" será un link hacia la página "Mis contenidos". Dicho link será seleccionable por el usuario. 
- Para Coship-9085 y STV, el texto "Mis contenidos" estará precedido de un shortcut (botón verde), el cual será focuseable. Cuando el usuario seleccione el botón desde el control remoto, la aplicación debe mostrar la página "Mis contenidos". 
- Para ATV4 se le debe indicar al usuario la funcionalidad Press&Hold para ir a la página "Mis contenidos".



#### HU025 - Opciones del programa: Cambiar Idioma del Canal

1. Se debe habilitar el panel de idiomas con las siguientes características:
   1. Cuando se visualice el panel de idioma la reproducción del contenido **no** se debe pausar.
   2. El panel de idioma debe dividirse en dos secciones: 
      1. Panel Izquierdo: que corresponderá a la metadata del contenido (**HU023**)
      2. Panel Derecho: que corresponderá a las opciones de audio y subtítulos (**HU024**, **HU025**, **HU026** y **HU027**)
   3. Cuando el usuario oprima el botón *Back* del control remoto se debe cerrar el Panel Idioma.



##### HU023 - Panel de Idiomas: Metadata

- Se debe visualizar la metadata del contenido en el panel de idioma con base en lo solicitado en la **HU022** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644)  y en la historia de usuario mencionada en la misma.

##### HU024 - Panel de Idiomas: Opciones de audio alterno

- Se deben habilitar las opciones de audio alterno en el panel de idiomas con base en lo solicitado en la **HU023** 

##### HU025 - Panel de Idiomas: Seleccionar audio alterno

- Se debe habilitar la selección de audio alterno en el panel de idioma con base en lo solicitado en los criterios **1** y **2** de la **HU025** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).

##### HU026 - Panel de Idiomas: Opciones de subtítulos alternos

- Se deben habilitar las opciones de subtítulos alternos en el panel de idiomas con base en lo solicitado en la **HU024** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644) y en la historia de usuario mencionada en la misma.



#### HU026 - Opciones del programa: Bloquear / Desbloquear Canal 

1. Se debe habilitar la opción **bloquear / desbloquear canal** en el panel de opciones del programa y canal con base en lo solicitado en las **HU050**, **HU052**, **HU124** y **HU124.1** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644).  



##### HU050 - Panel Opciones del Canal: Bloquear Canal

1. Si el usuario tiene activo el PIN parental la aplicación debe bloquear el canal y mostrar una notificación de que ha realizado la acción. (**HU052**)
2. En la EPG, un canal bloqueado se mostrará con un overlay, además de agregar un icono de bloqueo.
3. Si el usuario NO tiene activo el PIN parental la aplicación debe mostrar una Modal para crear el PIN de seguridad. (**HU051**)

##### HU052 - PIN de seguridad: Alerta de Canal bloqueado

1. La alerta se mostrará encima del player durante 12 segundos y desaparecerá automáticamente. El tiempo debe ser configurable por región y dispositivo.
2. El estado del proceso se representará con un icono y una etiqueta con el texto "Canal bloqueado". Ambos elementos deben ser configurables por región y dispositivo.
3. La alerta debe mostrar la leyenda: "Para ver este canal necesitarás tu PIN de seguridad. Donde el texto debe ser configurable por región y dispositivo.

##### HU124 - Bloquear/Desbloquear canal con PIN de seguridad

1. Únicamente el perfil administrador debe visualizar las opciones “Bloquear canal” y “Desbloquear canal” en el menú de Opciones de Canal de TV en Vivo.
2. El texto de la opción debe ser dinámico, donde:
   1. Cuando el usuario selecciona Bloquear canal, el texto cambia a Desbloquear canal.
   2. Cuando el usuario selecciona Desbloquear canal, el texto cambia a Bloquear canal,
3. Para Bloquear y Desbloquear un canal la plataforma debe solicitar el PIN de seguridad. **HU053**
   1. En caso de no tener creado el PIN de seguridad la plataforma debe solicitar la creación del PIN en ese momento. Ver **HU124.2**
4. Se anexa imagen de ejemplo en AAF.

##### HU124.1 - Bloqueo de canal en visualización

1. Si el usuario realiza el bloqueo del canal que está reproduciendo en ese momento, entonces la plataforma debe:
   1. Dirigirlo al siguiente canal disponible que pueda visualizar.
   2. Mostrar la alerta de canal bloqueado descrito en la **HU052**



#### HU027 - Comunicación de canal no contratado

1. Cuando el usuario no tenga derechos de reproducción del canal se visualice la pantalla de notificación solicitada en la **HU131** del [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644). 



##### HU131 - Tipos de oferta de canal

1. Cuando el usuario navega en la EPG y selecciona un canal que no tiene contratado se debe evaluar lo siguiente:
   1. Si el canal pertenece a un add on al cual el usuario no se encuentra suscrito entonces se debe presentar la pantalla descrita en la **HU087**
   2. Si el canal está incluido en uno o más paquetes de canales entonces se debe presentar la pantalla descrita en la **HU127**
   3.  Si el canal no se puede contratar desde la plataforma entonces se debe presentar la pantalla descrita en las **HU089**, **HU129, HU130,** según el dispositivo que aplique.



#### HU028 - Insumos de diseño: TV en vivo






### Action points

- [ ]    [GOOSE] Analizar
- [ ]    [JULI] Revisar Assets / Armar lista de problemas.
- [ ]    [GOOSE] Armar wireframe de propuesta (creo que ya estaba hecha, en algún lado)
- [ ]    [NACHO] Apis del manejo de categorías de los canales... cuál es? tiene imágenes?




## vCard [análisis1]

*Evolución de vCard.*

Status: **TODO[1er ANÁLISIS]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-8684

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3627548880/BRF-8684+Todos+OTT+Roku+Vcard



### HUs

#### HU001 - Vcards

- Cuando el usuario seleccione un contenido debe visualizar la Vcard correspondiente al tipo de contenido y suscripción, donde los tipos pueden ser:



##### Usuario suscrito a Claro Video (USER03)

Cuando el usuario suscrito seleccione un contenido de Claro Video que desea visualizar, la plataforma debe presentar la VCard de visualización.

- Si el contenido que el usuario desea visualizar es una serie, entonces el flujo continúa en **USER11**.
- Elementos y estructura de la Vcard
  - Imagen del contenido.
  - Primer renglón: Nombre
  - Segundo renglón: Nombre original, Género(s), Año de publicación, Categoría y Duración
  - Tercer renglón: Sinopsis
  - Botones
  - Carrusel de recomendaciones
- Los botones disponibles para los usuarios suscritos a Claro Video deben ser:
  - ***Reproducir*** (USER04)
  - ***Ver trailer*** (USER05)
  - ***Agregar a mi lista*** (USER06)
  - ***Compartir*** (USER07)

##### Usuario no suscrito a Claro Video (USER08)

- **[RECHAZAR]**

##### Usuario que desea alquilar o comprar contenido (USER09)

- El modelo de negocio depende de lo establecido en cada operación.
- Cuando el usuario seleccione un contenido de renta o compra de un Add on, la plataforma debe presentar la VCard de alquiler de contenido.
- Elementos y estructura de la Vcard
  - Primer renglón: Nombre
  - Segundo renglón: Nombre original, Género(s), Año de publicación, Categoría y Duración
  - Tercer renglón: Sinopsis
  - Botones
- Los botones disponibles para los usuarios que desean alquilar un contenido deben ser:
  - ***Alquilar película 48 h***
  - ***Comprar película***
  - ***Ver trailer*** (USER05)
  - ***Agregar a mi lista*** (USER06)
  - ***Compartir*** (USER07)
- El botón ***Alquilar película 48 h*** debe cumplir con las siguientes características:
  - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - El texto del botón debe ser personalizable de acuerdo al costo del alquiler del contenido.
- El botón ***Comprar película*** debe cumplir con las siguientes características:
  - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - El texto del botón debe ser personalizable de acuerdo al costo de compra del contenido.
- Realizar Transacción
  - Si el administrador tiene PIN de seguridad activado entonces la plataforma debe solicitar la captura del PIN para continuar con la transacción.
    - Esta opción se detalla en la historia de usuario **USER08** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - Una vez validado el PIN de seguridad el flujo continúa en la transacción
    - Esta opción se detalla en la historia de usuario **USER02** del [BRF-5435_Rediseño Transacciones](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2327117866) 
- Si el dispositivo del usuario no permite realizar el alquiler o compra del contenido entonces:
  - Se debe agregar una leyenda que indique que la transacción la puede realizar desde el sitio web de Claro Video o en otro dispositivo. [Boceto pendiente].
  - El botón ***Alquilar película 24 h*** y debe ocultarse y los demás botones deben alinearse al margen izquierdo de la Vcard.
- Cuando el usuario realiza el alquiler/compra, la plataforma debe direccionar al usuario a la Vcard de reproducción de contenido (USER04).

<img src="img/vcard-01.png" alt="vcard-01" style="zoom:50%;" />



##### Usuario no suscrito a un add on (USER10)

- El modelo de negocio depende de lo establecido en cada operación.
- Cuando el usuario seleccione el contenido de un add on al cual no está suscrito, la plataforma debe presentar la VCard correspondiente.
- Elementos y estructura de la Vcard
  - Primer renglón: Nombre
  - Segundo renglón: Nombre original, Género(s), Año de publicación, Categoría y Duración
  - Tercer renglón: Sinopsis
  - Cuarto renglón: Texto comercial
    - El texto comercial debe ser configurable por operación.
  - Botones
  - Carrusel de recomendaciones
    - Debe incluir únicamente contenido del mismo Add on.
- Los botones disponibles para los usuarios que desean alquilar un contenido deben ser:
  - ***Ver con [add on] ¿Qué incluye esta suscripción?***”
  - ***Suscribirme a [add on]***
  - ***Comprar***
  - ***Ver trailer*** (Ver USER05)
  - ***Agregar a mi lista*** (Ver USER06)
  - ***Compartir*** (Ver USER07)
- El botón ***Ver con [add on] ¿Qué incluye esta suscripción?*** debe cumplir con las siguientes características:
  - Las características del botón se detallan en la historia de usuario USR17 [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - El texto del botón debe ser configurable dependiendo del add on que el usuario desea contratar.
  - Esta opción se detalla en la historia de usuario **USER01** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
- El botón ***Suscribirme a [add on]*** debe cumplir con las siguientes características:
  - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - El texto interno del botón debe ser configurable dependiendo del costo de suscripción al add on.
  - El texto externo del botón debe ser configurable dependiendo del add on a suscribirse.
  - La descripción inferior del botón debe ser personalizable de acuerdo al add on que el usuario desea contratar.
- El botón ***Comprar película*** debe cumplir con las siguientes características:
  - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - El texto del botón debe ser personalizable de acuerdo al costo de compra del contenido.
- Realizar Transacción
  - Si el administrador tiene PIN de seguridad activado entonces la plataforma debe solicitar la captura del PIN para continuar con la transacción.
    - Esta opción se detalla en la historia de usuario **USER08** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - Una vez validado el PIN de seguridad el flujo continúa en la transacción
    - Esta opción se detalla en la historia de usuario **USER02** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
- Si el dispositivo del usuario no permite realizar la compra de suscripción entonces:
  - Se debe agregar una leyenda que indique que la compra se puede realizar desde el sitio web de Claro Video o en otro dispositivo. [Texto pendiente].
  - Los botones ***Ver con [add on] ¿Qué incluye esta suscripción?*** y ***Suscribirme a [add on]*** deben ocultarse y los demás botones deben alinearse al margen izquierdo de la vcard.
- Cuando el usuario realiza la suscripción, la plataforma debe direccionar al usuario a la Vcard de reproducción de contenido (USER04).

<img src="img/vcard-02-1673289247872-3.png" alt="vcard-02" style="zoom:50%;" />

##### Reproducción de series (USER11)

Cuando el usuario seleccione un contenido por temporadas, entonces en la plataforma:

- Elementos y estructura de la Vcard
  - Imagen del contenido
  - Primer renglón: Nombre
  - Segundo renglón: Nombre original, Género(s), Año de publicación, Categoría y Duración
  - Tercer renglón: Temporada [número de temporada], Episodio:[Nombre del episodio]
  - Cuarto renglón: Sinopsis
  - Botones
  - Carrusel de temporadas y capítulos (USER11)
- Las opciones disponibles para el usuario deben ser:
  - ***Reproducir*** (Ver USER04)
  - ***Ver trailer*** (Ver USER05)
  - ***Agregar a mi lista*** (Ver USER06)
  - ***Compartir*** (Ver USER07)
- El contenido de la vcard debe actualizarse de acuerdo a la temporada y capítulo seleccionado por el usuario.

#### HU002 - Usuario que desea comprar Temporadas/Episodios

- El modelo de negocio depende de lo establecido en cada operación.
- Cuando el usuario seleccione una serie que requiere contratación, la plataforma debe presentar la VCard correspondiente.
- Elementos y estructura de la Vcard
  - Imagen del contenido
  - Primer renglón: Nombre
  - Segundo renglón: Nombre original, Género(s), Año de publicación, Categoría y Duración
  - Tercer renglón: Temporada [número de temporada], Episodio:[Nombre del episodio]
  - Cuarto renglón: Sinopsis
  - Quinto renglón: Texto comercial
    - El texto comercial debe ser configurable por operación.
  - Botones
  - Texto informativo comercial
    - El texto debe ser configurable por operación.
  - Carrusel de temporadas y capítulos (USER11)
- Los botones disponibles para los usuarios que desean comprar un contenido de serie deben ser:
  - ***Temporada completa***
  - ***Compra episodio***
  - ***Temporadas*** [Verificar: Esto es para otro dispositivo]
  - ***Ver trailer*** (USER05)
  - ***Agregar a mi lista*** (USER06)
  - ***Compartir*** (USER07)
  - El botón ***Temporada completa*** debe cumplir con las siguientes características:
    - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
    - El texto del botón debe ser personalizable de acuerdo al costo de la temporada.
  - El botón ***Compra episodio*** debe cumplir con las siguientes características:
    - Las características del botón se detallan en la historia de usuario **USR17** [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
    - El texto del botón debe ser personalizable de acuerdo al costo del episodio.
  - Realizar Transacción
    - Si el administrador tiene PIN de seguridad activado entonces la plataforma debe solicitar la captura del PIN para continuar con la transacción.
      - Esta opción se detalla en la historia de usuario **USER08** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
    - Una vez validado el PIN de seguridad el flujo continúa en la transacción
      - Esta opción se detalla en la historia de usuario **USER02** del [BRF-5435_Rediseño Transacciones](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2327117866) 
  - Si el dispositivo del usuario no permite realizar la compra del contenido entonces:
    - Se debe agregar una leyenda que indique que la transacción la puede realizar desde el sitio web de Claro Video o en otro dispositivo. [Boceto pendiente].
    - Los botones de compra deben ocultarse y los demás botones deben alinearse al margen izquierdo de la Vcard.
- Cuando el usuario realiza la compra del contenido, la plataforma debe direccionar al usuario a la Vcard de reproducción de series **USER11**.

#### HU003 - Menú de navegación seleccionado vcard

- Cuando el usuario acceda a la vcard de un contenido, se debe visualizar seleccionado el nodo del menú de navegación principal desde el cual se accedió a la vcard con base en lo solicitado en la **HU029**

#### HU004 - Características generales de la vcard

Diseño

- Los elementos de texto de la Vcard se visualizarán superpuesto a la imagen del contenido
- Los elementos de texto y botones deben estar alineados al margen izquierdo de la vcard.
- Se debe aplicar a la imagen y Vcard las características de diseño establecidas por UX.
- El estilo de la imagen debe ser igual al aplicado para ATV4.
- El diseño debe ser responsivo adaptándose al dispositivo donde el usuario esté accediendo a la plataforma de Claro Video.

Menú de navegación

- Cuando el usuario acceda a la Vcard de un contenido, el menú se conserva visible en la parte superior de la vcard.
  - Todo el menú se debe visualizar con fondo gris
  - Solo se resaltan las letras de la opción del menú donde el usuario esté consultando la vcard del contenido.

<img src="img/vcard-03.png" alt="vcard-03" style="zoom:50%;" />

Botones

- Si la funcionalidad de un botón no está incluida para un contenido entonces:
  - No debe visualizarse el botón correspondiente
  - Los botones disponibles deberán alinearse al margen izquierdo de la Vcard.

Géneros

- Actualmente un contenido puede estar asociado a más de un género y en su mayoría no tienen indicado el género principal al que corresponde, por lo tanto para el segundo renglón de la Vcard las consideraciones por aplicar son:
  - Se deben visualizar únicamente los 2 primeros géneros asociados al contenido que el usuario esté consultando.

Carrusel de recomendaciones y talentos

- El orden de los carruseles que debe implementarse en las Vcard son:
  - Vcard de contenido unitario
    - Carrusel de recomendaciones
    - Carrusel de talentos
  - Vcard de Serie
    - Carrusel de episodios
    - Carrusel de recomendaciones
    - Carrusel de talentos
- Se cancela la **HU037**.

#### HU005 - Tabla de conversión categorías

1. Se debe implementar la conversión en los módulos que lo requieran:

   1. En los metadatos de la vcard, 
   2. Para mostrar el contenido adecuado cuando un perfil tiene restricción de contenido.

2. Las equivalencias para las categorías de los contenidos se deben asignar conforme a las siguientes tablas:

   1. Argentina:

      <img src="img/vcard-31a.png" alt="vcard-31a" style="zoom: 67%;" />

       

   2. Brasil

      <img src="img/vcard-31b.png" alt="vcard-31b" style="zoom: 67%;" />

   3. Estados Unidos

      <img src="img/vcard-31c.png" alt="vcard-31c" style="zoom: 67%;" />

       

   4. México

      <img src="img/vcard-31d.png" alt="vcard-31d" style="zoom: 67%;" />

       

   5. España

      <img src="img/vcard-31e.png" alt="vcard-31e" style="zoom:67%;" />

   6. Se anexa resumen en tabla de categorías por años:

<img src="img/vcard-31f.png" alt="vcard-31f" style="zoom: 33%;" />

3. Se cancela la **HU050**.



#### HU006 - Botones generales de vcard 

##### Para el botón **Reproducir,**

- El usuario accede a esta pantalla seleccionando de la vcard el botón ***Reproducir*****.**
  - Botón con fondo en color rojo, imagen representativa de la acción y texto de la acción en la parte inferior del botón.
- Cuando el usuario seleccione la opción ***Reproducir***
  - Si el contenido requiere la captura del PIN de seguridad entonces la plataforma debe solicitar la captura para permitir la reproducción.
    - Esta opción se detalla en la historia de usuario **USER08** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
  - De lo contrario se debe iniciar la reproducción del contenido en el player.

##### Para el botón **Ver trailer,**

- Aplica para todas las operaciones.
- Aplica para los dispositivos ***AAF***, ***Coship***, ***XBOX***.
- Se cancela la ***USR05.***
- Cuando el usuario selecciona el botón ***Ver trailer*** en la Vcard se debe dirigir al player y reproducir el trailer del contenido.
- El Player debe incluir las siguientes opciones
  - Alineadas de izquierda a derecha:
    1. Título del contenido 
    2. Barra de progreso
    3. Duración
  - Centrado debajo de la barra de progreso, alineadas de izquierda a derecha:
    1. Atrasar 10 segundos
    2. Play/Pausa
    3. Adelantar
    4. Idiomas
       1. Cuando el usuario seleccione esta opción, se debe mostrar el Panel de Cambiar idioma descrito en las **HU042** y **HU043** del brief: [https://dlatvarg.atlassian.net/browse/BRF-5405 - Can't find link](https://dlatvarg.atlassian.net/browse/BRF-5405)
- Cuando el tráiler termine de reproducirse se debe cerrar el player y dirigir al usuario nuevamente a la Vcard.



##### Para el botón **Agregar a mi lista**

- El usuario accede a esta opción seleccionando de la vcard el botón ***Agregar a mi lista*****.**
  - Botón con fondo en color gris, imagen representativa de la acción y la descripción de la acción en la parte inferior del botón.
- El botón debe incluir doble funcionalidad:
  - Cuando el usuario seleccione el botón ***Agregar a mi lista*** el contenido debe agregarse al carrusel correspondiente en el menú Mis Contenidos.
    - El texto del botón en la Vcard debe cambiar a ***Quitar de mi lista** .*
  - Cuando el usuario seleccione el botón ***Quitar de mi lista*** el contenido debe eliminarse del carrusel correspondiente en el menú Mis Contenidos.
    - El texto del botón en la Vcard debe cambiar por ***Agregar a mi lista.***



##### No se debe visualizar el botón **Compartir**

- **[RECHAZAR]**



#### HU007 - Botones Ver con [Add on] en Claro video y Suscripción

##### Se debe habilitar el botón ***Ver con [Add on] en Claro video***,

1. Únicamente para dispositivos móviles no deben aplicarse los criterios 2 y 3.
   1. El botón *Ver con [Add on] en Claro video* no debe ser responsivo.
   2. En el texto del botón se debe utilizar el nombre corto del add on.
2. El botón *Ver con [Add on] en Claro video* debe cumplir con las siguientes características:
   1. El botón debe ser responsivo, de tal forma que éste se ajuste a la longitud del texto.
   2. El texto ***Ver con [Add on] en Claro video*** debe visualizarse en una sola línea.
   3. En el texto de éste botón, si el nombre del add on se excede de los 17 caracteres entonces se debe implementar el nombre corto del add on.
3. Se deben recorrer horizontalmente los botones de la derecha, cuando el botón *Ver con [Add on] en Claro* *video* aumente en longitud.
   1. Todos los botones deben de visualizarse alineados horizontalmente.
4. En todos los dispositivos, el texto del botón de suscripción debe incluir el nombre corto del add on cuando éste exceda de 15 caracteres, ejemplo:
   1.  ***Picardía** **Nacional** (17 caracteres) **→ Picardía***
   2. **Qello Concerts by Stingray** (25) → ***Qello Concerts***



##### Cuando el usuario seleccione el botón ***Ver con [Add on] en Claro video*** se le debe dirigir a la Landing Comercial descrita en la **USER01** 

- **[REVISAR] Se puede vender suscripciones?**



#### HU008 - Barra de progreso visible

- **[REVISAR] los links estan rotos.**
- Cuando el usuario haya iniciado la visualización previa del contenido, se debe mostrar la barra de progreso en las vcards con base en lo solicitado en la **HU030** de la [BRF-5396_Rediseño VCards](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320466069)  y en las historias de usuario mencionadas en la misma.



#### HU009 - Opciones de reproducción

- **Quiero** que en el dispositivo Roku se habilite la pantalla con las opciones de reproducción



#### HU010 - Reproducir tráiler con categoría bloqueada

- Cuando un usuario seleccione el botón ***Ver tráiler*** en la Vcard y el contenido tenga la categoría bloqueada se le debe solicitar el ***PIN de seguridad*** para poder reproducir el tráiler.



#### HU011 - Carrusel de temporadas y capítulos

- El carrusel de temporadas y capítulos debe visualizarse en la parte inferior de la imagen de la Vcard.
- El carrusel debe incluir controles de desplazamiento (chevron), superpuestos en el carrusel, alineados de cada lado del mismo, para hacer más intuitivo el recorrido hacia ambos sentidos.
- Deben listarse las temporadas de la serie horizontalmente de izquierda a derecha, en forma de tabs.
  - Cuando el usuario seleccione una temporada, la lista de capítulos debe actualizarse con el contenido correspondiente.
- Siempre debe reanudarse la reproducción en la temporada y capítulo que esté visualizando el usuario.
- Para cada temporada debe mostrarse un carrusel de episodios, que debe incluir:
  - Temporada
  - Imagen correspondiente
  - Episodio
  - Resumen del episodio.

<img src="img/vcard-012.png" alt="vcard-012" style="zoom:50%;" />



#### HU012 - Mostrar metadata de un episodio

- Cuando el usuario se posicione en un episodio en el carrusel de temporadas y episodios, se debe mostrar la metadata de un episodio.
- La metadata debe tener los siguientes elementos:
  1. Temporada y episodio en el siguiente formato:
     1. Temporada {número de temporada} | Episodio {número de episodio}: {Nombre del episodio}
  2. Fecha de estreno del episodio en el siguiente formato:
     1. Mes día año
        Ejemplo: Mayo 1 2016
  3. Género(s)
  4. Año de publicación
  5. Categoría
  6. Sinopsis
- Los elementos deben cumplir con la posición y diseño solicitado en los insumos de diseño.



#### HU013 - Usuario anónimo-Botón de transacción

- **[RECHAZAR] - no tenemos usuario anonimo.**



#### HU014 - Ocultar botón Agregar a mi lista a los usuarios anónimos en las Vcards de contenido

- **[RECHAZAR] - no tenemos usuario anonimo.**



#### HU015 - Límite de dispositivos

- Cuando el usuario seleccione el botón Reproducir en la vcard de contenido y ha superado el límite de 2 dispositivos para la reproducción, la plataforma debe presentar la comunicación descrita en la **HU026**



1. Esta pantalla aplica para todos los dispositivos.
2. Cuando el usuario excede el límite de dispositivos permitidos para un contenido de alquiler, se debe mostrar una pantalla de notificación.
3. Esta pantalla debe visualizarse cuando el contenido alquilado se ha reproducido en 2 dispositivos y el usuario selecciona en un tercer dispositivo:
   1. En la **Vcard** del contenido alquilado la opción **Reproducir**.
4. Esta pantalla debe contar con la siguiente estructura y orden de los componentes:
   1. Imagen: Icono de advertencia (amarillo)
   2. Título: Límite de dispositivos alcanzados
   3. Texto: Este contenido ya comenzó a reproducirse en 2 dispositivos distintos. Solo puedes continuar la reproducción en esos mismos dispositivos.
   4. Botón: Aceptar
      1. Botón con esquinas redondeadas, fondo color rojo, texto color blanco en mayúsculas.
      2. Si el usuario selecciona esta opción, la plataforma lo debe dirigir al flujo donde fue invocada esta pantalla.
5. Los textos de esta pantalla deben ser configurables por operación.
6. Se incluye pantalla de referencia:

<img src="img/vcard-15.png" alt="vcard-15" style="zoom:50%;" />



#### HU016 - Insumos de diseño 

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3565944899/Insumos+Roku




### Action points

- [ ]   [GOOSE] Analizar
- [ ]   [JULI] Revisar Assets / Armar lista de problemas.




## Menu [análisis1]

*Evolución y rediseño de menú.*

Status: **TODO[1er ANÁLISIS]**



https://dlatvarg.atlassian.net/browse/BRF-8589

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3627778288/BRF-8589+Todos+OTT+Roku+Nodo+Men



### HUs

#### HU001 - Menú de navegación

- Se debe habilitar el menú de navegación con base a lo solicitado en la **USER01** del [BRF-5403 Rediseño Nodo Menú](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320269369) y en las HU mencionadas en la misma.
- El número de nodos del menú debe ser dinámico y configurable por operación.
- Si los nodos del menú exceden del total de nodos visibles, cuando el usuario navegue en el menú los nodos deben recorrerse hacia la izquierda o derecha dependiendo del desplazamiento que realice el usuario.
- Si la operación cuenta con nodos adicionales se deben respetar los nodos y el orden de los mismos.
- Se deben mostrar la selección y navegación de los nodos de la siguiente manera:
  1. Nodo seleccionado color #9B0F0F
  2. Nodo en focus color #DE1717
  3. Deben cumplir con las especificaciones de los insumos de diseño **HU015**

<img src="img/menu1.png" alt="menu1" style="zoom: 33%;" />

#### HU002 - Características generales - Carruseles



##### [USER04] **Quiero** que todos los carruseles tengan estructura y comportamiento similares

- Los carruseles secundarios pueden ser de tipo: editorializados y automáticos.
- Cada operación podrá definir para los carruseles:
  - El número de carruseles a incluir en la página principal de cada opción de menú.
  - Nombre del carrusel
  - Ordenamiento de los carruseles en la página principal de cada opción del menú.
- **[RECHAZAR]** El carrusel superdestacados debe incluir en la parte superior derecha los botones de desplazamiento.
- Cada uno de los carruseles secundarios debe cumplir con lo siguiente:
  - El nombre debe ubicarse en la parte superior izquierda del carrusel.
  - **[RECHAZAR]** Debe contar con botones de desplazamiento independientes,
  - **[RECHAZAR]** Los botones de desplazamiento deben ubicarse en la parte superior derecha del carrusel.
  - **[RECHAZAR]** Los botones deben conservar la alineación horizontal con el nombre del carrusel.
- Solo se deben mostrar los carruseles que tengan contenido disponible. Ejemplos:
  - Si es la primera vez que el usuario accede a la plataforma entonces no debe mostrarse el carrusel “Continuar viendo”
  - Si el usuario no tiene contenido agregado a su lista de reproducción entonces no debe mostrarse el carrusel “Mi lista”



##### [USER05] **Quiero** la plataforma incluya un carrusel principal

- Este carrusel debe incluir contenido editorializado.
- Se debe aplicar el formato de superdestacados implementado para Apple TV.
- Dependiendo de la opción del menú o add on que esté consultando el usuario, será el contenido a mostrar en el carrusel.
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.
- **Inicio**
  - El carrusel debe incluir el contenido más destacado de toda la plataforma, sin incluir los canales en vivo.
- **Películas**
  - El carrusel debe incluir únicamente las películas más destacadas de la plataforma.
- **Series**
  - El carrusel debe incluir únicamente las series más destacadas de la plataforma.
- **Add on**
  - El carrusel debe incluir únicamente contenido destacado del Add on seleccionado.
  - En la parte superior del carrusel debe visualizarse el nombre del Add on.



##### [USER06] **Quiero** que la plataforma incluya un carrusel donde el usuario identifique su contenido de visualización en progreso

- Carrusel horizontal, estándar.
- En este carrusel se debe incluir el contenido que tiene en proceso de visualización el usuario activo (perfil).
- El orden del contenido es del más reciente al más antiguo (ordenamiento PEPS).
- Cada contenido debe incluir:
  - Imagen del contenido
  - Barra de progreso de visualización (progress bar).
  - Debajo de la imagen del contenido debe ubicarse la siguiente información:
    - Nombre del contenido
    - **[RECHAZAR]** Opción para eliminar el contenido de este carrusel.
- Si el usuario no tiene contenido en proceso entonces este carrusel no debe visualizarse.
- Dependiendo de la opción del menú o canal que esté consultando el usuario, será el contenido a mostrar en el carrusel.
  - **Inicio**
    - El carrusel debe incluir todo el contenido que tenga en proceso de visualización el usuario activo (perfil).
  - **Películas**
    - El carrusel debe incluir únicamente las películas que tenga en proceso de visualización el usuario activo (perfil).
  - **Series**
    - El carrusel debe incluir únicamente las series que tenga en proceso de visualización el usuario activo (perfil).
  - **Kids**
    - El carrusel debe incluir únicamente contenido infantil que tenga en proceso de visualización el usuario activo (perfil).
  - **Add on**
    - El carrusel debe incluir únicamente contenido del add on que tenga en proceso de visualización el usuario activo (perfil).
  - **Mis contenidos**
    - El carrusel debe incluir todo el contenido que tenga en proceso de visualización el usuario activo (perfil).

##### [USER07] **Quiero** que la plataforma incluya un carrusel de programas de TV en vivo que los canales estén transmitiendo en ese momento

- Carrusel horizontal, estándar.
- Este carrusel es dinámico y se debe actualizar constantemente en tiempo real ejecutando una consulta a la EPG.
- Debe incluir el contenido en vivo que estén transmitiendo los canales contratados que haya visualizado recientemente el usuario.
- **[ANALiZAR]** **[RECHAZAR]**  En la tarjeta de cada contenido se debe visualizar lo siguiente:
  - Imagen del contenido
  - Progress bar
    - Debe ubicarse sobre la imagen del contenido.
  - Debajo de la imagen del contenido debe ubicarse la siguiente información:
    - Título del programa
    - Número de canal seguido del nombre del canal
    - Icono Tv seguido de la etiqueta de estatus del programa y el horario del programa.
    - Botón de reproducción.
    - Opción para eliminar el contenido de este carrusel.
- Cuando el usuario seleccione el botón de reproducción la plataforma debe direccionar al usuario a la visualización del contenido en vivo.
  - La funcionalidad se describe en el [BRF-5397_Rediseño TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320662644) 

- Para los dispositivos **STV**, **AAF** y **ATV4** el diseño de este carrusel varía, ver historia de usuario **USER39.**

![menu-07](img/menu-07.png)

##### [USER08] **Quiero** que se incluya en la plataforma un carrusel de los paquetes que oferta la plataforma



- Carrusel horizontal, estándar, editorializado.
- Este carrusel debe incluirse en las páginas principales de **Inicio**, **Películas** y **Series**, donde el contenido del carrusel debe ser el mismo para las tres opciones del menú.
- Este carrusel debe incluir una imagen por paquete ofertado en la plataforma.
- La imagen se debe de cambiar editorialmente según lo determine cada operación, y debe incluir:
  - Imagen editorializada que incluye tres contenidos del add on
  - Etiqueta con nombre del add on
    - Debe ubicarse debajo de la imagen del canal.
    - El color de la etiqueta ya esta definido por add on respecto a cada proveedor.
- El orden de los canales en el carrusel debe de coincidir con el orden establecido en el canal ***Selector de Planes.***
- Cuando el usuario seleccione una imagen del carrusel, la plataforma lo debe direccionar a la página principal del add on correspondiente. (USER26)

![menu-08](img/menu-08.png)

##### [USER09] **Quiero** que se incluya en la plataforma un carrusel con los mejores contenidos de los add on



- Carrusel horizontal, estándar.
- Este carrusel incorpora el mejor contenido de todos los add on y debe incluirse en las páginas principales de
  - **Inicio:** debe incluir contenido mixto.
  - **Películas:** debe incluir únicamente películas.
  - **Series**: debe incluir únicamente series.
  - **Kids:** debe incluir únicamente contenido infantil.
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.
- Cada contenido debe incluir:
  - Imagen del contenido
    - Con overlay
  - Etiqueta del add on
    - Debe ubicarse en la parte superior izquierda de la imagen
  - Nombre del contenido
    - Debe ubicarse sobre la imagen en la esquina inferior izquierda.

![menu-09](img/menu-09.png)

##### [USER10] **Quiero** que se incluya en la plataforma un carrusel con el contenido más popular de acuerdo a la experiencia de los usuarios en la plataforma

- Este carrusel tiene un formato vertical y más amplio que en los demás carruseles.
- Este carrusel incorpora el contenido más popular en la plataforma y debe incluirse en las páginas principales de
  - **Inicio:** debe incluir lo más popular de contenido mixto.
  - **Películas:** debe incluir las películas más populares.
  - **Series**: debe incluir las series más populares.
  - **Kids:** debe incluir el contenido infantil más popular.
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.

![menu-10](img/menu-10.png)



##### [USER11] **Quiero** que se incluya en la plataforma un carrusel con el top 10 del contenido de la plataforma

- Carrusel en horizontal, estándar, editorializado.
- El carrusel debe incluir la numeración del 1 al 10
  - El color del texto debe ser rojo
  - La imagen del contenido que ocupe cada posición debe ubicarse a la derecha del número correspondiente.
- Si el usuario está visualizando este carrusel desde la página del menú **Inicio** entonces:
  - El nombre propuesto para el carrusel es “Lo mas top”
  - El contenido del carrusel debe corresponder a los 10 contenidos mixtos más visto de acuerdo a la operación.
- Si el usuario está visualizando este carrusel desde la página del menú **Películas** entonces:
  - El nombre propuesto para el carrusel es “Top películas”
  - El contenido del carrusel debe corresponder a las 10 películas más vistas de acuerdo a la operación.
- Si el usuario está visualizando este carrusel desde la página del menú **Series** entonces:
  - El nombre propuesto para el carrusel es “Top series”
  - El contenido del carrusel debe corresponder a las 10 series más vistas de acuerdo a la operación.
  - Si el usuario ya está visualizando la serie entonces al seleccionarlo desde este carrusel la plataforma debe direccionar al usuario a la visualización en la temporada y capítulo en progreso.
- Si el usuario está visualizando este carrusel desde la página del menú **Kids** entonces:
  - El nombre propuesto para el carrusel es “Lo mas top”
  - El contenido del carrusel debe corresponder a los 10 contenidos infantiles más vistos de acuerdo a la operación.
- El contenido y ordenamiento del carrusel debe ser automatizado, de acuerdo a la operación
  - El criterio de inclusión para el contenido se debe tomar mediante parámetros de visualización de los usuarios en cada operación.
- Cuando el contenido sea una serie, debe considerarse lo siguiente:
  - Si el usuario selecciona una serie cuya visualización haya iniciado anteriormente, la VCard debe posicionarlo en el último capítulo reproducido.
  - Si el usuario selecciona una serie que va a visualizar por primera vez, la VCard debe posicionarlo en la primera temporada y primer capítulo.

![menu-11](img/menu-11.png)

##### [USER12] **Quiero** que se incluya en la plataforma un carrusel con el contenido recomendado

- Carrusel en horizontal, estándar.
- Este carrusel debe incluir el contenido recomendado de todos los add on ofrecidos en la plataforma, de acuerdo a la preferencia de visualización del usuario.
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.

![menu-12](img/menu-12.png)

##### [USER13] **Quiero** que se incluya en un carrusel el contenido agregado recientemente en la plataforma

- Carrusel en horizontal, estándar.
- Este carrusel incorpora el contenido de todos los add on agregados recientemente en la plataforma y debe incluirse en las páginas principales de
  - **Inicio:** debe incluir lo más agregado recientemente de contenido mixto.
  - **Películas:** debe incluir lo más agregado recientemente en películas.
  - **Series**: debe incluir lo más agregado recientemente en series .
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.

![menu-13](img/menu-13.png)

##### [USER14] **Quiero** que se incluya un carrusel con contenido temático

- Carrusel en formato vertical, temático.
- El tema del contenido del carrusel debe ser definido por Comercial.
- Este carrusel debe incluirse en las páginas principales de
  - **Inicio:**
    - Puede incluir Películas, Series y distintos modelos de negocio (alquiler, catálogo Claro Video, etc.).
  - **Películas:**
    - Debe incluir Películas y distintos modelos de negocio (alquiler, catálogo Claro Video, etc.).
- El nombre del carrusel siempre debe iniciar con el símbolo de numeral “#”.

![menu-14](img/menu-14.png)



##### [USER15] **Quiero** que se incluya un carrusel con contenido familiar

- Es un carrusel editorializado.
- El nombre y contenido lo determinará cada operación.
- Cada operación debe definir el criterio para la inclusión de contenido en este carrusel.
- Este carrusel debe incluirse en las páginas principales de
  - **Inicio:** debe incluir contenido mixto.
  - **Películas:** debe incluir únicamente películas.
  - **Series**: debe incluir únicamente series.
  - **Kids:** debe incluir únicamente contenido infantil.

![menu-15](img/menu-15.png)

##### [USER16] **Quiero** que se incluya un carrusel con el contenido que el usuario haya agregado a su lista de reproducción

- Debe incluir el contenido que el usuario tiene agregado a su lista de reproducción.
- El orden del contenido es del más reciente al más antiguo (ordenamiento PEPS).
- Cada contenido debe incluir:
  - Imagen del contenido
  - Etiqueta (chapita)
    - Debe ubicarse en la esquina superior izquierda de la imagen
    - Solo si aplica, dependiendo del modelo de negocio.
  - Nombre del contenido
  - Opción para ***eliminar*** el contenido.
- Este carrusel puede incluir distintos modelos de negocio (alquiler, catálogo Claro Video, etc.), y debe incluirse en las páginas principales de
  - **Inicio:** debe incluir contenido mixto.
  - **Películas:** debe incluir únicamente películas.
  - **Series**: debe incluir únicamente series.
  - **Kids:** debe incluir únicamente contenido infantil.
  - **Mis contenidos:** debe incluir contenido mixto.

- Cuando el usuario seleccione la opción ***Eliminar*** el flujo continúa en la historia de usuario **USER32**.

![menu-16](img/menu-16.png)

##### [USER17] **Quiero** que se incluya un carrusel con los planes ofrecidos en la plataforma

- Es el último carrusel de la página del menú **Inicio**
- Este carrusel solo se debe visualizar en la página del **menú** Inicio
- Debe incluir la tarjeta de los planes que aún no tenga contratado el usuario.
  - Si el usuario tiene todos los planes contratados entonces este carrusel no debe presentarse.
- El orden de los planes debe ser configurable por cada operación.
- Para Claro Video debe incluirse una tarjeta para cada tipo de suscripción: anual y mensual.
- Cada tarjeta debe incluir:
  - Imagen del plan
  - Logotipo
  - Precios
  - Promoción
  - Botón ***¿Qué incluye [nombre del paquete]?***
  - Botón ***Contratar [nombre del paquete]***
- El precio y promoción no son parte de la imagen, estos deben ser configurables por operación.
- El color del botón ***Contratar*** está definido por add on respecto a cada proveedor.
- Cuando el usuario seleccione ***¿Qué incluye [nombre del paquete]?*** debe visualizar la pantalla que muestra el detalle del add on, descrita en la historia de usuario **USER01** del [BRF-5427_Rediseño Componentes](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320498853) 
- Cuando el usuario seleccione ***Contratar [nombre del paquete]*** debe visualizar la VCard correspondiente, descrita en el USER03 del [BRF-5435_Rediseño Transacciones](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2327117866) 

![menu-17](img/menu-17.png)



#### HU003 - Buscador

- **Quiero** que en el dispositivo Roku se habilite el nodo buscador en el menú de navegación, con base al rediseño y con las funcionalidades requeridas 

#### HU004 - Home Inicio

- **Quiero** que en el dispositivo Roku se habilite el nodo de **Inicio** en el menú de navegación, con base al rediseño y con las funcionalidades requeridas 

#### HU005 - Menú Películas

- **Quiero** que en el dispositivo Roku se habilite el nodo de **Películas** en el menú de navegación, con base al rediseño y con las funcionalidades requeridas 
- Cuando el usuario seleccione en el menú de navegación la opción ***Películas,*** deben mostrarse en orden descendente las siguientes secciones:
  - Carrusel Superdestacado
  - Carrusel de clasificación de Géneros
  - Carruseles secundarios
- Todos los carruseles deben contener únicamente Películas.
- Cada operación debe definir el criterio para la inclusión de contenido en el carrusel Superdestacado.
- En el carrusel de géneros éstos deben presentarse como botones seleccionables, considerando que:
  - Es un submenú de navegación
  - Se deben visualizar en orden alfabético de izquierda a derecha
  - La sección de géneros contará con botones independientes de desplazamiento
  - Si el usuario selecciona un género específico el flujo continúa en la historia de usuario **USER20.**
- Los carruseles secundarios y el orden propuesto para esta opción del menú son:
  - *Seguir viendo **USER06***
  - *Canales Premium **USER08***
  - *Lo mejor de los canales premium **USER09***
  - *Lanzamientos en alquiler **USER18***
  - *Lo mas popular **USER10***
  - *Top películas **USER11***
  - *Agregados hace poco a Claro Video **USER13***
  - *#NavidadEnCasa (#Tematico) **USER14***
  - *Diversión en familia **USER15***
  - *Mi lista **USER16***
- Se anexan imágenes de referencia:



<img src="img/menu-19a.png" alt="menu-19a" style="zoom: 67%;" />



<img src="img/menu-19b.png" alt="menu-19b" style="zoom: 67%;" />



<img src="img/menu-19c.png" alt="menu-19c" style="zoom:67%;" />

#### HU006 - Vista de contenido filtrado

- Esta pantalla se despliega cuando el usuario selecciona un género específico desde la página de **Películas, Series, Add on.**

- El menú de navegación indica la posición del usuario, resaltando solo el texto de la opción del menú, desde el cuál fue invocado el filtro de contenido por género.

- La pantalla debe incluir dos combos de selección:

  - Lista de subcategorías

    - Representa el segundo nivel del nodo para la selección del contenido.

    - El combo debe ubicarse arriba del listado de contenido (listado infinito), alineado al margen izquierdo.

    - El contenido del combo depende de la pantalla que lo invoque, donde:

      - Si procede de **Películas** o **Series** entonces debe mostrar la lista de géneros

        <img src="img/menu-20a.png" alt="menu-20a" style="zoom:50%;" />

      - Si procede de un **Add on** entonces debe mostrar la lista de subcategorías.

        <img src="img/menu-20b.png" alt="menu-20b" style="zoom:50%;" />

  - Lista de ordenamiento

    - Su contenido debe ser: Lo más nuevo, lo más visto, lo más votado, A-Z, Z-A.
    - El combo debe ubicarse arriba del listado de contenido, alineado al margen derecho.

- Desde que se invoca la página debe mostrar contenido.

  - El combo de subcategorías inicia posicionado en la opción seleccionada en la pantalla que invoca.
  - El combo de ordenamiento inicia posicionado en la opción “Lo más nuevo”

- Cada vez que el usuario realice una consulta y se recargue la página se deben visualizar 6 filas de contenido, dando un total de 24 contenidos por consulta.

- Al mostrar los contenidos filtrados de películas, series o add ons, se deben presentar **todos** los contenidos relacionados con el filtro seleccionado, es decir no debe haber un límite específico.

- Los contenidos se deben mostrar dentro de un mosaico.

  - El tamaño del mosaico depende del dispositivo.

#### HU007 - Menú Series

- Se debe mostrar en formato grid las subcategorías para peliculas y series.
- Se debe habilitar el shortcut de **Asterisco** para filtrar el contenido del genero seleccionado por el usuario ***Filtrar y ordenar el contenido**:
  1. Lo más nuevo
  2. Lo más visto
  3. Lo más votado
  4. A - Z
  5. Z - A

#### HU008 - Menú TV en vivo

1. **[AVERIGUAR QUÉ ES ESTO!!!]**
2. Para el nodo de TV en vivo se deben considerar:
   1. En el primer MVP las funcionalidades descritas en el [BRF-8435_Todos | OTT | Roku: MVP con funcionalidades de TV en vivo](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3583739083/BRF-8435+Todos+OTT+Roku+MVP+con+funcionalidades+de+TV+en+vivo)
   2. En el segundo MVP las funcionalidades descritas en el [BRF-8767_Todos | OTT | Roku: TV en vivo - Control Player](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3627319528/BRF-8767+Todos+OTT+Roku+TV+en+vivo)

#### HU009 - Menú Kids

- Se debe habilitar el nodo **Kids** en el menú de navegación con base a lo solicitado en la **USER23** y en las historias de usuario mencionadas en la misma.

#### HU010 - Pantalla principal de Add on

- Se debe habilitar la pantalla principal de cada Add on con base en lo solicitado en la **USER26 Pantalla principal de Add on** del [BRF-5403 Rediseño Nodo Menú](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320269369) y en las historias de usuario mencionadas en la misma.
- Esta pantalla se despliega cuando el usuario selecciona desde la página del menú **Inicio**, un canal del carrusel de Canales Premium.
- El menú de navegación indica la posición del usuario resaltando la opción del menú desde el cuál fue invocado la pantalla del add on.
- La página debe contener las siguientes secciones:
  - Carrusel Superdestacado
  - Carrusel de subcategorías
  - Carrusel de canales
  - Carruseles secundarios
- El carrusel de canales solo debe visualizarse si el add on cuenta con este tipo de contenido.
- Los carruseles secundarios únicamente deben incluir contenido del add on seleccionado.
  - Todos son carruseles horizontales, estándar.

Carrusel de subcategorías

- Incluye las subcategorías del add on seleccionado:
  - Películas
  - Series
  - Especiales
  - Infantiles
  - Documentales
- Si el usuario selecciona una subcategoría la plataforma debe direccionar al usuario a la pantalla Vista de contenido por filtro (USER20)

Carruseles secundarios

- Carruseles horizontales donde cada contenido incluye:
  - Imagen del contenido con overlay
  - Nombre del contenido
  - Etiqueta del add on.
- Los carruseles deben ser configurables por la operación.
- Los carruseles secundarios propuestos para esta pantalla son:
  - *Canales en vivo **USER07***
  - *Películas*
  - *Series*
  - *Especiales*
  - *Infantil*
  - *Documentales*
  - *Terror y suspenso*
  - *Comedia*
  - *Drama*
  - *Lo más visto.*
- Se anexan imágenes de referencia:

<img src="img/menu-26a.png" alt="menu-26a" style="zoom:50%;" />



<img src="img/menu-26b.png" alt="menu-26b" style="zoom:50%;" />



#### HU011 - Menú Mis contenidos

- Para el carrusel **Seguir viendo** se deben implementar las características indicadas en la **USER06.**
- Para el carrusel **Mis canales favoritos** se deben implementar las características indicadas en la **USER27.**
- Para el carrusel **Mis grabaciones** se deben implementar las características indicadas en la **HU080 y** **HU081.**
- No se debe habilitar el carrusel **Mis descargas** descrito en la **USER29.**
- Para el carrusel **Mis alquileres** se deben implementar las características indicadas en la **USER30, HU134 y HU137.**
- Para el carrusel **Mi lista** se deben implementar las características indicadas en la **USER16.**

#### HU012 - Eliminar de mis contenidos

- Se debe habilitar la pantalla de confirmación que se muestra cuando el usuario elimina un contenido con base en lo solicitado en la **USER32, USER39 y HU084** del [BRF-5403 Rediseño Nodo Menú](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2320269369).

#### HU013 - Mis contenidos - Placeholders

- Únicamente en la página correspondiente al menú **Mis contenidos** debe implementarse esta funcionalidad.
- Únicamente en esta página cuando un carrusel no tenga contenido, se debe mostrar un placeholder para el carril, indicando qué puede almacenar el usuario en cada sección, donde:
  - El texto se debe adecuar conforme a la operación
  - El texto se oculta cuando el usuario agrega al menos un contenido en el carril.
- Si un carrusel no tiene contenido agregado:
  - La plataforma debe incluir un placeholder, con información descriptiva sobre el tipo de contenido que localizará en el carrusel.
  - El texto debe ubicarse al inicio del carrusel, alineado al margen izquierdo de la pantalla.
  - El texto debe ser configurable por la operación.
- Cuando el usuario agregue contenido al carrusel:
  - El placeholder debe desaparecer únicamente en el carrusel correspondiente.
  - El carrusel debe alinearse al margen izquierdo de la pantalla.
- El texto propuesto para cada carrusel es el siguiente, aunque deberá poder modificarse por cada operación.
  - **Seguir viendo**: Aquí encontrarás el contenido que aún no terminas de ver.
  - **Mis canales favoritos**: Añade tus canales de TV favoritos desde la guía de programación y accede a ellos desde aquí.
  - **Mis grabaciones**: Graba los programas de TV que te gustan y encuéntralos aquí cuando quieras.
  - **Mis alquileres**: Aquí encontrarás todos tus alquileres.
  - **Mi lista**: Añade películas y series con el botón “Agregar a mi lista” y accede a ellos aquí.

#### HU014 - Nodos de segundos nivel

- **[RECHAZAR - no puede ser configurable]** Si en el nodo principal tiene algún nodo secundario se deben respetar las funcionalidades y criterios establecidos por operación.

#### HU015 - Insumos

URL: https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/3565944899


### Action points

- [ ]   [GOOSE] Analizar
- [ ]   Validar faltantes
- [ ]   [JULI] Revisar Assets / Armar lista de problemas.




## TrickPlay [análisis1]

*Implementación de trickplay*

Status: **TODO[1er ANÁLISIS]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-6032 

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2444657048/BRF-6032+Todos+CV+Proyecto+Roku+Trickplay



### HUs

#### HU001 - Roku Trickplay

- **Quiero** que la aplicación en Roku cuente con Trickplay.

#### HU002 - Archivos BIF

- Se deben utilizar archivos .bif para la implementación de está funcionalidad, tal como lo marca la documentación de Roku:
  https://developer.roku.com/es-ar/docs/developer-program/media-playback/trick-mode/bif-file-creation.md
- Se deben generar para todos los contenidos. 


### Action points

- [ ]   [GOOSE] Analizar
- [ ]   [JULI] Revisar Assets / Armar lista de problemas.




## Feed [análisis1]

*Feed para Roku para la búsqueda desde menú  central.*

Status: **TODO[1er ANÁLISIS]**



Brief: https://dlatvarg.atlassian.net/browse/BRF-6031 

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/2444263602/BRF-6031+Todos+CV+Feed+Deeplinking



### HUs

#### HU001 - Feed Roku

Documentación: https://developer.roku.com/es-ar/docs/specs/search-feed.md

#### HU002 - Deeplink




### Action points

- [ ]  [GOOSE] Analizar.
- [ ]  [NACHO] Armar reunión con GPS.



