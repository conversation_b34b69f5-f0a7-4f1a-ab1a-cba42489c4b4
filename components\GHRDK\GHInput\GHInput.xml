<?xml version="1.0" encoding="utf-8"?>
<component name="GHInput" extends="Group" initialFocus="label">
  <script type="text/brightscript" uri="GHInput.brs" />
  <script type="text/brightscript" uri="GHInput_fields.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <interface>
    <!-- input -->
    <field id="value" type="string" value="" onChange="updateFieldText" alias="input.text" />
    <field id="placeholder" type="string" value="Correo" onChange="updateFieldText" alias="input.hintText" />
    <field id="maxTextLength" type="string" value="100" onChange="updateFieldmaxTextLength" />
    <field id="color" type="string" value="0xFFFFFF" onChange="updateFieldColor" />
    <field id="selColor" type="string" value="0xFFFFFF" onChange="updateFieldSelColor" />
    <field id="placeholdercolor" type="string" value="0xFFFFFF" onChange="updateFieldPlaceholderColor" />
    <!-- dialog -->
    <field id="title" type="string" value="Title" />
    <field id="message" type="string" value="" />
    <field id="titleColor" type="string" value="0x00FFFF" />
    <field id="messageColor" type="string" value="0x00FFFF" />
    <!-- input + dialog -->
    <field id="password" type="boolean" value="false" onChange="updateFieldPassword" />
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" alwaysNotify="true" />
    <field id="width" type="string" value="0" onChange="updateFieldWidth" alwaysNotify="true" />
    <field id="height" type="string" value="0" onChange="updateFieldHeight" alwaysNotify="true" />
    <!-- border -->
    <field id="focusPadding" type="string" value="12" onChange="updateFieldFocusPadding" />
    <field id="focusMap" type="string" value="pkg:/images/focus01.9.png" alias="border.uri" />
    <field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor" />
    <!-- general -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
    <field id="keyboardType" type="string" value="normal" alwaysNotify="true" />
    <field id="dataValues" type="array" alwaysNotify="true" />
    <field id="headerText" type="string" alwaysNotify="true" onChange="updateHeaderText"/>
    <field id="dropDownIconVisibility" type="boolean" alwaysNotify="true" alias="dropDownIcon.visible" value="false"/>


    <!-- end -->
  </interface>
  <children>
    <LayoutGroup id="GHInputLayout" layDirection="vert" itemSpacing="[5]">
      <Group>
        <Poster id="border" translation="[0,0]" width="320" height="120" uri="" visible="false" blendColor="0x0000ff" />
        <TextEditBox id="input" translation="[0,0]" textColor="0xFFFFFF" width="0" height="0" />
      </Group>
    </LayoutGroup>
    <Poster id="dropDownIcon" uri="pkg:/images/dropDownIcon.png" visible="false"/>
  </children>
</component>
