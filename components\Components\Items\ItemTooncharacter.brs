sub Init()
  m.itemPoster = m.top.findNode("itemPoster")
  m.itemMask = m.top.findNode("itemMask")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  imagen = ghGetChild(data, "image_highlight")
  if imagen = invalid or imagen = "" then
    imagen = ghGetImageByMode("TalentoDefault.png")
    ' else
    ' imagen = imagen + "?size=290x163"
  end if

  ' envio en cada card, la cantidad total de cards en el carousel
  ' si hay menos cantidad de 5, no desplazo la imagen ni el texto
  ' si hay muchas card, tengo que desplazar
  if ghGetDisplayMode() = "FHD" then
    ejeX = -5
  else
    ejeX = 2
  end if
  if ghGetChild(data, "cantTotal", 50) >= 6 then
    if ghGetDisplayMode() = "FHD" then
      ejeX = 18
    else
      ejeX = 17
    end if
  end if

  m.itemPoster.setFields({
    width: 175
    height: 175
    translation: [ejeX, 18]
    uri: imagen
  })

  ' mask
  ' m.itemMask.setFields({
  '   width: 180
  '   height: 250
  '   translation: [-2, 16]
  '   uri: ghGetImageByMode("4px_RoundMask.png")
  '   visible: true
  ' })

  m.title.setFields({
    width: m.itemPoster.width - 25
    height: m.itemPoster.height - 25
    text: ghGetChild(m.top.itemContent, "data.text_highlight", "")
    font: ghGetFont(16, "bold")
    translation: [ejeX + 10, 100]
    color: "0xCCCCCC"
    wrap: "true"
    lineSpacing: "0"
    horizAlign: "center"
    vertAlign: "bottom"
  })
end sub

function getNames(data)
  fullName = ghGetChild(data, "fullname")
  if fullName = invalid then
    fullName = ghGetChild(data, "last_name", "")
    if fullName = "" then fullName = ghGetChild(data, "surname", "")
    if fullName <> "" then fullName = fullName + ", " ' si hay algo, necesito una coma
    fullName = fullName + ghGetChild(data, "first_name", "")
    if fullName = invalid then
      fullName = "UnFullname"
    end if
  end if
  rolName = ghGetChild(data, "rolname")
  if rolName = invalid then
    rolName = "UnRolName"
  end if
  rolName = UCase(rolName)
  res = fullName + chr(10) + rolName
  return res
end function
