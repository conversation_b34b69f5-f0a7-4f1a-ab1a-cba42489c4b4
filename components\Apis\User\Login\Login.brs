' Login
' -----------------------------

sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/user/login"
  m.api.query.Append({
    "username": m.top.username,
    "password": m.top.password,
    "includpaywayprofile": true,
    "appversion": ghGetAppVersion(),
  })
  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  if res = invalid then
    m.top.content = {}
    return
  end if

  content = {}
  response = res.response

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
    print ghLogHead();"ProcessData -- response.lasttouch = ";response.lasttouch
    print ghLogHead();"ProcessData -- error_auth_login = ";response.error_auth_login
  end if

  if response.error = invalid then ' ================= OK

    social = ghGetChild(response, "socialNetworks", [])
    if m.top.debug then print ghLogHead();"social = ";social
    if social.Count() > 0 then
      if m.top.debug then print ghLogHead();"social[0] = ";response.socialNetworks[0]
    end if

    content.addReplace("isLoggedIn", true)
    print "ACCEPTED-TERMS (login) >> "ghGetChild(response, "accepted_terms")
    content.addReplace("accepted_terms", ghGetChild(response, "accepted_terms", 1))

    ' global
    ' ----------------------------------------------------
    ghSetRegistry("isLoggedIn", "true")
    print ghLogHead();"ProcessData -- Check Region -- ";ghGetRegistry("region");" <> ";response
    m.global.superhighlight = ghGetChild(response, "superhighlight", [])
    if ghGetRegistry("region") <> response.region then
      content.addReplace("ReloadTranslations", true)
      ghSetRegistry("region", response.region)
    else
      content.addReplace("ReloadTranslations", false)
    end if


    try ' Payment Methods
      payMethods = response.paywayprofile.paymentmethods
      txtPayMethods = ""
      for i = 0 to payMethods.Count() - 1
        cat = payMethods[i].user_category
        if cat <> invalid then
          txtPayMethods += payMethods[i].user_category + "-"
        end if
      end for
      if Len(txtPayMethods) > 0 then
        txtPayMethods = Left(txtPayMethods, Len(txtPayMethods) - 1)
      end if
    catch err
      print "ERROR >> ";err
      txtPayMethods = ""
    end try
    ' subRegion
    subRegion = response.subregion
    if subRegion = invalid then subRegion = ""

    ' user
    ' ----------------------------------------------------
    ghSetRegistry("user_id", response.user_id, "user")
    ghSetRegistry("parent_id", ghGetChild(response,"parent_id"), "user")
    ghSetRegistry("session_userhash", response.session_userhash, "user")
    ghSetRegistry("user_token", response.user_token, "user")
    ghSetRegistry("user_session", response.user_session, "user")
    ghSetRegistry("language", response.language, "user")
    ghSetRegistry("username", response.username, "user")
    ghSetRegistry("firstname", response.firstname, "user")
    ghSetRegistry("lastname", response.lastname, "user")
    ghSetRegistry("email", response.email, "user")
    ghSetRegistry("region", response.region, "user")
    ghSetRegistry("subregion", subRegion, "user")
    ghSetRegistry("paymentMethods_user_category", txtPayMethods, "user")
    ghSetRegistry("country_code", response.country_code, "user")
    ghSetRegistry("HKS", response.session_stringvalue)
    ghSetRegistry("HKS-login", response.session_stringvalue)
    ' ----------------------------------------------------
    updateGlobalArray("lasttouch", ghGetChild(response, "lasttouch"))
    ' ----------------------------------------------------
    m.top.content = content

  else ' ==================================================== FALLO
    content.addReplace("isLoggedIn", false)
    content.error = true
    content.error_code = res.errors.code
    if content.error_code = "user_login_invalido" then ' errores varios
      content.error_code = ghTranslate("login_error_generic_title", "Valida la información")
      content.error_msg = ghTranslate("login_error_generic_description", "Para continuar, valida la información solicitada en los campos correspondientes. (LOG-01)", {})
    end if
    if content.error_code = "error_params" then ' errores varios
      content.error_code = ghTranslate("login_error_params_title", "Valida la información")
      content.error_msg = ghTranslate("login_error_params_description", "Para continuar, valida la información solicitada en los campos correspondientes. (LOG-01)", {})
    end if
    ' global
    ' ----------------------------------------------------
    ghSetRegistry("isLoggedIn", "false")
    ' ghDeleteSectionRegistry() ' no deberia
    ' user
    ' ----------------------------------------------------
    ghDeleteSectionRegistry("user")
    ' ----------------------------------------------------
    m.top.error = content
  end if
  ' ----------------------------------------------------
  if m.top.debug then
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
    print ghLogHead();"ProcessData -- registry:";ghListSectionData()
    print ghLogHead();"ProcessData -- reg.user:";ghListSectionData("user")
    print ghLogHead();"ProcessData -- content/error: ";content
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
  end if
  ' ----------------------------------------------------
end sub
