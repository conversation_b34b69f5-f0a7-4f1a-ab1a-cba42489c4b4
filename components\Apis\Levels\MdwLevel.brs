sub DataInit()
  m.api.url = m.config.middleware.host + "/level"
  m.api.query.Append({
    "node": m.top.node,
    "user_hash": ghGetRegistry("session_userhash", "user")
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  result = CreateObject("roSGNode", "GHContent")
  ' cintas
  for each row in res
    ' row
    r = CreateObject("roSGNode", "GHContent")
    r.id = row.id
    r.title = row.title
    if m.top.debug then print ghLog<PERSON><PERSON>();"r> ";r.title;" ----------------"

    ' items
    for each item in row.items
      i = CreateObject("roSGNode", "GHContent")
      i.title = item.title
      i.hdposterurl = item.thumbnail
      i.data = item

      if m.top.debug then print ghLogHead();"i> ";i.title

      r.appendChild(i)
    end for

    result.appendChild(r)
  end for

  m.top.content = result
end sub