function init()
  m.title = m.top.findNode("titleText")
  m.title.font = ghGetFont(18, "medium")
  ' m.title.text = gh Translate("playing_modal_keepWatching_title_label", "Continua con tu reproduccion")

  m.buttonOK = m.top.findNode("ok")
  ' m.buttonOK.text = gh Translate("playing_modal_option_button_fromNow", "REANUDAR")

  m.buttonCancel = m.top.findNode("cancel")
  ' m.buttonCancel.text = gh Translate("playing_modal_option_button_fromTheStart", "DESDE EL INICIO")

  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "ButtonSelected")
  m.botonera.ObserveField("backSelected", "BackSelected")
  m.botonera.map = {
    "ok": { "up": invalid, "right": invalid, "down": "cancel", "left": invalid },
    "cancel": { "up": "ok", "right": invalid, "down": invalid, "left": invalid },
  }
end function

sub updateFieldFocus(event)
  data = event.getData()

  if data then
    ghFocusJumpTo("botonera")
  else
    m.buttonOK.focus = false
    m.buttonCancel.focus = false
  end if
end sub

sub ButtonSelected(event)
  child = event.getRoSGNode()

  m.top.selected = child.value
end sub

sub BackSelected() ' event
  ' data = event.getData()

  m.top.selected = ""
end sub