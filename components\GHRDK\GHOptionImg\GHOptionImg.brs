' GHOption
' by<PERSON><PERSON>e(2021) <EMAIL>

function init()
  m.top.debug = false
  m.top.focusable = true
  ' componentes
  m.fondo = m.top.findNode("fondo")
  m.img = m.top.findNode("image")
  m.imgFondo = m.top.findNode("imageFondo") ' la famosa pildora
  m.top.ObserveField("menuSelected", "recalcColors")
  ' refresh()
end function

sub refresh() ' event = invalid
  if m.top.debug then
    print ghLogHead();"refresh -- w=";m.top.width;" h=";m.top.height
    print ghLogHead();"refresh -- translation=";getImageHorizTranslation();" ";getImageVertTranslation()
  end if
  m.img.setFields({ ' icono
    loadingBitmapUri: ghGetImageByMode("avatar.png") ' mientras se carga
    failedBitmapUri: ghGetImageByMode("avatar.png") ' si no tengo imagen
    width: m.top.imageWidth,
    height: m.top.imageHeight,
    translation: [getImageHorizTranslation(), getImageVertTranslation()]
  })
  m.imgFondo.setFields({ ' fondo - pildora
    width: m.top.width,
    height: m.top.height,
  })
  recalcColors()
end sub
function getImageHorizTranslation()
  if m.top.imageHorizAlign = "left" then
    return 0
  else if m.top.imageHorizAlign = "center" then
    return (m.fondo.width - m.img.width) / 2
  else if m.top.imageHorizAlign = "right" then
    return m.fondo.width - m.img.width
  else
    return m.top.imageTranslation[0]
  end if
end function
function getImageVertTranslation()
  if m.top.imageVertAlign = "top" then
    return 0
  else if m.top.imageVertAlign = "center" then
    return (m.fondo.height - m.img.height) / 2
  else if m.top.imageVertAlign = "bottom" then
    return m.fondo.height - m.img.height
  else
    return m.top.imageTranslation[1]
  end if
end function

sub recalcColors()
  if m.top.debug then print ghLogHead();"recalcColors -- focus=";m.top.focus;" selected=";m.top.selected;" menuSelected=";m.top.menuSelected;" menuFocused=";m.top.menuFocused
  if m.top.focus then
    m.img.setFields({ blendColor: m.top.selColor }) ' #FFFFFF
    m.imgFondo.setFields({ visible: true, uri: ghGetImageByMode(m.top.overImageURI) })
    m.imgFondo.blendColor= "#DE1717"
  else
    m.img.setFields({ blendColor: m.top.color }) ' #656767
    m.imgFondo.setFields({ visible: false })
    m.imgFondo.blendColor= "#9B0F0F"
    if m.top.menuSelected then
      m.imgFondo.setFields({ visible: true, uri: ghGetImageByMode(m.top.selectedImageURI) })
      if m.top.menuFocused = true then
        m.img.setFields({ blendColor: m.top.selColor }) ' #FFFFFF
        m.imgFondo.setFields({ visible: true })
      end if
    end if
  end if
end sub
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function
sub updateFieldFocus() ' event
  if m.top.focus then m.top.setFocus(true)
  Refresh()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub

' END FILE ------------------