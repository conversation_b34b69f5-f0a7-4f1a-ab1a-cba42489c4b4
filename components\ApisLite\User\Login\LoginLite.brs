' Login
' -----------------------------

sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/user/v1/ott/login"

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  m.api.query.delete("user_id")
  if m.top.mode = "user-pass" then
    m.api.query.Append({
      "username": m.top.username,
      "password": m.top.password,
      "region": ghGetRegistry("region"),
      "includpaywayprofile": true,
      '"appversion": ghGetAppVersion(),
    })
  else
    m.api.query.Append({
      "userhash": m.top.userhash,
      "region": ghGetRegistry("region"),
      "includpaywayprofile": true,
    })
  end if
  m.logger.debug("DataInit", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })
  if res = invalid then
    m.top.content = {}
    return
  end if

  content = {}
  response = res.data
  errors = ghGetChild(res, "errors")

  if errors = invalid then ' ================= OK

    social = ghGetChild(response, "socialNetworks", [])
    m.logger.debug("ProcessData -- social=", { social: social })

    if social.Count() > 0 then
      m.logger.debug("ProcessData -- social[0]=", { social: response.socialNetworks[0] })
    end if

    content.addReplace("isLoggedIn", true)
    m.logger.debug("ProcessData -- ACCEPTED-TERMS (login) >> ", { accepted_terms: ghGetChild(response, "accepted_terms") })

    content.addReplace("accepted_terms", ghGetChild(response, "accepted_terms", 1))

    ' global
    ' ----------------------------------------------------
    ghSetRegistry("isLoggedIn", "true")
    if m.top.debug then print ghLogHead();"ProcessData -- Check Region -- ";ghGetRegistry("region");" <> ";response.region
    m.global.superhighlight = ghGetChild(response, "superhighlight", [])
    if ghGetRegistry("region") <> response.region then
      content.addReplace("ReloadTranslations", true)
      ghSetRegistry("region", response.region)
    else
      content.addReplace("ReloadTranslations", false)
    end if

    ' --------------------------------
    ' ahora esta en la ProfileLite
    ' --------------------------------
    ' try ' Payment Methods
    '   payMethods = response.paywayprofile.paymentmethods
    '   txtPayMethods = ""
    '   for i = 0 to payMethods.Count() - 1
    '     cat = payMethods[i].user_category
    '     if cat <> invalid then
    '       txtPayMethods += payMethods[i].user_category + "-"
    '     end if
    '   end for
    '   if Len(txtPayMethods) > 0 then
    '     txtPayMethods = Left(txtPayMethods, Len(txtPayMethods) - 1)
    '   end if
    ' catch err
    '   print "ERROR >> ";err
    '   txtPayMethods = ""
    ' end try
    ' --------------------------------


    ' subRegion
    ' -----------------------
    subRegion = response.subregion
    if subRegion = invalid then subRegion = ""

    ' user
    ' ----------------------------------------------------
    ghSetRegistry("user_id", response.user_id, "user")
    ghSetRegistry("parent_id", ghGetChild(response,"parent_id"), "user")
    ghSetRegistry("session_userhash", response.session_userhash, "user")
    ghSetRegistry("user_token", response.user_token, "user")
    ghSetRegistry("user_session", response.user_session, "user")
    ghSetRegistry("language", response.language, "user")
    ghSetRegistry("username", response.username, "user")
    ghSetRegistry("firstname", response.firstname, "user")
    ghSetRegistry("lastname", response.lastname, "user")
    ghSetRegistry("email", response.email, "user")
    ghSetRegistry("region", response.region, "user")
    ghSetRegistry("subregion", subRegion, "user")
    ' ghSetRegistry("paymentMethods_user_category", txtPayMethods, "user") ' ahora esta en la ProfileLite
    ghSetRegistry("country_code", response.country_code, "user")
    ghSetRegistry("HKS", response.session_stringvalue)
    ghSetRegistry("gamification_id", response.gamification_id)

    ' ----------------------------------------------------
    updateGlobalArray("lasttouch", ghGetChild(response, "lasttouch"))
    ' ----------------------------------------------------
    m.top.content = content
  else ' ==================================================== FALLO
    print "################### ";type(errors)
    if type(errors) = "roArray" then
      if errors.count() > 0 then
        errors = errors[0]
      end if
    end if
    content.addReplace("isLoggedIn", false)
    content.error = true
    content.error_code = errors.code
    if content.error_code = "user_login_invalido" or content.error_code = "USR_PSW_00012" then ' errores varios
      content.error_code = ghTranslate("login_error_generic_title", "Valida la información")
      content.error_msg = ghTranslate("login_error_generic_description", "Para continuar, valida la información solicitada en los campos correspondientes. (LOG-01)", {})
    end if
    if content.error_code = "error_params" then ' errores varios
      content.error_code = ghTranslate("login_error_params_title", "Valida la información")
      content.error_msg = ghTranslate("login_error_params_description", "Para continuar, valida la información solicitada en los campos correspondientes. (LOG-01)", {})
    end if
    ' global
    ' ----------------------------------------------------
    ghSetRegistry("isLoggedIn", "false")
    ' ghDeleteSectionRegistry() ' no deberia
    ' user
    ' ----------------------------------------------------
    ghDeleteSectionRegistry("user")
    ' ----------------------------------------------------
    m.top.error = content
  end if
  ' ----------------------------------------------------
  if m.top.debug then
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
    print ghLogHead();"ProcessData -- registry:";ghListSectionData()
    print ghLogHead();"ProcessData -- reg.user:";ghListSectionData("user")
    print ghLogHead();"ProcessData -- content/error: ";content
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
  end if
  ' ----------------------------------------------------
end sub
