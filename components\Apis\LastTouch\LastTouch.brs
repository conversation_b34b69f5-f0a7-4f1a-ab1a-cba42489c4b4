sub DataInit()
  ' m.top.debug = true
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/v1/lasttouch/get"

  m.api.query.Append({
    "user_token": ghGetRegistry("user_token", "user")
    "region": ghGetRegistry("region"),
    '"api_version": invalid ' saco la variable!!
  })
  m.api.query.delete("api_version") ' sin api version
  'm.api.query.delete("HKS")

  if m.top.debug then
    print ghLogHead();"DataInit -- api=";m.api
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 100)
  end if

  if ghGetChild(res, "errors") <> invalid then
    if m.top.debug then print ghLogHead();"ProcessData -- ERROR(!)"
    ' m.top.error = { errors: res.errors }
    m.top.content = {}
    if m.top.debug then
      print " "
      print " "
      print "::::::::::::::::::::::::::"
      print "LAST TOUCH NOT UPDATED"
      print "--------------------------"
      print m.global.lastTouch
      print "::::::::::::::::::::::::::"
      print " "
      print " "
    end if
    return
  end if

  lastTouch = ghGetChild(res, "response.lasttouch")
  m.global.lastTouch = lastTouch ' guardo!!
  if m.top.debug then
    print " "
    print " "
    print "::::::::::::::::::::::::::"
    print "LAST TOUCH UPDATED"
    print "--------------------------"
    print m.global.lastTouch
    print "::::::::::::::::::::::::::"
    print " "
    print " "
  end if

  m.top.content = lastTouch
end sub
