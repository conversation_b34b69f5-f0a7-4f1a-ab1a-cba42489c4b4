<?xml version="1.0" encoding="utf-8" ?>

<component name="RokupayService" extends="Group">

  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="RokupayService.brs"/>
  <script type="text/brightscript" uri="RokupayService_RokuFuncs.brs"/>
  <script type="text/brightscript" uri="RokupayService_ScrFuncs.brs"/>

  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- del servicio -->
    <field id="tick" type="boolean" alwaysNotify="true" onChange="onTick" />
    <!-- comandos -->
    <field id="cmd" type="assocarray" alwaysNotify="true" onChange="onCmd" />
    <!-- respuestas -->
    <field id="cmdResult" type="assocarray" alwaysNotify="true" />
    <!-- internos -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Group id="display" visible="false">
      <Rectangle id="background" />
      <InfoPane id="panel" />
    </Group>
  </children>

</component>