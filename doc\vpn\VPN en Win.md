# VPN en Win



El listado de las VPNs disponibles se encuentra en:

https://dlatvarg.atlassian.net/wiki/spaces/NET/pages/3938418757/Documentaci+n+VPNs+L2TP



## Proceso para incorporar una vpn

1. Armar el comando de inscripción de la vpn
2. Abrir el PowerShell en modo Administrador (importante)
3. Ejecutar el comando de inscripción de la vpn
4. Ejecutar el comando para agregar regla 1
5. Rebootear la máquina
6. En el módulo de vpns de win, editar la conexión a la vpn y dejar cargado el usuario y el password (si no, lo pide cada vez que se conecta)
7. Conectarse a la VPN
8. Habilitar el Mobile Hotspot
9. Abrir el control Panel > View network status and task > Change adapter setting (a la izquierda).
10. Buscar la configuración de la vpn registrada > Botón derecho > Propiedades > Sharing
11. Encender el `Allow other network users to connect through this compurter’s internet conection` 
12. En el combo `Home networking connection` seleccionar `Ethernet 2` > `Ok`
13. Desconectarse de la vpn.
14. Volver a conectarse.
15. En ROKU, seleccionar `Configuración` > `Red` > `Configura la conexión` > `Inalámbrica` 
16. Seleccionar la red de la computadora, ingresar la clave.
17. ROKU ya debería estar conectado.



#### 1. Comando de inscripción de la vpn

Armar el comando de inscripción de la vpn

```bash
Add-VpnConnection -Name 'PreUAT-Mexico' -ServerAddress '**************' -L2tpPsk 'amco2020' -TunnelType L2tp -EncryptionLevel Required -AuthenticationMethod Chap,MSChapv2 -Force -RememberCredential -PassThru
```



#### 2. PowerShell en modo Administrador

Abrir el PowerShell en modo Administrador (importante). Desde el panel de inicio de aplicación, buscar `PowerShell` y en la parte derecha seleccionar `Run as Administrator`



#### 3. Ejecutar el comando de inscripción de la vpn

Ejecutar el comando de inscripcion armado.


![2](img/2.png)



#### 4. Ejecutar el comando para agregar regla

Ejecutar el comando para agregar regla en el mismo PowerShell

```bash
REG ADD HKLM\SYSTEM\CurrentControlSet\Services\PolicyAgent /v AssumeUDPEncapsulationContextOnSendRule /t REG_DWORD /d 0x2 /f
```

![3](img/3.png)

#### 5. Rebootear la máquina

Antes de seguir, es necesario rebootear la máquina para que la regla quede cargada.



#### 6. En el módulo de vpns de win, editar la conexión a la vpn y dejar cargado el usuario y el password (si no, lo pide cada vez que se conecta)

```text
usuario = preuat-global-mex01
clave = amco123
```

<img src="img/2024-05-31%20(1).png" alt="2024-05-31 (1)" style="zoom:50%;" />

<img src="img/2024-05-31%20(2).png" alt="2024-05-31 (2)" style="zoom:50%;" />



#### 7. Conectarse a la VPN

<img src="img/2024-05-31%20(3).png" alt="2024-05-31 (3)" style="zoom:50%;" />



#### 8. Habilitar el Mobile Hotspot

<img src="img/2024-05-31%20(4).png" alt="2024-05-31 (4)" style="zoom:50%;" />

En esta pantalla se puede ver el nombre de la conexión (`BUE-GOOSE`), el password y una lista de dispositivos conectados (aquí se verá cuando roku se consiga conectar).



#### 9. Cambiar la configuración del adaptador

Abrir el `Control Panel` > `View network status and task` > `Change adapter setting` (a la izquierda).

<img src="img/2024-05-31%20(5).png" alt="2024-05-31 (5)" style="zoom: 50%;" />

<img src="img/2024-05-31%20(6).png" alt="2024-05-31 (6)" style="zoom:50%;" />

<img src="img/2024-05-31%20(7).png" alt="2024-05-31 (7)" style="zoom:50%;" />



#### 10. Buscar configuración de compartición

Buscar la configuración de la vpn registrada > `Botón derecho` > `Propiedades` > `Sharing`

![2024-05-31 (8)](img/2024-05-31%20(8).png)



#### 11. Permitir la coneción a Internet

Encender el `Allow other network users to connect through this compurter’s internet conection` 



#### 12. Seleccionar la conexión

En el combo `Home networking connection` seleccionar `Ethernet 2` > `Ok`



#### 13. Desconectarse de la vpn.

<img src="img/2024-05-31%20(3)-1717182061126-18.png" alt="2024-05-31 (3)" style="zoom:50%;" />



#### 14. Volver a conectarse.

<img src="img/2024-05-31%20(11).png" alt="2024-05-31 (11)" style="zoom:50%;" />



#### 15. Configurar red en Roku

En ROKU, seleccionar `Configuración` > `Red` > `Configura la conexión` > `Inalámbrica` 

<img src="img/a.jpg" alt="a" style="zoom: 15%;" />

<img src="img/b.jpg" alt="b" style="zoom:15%;" />

<img src="img/c.jpg" alt="c" style="zoom:15%;" />



#### 16. Seleccionar la red de la computadora, ingresar la clave.

<img src="img/d.jpg" alt="d" style="zoom:15%;" />

<img src="img/e.jpg" alt="e" style="zoom:15%;" />



#### 17. ROKU ya debería estar conectado.

<img src="img/f.jpg" alt="f" style="zoom:15%;" />

<img src="img/g.jpg" alt="g" style="zoom:15%;" />



