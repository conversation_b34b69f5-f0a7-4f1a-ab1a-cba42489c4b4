<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemCarrouselvertical" extends="Group">
  <script type="text/brightscript" uri="ItemCarrouselvertical.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemBack" visible="false"/>
    <Poster id="itemPoster"/>
    <Poster id="itemMask" visible="false"/>

    <Label id="title" visible="true" translation="[10,10]" width="150" color="0xCCCCCC" wrap="true" height="90" lineSpacing="0" vertAlign="bottom" />
    <GHTag id="rating" visible="false" />
    <GHProgressBar id="progress" visible="false" translation="[0,50]" backColor="#2C2C2C" barColor="#DE1717"/>

    <GHPanel id="thePanels"/>
  </children>

</component>