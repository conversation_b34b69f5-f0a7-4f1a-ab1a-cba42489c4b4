sub DataInit()
  m.top.debug = true
  desc = <PERSON><PERSON><PERSON><PERSON>(m.top.description)
  if GetInterface(desc, "ifAssociativeArray") = invalid then
    desc = m.top.description
  end if

  m.api.method = "POST"
  m.api.name = "logs"
  m.api.url = m.config.mfwk.host + "/logs"
  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })
  data = {
    type: m.top.type,
    description: desc,
    "user_token": ghGetRegistry("user_token", "user"),
    "authpn": m.config.mfwk.authpn,
    "authpt": m.config.mfwk.authpt,
    "device_type": m.config.mfwk.device_type,
    "device_model": m.config.mfwk.device_model,
    "device_manufacturer": m.config.mfwk.device_manufacturer,
    "device_category": m.config.mfwk.device_category,
    "format": m.config.mfwk.format,
    "HKS": ghGetRegistry("HKS"),
    "region": ghGetRegistry("region"),
    "appversion": ghGetAppVersion(),
    "user_id": ghGetRegistry("session_userhash", "user"),
  }
  data = {
    logs: [{
      time: "",
      user: {
        "username": "T co",
        "userid": 654321,
        "userhash": "MjE3NjE4Mjd8MTUwMzk1NjA0N3xlYWVmMWNiYmI1ZTk0NWIyZjE3MjQ4Mjg5NWYwNTUyNWZhOGY3Y2JmZmRlMWFhYzMxOQ==",
        "email": "<EMAIL>",
        "suscription": true,
        "region": "ecuador"
      }
      "application": {
        "platform": "Web",
        "version": "TBD"
      },
      "device": {
        "ip": "************",
        "kind": "html5",
        "brand": "windows",
        "model": "html5",
        "os": "Chrome",
        "deviceid": "NA"
      },

      type: m.top.type,
      description: desc,
      "user_token": ghGetRegistry("user_token", "user"),
      "authpn": m.config.mfwk.authpn,
      "authpt": m.config.mfwk.authpt,
      "device_type": m.config.mfwk.device_type,
      "device_model": m.config.mfwk.device_model,
      "device_manufacturer": m.config.mfwk.device_manufacturer,
      "device_category": m.config.mfwk.device_category,
      "format": m.config.mfwk.format,
      "HKS": ghGetRegistry("HKS"),
      "region": ghGetRegistry("region"),
      "appversion": ghGetAppVersion(),
      "user_id": ghGetRegistry("session_userhash", "user"),
    }]
  }

  m.api.body = FormatJSON(data)
  m.api.headers.addReplace("Content-Type", "application/json")

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then print ghLogHead();"log ProcessData=[";res;"][";raw;"]"
  if raw = "true" then
    m.top.content = { result: "OK" }
  else
    m.top.error = { result: "FAILED" }
  end if
end sub


' [23/11/22 10:26] Gerardo Ruben Perosio POST a http://dashboard-producer.clarovideo.net/send
' [23/11/22 10:27] Gerardo Ruben Perosio
' ejemplo={
'   "logs": [{
'     "time": "2022-05-04T09:01:00.000Z",
'     "user": {
'       "username": "T co",
'       "userid": 654321,
'       "userhash": "MjE3NjE4Mjd8MTUwMzk1NjA0N3xlYWVmMWNiYmI1ZTk0NWIyZjE3MjQ4Mjg5NWYwNTUyNWZhOGY3Y2JmZmRlMWFhYzMxOQ==",
'       "email": "<EMAIL>",
'       "suscription": true,
'       "region": "ecuador"
'     },
'     "application": {
'       "platform": "Web",
'       "version": "TBD"
'     },
'     "device": {
'       "ip": "************",
'       "kind": "html5",
'       "brand": "windows",
'       "model": "html5",
'       "os": "Chrome",
'       "deviceid": "NA"
'     },
'     "servername": "************",
'     "cdn": {
'       "node": "************",
'       "X-Cache": "default"
'     },
'     "operator": {
'       "contentid": "",
'       "operatorKey": "pause",
'       "status": 1,
'       "request": "https://mediatest.clarovideo.net/multimediav81/plataforma_vod/MP4/201511/WMP4H17907MTSS_full/WMP4H17907MTSS_full_WV_DASH.ism/dash/WMP4H17907MTSS_full_WV_DASH-video=2524000-1585584.dash",
'       "response": null,
'       "faildata": {
'         "msg": "segment_timeout"
'       }
'   } }]
' }
' [23/11/22 10:27] Gerardo Ruben Perosio cdn no hace falta
' [23/11/22 10:30] Gerardo Ruben Perosio los unicos requeridos dentro de una lista de logs
' [23/11/22 10:31] Gerardo Ruben Perosio user, time y region
' [23/11/22 10:31] Gerardo Ruben Perosio y dentro de operator deberias mandar el mensaje

