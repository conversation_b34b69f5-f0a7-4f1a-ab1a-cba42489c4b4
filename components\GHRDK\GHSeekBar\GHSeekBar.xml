<?xml version="1.0" encoding="utf-8" ?>

<component name="GHSeekBar" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHSeekBar.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>

    <!-- INTERFAZ  -->
    <!-- de entrada -->
    <field id="curValue" type="float" value="0" onChange="refresh" />
    <field id="maxValue" type="float" value="100" onChange="refresh" />
    <field id="stepValue" type="int" value="10" onChange="refresh" />
    <field id="stepMultiplier" type="int" value="5" onChange="refresh" />
    <!-- de entrada: agrego emulacion de keys a la barra, para las keys trapeadas antes -->
    <field id="keyBuffer" type="array" onChange="onKeyBuffer" />
    <!-- de salida -->
    <field id="newValue" type="float" value="0" />
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <!-- <field id="back" type="boolean" value="false" alwaysNotify="true" /> -->

    <!-- VISUAL -->
    <!-- position -->
    <field id="width" type="float" value="150" alias="fondo.width" onChange="refresh"/>
    <field id="height" type="float" value="8" alias="fondo.height" onChange="refresh"/>
    <field id="padding" type="float" value="2" onChange="refresh"/>
    <!-- colores -->
    <field id="barColor" type="string" value="#8A0000" onChange="refreshColor" />
    <field id="barSelColor" type="string" value="#8A0000" onChange="refreshColor" />
    <field id="backColor" type="string" value="#000000" onChange="refreshColor" />
    <field id="backSelColor" type="string" value="#000000" onChange="refreshColor" />
    <!-- boton -->
    <field id="knotUri" type="string" value="pkg:/images/knotknot1.png" onChange="refresh" />
    <field id="knotWidth" type="int"  onChange="refresh" />
    <field id="knotHeight" type="int"  onChange="refresh" />
    <!-- <field id="knotColor" type="string" value="0xFFFFFFFF" onChange="refresh" /> -->
    <field id="knotSelUri" type="string" value="knotknot2.png" onChange="refresh" />
    <field id="knotSelWidth" type="int"  onChange="refresh" />
    <field id="knotSelHeight" type="int"  onChange="refresh" />
    <!-- <field id="knotSelColor" type="string" value="0xFFFFFFFF" onChange="refresh" /> -->
    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />


    <!-- <field id="knotColor" type="string" value="0xFF000088" onChange="refreshColor" /> -->
    <!-- <field id="knotSelColor" type="string" value="0xFF0000FF" onChange="refreshColor" /> -->

  </interface>

  <children>
    <Rectangle id="fondo" opacity="0.5" />
    <Rectangle id="barra" />
    <Poster id="knot" />

  </children>

</component>