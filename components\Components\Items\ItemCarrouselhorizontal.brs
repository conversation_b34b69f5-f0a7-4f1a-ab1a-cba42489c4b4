sub Init()
  if m.top.debug then print ghLogHead();"Init"

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.info = m.top.findNode("info")
  m.progress = m.top.findNode("progress")
  ' m.trash = m.top.findNode("trash")
  m.infoCarrusel = m.top.findNode("infoCarrusel")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 274
  m.itemPoster.height = 154
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height)
  initTimer()
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)



  ' if data <> invalid and getIdMiLista(data.carouselId) or getIdContinuarViendo(data.carouselId) then
  'm.title.setFields({
  '  ' width: m.itemPoster.width - 20
  '  text: ghGetChild(data, "title")
  '  ' text: "titulo de la pelicula largo para ver como queda"
  '  font: ghGetFont(20, "medium")
  '  translation: [0, 160]
  '  color: "0xFFFFFF50"
  '  horizAlign: "center"
  '  vertAlign: "top"
  '})
  'm.trash.translation = [316, 629.33]
  ' m.trash.width = 628.67
  ' m.trash.height = 64
  ' m.trash.uri = "pkg:/images/Molecules_ATV_ButtonOptions.png"
  ' m.info.setFields({
  '   ' text: ghGetChild(data, "title")
  '   text: gh Translate("contentToDelete_hold_ok", "PRESS OK")
  '   font: ghGetFont(20, "medium")
  '   translation: [185, 300]
  '   color: "0xFFFFFF50"
  '   horizAlign: "center"
  '   vertAlign: "top"
  ' })
  ' end if
end sub

sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")

  if data <> invalid then
  end if
end sub

