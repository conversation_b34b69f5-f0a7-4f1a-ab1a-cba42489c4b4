sub DataInit()
  m.top.debug= true
  m.api.url = m.config.mfwk.host + "/services/user/controlpin/channels/add"
  m.api.query.Append({
    "api_version": ghGetChild(m.global.config, "api.version.ControlPinAdd", m.global.config.api.versions.default),
    "region": ghGetRegistry("region")
    "group_id": m.top.group_id
    "user_hash": ghGetRegistry("session_userhash", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' agrego el group_id a la lista de bloqueados
  parental = ghGetChild(m.global, "parental.parental")
  purchase = ghGetChild(m.global, "parental.purchase")
  channels = ghGetChild(m.global, "parental.channels", {})

  channels[m.top.group_id] = m.top.group_id

  m.global.setFields({
    parental: {
      channels: channels
      parental: parental
      purchase: purchase
    }
  })

  m.top.content = { channels: channels }
end sub