
' ----------------------------------------------
' ON GRACE
' ----------------------------------------------
sub ScrOnGrace(data)
  m.logger.debug("Rpay init srcOnGrace", { data: data })

  ' previous focus
  m.prevFocus = m.global.currFocus

  ' builds and load the screen
  m.screenGrace = CreateObject("roSGNode", "RPayOnGraceScreen")
  m.screenGrace.productype = ghGetChild(m.offerSelected, "producttype")
  m.screenGrace.ObserveField("wasClosed", "ScrOnGraceReturn")
  m.top.appendChild(m.screenGrace)
  m.screenGrace.focus = true
end sub

sub ScrOnGraceReturn(event)
  data = event.getData()
  root = event.getRoSGNode()

  m.top.removeChild(m.screenGrace)
  if m.prevFocus <> invalid then
    m.prevFocus.setFocus(true)
  end if

  m.logger.debug("SrcOnGraceReturn", { data: data, value: root.value })

  if ghGetChild(root, "value") = "OK" then
    m.top.cmdResult = {
      "status_subscription": "InGrace"
      "status": "OK",
      "offer": m.offerSelected
    }
    return
  end if

  m.top.cmdResult = {
    "status_subscription": "InGrace"
    "status": "CANCEL"
  }

end sub

' ----------------------------------------------
' SUSPENDED
' ----------------------------------------------
sub ScrSuspended(data)
  m.logger.debug("Rpay init ScrSuspended ", { data: data })

  ' previous focus
  m.prevFocus = m.global.currFocus

  ' builds and load the screen
  m.screenSuspended = CreateObject("roSGNode", "RPaySuspendedScreen")
  m.screenSuspended.productype = ghGetChild(m.purchaseSelected, "data.name")
  m.screenSuspended.ObserveField("wasClosed", "ScrSuspendedReturn")
  m.top.appendChild(m.screenSuspended)
  m.screenSuspended.focus = true
end sub

sub ScrSuspendedReturn(event)
  data = event.getData()
  root = event.getRoSGNode()

  m.top.removeChild(m.screenSuspended)
  if m.prevFocus <> invalid then
    m.prevFocus.setFocus(true)
  end if

  m.logger.debug("ScrSuspendedReturn", { data: data, value: root.value })

  if ghGetChild(root, "value") = "OK" then
    m.top.cmdResult = {
      "status_subscription": "OnHold"
      "status": "OK"
      "offer": m.offerSelected
    }
    return
  end if

  m.top.cmdResult = {
    "status_subscription": "OnHold"
    "status": "CANCEL"
  }
end sub

' ----------------------------------------------
' CANCELED
' ----------------------------------------------
sub ScrCanceled(data)
  m.logger.debug("Rpay init ScrCanceled", { data: data })

  ' previous focus
  m.prevFocus = m.global.currFocus

  ' builds and load the screen
  m.screenCanceled = CreateObject("roSGNode", "RPayCanceledScreen")
  m.screenCanceled.ObserveField("wasClosed", "ScrCanceledReturn")
  m.top.appendChild(m.screenCanceled)
  m.screenCanceled.focus = true
end sub

sub ScrCanceledReturn(event)
  data = event.getData()
  root = event.getRoSGNode()

  m.top.removeChild(m.screenCanceled)
  if m.prevFocus <> invalid then
    m.prevFocus.setFocus(true)
  end if

  m.logger.debug("ScrCanceledReturn", { data: data, value: root.value })

  m.top.cmdResult = {
    "status_subscription": "Canceled"
    "status": "CANCEL"
  }
end sub