sub Init()
  ' m.top.debug = true

  m.versionSuffix = ""
  if ghGetChild(m.global, "model", "") = "lite" then
    m.versionSuffix = "Lite"
  end if

  m.rowConfig = getConfigurationCards()

  ' configuracion de la grilla
  ' tiene los focos, tamaños, etc
  ' lo uso para actualizar la grilla en
  ' caso de eliminar o agregar un carusel
  m.cintas = []

  ' al cargar rows, agrego pendientes que voy a ir restando
  ' a medida que se vayan cargando
  ' al llegar a 0, actualizo la grilla (focos, tamaños, etc)
  m.pendientes = 0

  m.theGrid = m.top.findNode("theGrid")
  m.theGrid.ObserveField("position", "OnRowFocused")
  m.theGrid.ObserveField("value", "OnCardSelect")

  ' configuracion de la grilla, no se altera
  ' m.theGrid.translation = [-50, 0]
  m.theGrid.itemComponentName = "ItemPolymorphic"
  m.theGrid.numRows = 3
  m.theGrid.itemSize = [1280, 210]
  m.theGrid.itemSpacing = [0, 0]
  m.theGrid.rowLabelOffset = [0, 15]
  m.theGrid.showRowLabel = [true]
  m.theGrid.rowFocusAnimationStyle = "fixedFocusWrap"
  m.theGrid.rowCounterRightOffset = 0
  m.theGrid.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  m.theGrid.jumpToRowItem = [0, 0]

end sub

sub updateFieldFocus(event)
  data = event.getData()

  m.theGrid.focus = data
  m.theGrid.setFocus(data)
end sub

' para saber en la home en que carusel estoy
' y asi mostrar cartel si es necesario
sub OnRowFocused(event)
  rowNum = event.getData()[0]

  if rowNum <> invalid then
    cinta = m.theGrid.content.getChild(rowNum)
    m.top.itemFocused = {
      focus: event.getData(),
      cinta: cinta
    }
  end if
end sub

sub OnCardSelect(event)
  data = event.getData()

  m.top.contenido = data
end sub

sub OnTitleChange(event)
  m.title = m.top.findNode("title")
  m.title.visible = false

  title = event.getData()
  if title <> invalid or title <> "" then
    m.title.text = title.Tostr()
    m.title.visible = true
  end if
end sub

sub handleReload()
  cargarLevelUser()
end sub

sub UpdateLevel(event)
  cintas = event.getData()
  m.cintas = cintas

  cargarCaruselesUsuario = false

  if cintas <> invalid then
    content = CreateObject("roSGNode", "GHContent")
    content.Update({ children: cintas }, true)
    m.theGrid.content = content

    refreshTypes(m.cintas)

    cantCintas = cintas.count()

    for c = 0 to cantCintas - 1
      cinta = ghGetChild(cintas, "#" + c.Tostr())

      if ghGetChild(cinta, "data.type") <> "navchilds" then
        if ghGetChild(cinta, "data.properties.byuser") = "false" then
          getRow(cinta)
        else
          ' si tiene caruseles de usuario, cargo level-user
          cargarCaruselesUsuario = true
        end if
      end if
    end for
  end if

  if cargarCaruselesUsuario = true then
    cargarLevelUser()
  end if
end sub

sub getRow(cinta)
  addPendientes(1)

  apiRow = ghCallApi("Row" + m.versionSuffix, "RowOK", "RowERROR", false)
  apiRow.id = cinta.id ' uso el numero de child a la vuelta
  apiRow.rowType = ghGetChild(cinta, "type", "desconocido")
  apiRow.longOKType = ghGetChild(cinta, "longOkType", "")
  apiRow.url = ghGetChild(cinta, "data.properties.url")
  apiRow.control = "run"
end sub

sub RowOk(event)
  data = event.getData()
  cinta = ghGetChild(data, "cinta")

  m.logger.debug("RowOk -- ", { cinta: cinta })

  if cinta.getChildCount() > 0 then
    updateRowLive(cinta.id, cinta)
  else
    ' si el carusel es de usuario, no tiene items y estoy en el nodo mis contenidos, muestro
    ' carusel especial
    if cinta.longOkType <> "" then
      if isNodoMisContenidos(m.apiUserContenido.node) = true then
        isSpecialEmpty(cinta)
      else
        removeRow(cinta.id)
      end if
    else
      removeRow(cinta.id)
    end if
  end if
end sub

sub RowERROR(event)
  data = event.getData()

  print ghLogHead();"RowERROR -- removing row id=";ghGetChild(data, "id")

  id = ghGetChild(data, "id")
  if id = invalid then
    id = ghGetChild(data, "payload.id") ' error de red
  end if

  if id <> invalid then
    removeRow(id)
  end if
end sub

sub cargarLevelUser()
  m.apiUserContenido = ghCallApi("UserLevel" + m.versionSuffix, "UserLevelOK", "UserLevelERROR", false)
  m.apiUserContenido.node = m.top.node
  m.apiUserContenido.control = "run"
end sub

sub UserLevelOK(event)
  data = event.getData()

  cintas = data.cintas

  m.logger.debug("UserLevelOK -- data: ", { cintas: cintas })

  for c = 0 to cintas.count() - 1
    cinta = ghGetChild(cintas, "#" + c.tostr())

    m.logger.debug("llamando a row con cinta: ", { cinta: cinta })

    getRow(cinta)
  end for
end sub

sub UserLevelERROR(event)
  data = event.getData()

  id = ghGetChild(data, "id")
  if id = invalid then
    id = ghGetChild(data, "payload.id") ' error de red
  end if

  if id <> invalid then
    removeRow(id)
  end if

  ' cant = m.aLevelUser.count()
  ' for i = 0 to cant - 1
  '   cinta = ghGetChild(m.aLevelUser, "#" + i.Tostr())
  '   removeRow(cinta)
  ' end for
end sub

' ROW MANAGEMENT
' ------------------------------
sub updateRowLive(id, data)
  rowNumber = getRowNumberById(id)
  rowNumberContent = getRowNumberFromContent(id, m.theGrid.content)

  m.logger.debug("updateRowLive ", { id: data, rowNumber: rowNumber, rowNumberContent: rowNumberContent })

  data.longOkType = data.longOkType
  data.type = data.type

  cinta = m.cintas[rowNumber]
  data.title = cinta.title

  chs = m.theGrid.content

  ' si el primer item tiene id = empty
  ' entonces dejo la cinta como emptywithmessage
  ' si no lo es vuelvo a su tipo original
  primerItem = data.getChild(0)
  if cinta.type = "emptywithmessage" and primerItem.id <> "empty" then
    cinta.type = cinta.oldType
  end if

  ' si no esta visible, lo agrego para
  ' que se actualice la configuracion
  ' y queden bien los focos
  if cinta.visible = false then
    ' cambio la configuracion de la cinta
    cinta.visible = true
    m.cintas[rowNumber] = cinta

    ' obtener el numbero de la cinta
    ' tengo en cuenta cintas no visibles antes de esta ultima y resto,
    ' para obterner el numero de la cinta
    realNumber = getRealNumber(rowNumber)

    ' agrego el contenido
    chs.insertChild(data, realNumber)
  else
    ' solo reemplazo el contenido
    ' la configuracion ya esta visible
    chs.replaceChild(data, rowNumberContent)
  end if

  m.theGrid.content.update(chs)

  donePendiente()
end sub

function getRealNumber(rowNumber)
  result = rowNumber

  for i = 0 to m.cintas.Count() - 1
    cinta = m.cintas[i]
    if i < rowNumber then
      if cinta.visible = false then
        result = result - 1
      end if
    end if
  end for

  return result
end function

sub removeRow(id)
  rowNumberConfig = getRowNumberById(id)
  ' cambio la configuracion para no tener en cuenta este carusel
  m.cintas[rowNumberConfig].visible = false

  rowNumberContent = getRowNumberFromContent(id, m.theGrid.content)
  if rowNumberContent <> invalid then
    chs = m.theGrid.content
    chs.removeChildIndex(rowNumberContent)
    m.theGrid.content = chs
  end if

  donePendiente()
end sub

function getRowNumberFromContent(id, content)
  for i = 0 to content.getChildCount() - 1
    cinta = content.GetChild(i)
    if cinta.id = id then
      return i
    end if
  end for
end function

function getRowNumberById(id)
  for i = 0 to m.cintas.Count() - 1
    cinta = m.cintas[i]
    if cinta.id = id then
      return i
    end if
  end for
end function

sub refreshTypes(content)
  rowHeights = []
  rowSpacings = []
  rowItemSize = []
  rowItemSpacing = []
  focusXOffset = []
  focusBitmapUri = []
  rowLabelOffset = []
  showRowLabel = []
  showRowCounter = []

  cantCintas = content.Count()
  for c = 0 to cantCintas - 1
    cinta = m.cintas[c]

    if cinta.visible = true then
      cType = ghGetChild(cinta, "type", "carrouselhorizontal")
      rowConfig = ghGetchild(m.rowConfig, cType)

      if rowConfig = invalid then
        print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
        print "PANIC: cType: FALTA [";cType;"]"
        print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
      else

        rowHeights.push(ghGetChild(rowConfig, "rowHeights"))
        rowSpacings.push(ghGetChild(rowConfig, "rowSpacings"))
        rowItemSize.push(ghGetChild(rowConfig, "rowItemSize"))
        rowItemSpacing.push(ghGetChild(rowConfig, "rowItemSpacing"))
        focusBitmapUri.push(ghGetChild(rowConfig, "focusBitmapUri", ghGetImageByMode("4px_Focus.9.png")))
        focusXOffset.push(ghGetChild(rowConfig, "focusXOffset"))
        rowLabelOffset.push(ghGetChild(rowConfig, "rowLabelOffset"))
        showRowLabel.push(ghGetChild(rowConfig, "showRowLabel"))
        showRowCounter.push(ghGetChild(rowConfig, "showRowCounter"))
      end if
    end if
  end for

  m.theGrid.setFields({
    rowHeights: rowHeights,
    rowSpacings: rowSpacings,
    rowItemSize: rowItemSize,
    rowItemSpacing: rowItemSpacing,
    focusXOffset: focusXOffset,
    focusBitmapUris: focusBitmapUri,
    rowLabelOffset: rowLabelOffset,
    showRowLabel: showRowLabel,
    showRowCounter: showRowCounter
    rowTitleComponentName: "RowLabelTitle",
  })

  ' curPos = m.theGrid.position
  ' m.theGrid.jumpToRowItem = curPos
end sub

function isSpecialEmpty(cinta)
  ' agrego un child a la cinta vacia
  ' con id "empty" ( se tiene en cuenta en la home)
  child = CreateObject("roSGNode", "GHContent")
  child.setFields({
    id: "empty"
    TITLE: cinta.id
    data: { rowtype: "emptywithmessage" }
  })
  cinta.appendChild(child)

  ' cambio la configuracion para tomar el foco de empty
  rowNumber = getRowNumberById(cinta.id)
  m.cintas[rowNumber].type = "emptywithmessage"

  updateRowLive(cinta.id, cinta)
end function

sub addPendientes(cant)
  m.pendientes = m.pendientes + cant
end sub

sub donePendiente()
  m.pendientes = m.pendientes - 1

  if m.pendientes = 0 then
    refreshTypes(m.cintas)
  end if
end sub