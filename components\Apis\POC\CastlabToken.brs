' CastlabToken
' Ejemplo
' http://34.236.95.18:5002/metadata/?group_id=677538&stream_type=dashwv&content_id=1257872

sub DataInit()
  m.top.debug = true

  m.api.async = false
  m.api.url = "http://34.236.95.18:5002/metadata/"

  ' borro todo lo que hay, no uso parametros generales
  m.api.query = {
    "content_id": m.top.content_id
    "group_id": m.top.group_id
    "stream_type": m.top.stream_type
  }

  if m.top.debug then
    print ghLogHead();"DataInit -- api=", m.api
    print ghLogHead();"DataInit -- api.query=", m.api.query
  end if
end sub

sub ProcessData(res, raw)
  textHtml = Instr(1, raw, "<HTML>")

  if textHtml > 0 then
    m.top.error = res
    return
  end if

  if m.top.debug then
    print ghLogHead();"res= ";res
    print ghLogHead();"raw= ";raw
  end if

  ' por ahora, como viene
  m.top.content = res

end sub