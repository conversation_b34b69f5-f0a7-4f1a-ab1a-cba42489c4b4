<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemPlanselector" extends="Group">
  <script type="text/brightscript" uri="ItemPlanselector.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>

    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" onChange="showfocus"/>
    <field id="rowHasFocus" type="boolean" onChange="showfocus"/>

    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemPoster"/>
    <Label id="title" visible="true" translation="[0,10]" width="185" color="0xCCCCCC" wrap="true" height="30" lineSpacing="0" vertAlign="bottom" />
    <LayoutGroup id="infoPlan" translation="[80,190]" layoutDirection="vert" vertAlignment= "center" horizAlignment= "center" itemSpacings="[20]" visible="true">
      <Poster id="logoAddon" translation="[0,0]" visible= "true"/>
      <LayoutGroup id="infoPlan2" translation="[0,0]" layoutDirection="vert" vertAlignment= "center" horizAlignment= "center" itemSpacings="[3]" visible="true">
        <LayoutGroup id="priceAndCurrency" translation="[0,0]" layoutDirection="horiz" itemSpacings="[5]" visible="true" horizAlignment= "center" vertAlignment= "center">
          <Label id="price" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
          <Label id="currency" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
        </LayoutGroup>
        <Label id="iva" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
      </LayoutGroup>
    </LayoutGroup>
    <Poster id="msgBack" visible="true" width="157" height="24" translation="[4,340]" uri="pkg:/images/pill_plan_selector.9.png" />
    <Label id="suscInfo" visible="true" translation="[4,340]" width= "157" height= "24" color="#FFFFFF" wrap="true" lineSpacing="0" horizAlign="center" vertAlign="center"/>
    <LayoutGroup id="infoCarrusel" translation="[83,460]" layoutDirection="vert" itemSpacings="[8]" visible="true" horizAlignment= "center" vertAlignment= "center">
      <!--Label id="info" visible="false" translation="[50,10]" width="165" color="0xCCCCCC" wrap="false" height="30" lineSpacing="0" vertAlign="center" horizAlign="center" /-->
      <!--Label id="moreInfo" visible="false" vertAlign="center" height="22" horizAlign="center"/-->
    </LayoutGroup>
    <GHProgressBar id="progress" visible="false" translation="[0,50]" backColor="#2C2C2C" barColor="#DE1717"/>
    <GHPanel id="thePanels"/>
  </children>

</component>