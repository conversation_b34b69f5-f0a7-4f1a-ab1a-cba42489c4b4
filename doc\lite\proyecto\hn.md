# Historias de negocio

 [volver](README.md) 



### HN001 Modalidades Premium y Freemium

1. Debe habilitarse en la plataforma la convivencia de las ***modalidades Premium y*** ***Freemium.***
   1. Para ambas modalidades se debe actualizar la UX/UI.



### HN002 : Se mantiene la anterior como Premium

1. Un usuario debe acceder a la ***modalidad Premium*** cuando:
   1. Previamente inició sesión en la plataforma, accedió por IP Telmex o accedió con sesión activa y
   2. ***Si cuenta*** con al menos una suscripción activa/comprada (OTT o IPTV) ó
   3. Realizó una compra o renta de contenido VOD.
2. La ***modalidad Premium*** debe mostrar la experiencia actual de la plataforma.
3. Al usuario en la ***modalidad Premium*** se le debe permitir acceder al contenido, donde:
   1. El tipo de contenido (***Freemium*** o ***Premium***) disponible en esta modalidad , dependerá de lo definido por la operación.



### HN003 : Navega como Freemium

1. El usuario podrá navegar en la ***modalidad Freemium*** ***con*** inicio de sesión o ***sin*** inicio de sesión.
2. Al usuario en la ***modalidad Freemium*** se le debe permitir acceder al contenido, donde:
   1. El tipo de contenido (***Freemium*** o ***Premium***) disponible en esta modalidad , dependerá de lo definido por la operación.



### HN004 :  Publicidad en ambos contenidos

1. Se debe incluir en Claro video con publicidad ***contenido Premium*** y ***contenidos Freemium***



### HN005 : Contenido premium

1. Un ***contenido premium*** es aquel para el cual el usuario necesita suscribirse, comprar o rentar para su reproducción.



### HN006 : Contenido freemium

 Un ***contenido Freemium*** es aquel que el usuario puede visualizar sin necesidad de suscripción, compra o renta.Un ***contenido Freemium*** puede incluir o no publicidad en su reproducción dependiendo de las características del contenido.



### HN007 : Reglas de publicidad

1. A nivel de ***contenido*** se debe poder determinar si se debe ***visualizar con publicidad*** o ***sin publicidad***
   1. Para cada contenido a través de un atributo se determinará si tiene derecho de publicidad o no.
2. Un ***contenido*** puede pertenecer de manera simultánea a la modalidad Premium y Freemium.
   1. Dependiendo si el usuario tiene adquirida la oferta se le presentará una experiencia u otra.
3. Un ***contenido*** puede pertenecer de manera exclusiva a la modalidad Premium o Freemium dependiendo del atributo del contenido.
4. Para ***contenido Freemium*** donde el usuario puede o no estar logueado:
   1. Si el contenido ***no tiene el derecho de publicidad*** debe reproducirse de manera automática ***sin*** presentar la ***publicidad.***
   2. Si el contenido ***tiene el derecho de publicidad*** debe reproducirse de manera automática ***con publicidad*** incluida.
5. Para ***contenido Premium*** se deben conservar las funcionalidades actuales, donde:
   1. Si el usuario ***cuenta con derechos de reproducción*** entonces se debe iniciar la reproducción del contenido.
   2. Si el usuario ***no cuenta con derechos de reproducción*** entonces deberá previamente:
      1. Suscribirse a un add on o paquete
      2. Comprar o 
      3. Rentar el contenido VOD.



### HN008 : Información sobre contenidos freemium

1. Para los ***contenidos*** **Freemium** la plataforma debe identificar al menos la siguiente información:
   1. Dispositivo desde el cual está accediendo el usuario (marca, modelo)
   2. IP desde la cual se visualizó el contenido Freemium
   3. Fecha de visualización
   4. Hora, minuto y segundo de visualización del contenido.



### HN009 : Formato de publicidad

1. Para la ***Etapa 1*** de la iniciativa se debe incluir publicidad en las reproducciones de los contenidos VOD mediante **Google Ad Manager**.
2. Los formatos iniciales de publicidad son los siguientes:
   1. ***Player*** (películas, series y temporadas):
      1. Pre-roll 
      2. Post-roll
   2. ***Marketing***:
      1. Newsletter
      2. Redes sociales (Facebook)
      3. Portales de terceros (Telcel, Telmex, Etc)
3. Los formatos de publicidad en esta etapa son ***banners en imagen***, ***gif*** y ***video (mp4)***. 
4. Se debe permitir en las campañas el link ***vast tag*** para que la agencias puedan modificar la creatividad del banner sin intervención de nuestros equipos.
5. Los espacios de publicidad deben ser ***responsivos*** en cada pantalla y para cada dispositivo y compatibles con ***IAB***.
6. Se debe manejar ***publicidades similares*** para todos los usuarios independientemente del contenido visualizado.
   1. ***No*** se debe contemplar el ***perfilado*** para la visualización de la publicidad.



### HN010 : PreRoll

1. El ***inicio*** de visualización del ***Pre-roll*** debe ocurrir cuando el usuario inicia la reproducción de un contenido para el cual tiene derechos de reproducción. 



### HN011 : PostRoll

1. El ***inicio*** de visualización del ***Post-roll*** para un contenido debe ser al finalizar los ***créditos*** conservando la funcionalidad actual del Fin Player, es decir:
   1. Si el usuario cierra el Fin player o se cierra automáticamente sin interacción del usuario (una vez transcurrido el tiempo definido de visualización), se debe continuar visualizando los créditos, al término de estos se muestra el Post-roll y al finalizar se le dirige a la vCard del contenido visualizado.
2. Cuando el contenido que esté visualizando el usuario corresponda a una ***serie*** y ***cuente*** con ***permisos de reproducción del siguiente episodio***, 
   1. Si el usuario presiona el ***botón*** del ***Fin Player*** o deja correr el ***temporizador*** para el cierre automático:
      1. Si el contenido tiene derechos de publicidad, automáticamente se le debe dirigir al ***Pre-roll*** del siguiente episodio.
      2. Si el contenido no tiene derechos de publicidad, automáticamente se le debe dirigir a la reproducción del episodio.
3. Cuando el contenido que esté visualizando el usuario corresponda a una ***serie*** y ***no cuente*** con ***permisos de reproducción del siguiente episodio***, 
   1. Si el usuario cierra el Fin player, continúa en los créditos se debe habilitar el ***Post-roll*** y al finalizar se le dirige a la vCard del episodio que estaba visualizando.



### HN012 : Continuar viendo

1. Cuando el usuario solicite ***reanudar*** la reproducción de un ***contenido con derechos de publicidad*** desde el flujo ***Continuar reproducción,*** al reanudar la reproducción se debe mostrar el ***Pre-roll***.
   1. Aplica para las dos opciones de reanudación de reproducción incluidas en el formulario: ***Reanudar*** y ***Desde el principio***.



### HN013 : Saltar publicidad

1. Cada dispositivo deberá obtener en la respuesta del ***ad server*** para la campaña específica:
   1. La URL del consumo y
   2. El call to action de la publicidad
   3. Parámetro que indique si la campaña incluye salto de publicidad
   4. Tiempo de inicio de visualización del botón
2. En caso de que en la campaña se deba habilitar el ***botón para saltar la publicidad***:
   1. Se debe tomar por ***default*** el ***tiempo*** de inicio de visualización obtenido en la respuesta del ***ad server***.
   2. Si desde el ad server ***no se obtiene el tiempo*** de inicio de visualización del botón entonces, se debe configurar como valor por ***default en cada operación*** de ***5 segundos*** (default estándar del mercado).
3. Si el usuario ***selecciona*** la opción para saltar la publicidad:
   1. Desde el ***Pre-roll*** se le debe dirigir inmediatamente a la reproducción del contenido.
   2. Desde el ***Post-roll*** se debe cerrar la publicidad y dirigir al flujo correspondiente de acuerdo a las reglas de visualización.
4. Si el usuario ***no** **selecciona*** la opción para saltar la publicidad:
   1. Desde el ***Pre-roll*** se debe visualizar la publicidad completa y al finalizar ésta se debe iniciar la reproducción del contenido.
   2. Desde el ***Post-roll*** se debe visualizar la publicidad completa  y al finalizar se debe dirigir al flujo correspondiente de acuerdo a las reglas de visualización.



### HN014 : Conteidos alcanzados

1. Los ***contenidos*** que deben ser incluidos en ambos formatos (Freemium y Premium) pueden ser:
   1. Contenidos ***propios***, es decir ingestados a través del laboratorio.
   2. Contenidos de los ***addons*** (AtresPlayer, MGM, Edye, etc).
   3. Contenidos de ***terceros*** (Disney, Amazón, etc) 
      1. Para estos contenidos solo tendremos metadata y posters
      2. La reproducción de estos contenidos será en la app del proveedor para los dispositivos que tengamos derecho.



### HN015 : Configuración de contenidos

1. La ***operación***  debe poder ***configurar*** si debe visualizarse en la modalidad del usuario únicamente contenido Freemium o Premium o la mezcla de Freemium y Premium.
2. Si la operación requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO aparezcan en***:
   1. Carruseles
   2. Secciones propias del usuario (mis grabaciones, mis compras) 
   3. Buscador 
   4. Recomendadores.
3. Si la operación requiere mostrar los contenidos ***Freemium*** y ***Premium*** al mismo tiempo:
   1. En caso de acceder al contenido Premium, si el usuario no está logueado se debe presentar la pantalla de identificación  y una vez logueado el usuario, también deberán presentarse los contenidos Freemium y mostrar la publicidad si el contenido tiene el derecho correspondiente.



### HN016 : Inicio de la aplicación

1. Cuando el usuario acceda a ***Claro video,*** desde la URL o desde el dispositivo se le debe dirigir al nodo ***Inicio***.
2. Cuando el usuario acceda a ***Claro video,*** a través de un deeplink  se le debe dirigir al nodo correspondiente indicado en el deeplink.
3. No debe aparecer por default la pagina default actual donde se presenta Inicio de sesión, Registro y Ver gratis.



### HN017 : Inico con IPTelmex

1. Cuando el usuario acceda a la plataforma por IP Telmex, autenticado o con sesión activa:
   1. Si tiene suscripciones activas debe ingresar a la modalidad ***Premium***
   2. Si no tiene suscripciones activas debe ingresar a la modalidad ***Freemium*** con usuario logueado.
2. Se debe asegurar que se conserve la funcionalidad actual del cintillo azul en el flujo de IP Telmex:
   1. Cuando el usuario ha ingresado a la modalidad por IP Telmex, pero aún no tiene asociado un email para navegar fuera de su casa, se debe seguir manteniendo el cintillo azul para completar el registro de datos.



### HN018 : Landing en modo “Classic”

1. Se debe ***retirar únicamente*** de la experiencia ***de Claro video con publicidad*** la ***página de inicio*** donde actualmente mostramos las opciones Regístrate, Inicia sesión y Ver gratis.
2. En los países donde no se habilite Claro video con publicidad se debe seguir manteniendo la ***página de inicio*** de la experiencia actual.



### HN019 : Arte en Login

1. El ***arte*** del formulario de *Inicio de sesión* debe ser ***configurable***.
2. Se debe asegurar que el ***cambio de arte*** en el formulario de *Inicio de sesión* para la iniciativa de *Claro video con publicidad* ***no se replique*** en:
   1. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   2. Las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   3. En dispositivos ***STB***



### HN020 : Arte en Register

1. El ***arte*** del formulario de *Registro* debe ser ***configurable***.
2. Se debe asegurar que el ***cambio de arte*** en el formulario de *Registro* para la iniciativa de *Claro video con publicidad* ***no se replique*** en:
   1. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   2. Las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   3. En dispositivos ***STB***



### HN021 : Nav

1. Se debe preparar el producto para poder manejar un ***árbol*** de navegación para la modalidad ***Premium*** y otro distinto para la modalidad ***Freemium***.
2. La operación podrá definir si se deben visualizar árboles de navegación totalmente diferentes.
3. Para los dispositivos deprecados se deberán mantener los árboles actuales.
4. Para las operaciones donde no esté habilitado Claro video con publicidad se deberán mantener los árboles actuales.
5. Para los dispositivos STB se deberán mantener los árboles actuales.



### HN022 : ?? Sur a Norte

1. Se debe ***mantener*** la funcionalidad actual de ***aprovisionamiento*** de ***Sur a Norte*** y ***Norte a Sur*** para:
   1. Suscripciones
   2. PPE
   3. Compras.



### HN023 :  Etapas de publicidad

1. La iniciativa evolucionará por etapas donde la ***Etapa 1***:
   1. Debe entregarse a finales de ***Febrero*** en ambiente ***UAT*** 
   2. Debe habilitarse únicamente para la operación de ***México***
      1. El resto de las operaciones deben seguir funcionando bajo la modalidad actual. 
      2. Se debe poder activar esta funcionalidad en cada país con base a la necesidad de Negocio.
   3. La visualización de ***publicidad*** debe habilitarse únicamente en ***contenido VOD***.
      1. El alcance no incluye visualización de publicidad en canales lineales. 
   4. ***No*** se debe contemplar el ***perfilado*** de usuarios para la visualización de publicidad.
   5. ***No*** se debe implementar ***publicidad*** en ***carruseles*** y ***superdestacados***.
   6. Debe ***implementarse*** en los dispositivos: ***Web, Android, iOS, STV, Fire TV, Roku y tvOS***
      1. El alcance ***no incluye*** la implementación en los ***STB***
   7. Los dispositivos que ***no*** se ***integran*** directamente con ***Google add manager*** no debe ser condicionante la entrega en esta primera etapa. 
      1. Esto dependerá del análisis técnico de los equipos.
   8. Se debe generar un listado de los ***dispositivos que no soportan técnicamente la nueva funcionalidad***, para poder notificar a las operaciones.
      1. Este listado se debe obtener antes de finalizar enero.
   9. Para esta primera etapa no es condicionante la implementación del player de ***Bitmovin,*** esto dependerá de cada gerente responsable.
2. Se debe asegurar que las reglas y funcionalidades solicitadas para ***Claro video con publicidad*** no afecten:
   1. La experiencia de las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   2. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   3. La experiencia en los ***dispositivos no incluidos*** en el alcance de la ***Etapa 1***.
3. En esta Etapa 1 se debe mostrar la misma publicidad en todos los contenidos Freemium, sin distinción de proveedor o type, permitiendo desde el **Google Ad Manager** configurar la publicidad por dispositivo y operación.



### HN024 : Google Analytics

1. Adicional a los reportes y métricas de requeridas en la sección de ***Consideraciones*** se deben generar reportes sobre las campañas publicitarias considerando impresiones, clicks y CTR. Considerando el siguiente Set Up:[Insumos Analytics](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4085252586) 
   https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4085252586/Insumos+Analytics



### HN025 : Mantener experiencia

1. Cuando el usuario acceda a la plataforma desde un  ***dispositivo no incluido*** en el alcance de la ***Etapa 1***, se debe mantener la ***experiencia actual***.
2. Cuando el usuario acceda a la plataforma en algún dispositivo ***deprecado que no soporte publicidad,*** se debe proporcionar la ***experiencia actual***.
3. Cuando el usuario acceda a la plataforma en una ***operación*** que ***no*** tenga habilitado ***Claro video con publicidad***, se debe proporcionar la ***experiencia actual.***



### HN026 : Mantener el modo “Classic”

1. Se debe asegurar que los cambios de diseño y flujos solicitados para *Claro video con publicidad* no afecten:
   1. La experiencia de las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   2. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   3. La experiencia en los ***dispositivos no incluidos*** en el alcance de la ***Etapa 1***.



### HN027 : Nav

1. El menú de navegación debe ser ***dinámico*** de acuerdo a la modalidad a la cuál esté accediendo el usuario.
   1. Cada operación podrá definir los nodos del menú de navegación para cada modalidad ***Premium*** o ***Freemium***.
2. De acuerdo a la operación a la que acceda el usuario:
   1. En la modalidad ***Premium*** se deben visualizar todos los nodos del producto actual
   2. En la modalidad ***Freemium*** con usuario no logueado se deben visualizar los nodos configurados por la operación.
      1. Si la operación no ha configurado los nodos entonces los nodos default deben ser: *Buscador, Inicio, Canales, Premium y la opción Ingresar*.
   3. En la modalidad ***Freemium*** con usuario logueado se deben visualizar los nodos configurados por la operación.
      1. Si la operación no ha configurado los nodos entonces los nodos default deben ser: *Buscador, Inicio, Canales, Premium,* *Mis contenidos* y *Menú avatar*.
   4. En la modalidad ***Freemium*** para el ***nodo Premium*** cada operación:
      1. Puede habilitar o deshabilitar el nodo.
      2. Puede cambiar el nombre del nodo.
3. Cuando el usuario seleccione la opción ***Ingresar*** se le debe dirigir al formulario de ***Inicio de Sesión***.



### HN028 : Nav

1. Cuando el usuario seleccione el nodo Premium en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes/imágenes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
2. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.



### HN029 : Login

1. Se debe actualizar la pantalla actual (rediseño) de ***Inicio de Sesión*** por cambios de diseño en:
   1. Texto *¿Nuevo en Claro video?*
   2. Opción *REGÍSTRATE*
      1. Incluye una modificación en la morfología del text link a botón.
   3. Texto *¿Necesitas ayuda?*
   4. Text link *Inicia chat*
2. Se deben implementar los cambios con base a los insumos de diseño.



### HN030 : Registro

1. Se debe actualizar la pantalla actual (rediseño) de ***Registro*** por cambios de diseño en:
   1. Texto *¿Ya tienes cuenta?*
   2. Opción *INICIA SESIÓN*
      1. Incluye una modificación en la morfología del text link a botón.
   3. Texto *¿Necesitas ayuda?*
   4. Text link *Inicia chat*
2. Se deben implementar los cambios con base a los insumos de diseño.



### HN031 : Compra en Freemiun > CheckOut

1. Cuando el usuario que accede a la ***modalidad Freemium*** seleccione la opción para iniciar una transacción (*suscripción, compra o renta de contenido VOD*), se le debe dirigir a la pantalla de ***Check out*** para *Claro video con publicidad.*
2. Este flujo aplica cuando el usuario decida iniciar un flujo transaccional desde:
   1. Selector de planes
   2. vCard
   3. Fin player
   4. Formulario de oferta de contratación de canal.



### HN032 : Pantalla CheckOut

1. Aplica cuando el usuario ingresó a la ***modalidad*** ***Freemium*** con ***usuario no logueado***.
2. Se debe implementar para la iniciativa de Claro video con Publicidad la pantalla actual (rediseño) de ***Check out*** con las siguientes actualizaciones:
   1. La pantalla solo debe tener un botón accionable
   2. El texto del botón accionable debe ser: **INGRESA PARA CONTINUAR**
      1. El texto del botón debe ser configurable por operación.
   3. Cuando el usuario seleccione el botón accionable se le debe dirigir a la pantalla de ***Inicio de Sesión***.
3. Se deben implementar los cambios con base a los insumos de diseño.



### HN033 : Chequeo de medio de pago

1. Aplica cuando el usuario ingresó a la ***modalidad*** ***Freemium*** con ***usuario logueado***.
2. Si el usuario cuenta con medio de pago registrado entonces se le debe dirigir a la nueva pantalla ***Continúa con tu contratación*** que le permitirá efectuar la transacción.
3. Si el usuario no cuenta con medio de pago registrado entonces se le debe dirigir a la pantalla para ***Agregar medio de pago*** que le permitirá dar de alta un medio de pago en la plataforma para continuar en el proceso transaccional. 
   1. La pantalla y flujo para ***Agregar medio de pago*** debe ser el mismo que se tiene actualmente en la plataforma (rediseño).



### HN034 : Flujo de compra

1. La pantalla para continuar la contratación debe incluir los mismos elementos de la pantalla actual de Check out, aplicando la siguiente actualización:
   1. El título debe ser: ***Continúa con tu contratación***
   2. El título debe ser configurable por operación
2. Los información visualizada en la pantalla debe cumplir con las reglas, diseño y estructura implementada en la experiencia actual.
3. Los botones accionables deben dirigir al usuario al flujo o pantalla de la experiencia actual.



### HN035 : Omitir anuncio

1. Aplica para ***contenido Freemium*** con derechos de visualización de publicidad.
2. Cuando se inicie la visualización de la publicidad en la reproducción del contenido:
   1. Si el contenido ***incluye la opción para saltar la publicidad*** se debe visualizar la opción OMITIR ANUNCIO a partir del tiempo (segundos) establecido ***en Reglas para saltar publicidad***.
   2. Si el contenido ***no incluye la opción para saltar la publicidad*** no se debe visualizar la opción OMITIR ANUNCIO.
3. La opción OMITIR ANUNCIO debe visualizarse en la publicidad con base a los insumos de diseño:
   1. Con la estructura definida y
   2. En la posición esperada
4. Si el usuario decide ***no saltar la publicidad*** entonces la opción OMITIR ANUNCIO debe permanecer visible hasta que finalice la publicidad. 
5. Si el usuario selecciona la opción OMITIR ANUNCIO:
   1. Desde el Pre roll se le debe dirigir a la reproducción del contenido.
   2. Desde el Post roll se le debe dirigir al flujo establecido con base a las ***Reglas de reproducción del Post roll al término del contenido***



### HN036 : Chapitas

1. Aplica para modalidad ***Premium*** y ***Freemium.***
2. Ningún contenido debe mostrar la chapita Ver ahora en todos los nodos de la plataforma.



### HN037 : Insumos

1. Se debe actualizar la plataforma para la experiencia de Claro video con publicidad considerando los siguientes insumos de diseño:[Insumos de diseño Claro video con publicidad](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4066082896) 
   https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4066082896/Insumos+de+dise+o+Claro+video+con+publicidad



https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Ffreehand%2FCV-ROKU-FHD-TelmexComercial-SGdhbtUGW

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Ffreehand%2FCVROKUFHDTelmexComercial-4wUtMhFAo

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Foverview%2FSpecs_CV_Roku_HD_TelmexComercial-clsi52lhk00r501999wli0d9d%2Fscreens%3FsortBy%3D1%26sortOrder%3D1%26viewLayout%3D1

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Foverview%2FSpecs_CV_ROKU_FHD_TelmexComercial-clsi7mw2h00sn01990qnjfngo%2Fscreens%3FsortBy%3D3%26sortOrder%3D1%26viewLayout%3D1



### HN038 : IPs de Bitmovin

1. Se debe habilitar en el Whitelist de la aplicación las siguientes IPs de Bitmovin para llevar a cabo la iniciativa de Claro Video con Publicidad en el ambiente UAT de Global en México:
   - *************
   - ************
   - ************



### HN039 : !! Nodo Telmex

1. Aplica solo para la modalidad  Freemium.
2. Se debe incluir el nodo Telmex en el menú de navegación, donde de acuerdo a lo requerido por la operación:
   1. El nodo debe poder habilitarse o deshabilitarse
   2. El nombre del nodo debe ser configurable
   3. La posición del nodo debe ser configurable
3. Por default el nodo Telmex debe visualizarse posterior al nodo Premium, donde:
   1. Tiene prioridad la posición del nodo requerida por la operación.
   2. Si el nodo Premium no se encuentra habilitado entonces se recorre la posición a la izquierda
4. Cuando el usuario seleccione el nodo Telmex se le debe dirigir al formulario correspondiente.



### HN040 : !! Nodo Telmex

1. Aplica solo para la modalidad  Freemium.
2. Cuando el usuario seleccione el nodo Telmex en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes/imágenes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
3. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.
4. Un botón accionable que debe dirigir al usuario al flujo transaccional definido para la modalidad Freemium.



### HN041 :  !! Nodo Telcel

1. Aplica solo para la modalidad  Freemium.
2. Se debe incluir el nodo Telcel en el menú de navegación, donde de acuerdo a lo requerido por la operación:
   1. El nodo debe poder habilitarse o deshabilitarse
   2. El nombre del nodo debe ser configurable
   3. La posición del nodo debe ser configurable
3. Por default el nodo Telcel debe visualizarse posterior al nodo Telmex, donde:
   1. Tiene prioridad la posición del nodo requerida por la operación.
   2. Si el nodo Telmex no se encuentra habilitado entonces se recorre la posición a la izquierda.
4. Cuando el usuario seleccione el nodo Telmex se le debe dirigir al formulario correspondiente.



### HN042 :  !! Nodo Telcel

1. Aplica solo para la modalidad  Freemium.
2. Cuando el usuario seleccione el nodo Telcel en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
3. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.
4. Un botón accionable que debe dirigir al usuario al flujo transaccional definido para la modalidad Freemium.



### HN043 : !! Accióm nodo Telmex / Telcel

1. Aplica para la pantalla informativa de los nodos: Premium, Telmex, Telcel.
2. La pantalla informativa debe permitir la inclusión de una opción accionable, de tipo:
   1. Link
   2. Botón
3. La opción accionable debe dirigir al usuario al flujo definido por la operación.



