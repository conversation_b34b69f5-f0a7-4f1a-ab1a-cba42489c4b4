' LANDINGpage
' ----------------------

sub Init()
  m.top.debug = true
  ' temas
  m.top.getScene().updateTheme = m.global.config.theme
  ' fondo
  ' m.backPage = m.top.findNode("backPage")
  ' m.backPage.uri = ghGetAssetByMode ("tyc_back")
  ' logo / titulo
  m.title = m.top.FindNode("overhang")
  m.title.setFields({
    showOptions: false
    logoUri: ghGetAsset("overhang_logo_cv", "pkg:/images/logo.png")
    'title:
    'titleColor: "#FFFFFF"
  })
  ' contenido
  m.apiTerminos = invalid
  m.texto = m.top.findNode("texto")
  m.texto.setFields({
    translation: [120, 100]
    width: 1080
    height: 500
    showhelp: false
    text: "Cargando..."
  })
  ' eventos generales
  m.texto.ObserveField("selected", "OnScrollTextSelected")
  ' carga inicial
  cargarInfo()
  ghFocusJumpTo("texto")
end sub

sub onWasShown() ' event
  GA4Event("screen_view", {
    screen_name: "terminos",
    screen_class: "/login"
  })
end sub

sub cargarInfo()
  if m.top.debug then print ghLogHead();"cargarInfo -- init"
  m.apiTerminos = ghCallApi("ApaTerminos", "onTextFieldChange")
end sub
' EVENTS
' ---------------------
sub onTitleFieldChange() ' event
  if m.top.debug then print ghLogHead();"onTitleFieldChange -- init"
  m.title.setField("title", ghTranslate(m.top.titleField, "Condiciones de uso para México"))
end sub
sub onTextFieldChange()

  if m.top.debug then print ghLogHead();"onTextFieldChange -- init :: ";m.top.textField
  if m.top.debug then print ghLogHead();"onTextFieldChange -- init :: ";type(m.apiTerminos.content)

  ' if m.top.debug then print ghLogHead();"Busco.. ";m.apiTerminos.content, "textos." + m.top.textField

  texto = ghGetChild(m.apiTerminos.content, "textos." + m.top.textField)
  if m.top.debug then print ghLogHead();"onTextFieldChange -- init :: ";type(texto)
  print
  if texto <> invalid then
    m.texto.text = ghReplaceStr(texto, "    ", chr(10))
    m.title2 = m.top.findNode("title2")
    m.title2.text = ghTranslate(m.top.titleField, "Condiciones de uso para México")
    m.title2.visible = "false" 'pasarlo a true si quieren que aparezca el título de en qué sección se está
    m.title2.font = ghGetFont(20, "bold")
    m.title.title = ""
    m.title.visible = "true"
  else
    m.texto.text = "Cargando..."
  end if
end sub



' MANEJO
' ---------------------
sub updateFieldFocus() ' donde me paro cuando arranca la pantalla
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
  ghFocusJumpTo("texto")
  m.texto.reading = true
  if m.top.debug then print ghLogHead();"changeFieldFocus -- end"
end sub
sub OnScrollTextSelected(event)
  if m.top.debug then print ghLogHead();"OnScrollTextSelected -- "
  child = event.getRoSGNode()
  if m.top.debug then print ghLogHead();"OnScrollTextSelected -- selected=";child.selected;" reading=";child.reading;" focus=";child.focus
  if child.reading = false then
    m.top.routerClose = true
  end if
end sub
