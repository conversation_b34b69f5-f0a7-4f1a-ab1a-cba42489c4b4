<?xml version="1.0" encoding="utf-8" ?>

<component name="ChannelsService" extends="Group">

  <script type="text/brightscript" uri="ChannelsService.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="pause" type="boolean" value="false" alwaysNotify="true" onChange="onPauseTurn" />
    <field id="tick" type="boolean" alwaysNotify="true" onChange="onTick" />
    <field id="debug" type="boolean" value="false" onChange="onDebugTurn" />
    <!-- comando forzada -->
    <field id="cmd" type="assocarray" alwaysNotify="true" onChange="onCmd" />
  </interface>

  <children>
    <Group id="display" visible="false">
      <Rectangle id="background" />
      <InfoPane id="panel" />
    </Group>
  </children>

</component>