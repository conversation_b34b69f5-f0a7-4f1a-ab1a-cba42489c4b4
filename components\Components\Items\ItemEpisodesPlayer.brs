sub Init()
  if m.top.debug then print ghLogHead();"Init"

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.info = m.top.findNode("info")
  m.progress = m.top.findNode("progress")
  m.infoCarrusel = m.top.findNode("infoCarrusel")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 274
  m.itemPoster.height = 154
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  drawProgress() ' antes que el titulo
    m.info.setFields({
      text: ghGetChild(data,"episode_number","") + ". " +  ghGetChild(data,"title","") 
      font: ghGetFont(18, "regular")
      translation: [185, 300]
      horizAlign: "center"
      vertAlign: "top"
      color: "0xFFFFFF50"
    })
end sub

sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")

  if data <> invalid then
      if m.top.rowHasFocus = true then
        ' if m.top.focusPercent = 1
        if m.top.itemHasFocus= 1
          m.info.setFields({
            color: "0xFFFFFF"
          })
        else
          m.info.setFields({
            color: "0xFFFFFF50"
          })
        end if
      else
        m.info.setFields({
          color: "0xFFFFFF50"
        })
      end if
    end if
end sub