<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLivePanelMiniEPGItem" extends="Group">
  <script type="text/brightscript" uri="GHLivePanelMiniEPGItem.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="width" type="integer" value="100" onChange="Draw"/>
    <field id="height" type="integer" value="100" onChange="Draw"/>
    <field id="titleWidth" type="integer" value="240" alias="title.width" />
    <field id="backPaddingX" type="integer" value="3.8" onChange="Draw" />
    <field id="backPaddingY" type="integer" value="0,8" onChange="Draw" />
    <field id="cardPaddingX" type="integer" value="8" onChange="Draw" />
    <field id="cardPaddingY" type="integer" value="8" onChange="Draw" />
    <!-- fondos -->
    <field id="border_url" type="string" value="" onChange="Draw" />
    <field id="back_color" type="string" value="0x000000" onChange="Draw" alias="backpanel.color" />
    <!-- datos -->
    <field id="content" type="node" onChange="onContentChange" />
    <field id="channelImage" type="string" />
    <field id="recordatorio" type="boolean" onChange="Draw" />
    <!-- <field id="tag" type="string" onChange="Draw" /> -->
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Poster id="border"/>
    <Rectangle id="backpanel" />
    <LayoutGroup id="card">
      <Poster id="image"/>
      <LayoutGroup id="data" layoutDirection="vert" horizAlignment="left" vertAlignment="bottom" itemSpacings="[5]">
        <Label id="espacio" height="9" text="" />    <!-- de momento esto se está usando para hacer que el texto baje sin modificar el layout, en un futuro si se agrega la opción de grabar, debe ser reemplazado -->
        <Label id="title" text="title" />
        <LayoutGroup id="info" layoutDirection="horiz" horizAlignment="left" vertAlignment="center" itemSpacings="[10]">
          <LayoutGroup id="latTime" layoutDirection="horiz" horizAlignment="left" vertAlignment="center" itemSpacings="[0]">
            <Label id="from" text="from" />
            <Label id="separator" text="*"/>
            <Label id="to" text="to" />
          </LayoutGroup>
          <Poster id="tag"/>
          <Poster id="recordatorio"/>
        </LayoutGroup>
        <GHProgressBar id="avance" visible="false" />
      </LayoutGroup>
    </LayoutGroup>
  </children>

</component>
