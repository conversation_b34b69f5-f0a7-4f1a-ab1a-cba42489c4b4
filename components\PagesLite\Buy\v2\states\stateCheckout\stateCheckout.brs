' stateCheckout
' -----------------------

sub stateCheckout()
  m.logger.debug("stateCheckout", { checkout: m.buy.states["checkout"] })

  substate = m.buy.states["checkout"].state

  if substate = invalid then ' validates incoming data, all ok?
    checkoutCheckData()
  else if substate = "showCheckout" then
    checkoutShowInfo()
  else if substate = "selectPaymentMethod" then
    checkoutSelectPaymentMethod()
  else if substate = "makePurchase" then
    JumpTo("purchase")
  else if substate = "out" then
    JumpTo("out")
  else if substate = "error" then
    jumpTo("out", "error")
  else if substate = "paymentmethodnotimplemented" then ' TODO: que vuelva a pedirlo
    ' hago algo.... despues vuelvo a mandarlo
    JumpTo("purchase")
  else
    a = 10 ' TODO: error interno
  end if

end sub

sub checkoutCheckData()
  methods = ghGetChild(m.buy, "states.start.methods", [])

  m.logger.debug("checkout buySelect", { methods: methods, start_info: ghGetChild(m.buy, "states.start") })

  if ghGetChild(m.buy, "states.start.object_type") = invalid then
    JumpTo("checkout", "error")
  else if methods.count() = 0 then
    JumpTo("checkout", "error")
    ' else if ghGetChild(m.buy, "buypin.controlPIN", "") = "" then ' TODO: puede estar vacio?
  end if

  ' selectedPaymentMethod = ghGetChild(m.buy, "states.start.selectedPaymentMethod")
  selectedPaymentMethod = invalid

  ' si ya tengo un metodo de pago por default
  ' seteo el checkout con el metodo de pago
  if selectedPaymentMethod <> invalid then
    methods = ghGetChild(m.buy, "states.start.methods", [])
    for each item in methods
      if item.gateway = selectedPaymentMethod then
        print "jose encontre"
        m.buy.states["checkout"].paymentMethod = {
          selected: {
            gateway: ghGetChild(item, "gateway", ""),
            data: {
              access_code: ghGetChild(m.buy, "buypin.controlPIN", "")
              object_type: ghGetChild(m.buy, "start.object_type", "")
              buylink: ghGetchild(item, "buylink", "")
              buyToken: ghGetchild(item, "buyToken", "")
            }
          }
        }
        exit for
      end if
    end for
  end if

  jumpTo("checkout", "showCheckout")
end sub

sub checkoutShowInfo()
  ScrCheckoutLite = CreateObject("roSGNode", "CheckoutPaymentReview")
  ScrCheckoutLite.id = "CheckoutPaymentReview"
  ScrCheckoutLite.ObserveField("wasClosed", "checkoutShowInfoReturn")

  m.logger.debug("checkoutShowInfo", { checkout: m.buy.states["checkout"] })

  ScrCheckoutLite.SetFields({
    buyData: {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")

      ' ya viene seteado del paso inicial de checkout
      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
    }
  })

  m.top.routerChild = { page: ScrCheckoutLite }
end sub

sub checkoutShowInfoReturn(event)
  scr = event.getRoSGNode()

  m.logger.debug("checkoutShowInfoReturn", { option: scr.value.option, value: scr.value })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    JumpTo("checkout", "out")
  else if scr.value.option = "SELECT" then
    JumpTo("checkout", "selectPaymentMethod")
  else if scr.value.option = "ACCEPT" then
    JumpTo("checkout", "makePurchase")
  else
    m.logger.error("no se reconoce la seleccion", { option: scr.value.option })
    JumpTo("checkout", "error")
  end if
end sub

sub checkoutSelectPaymentMethod()
  ScrSelect = CreateObject("roSGNode", "checkoutSelectPaymentOption") 'namechanged : checkoutSelectPaymentMehtod'
  ScrSelect.id = "checkoutSelectPaymentMehtod"
  ScrSelect.ObserveField("wasClosed", "checkoutSelectPaymentMethodReturn")
  ScrSelect.SetFields({ methods: ghGetChild(m.buy, "states.start.methods", []) })
  m.top.routerChild = { page: ScrSelect }
end sub

sub checkoutSelectPaymentMethodReturn(event)
  scr = event.getRoSGNode()

  m.logger.debug("checkoutSelectPaymentMethodReturn", { option: scr.value.option, value: scr.value })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    JumpTo("checkout", "showCheckout")

  else if scr.value.option = "ADD" then
    JumpTo("checkout", "out")

  else if scr.value.option = "SELECT" then
    m.logger.debug("selected", { data: scr.value.data })

    m.buy.states["checkout"].paymentMethod = {
      selected: {
        gateway: ghGetChild(scr.value, "data.gateway", ""),
        data: {
          access_code: ghGetChild(m.buy, "buypin.controlPIN", "")
          object_type: ghGetChild(m.buy, "start.object_type", "")
          buylink: ghGetchild(scr.value, "data.buylink", "")
          buyToken: ghGetchild(scr.value, "data.buyToken", "")
        }
      }
    }

    JumpTo("checkout", "showCheckout")
  else
    JumpTo("checkout", "out")
  end if
end sub