<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemCarrouselmenu" extends="Group">
  <script type="text/brightscript" uri="ItemCarrouselmenu.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="foco.width" />
    <field id="height" type="float" alias="foco.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- interfaz interna -->
    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" />
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <!-- para correr todo hacia la derecha, para que entre el foco -->
    <Rectangle translation="[4,0]" width="50" height="36" color="1xffffffff" visible="true" >
      <Rectangle id="background" translation="[0,8]" width="100" height="36" color="#28292F" visible="true" />
      <Poster id="foco" visible="false" uri="" translation="[0,8]" />
      <Poster id="icon" uri="" width="50" height="36" translation="[0,12]" />
      <Label id="label" translation="[7,12]" text="" width="0" height="36" color="0x000000" visible="true"/>
    </Rectangle>
  </children>

</component>