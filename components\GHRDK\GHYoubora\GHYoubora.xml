<?xml version="1.0" encoding="utf-8" ?>
<component name="GHYoubora" extends="YBPluginRokuVideo">

  <script type="text/brightscript" uri="GHYoubora.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- PROPIO, para el seteo del host -->
    <!-- <field id="host" type="string" value="a-fds.youborafds01.com"/> -->

    <!-- Heredado de YBPluginGeneric -->
    <!-- <field id = "videoplayer" type = "node" value = "" /> - Video object from where to listen for events -->
    <!-- <field id = "event" type = "assocarray" /> - Field to emit a youbora event -->
    <!-- <field id = "options" type = "assocarray" /> - Youbora options containing all media related info -->
    <!-- <field id = "logging" type = "boolean" value = "false" /> - Logging flag. Set to true to view Youbora logs -->
    <!-- <field id = "adevent" type = "assocarray" - Field to emit a youbora adevent /> -->
    <!-- <field id = "imaadevent" type = "assocarray" - Field to emit a youbora IMA adevent /> -->
    <!-- <field id = "session" type = "assocarray" /> -->
    <!-- <field id = "monitoring" type = "boolean" /> - Determines if the plugin is actively listening for events. (READ-ONLY) -->
    <!-- Heredado de YBPluginRokuVideo -->
    <!-- <field id = "taskState" type = "string" alwaysNotify = "true"/> -->

    <!-- Especifico -->
    <field id="debug" type="boolean" value="false" onChange="updateDebugGlobal" />

  </interface>

  <children />

</component>
