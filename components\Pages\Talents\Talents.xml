<?xml version="1.0" encoding="utf-8" ?>

<component name="Talents" extends="Page">
  <script type="text/brightscript" uri="Talents.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="data" type="assocarray" />
    <field id="reloadHome" type="boolean" value="false" />
  </interface>

  <children>
    <Label id="title" focusable="false" translation="[0,80]" width="1280" height="48" text="*" />
    <Label id="msg" focusable="false" translation="[0,150]" width="1280" height="48" text="No se encontraron resultados" visible="false" horizAlign="center" />
    <GHLoading id="spinner" visible="true"/>

    <MarkupGrid 
      id="Grid" 
      focusBitmapUri="pkg:/images/FHD/4px_Focus.9.png"
      imageWellBitmapUri=""
      itemComponentName="ItemCarrouselhorizontal" 
      translation="[ 60, 160 ]" 
      numColumns="4" 
      numRows="5" 
      itemSize="[ 274, 154 ]" 
      itemSpacing="[ 20, 20 ]" 
    />
  </children>
</component>
