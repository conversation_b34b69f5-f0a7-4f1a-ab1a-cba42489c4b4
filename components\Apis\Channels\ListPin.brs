sub DataInit()
  ' m.top.debug= true
  m.api.url = m.config.mfwk.host + "/services/user/statuscontrolpin"
  m.api.query.Append({
    "api_version": ghGetChild(m.global.config, "api.versions.StatusControlPin", m.global.config.api.versions.default),
    "region": ghGetRegistry("region")
    ' "user_hash": ghGetRegistry("session_userhash", "user")
    ' "device_id": "roku"
    ' "device_so": "roku"
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
    print ghLogHead();"ProcessData -- response = ";response.pin_channel
    print ghLogHead();"ProcessData -- response = ";response.pin_parental
    print ghLogHead();"ProcessData -- response = ";response.pin_purchase
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  channels = {}

  'pruebas
  ' channels = { "888142": "888142" }

  parental = ghGetChild(response, "pin_parental.info.name", "")
  purchase = ghGetChild(response, "pin_purchase.status", false)

  'status pins
  pin_channel = ghGetChild(response, "pin_channel.status", 0)
  pin_parental = ghGetChild(response, "pin_parental.status", 0)
  pin_purchase = ghGetChild(response, "pin_purchase.status", 0)
  ' si alguno de estos es distinto de 0 es xq tiene pin
  hasAPin = pin_channel <> 0 or pin_parental <> 0 or pin_purchase <> 0

  for i = 0 to ghGetChild(response, "pin_channel.info", []).count() - 1
    item = ghGetChild(response, "pin_channel.info.#" + i.toStr())
    channels[item] = item
  end for

  m.global.setFields({
    parental: {
      channels: channels
      parental: parental
      purchase: purchase
    },
    status_pin: {
      has_a_pin: hasAPin
      pin_channel: pin_channel
      pin_parental: pin_parental
      pin_purchase: pin_purchase
    }
  })

  m.top.content = { channels: channels }
end sub