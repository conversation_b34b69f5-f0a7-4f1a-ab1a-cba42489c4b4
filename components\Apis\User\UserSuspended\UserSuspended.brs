' UserSuspended
' -----------------------

sub DataInit()
  ' m.top.debug = true
  try
    m.api.method = "GET"
    path = ghInsertVersion("/services/payway/paymentservice/{@version}/usersuspended", "UserSuspended")
    m.api.url = m.config.mfwk.host + path
    m.api.query.Append({
      "user_id": ghGetRegistry("user_id", "user")
      "region": ghGetRegistry("region"),
    })
    m.api.query.Delete("api_version")
  catch error
    ghErrorDumpToLog("user/isloggedin", {}, error)
    ghErrorDump(error)
    m.top.content = ghErrorGetJson(error)
    m.top.content.addReplace("isLoggedIn", false)
  end try
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    if raw <> invalid then print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
    print ghLogHead();"ProcessData -- entry= ";res.entry
    print ghLogHead();"ProcessData -- entry= ";res.response
  end if
  result = { "isSuspended": false } ' por defecto lo dejo pasar
  ' -----
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    print ghLogHead();"ProcessData -- errors= ";res.errors[0]
  end if
  ' -----
  resultado = ghGetChild(res, "response")
  if resultado <> invalid then
    ' print "*** ";resultado
    result = resultado
  end if
  ' -----
  if m.top.debug then print ghLogHead();"ProcessData -- content= ";result
  m.top.content = result
end sub
