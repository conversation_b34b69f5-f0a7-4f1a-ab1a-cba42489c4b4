function init()
  m.top.debug = false

  m.profile = m.top.findNode("profile")
  m.profile.ObserveField("selected", "handleProfile")
  m.label = m.top.findNode("label")
  m.label.font = ghGetFont(21, "regular")

  m.lista = m.top.findNode("lista")
  m.lista.ObserveField("value", "OnCardSelect")

  m.map = {
    "lista": { "up": invalid, "right": "profile", "down": invalid, "left": invalid }
    "profile": { "up": invalid, "right": invalid, "down": invalid, "left": "lista" }
  }

  m.profile.setFields({
    imageURI: ghGetAsset("", "pkg:/images/FHD/ic_avatar.png")
    height: 48.08
    width: 48.33
    imageHeight: 38.08
    imageWidth: 38.33
    imageVertAlign: "center"
    imageHorizAlign: "center"
    translation: "[0,11]"
  })

  m.menuBack = m.top.findNode("menuBack")
  m.menuBack.uri = ghGetAsset("", "pkg:/images/TopNavBarBG.png")

  m.logo = m.top.findNode("logo")
  m.logo.uri = ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
end function

' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = true
  if press then
    if key = "back" then ' apreto el back?
      m.top.backSelected = true

      if m.top.debug then print ghLogHead();"onKeyEvent -- Salgo por BACK >>"
      return true ' forzado, sacame de aca.
    end if

    ' if key = "up" then
    ' else if key = "down" then
    if key = "down" or key = "up" then
      m.profile.focus = false

      m.lista.visible = true

      handled = false
      m.lista.jumpToRowItem = [0, 0] ' para que al ir al item se vea el search si se puede
      m.lista.jumpToRowItem = [0, m.valueSelected]
      ' setChilds(m.options)
      childs = setChilds(m.options)

      buildOptions(childs.width)

    else

      focusTo = guessFocusTo(key)

      if focusTo <> invalid then

        cant = m.options.count()

        if focusTo <> "profile" then
          m.lista.jumpToRowItem = [0, cant - 1]
        end if

        turnFocusTo(focusTo)
      end if
    end if
  end if

  return handled
end function

function guessFocusTo(direction)
  current = getCurrentFocus()
  ' a donde voy?
  focusTo = invalid
  if current <> invalid then
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    end if
  end if
  return focusTo
end function

sub turnFocusTo(id)
  current = getCurrentFocus()
  if id <> invalid then
    if current <> id then
      if current <> invalid then
        m.top.findNode(current).focus = false ' apago el actual
      end if
      if m.top.findNode(id).focus <> invalid then
        if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
        m.top.findNode(id).focus = true
      end if
    end if
  end if
end sub

function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if

  return current
end function

sub updateFieldFocus(event)
  m.lista.setFocus(event.getData())

  if event.getData() then
    if m.valueSelected <> invalid then
      m.lista.jumpToRowItem = [0, m.valueSelected]
    end if
  end if
end sub

sub OnCardSelect(event)
  data = event.getData()

  if ghGetChild(data, "noSelection", false) = false then
    ' para que despinte tv-vivo
    if m.valueSelected <> invalid then
      ' setChilds(m.options)
      childs = setChilds(m.options)

      buildOptions(childs.width)

      m.lista.jumpToRowItem = [0, m.valueSelected]
    end if
  end if

  ' m.valueSelected = data.position[1]
  m.top.value = ghGetChild(data, "data.data")
end sub

sub changeContent(event)
  options = event.getData()

  m.padding = 14
  m.itemSpacing = 28
  m.valueSelected = invalid

  m.options = options
  childs = setChilds(options)

  m.lista.content = childs.content

  buildOptions(childs.width)
end sub

sub changeProfile(event)
  data = event.getData()
  m.profile.imageURI = data
end sub

sub handleProfile() ' event
  ' para que no quede el foco en el perfil al hacer click
  m.profile.setFocus(false)
  m.profile.focus = false

  m.lista.jumpToRowItem = [0, 0] ' para que al ir al item se vea el search si se puede
  m.lista.jumpToRowItem = [0, m.valueSelected]

  turnFocusTo("lista")
  ' ===============

  m.top.openProfile = true
end sub

function setChilds(options)
  content = CreateObject("roSGNode", "ContentNode")
  row = CreateObject("roSGnode", "ContentNode")

  sizes = m.itemSpacing ' espacio al inicio

  cant = options.count()
  for i = 0 to cant - 1
    nodo = options[i]
    text = ghGetChild(nodo, "optLabel.props.text", "")

    width = 0
    if text = invalid or text = "" then
      width = ghGetChild(nodo, "optLabel.props.width", 100)
    else
      ' mido tamaño del item del menu
      m.label.text = text
      bounding = m.label.boundingRect()
      width = bounding["width"]
    end if

    item = row.CreateChild("ContentNode")
    item.addField("HDItemWidth", "float", false)
    item.addField("selected", "boolean", false)
    item.addField("data", "assocarray", false)
    item.title = text

    selected = false
    selected = nodo.selected
    if selected = true then
      m.valueSelected = i
    end if

    item.selected = selected
    ' propWidth = ghGetChild(nodo, "optLabel.props.width")

    item.HDItemWidth = width + m.padding
    item.data = nodo

    ' al tamaño final agrego el padding y el spacing entre items
    sizes = sizes + width + m.padding + m.itemSpacing
  end for

  content.AppendChild(row)
  content.AppendChild(content)

  return { content: content, width: sizes }
end function

sub buildOptions(width)
  ' ---------------------------------------
  ' REACOMODAMIENTO MENU ANCHO Y POSICION
  ' ---------------------------------------

  widthlista = width
  if width > 900 then
    width = 900
    widthlista = 850
  end if

  anchoPerfil = m.profile.width + m.itemSpacing

  izquierda = m.logo.translation[0] + 40 ' margen del logo
  resto = 1090 - izquierda ' lo que queda a la derecha del logo / 1280=pantalla
  'resto = 1180 - izquierda ' lo que queda a la derecha del logo / 1280=pantalla. Con esto el menú estaba centrado, se cambió por el QCO-21928
  padDinamico = ((resto - width) / 2) ' agregado por ancho de menu

  if width + anchoPerfil < 835 then
    m.menuBack.setFields({
      "width": width + anchoPerfil
      "translation": [
        izquierda + padDinamico,
        m.menuBack.translation[1]
      ]
    })

    m.lista.setFields({
      ' espacio entre items
      rowItemSpacing: [[m.itemSpacing, 0]]

      ' tamaño de la rowList
      itemSize: [widthlista, 35]

      ' tamaño promedio de cada item, para que cuando se desplace muestre todo el item
      ' rowItemSize: [[(widthlista / m.options.Count(), 35]]
      rowItemSize: [[100, 35]]

      "translation": [
        izquierda + padDinamico + m.itemSpacing
        m.lista.translation[1]
      ]
    })
  else
    m.menuBack.setFields({
      "width": 900
      "translation": [180, 11.5]
    })

    m.lista.setFields({
      rowItemSpacing: [[28, 20]]
      rowItemSize: [[100, 35]]
      ' tamaño de la rowList
      itemSize: [widthlista, 35]
      translation: [190, 10]
    })
  end if

  m.profile.setFields({
    "translation": [
      izquierda + padDinamico + width,
      m.profile.translation[1]
    ]
  })

  m.lista.jumpToRowItem = [0, m.valueSelected]
end sub