# RouterController

## Init

@Public

ComponentController (CC) is a node that responsible to make basic View interaction logic.

From developer side, CC is used to show Views, view stacks for different use cases.

There are 2 flags to handle close behaviour:

- allowCloseChannelOnLastView:bool=true
- allowCloseLastViewOnBack:bool=true

and 4 fields to operate with view stacks that makes available multi stack functionality :

- addStack:string,
- removeStack:string,
- selectStack:string,
- activeStack:string

@Sample: in Scene context in channel

```javascript
  m.top.ComponentController.callFunc("show", {
      view: View
      setFocus: true
  })
```

## Interface

_@Public_ Holds the reference to view that is currently shown. Can be used for hecking in onkeyEvent

```xml
<field id="currentView" type="node" />
```

_@Public_ If developer set this flag channel closes when press back or set close=true on last view

```xml
<field id="allowCloseChannelOnLastView" type="boolean" value="true" alwaysNotify="true" />
```

_@Public_ If developer set this flag the last View will be closed and developer can open another in wasClosed callback

```xml
<field id="allowCloseLastViewOnBack" type="boolean" value="true" alwaysNotify="true" />
```

_@Public @WRITE-ONLY_ Adds new stack assuming given value as new stack ID and makes it active. If there is already stack with such ID (e.g. "default" which always exists) it will become active and a new stack is not added.

```xml
<field id="addStack" type="string" value="" alwaysNotify="true" />
```

_@Public WRITE-ONLY_ Accepts stack ID. If there is a stack with such ID, it gets removed from ComponentController. If active stack gets removed, ComponentController automatically switches to the previously active stack.

```xml
<field id="removeStack" type="string" value="" alwaysNotify="true" />
```

_@Public WRITE-ONLY_ Accepts stack ID. If there is a stack with such ID, ComponentController switches to it and makes it active. Otherwise does nothing.

```xml
<field id="selectStack" type="string" value="" alwaysNotify="true" />
```

_@Public READ-ONLY_ ID of the active stack.

```xml
<field id="activeStack" type="string" value=""/>
```

_@Public_ Function that has to be called when you want to add view to view stack, and set focus to view

```xml
<function name="show" />
```

View manager is a reference to node that handles all View stack functionality

```xml
<field id="ViewManager" type="node" />
```
