# Perfiles



https://dlatvarg.atlassian.net/wiki/spaces/RAF/pages/2981855566/Perfiles+-+AAF





## APA

### profiles_config

Habilita/deshabilita por región el flujo de perfiles:enable: true/false, habilitado y deshabilitado para la regióndefault, si no esta definida la región.

```json
"profiles_config": {
    "default": {
      "enable": false
    },
    "mexico": {
      "enable": true
    }
  }
```



## Selección del Perfil



READ

https://app.swaggerhub.com/apis/ClaroVideo/ProfileRead/1.0.0#/



???

https://app.swaggerhub.com/apis/ClaroVideo/profileList/1.0.0#/Microframework/get_services_user_v1_profile_list





AVATARS
https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029276942/user+profile+avatars




DELETE

https://app.swaggerhub.com/apis/ClaroVideo/profileDelete/1.0.0



UPDATE

https://app.swaggerhub.com/apis/ClaroVideo/profileUpdate/1.0.0



CREATE
https://app.swaggerhub.com/apis/ClaroVideo/profileCreate/1.0.0







controlpinSET

controlpinGET

avatarCollection











- [/user/profile/read](https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029309258) 
- [/user/profile/avatars](https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029276942) 
- [/user/profile/delete](https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029440621) 
- [/user/profile/update](https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029407897) 
- [/user/profile/create](https://dlatvarg.atlassian.net/wiki/spaces/GCV/pages/1029374919) 
- [/user/controlpin/set](https://dlatvarg.atlassian.net/wiki/spaces/USR/pages/3087138915) 
- [/user/controlpin/get](https://dlatvarg.atlassian.net/wiki/spaces/USR/pages/3089432577) 
- [user/profile/avatars/collection](https://dlatvarg.atlassian.net/wiki/spaces/USR/pages/3209036019) 















