# Operation

## Status

4.1 Ok

4.2 Futuro

4.3 *(Roku Event Dispatcher)* ??

4.4 Ok

4.5 Ok

4.6 Ok

4.7 *(Trickplay)* Working 

4.8 Futuro

4.9 *(Instant replay)* Revisar -- Fernando

4.10 *(BookMarks)* Working -- Fernando

4.11 Futuro



## Items

**4.1** Channel updates do not break saved data or require re-activation/re-linking/re-login.

**4.2** Automatic Account Link must be integrated in channels that require a user account to login and that have streamed more than an average of 5 million hours per month over the last three months (changing from the current 5 million hours per month threshold to 1 million hours per month effective after March 31, 2021). This requirement is also applicable to new channels projected to reach the specified streaming hour threshold shortly after launch. This allows the user who has been authenticated on a Roku device to be automatically signed in on other Roku devices. See [Automatic Account Link](https://developer.roku.com/docs/developer-program/authentication/universal-authentication-protocol-for-single-sign-on.md). Streaming hours per month information is available in the Developer Dashboard.

**4.3** Channels that require authentication (SVOD, TVE, and other subscription services) use the [Roku Event Dispatcher](https://developer.roku.com/docs/developer-program/discovery/search/prioritizing-authenticated-channels-in-roku-search.md) to communicate authentication status.

**4.4** The video node must be in focus during full screen video playback when there are no active UI components over the video. This is because Roku [reserves use of the "Options"](https://developer.roku.com/docs/developer-program/design/masterui.md) ("*") button when in this state for the sole purpose of Roku system displays. The "Options" button can, however, be used for additional in-channel options while UI components are displayed on top of the video.

**4.5** The channel may not prevent Roku's system screensaver from activating over UI.

**4.6** The [back button](https://developer.roku.com/docs/developer-program/design/remote-control-buttons.md) directly returns the user to the previous screen and/or state. On the channel's home page, the back button exits the channel and returns the user to the Roku home screen. A channel may display a single confirmation dialog immediately before the user exits the channel—and then return the user to the Roku home screen upon receiving confirmation that they want to leave the channel. Ads must adhere to this requirement as well.

**4.7** Thumbnails must be displayed during [trick play](https://developer.roku.com/docs/developer-program/media-playback/trick-mode/trick-mode.md) for VOD content longer than 15 minutes.

**4.8** Closed captions are available in the channel for live/linear content where required by law. If including closed captions, the channel follows the user global settings for closed caption, and supports the following closed captioning settings in the Options menu: "On," "Off," "On instant replay", and "On mute". For VOD content, captions are in sync with audio. See [Closed Caption Support](https://developer.roku.com/docs/developer-program/media-playback/closed-caption.md).

**4.9** Instant replay must rewind between 10 to 25 seconds.

**4.10** VOD content longer that 15 minutes must include video bookmark functionality that allows the user to resume playback from the last-viewed location. Bookmarks should be persisted for a minimum of 30 days.

**4.11** For all channels that have streamed more than an average of 10 million hours per month over the last three months (changing from the current 10 million hours per month threshold to 5 million hours per month effective after March 31, 2021): Channel must implement voice controls that are supported on the Roku platform. This requirement is also applicable to new channels projected to reach the specified streaming hour threshold shortly after launch. See [Implementing voice controls](https://developer.roku.com/docs/developer-program/media-playback/transport-controls.md).





## Investigación

### 4.1 Channel updates do not break saved data

---

### 4.2 Automatic Account Link must be integrated

---

### 4.3 Channels that require authentication use the Roku Event Dispatcher

---

### 4.4 The video node must be in focus during full screen

---

### 4.5 Not prevent Roku's system screensaver

---

### 4.6.a The back button returns to the previous screen

---

### 4.6.b Home page, the back button exits the channel 

---

### 4.7 Thumbnails

*must be displayed during [trick play](https://developer.roku.com/docs/developer-program/media-playback/trick-mode/trick-mode.md) for VOD content longer than 15 minutes.*



Hay otra alternativa de mostrar las miniaturas en el player y es generando un archivo .bif

 VENTAJA: 

1. Solo se tiene que pasar una URL en el contentNode  
2. Mejora la performance 
3. No hay que hacer resize de imágenes 
4. Te levanta las miniaturas con un diseño pre armado y estandarizado (supongo que es nativo de roku)

![trickPlay](trickPlay.jpg)



<u>Ejemplo:</u>

``` basic
ContentNode.sdbifurl = "https://image.roku.com/ZHZscHItc2Ft/thumbnails/simplevideo-with-bif/video-sd.bif"
```

`Fernando` Roku ya arma todo el diseño hasta con el foco de los bordes en blanco (ver imagen).  Seria lo ideal que nos puedan mandar esa url del archivo .bif en la getmedia e ir probando.



#### Creación del archivo BIF

Es una aplicación que corre por línea de comando. [[Ver Info]][creation]. Tiene librerías viejas. Funciona bien en un Ubuntu18.04

```bash
user@yui18:~/Downloads$ ll
total 166916
drwxr-xr-x  2 <USER> <GROUP>      4096 Feb 24 15:19 .
drwxr-xr-x 27 <USER> <GROUP>      4096 Feb 24 15:14 ..
-rw-------  1 <USER> <GROUP> 169858944 Feb 24 15:19 101.avi
-r-xr-xr-x  1 <USER> <GROUP>    620600 May  9  2017 biftool
-rw-rw-r--  1 <USER> <GROUP>    272709 Feb 24 15:16 biftool_linux.zip
-r-xr-xr-x  1 <USER> <GROUP>    151720 May  9  2017 biftool_processor
user@yui18:~/Downloads$ ./biftool 101.avi 
Finding candidate frames in 1 files
Processing: 101.avi
Detected stream PTS offset of 0ms
stream progress: #frames=13672 PTS=546880ms
stream progress: #frames=27720 PTS=1108800ms
stream progress: #frames=42827 PTS=1713080ms
stream progress: #frames=57768 PTS=2310720ms
stream progress: #frames=72494 PTS=2899760ms
Captured 74130 candidate frames in 51s
Selected 298 BIFs
Success: ./101-fhd.bif (size=5.286MiB, numImages=298, avgSize=18.155KiB)
Success: ./101-hd.bif (size=2.876MiB, numImages=298, avgSize=9.874KiB)
Success: ./101-sd.bif (size=1.328MiB, numImages=298, avgSize=4.555KiB)
user@yui18:~/Downloads$ ll
total 603924
drwxr-xr-x  2 <USER> <GROUP>      4096 Feb 24 15:21 .
drwxr-xr-x 27 <USER> <GROUP>      4096 Feb 24 15:14 ..
-rw-------  1 <USER> <GROUP> 607393792 Nov 11 19:09 101.avi
-rw-r--r--  1 <USER> <GROUP>   5542782 Feb 24 15:21 101-fhd.bif
-rw-r--r--  1 <USER> <GROUP>   3015678 Feb 24 15:21 101-hd.bif
-rw-r--r--  1 <USER> <GROUP>   1392521 Feb 24 15:21 101-sd.bif
-r-xr-xr-x  1 <USER> <GROUP>    620600 May  9  2017 biftool
-rw-rw-r--  1 <USER> <GROUP>    272709 Feb 24 15:16 biftool_linux.zip
-r-xr-xr-x  1 <USER> <GROUP>    151720 May  9  2017 biftool_processor
user@yui18:~/Downloads$ 


```



---

### 4.8 Closed captions

Starting from Roku OS 8, it is no longer necessary for a channel to partake in the closed caption track selection, apart from adding any available tracks to the list of available tracks. the Roku OS now selects a closed caption track based on the preferred caption language selection in the system preferences. When the selected language is not available, it defaults to the system's UI language. ([Documentación][caption])



---

### 4.9 Instant replay must rewind between 10 to 25 seconds.

---

### 4.10 VOD content longer that 15 minutes must include video bookmark.

---

### 4.11 For channels over 10 million hours must implement voice controls.

---

By default, all channels support the following voice commands without any development work required: "fast forward", "rewind", "pause", "resume", and "replay". In order for your channel to support advanced voice commands, which include "seek", "start over", and "next", you must enable the following flags in the [channel manifest](https://developer.roku.com/docs/developer-program/getting-started/architecture/channel-manifest.md):

| Voice Command        | Manifest Entry      |
| :------------------- | :------------------ |
| "seek", "start over" | supports_etc_seek=1 |
| "next"               | supports_etc_next=1 |

If you do not enable these manifest flags, an error message will be displayed when users try to use the "seek", "start over", and "next" voice commands on your channel.

If your channel is using a custom trick mode or it is using a server-side ad insertion (SSAI) implementation of the Roku Advertising Framework (RAF), it must explicitly [handle advanced voice commands](https://developer.roku.com/es-ar/docs/developer-program/media-playback/transport-controls.md#handling-voice-commands) (see the next section) in order for customers to use "seek", "start over", and "next" commands when watching content.

If your channel has content organized in a playlist, and it is using standard trick mode and a client-side RAF implementation, it only must handle the "next" command to play the next clip in the list.

The following table summarizes which channels need to implement handling for advanced voice commands:

| Channel implementation      | Handling "seek" and "start over" commands required? | Handling "next" command required (content is in a playlist)? |
| :-------------------------- | :-------------------------------------------------- | :----------------------------------------------------------- |
| Standard trick mode         | no                                                  | yes                                                          |
| Custom trick mode           | yes                                                 | yes                                                          |
| Client-side RAF integration | no                                                  | yes                                                          |
| SSAI RAF integration        | yes                                                 | Yes                                                          |





## Bibliografía

BIF file creation using the Roku BIF tool
https://developer.roku.com/es-ar/docs/developer-program/media-playback/trick-mode/bif-file-creation.md

BIF file specification
https://developer.roku.com/es-ar/docs/developer-program/media-playback/trick-mode/bif-file-creation.md#bif-file-specification

rokudev/trickplay-samples
https://github.com/rokudev/trickplay-samples

Closed caption
https://developer.roku.com/es-ar/docs/developer-program/media-playback/closed-caption.md



---

[creation]: https://developer.roku.com/es-ar/docs/developer-program/media-playback/trick-mode/bif-file-creation.md

[caption]: https://developer.roku.com/es-ar/docs/developer-program/media-playback/closed-caption.md