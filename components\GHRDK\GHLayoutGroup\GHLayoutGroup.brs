sub Init()
  if m.top.debug then print ghLogHead();"Init"
  ' EVENT
  ' m.top.ObserveField("itemSelected", "OnItemSelected") ' row
  ' m.top.ObserveField("itemFocused", "OnItemFocused")' row
  ' m.top.ObserveField("rowItemFocused", "OnRowItemFocused")' row/item
  ' m.top.ObserveField("rowItemSelected", "OnRowItemSelected") ' row/item
end sub

sub updateFieldFocus(event)
  data = event.getData()
  m.top.setFocus(data)
  if m.top.debug then print ghLogHead(); "updateFieldFocus to "; data
end sub

' ITEM
' -------------------------------------
' sub OnRowItemFocused(event)
'   data = event.getData()
'   if m.top.debug then print ghLogHead();"OnRowItemFocused: ";data
'   m.top.position = data
' end sub
' sub OnRowItemSelected(event)
'   data = event.getData()
'   if m.top.debug then print ghLogHead();"OnRowItemSelected: row=";data[0];" col=";data[1]
'   row = m.top.content.getChild(data[0])
'   item = row.getChild(data[1])
'   if m.top.debug then print ghLogHead();"OnRowItemSelected itemData=";item
'   m.top.value = {
'     position: data,
'     data: item
'   }
' end sub

