sub Init()
  ' m.top.debug = true

  m.rowConfig = getConfigurationCards()

  ' navegacion general de pantalla
  m.map = {
    "theGrid": { "up": invalid, "right": invalid, "down": "administrar", "left": invalid }
    "administrar": { "up": "theGrid", "right": invalid, "down": invalid, "left": invalid }
  }

  ' variables
  m.opcion = invalid

  ' elementos
  m.logo = m.top.findNode("logo")
  m.spinner = m.top.findNode("spinner")
  m.title = m.top.findNode("title")
  m.title.text = ghTranslate("listProfiles_access_title_label", "¿Quién está viendo ahora?") 'qué key de apa va?
  m.title.font = ghGetFont(32, "bold")

  m.administrar = m.top.findNode("administrar")

  m.theGrid = m.top.findNode("theGrid")
  m.theGrid.ObserveField("value", "OnCardSelect")
  ' m.theGrid.ObserveField("position", "OnCardOver")

  m.theGrid.itemComponentName = "ItemPolymorphic"
  m.theGrid.itemSize = [1280, 210]
  m.theGrid.itemSpacing = [0, 0]
  m.theGrid.rowLabelOffset = [50, 15]
  m.theGrid.showRowLabel = [true]
  m.theGrid.rowFocusAnimationStyle = "floatingFocus"
  m.theGrid.rowCounterRightOffset = 0
  m.theGrid.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  m.theGrid.jumpToRowItem = [0, 0]

  componentInit()
  dataInit()

end sub
sub loading(visible as boolean)
  m.spinner.visible = visible
end sub
sub componentInit()
  m.logo = m.top.findNode("logo")
  m.logo.uri = ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
  ' title
  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(32, "bold")
  ' administrar
  m.administrar.text = ghTranslate("listProfiles_access_option_button_manageProfiles", "ADMINISTRAR PERFILES")
  m.administrar.font = ghGetFont(20, "bold")
  m.administrar.ObserveField("selected", "onAdministrar")
end sub

sub dataInit()
  m.logger.debug("dataInit.")
  ghCallApi("ProfileReadLite", "profileReadOK", "profileReadFail")
end sub

sub profileReadOK(event)
  data = event.getData()

  m.logger.debug("profileReadOK.", { data: data })

  m.top.data = data.data

  ' muestro o no el boton de administrar perfiles
  is_kids = m.global.profiles_current?.is_kids

  if is_kids = "false" then
    m.administrar.visible = true
  else
    m.administrar.visible = false
  end if

  setLoading(false)
end sub

sub profileReadFail(event)
  data = event.getData()

  m.logger.debug("profileReadFail.", { data: data })

  onCancel()
end sub

' Entrada / Salida
' -----------------------------
sub refreshData() ' event
  m.logger.debug("refreshData.")
  data = m.top.data

  members = ghGetChild(data, "members", [])

  ' TEST
  ' for v = 1 to 6
  '   members.push({
  '     username: "Username"
  '     user_hash: "userHash=999"
  '     user_image: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar04.png?1574450476"
  '   })
  ' end for

  if members.Count() < 5
    new = {
      id: "new"
      username: ghTranslate("listProfiles_access_addProfile_label", "Agregar Perfil")
      user_image: ghGetImageByMode("addProfile.png")
      user_hash: ""
    }
    members.push(new)
  end if

  perfiles = GridContent(members)

  ' agrego cintas
  result = CreateObject("roSGNode", "GHContent")
  result.appendChild(perfiles.cinta)

  refreshTypesGrid(result, m.theGrid)

  m.theGrid.numRows = 1
  m.theGrid.content = result
  m.theGrid.jumpToRowItem = [0, perfiles.selected]

  itemCount = perfiles.cinta.getChildCount()
  m.theGrid.translation = [calculateLeft(itemCount), 200]

  loading(false)

  turnFocusTo("theGrid") ' cambiar el foco
end sub

function calculateLeft(itemCount as integer) as integer
  screenWidth = 1280
  itemWidth = 210
  visibleItems = Int(screenWidth / itemWidth)

  if itemCount >= visibleItems
    return 0
  else
    totalItemWidth = itemCount * itemWidth
    return Int((screenWidth - totalItemWidth) / 2) - 30 ' 30 para centrar mejor ?
  end if
end function

sub onWasShown()
  if m.top.debug then print ghLogHead();"onWasShown."

  turnFocusTo("theGrid")
end sub

sub updateFieldFocus()
  m.logger.debug("updateFieldFocus", { focus: m.top.focus, cant: m.top.cantidad })

  if m.top.focus then ' entrada - donde me paro cuando arranca la pantalla
    m.prevTheme = ghPushTheme(m.global.config.theme) ' mi tema
    m.top.value = {
      opcion: "NADA"
    }
    turnFocusTo("theGrid")
  else
    if m.top.debug then print ghLogHead();"updateFieldFocus -- ME VOY!!!"
  end if
end sub

' EVENTS
' -----------------------------
sub onCancel()
  LogClear()
  closeScreen({ opcion: "BACK" })
end sub

sub OnCardSelect(event)
  m.opcion = ghGetChild(event.getData(), "data.data", {})
  m.logger.debug("OnCardSelect", { data: m.opcion })
  if m.opcion.id = "new" then
    m.logger.debug("TO BE DEVELOPED || OnCardSelect -- es el new", { option: m.opcion })
    addProfile()
  else
    m.global.profile_img = ghGetChild(m.opcion, "user_image", invalid)
    changeProfile(m.opcion)
  end if
end sub

sub closeScreen(value = invalid)
  if value <> invalid then m.top.value = value
  m.top.focus = false
  ' m.top.wasClosed = true
  m.top.close = true
end sub

sub onAdministrar(event)
  m.logger.debug("onAdministrar")

  ' para apagar el foco y que al volver de adminstrar, no este prendido en el boton
  turnFocusTo("theGrid")

  m.ProfManage = CreateObject("RoSGNode", "ProfileManagementPageLite")
  m.ProfManage.id = "ProfileManagementPageLite"
  m.ProfManage.ObserveField("wasClosed", "onAdministrarBack")
  m.top.routerChild = { page: m.ProfManage } ' modo router
end sub

sub onAdministrarBack(event)
  m.logger.debug("onAdministrarBack")
  dataInit()
end sub

' PROFILE LOGIN
' ------------------------------
sub changeProfile(perfil)
  m.logger.debug("changeProfile to >>", { perfil: perfil })
  login = ghCallApi("LoginLite", "changeProfileBackOk", "changeProfileBackFail", false)
  login.userhash = perfil.user_hash
  login.mode = "userhash"
  login.control = "run"
end sub

sub addProfile()
  m.logger.debug("addProfile >> NEW")
  m.ProfManage = CreateObject("RoSGNode", "ProfileEditPageLite")
  m.ProfManage.id = "ProfileEditPageLite"
  m.ProfManage.ObserveField("wasClosed", "EditProfileBack")
  m.top.routerChild = { page: m.ProfManage } ' modo router
end sub
sub EditProfileBack(event)
  data = event.getData()
  m.logger.debug("EditProfileBack >>", { data: data })
  dataInit()
end sub

sub changeProfileBackOk(event)
  data = event.getData()
  m.logger.debug("changeProfileBackOk")
  m.global.profiles_current = m.opcion ' ahora sí, actualizo el elegido
  ghSetRegistry("gamification_id", m.global.profiles_current.gamification_id)

  print "changeProfileBackOk CURRENT>>> ";m.global.profiles_current

  ghCallApi("PushSession", "pushSessionBack", "pushSessionBack")
end sub

sub pushSessionBack()
  m.logger.debug("pushSessionBack.")
  closeScreen({ opcion: "SELECT" })
end sub

sub changeProfileBackFail(data)
  m.logger.debug("changeProfileBackFail to >>", { data: data })
  closeScreen({ opcion: "ERROR", data: data })
end sub

function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press

  handled = true ' siempre manejo

  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    else
      onCancel()
    end if
  end if

  return handled
end function

function GridContent(members)
  m.logger.debug("GridContent", { membersCount: members.Count() })

  ' detecto en cual me tengo que parar.
  current = ghGetChild(m.global, "profiles_current")

  m.logger.debug("GridContent current=", { current: current })

  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.type = "ProfileSelect"
  obCinta.visible = true

  selected = 0
  for v = 0 to members.Count() - 1
    op = members[v]
    op.rowType = "ProfileSelect"
    op.cantTotal = members.Count()

    m.logger.debug("item Perfil ", { perfil: op.username })

    if current <> invalid then
      if op.gamification_id = current.gamification_id then
        selected = v
      end if
    end if

    it = CreateObject("roSGNode", "GHContent")
    it.id = v
    it.data = op
    obCinta.appendChild(it)
  end for

  return { cinta: obCinta, selected: selected }
end function
