# ToDo


## Global

### Blindajes

- Falla de red
	- Revisar que pasa cuando hay falla de red en general. ROKU lo maneja?
	- Hay que hacer el heartBeat?
	- En caso de falla de red, se puede hacer un dump provisional a disco para mandarlo cuando vuelva la red?
- Agregar el CallBack de error a todos los llamados de `api`. Importante definir en cada situación qué es lo que tiene que hacer el `app`

### Logs

- Manejo de debug de errores en la `app`
	- Hay algún servicio que sirva o hay que fabricar uno? 
		- Posibilidad de recibir llamados en el míddleware que lo único que hagan sea escribir en el `log`, inclusive en otro archivo. Esto podría permitir que tengamos los errores como hagan falta en el SumoLogic.





## Páginas

### HomePage

#### General

- Implementación de un Loading.
- Agregar manejo de `deeplink`
	- Agregar el `deeplink` a la interfaz de entrada.
	- Agregar el llamado automático a `vcard` a la interfaz de salida. Para `deeplink`
- Agregar salida de canal forzada para el nodo `homeuser`.
- Revisar y adecuar el diseño general del componente.

#### Componentes implicados

- LevelComponent
- MenuComponent



### VCard

#### General

- (!) Esta página hay que armarla por completo. *Hay que documentar todos los flujos de datos de este componente, par tener una imagen general antes de diseñarlo*.

* Definir interfaz de entrada para recibir info de `deeplink`.
* Definir interfaz de entrada que funcione igual desde `LevelComponent` y desde `Search`.
* Definir interfaz de salida para que vuelva al lugar correcto desde `LevelComponent` y desde `Search`.

#### Componentes implicados

- GHButtonImg
- GHProgressBar
- TSelComponent
- EpisodeComponent
- TalentComponent
- RecommendComponent



### EdPerfil

- (!) Esta página hay que armarla por completo. *Hay que documentar todos los flujos de datos de este componente, par tener una imagen general antes de diseñarlo*.

#### Componentes implicados

- AvatarSelComponent



### Search

- Migración del componente al directorio correspondiente, y revisar dependencias.
- Modificar la interfaz de salida (cierre), para que se ajuste al llamado desde menu.



### Player

- Revisar dependencias.
- Agregar manejo de `api:getmedia`, para videos tipo `singleaudio`.





## Componentes

### LevelComponent

#### General

- Blindar cuando falla la `api:level`. Pantalla de falla.
- Pensar en un `stack` de nodos.
	- que guarde los últimos nodos, en el caso de apretar `back` se podría recorrer ese stack, cuya primera opcion siempre es la home. 
	- Se tiene que autoresetear cuando pasa por el home.
- Agregar la definición de un level home.
- Revisar el tema de floating y wrapping en la configuración: *en el diseño aparece la highlight como wrapped y las otras como normales, pero eso no se puede elegir para cada cinta.*
- Revisar y adecuar el diseño general del componente.

#### Rows

- Revisar blindaje cuando falla la `api:row`.
- Armar un config en archivo que se levante a global *(ojo, no se puede leer el file dentro del componente, porque lo haría mil veces)*
- Falta documentación sobre los tipos de row que pueden venir. Ahora tenemos `Highlight`, `Carrouselhorizontal` y `Carrouselvertical`, y tener en cuenta que en realidad el vertical es horizontal pero mas chico... Hay que rever esto.
- Probar la posibilidad de generar un row donde cada item tenga un formato distinto: *entiendo que a futuro van a querer hacer eso*.

#### Title

- (!) Este componente hay que armarlo por completo. *Falta documentación de cómo viene la info de los titulos complejos*.
- Investigar si con un componente propio sigue funcionando la numeración de items en la parte derecha de la cinta.

#### Items

- Definir cómo se que el item es un subnodo.
- Probar llamado al LevelComponent desde el evento de un item.

#### Componentes implicados

- ItemComponent
- TitleComponent





### ItemComponent

#### General

- Emprolijar el armado de los diferentes tipos de componentes.
- Armar un config en archivo que se levante a global *(ojo, no se puede leer el file dentro del componente, porque lo haría mil veces)*
- Agregar los items complejos:
	- Item redondo
	- Item de recomendacion

#### Componentes implicados

- GHProgressBar






### MenuComponent

#### General

- Armar llamado a la Nav desde la aplicación. *Falta documentación de la nav*.
- Armar un config en archivo que se levante a global *(ojo, no se puede leer el file dentro del componente, porque lo haría mil veces)*.
- Agregar la funcionalidad para las imágenes que requiere diseño.
- Algoritmo que levante los datos de la `api:nav` y los mergee con los de las opciones fijas de forma apropiada. 
- Revisar el manejo de alineación vertical de los items.

#### Componentes implicados

- GHOption
- GHOptionImg



### TitleComponent

- (!) Este componente hay que armarlo por completo.



### TSelComponent

- (!) Este componente hay que armarlo por completo.

#### Componentes implicados

- GHRowBar



### EpisodeComponent

- (!) Este componente hay que armarlo por completo.

#### Componentes implicados

- GHRowBar
- ItemComponent



### TalentComponent

- (!) Este componente hay que armarlo por completo.

#### Componentes implicados

- GHRowBar
- TalentItemComponent



### TalentItemComponent

- (!) Este componente hay que armarlo por completo.

 

### RecommendComponent

- (!) Este componente hay que armarlo por completo.

#### Componentes implicados

- GHRowBar
- ItemComponent



### AvatarSelComponent

- (!) Este componente hay que armarlo por completo.

#### Componentes implicados

- GHWallPanel





## GHRDK

### GHOption

- Agregar la funcionalidad para las imágenes de fondo y selección que requiere diseño.




### GHOptionImg

- Revisar la funcionalidad de formato de imagen en el componente: *requiere mucha configuración para la ubicación*




### GHButtonImg

- (!) Este componente hay que armarlo por completo.
- Agregar interfaz de entrada para `disable` y `description` (el texto abajo del boton)



### GHProgressBar

- (!) Este componente hay que armarlo por completo.
- Tiene que tener la posibilidad de cálculo *on the fly* de tamaño y porcentajes.



### GHRowBar

- (!) Revisar funcionalidad para ver si se ajusta a todas las necesidades



### GHWallPanel

- (!) Este componente hay que armarlo por completo.
- Hay que revisar la factibilidad de este componente.

#### Componentes implicados

- GHOptionImg (?)





## Investigación a futuro

- Se puede hacer la implementación de un Markup mínimo? un detector de `hx`?







