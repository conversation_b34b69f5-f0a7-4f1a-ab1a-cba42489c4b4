<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLivePanel" extends="Group">
  <script type="text/brightscript" uri="GHLivePanel.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>

    <!-- interfaz de entrada -->
    <field id="xresult" type="integer" />

    <field id="jumpTo" type="integer" onChange="onJumpTo" />
    <field id="channels" type="array" onChange="onChannelsUpdate" />
    <field id="info" type="assocarray" />
    <field id="data" type="node" />

    <!-- interfaz de salida -->
    <field id="selected" type="assocarray" alwaysNotify="true" />
    <!-- cmd -->
    <field id="cmd" type="assocarray" alwaysNotify="true" />

    <!-- interno -->
    <field id="debug" type="boolean" value="false" />

  </interface>

  <children>

    <!-- reales -->
    <Rectangle id="pBackground" />
    <GHJoystick id="joystick" translation="[10,30]" width="600" height="100" offset="20" title="pruebaJoyStick" />
    <Label id="lblAudios"/>

    <!-- para pruebas -->
    <Label id="title" focusable="false" translation="[10,10]" width="1280" height="48" text="GHLivePanel" color="0x00FFFF"/>
    <GHButtonGroup id="botonera" layout="childs" orientation="vertical" exitLeft="true" exitRight="true">
      <GHButton value="masUno" id="btnMasUno" width= "100" height= "50" text="+1" translation="[1000,5]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
      <GHButton value="masDiez" id="btnMasDiez" width= "100" height= "50" text="+10" translation="[1000,45]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
      <GHButton value="menosDiez" id="btnMenosDiez" width= "100" height= "50" text="-10" translation="[1000,85]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
      <GHButton value="menosUno" id="btnMenosUno" width= "100" height= "50" text="-1" translation="[1000,125]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
    </GHButtonGroup>

  </children>

</component>