<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2016 Roku Corp.  All Rights Reserved. -->

<component name="Request" extends="Task">
  <interface>

    <field id = "host" type = "string" value = "" />
    <field id = "service" type = "string" value = "" />
    <field id = "args" type = "assocarray" />

    <!-- <field id = "config" type = "assocarray" /> -->
    <field id = "response" type = "string" />

  </interface>

  <script type="text/brightscript" uri="Request.brs"/>
  <script type="text/brightscript" uri="Utils.brs"/>
</component>