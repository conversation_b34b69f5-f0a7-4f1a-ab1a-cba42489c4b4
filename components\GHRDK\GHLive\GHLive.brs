sub Init()
  m.top.debug = true
  m.logger = CreateLogger()

  m.msgError = m.top.findNode("msgError") ' seteo de mensaje de error de player
  m.error_image = m.top.findNode("error_image")
  m.error_image.uri = ghGetAsset("message_warning_icon", ghGetImageByMode("peligro.png"))
  m.error_description = m.top.findNode("error_description")
  m.error_description.text = ghTranslate("error_channel_description", "Continúa viendo tu programación favorita probando con otro canal.")
  m.error_description.font = ghGetFont(24, "regular")
  m.error_title = m.top.findNode("error_title")
  m.error_title.font = ghGetFont(32, "bold")
  ' CHANNELS -----------------------
  m.pChannels = m.top.findNode("channelsGrid")
  m.pChannels.ObserveField("visible", "onChannelVisibleChange")
  m.pChannels.ObserveField("cmd", "onCmd")
  m.pChannels.ObserveField("keypressed", "onKeyPressed")
  ' EPG ----------------------------
  m.pEPG = m.top.findNode("timeGrid")
  m.pEPG.ObserveField("visible", "onGridVisibleChange")
  m.pEPG.ObserveField("cmd", "onCmd")
  m.pEPG.ObserveField("keypressed", "onKeyPressed")
  ' MINI EPG -----------------------
  m.pMain = m.top.findNode("panel")
  m.pMain.ObserveField("visible", "onMainVisibleChange")
  m.pMain.ObserveField("cmd", "onCmd")
  m.pMain.ObserveField("keypressed", "onKeyPressed")
  m.channelPosition = 0

  m.options = m.top.findNode("options")
  m.options.ObserveField("selected", "onOptionSelected")
  m.options.ObserveField("visible", "onOptionVisibleChange")
  m.options.ObserveField("keypressed", "onKeyPressed")

  m.top.ObserveField("channelPosition", "onChangePosition")
  m.top.ObserveField("availableAudioTracks", "onAvailableAudioTracks")
  m.top.ObserveField("availableSubtitleTracks", "onAvailableSubtitleTracks")
end sub
' EVENTS
' -----------------------------
sub onShowError(event)
  data = event.getData()
  if ghGetChild(data, "title", "") <> "" then
    m.error_title.text = ghGetChild(data, "title", "")
    m.msgError.visible = true
  else
    m.msgError.visible = false
  end if
end sub
sub onFocusChange(event)
  data = event.getData()
  if data = true then
    if m.pChannels.visible = true then
      m.pChannels.focus = true
    else if m.pEPG.visible = true then
      m.pEPG.focus = true
    else if m.pMain.visible = true then
      m.pMain.focus = true
    end if
  end if
end sub
sub onChangeVisible(event)
  data = event.getData()
  print ghLogHead();"onGridVisibleChange -- data=";data
  if data = false then
    m.pChannels.visible = false
    m.pEPG.visible = false
    m.pMain.visible = false
  else
    ' m.pMain.visible = true
  end if
end sub
sub showGridChannels() 'event
  m.pChannels.jumpTo = m.channelPosition
  m.pChannels.visible = true
end sub
sub showMainPanel() 'event
  m.pMain.jumpTo = m.channelPosition
  m.pMain.visible = true
end sub
sub onChangePosition(event)
  data = event.getData()
  m.channelPosition = data
  m.pChannels.jumpTo = data
  m.pEPG.jumpTo = data
end sub
sub onEventsChange(event)
  data = event.getData()
  events = ghGetChild(data, "result")
  m.pEPG.data = events
  m.pMain.events = events
end sub
sub onChannelsChange(event)
  data = event.getData()
  m.pChannels.channels = data
  m.pMain.channels = data
  ' si refresco canales, por candados
  ' tambien tengo que refreshcar epg
  events = ghGetChild(m.top.events, "result")
  if events <> invalid then
    m.pEPG.data = events
    m.pMain.events = events
  end if
end sub
' KEY HANDLING
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press

  handled = true ' que no bubblee

  if press then
    handled = false

    ' MINI EPG
    if key = "OK" then ' MINI EPG ??
      print ghLogHead();"onKeyEvent ***** MAIN ***************"
      if m.top.state = "buffering" then
        m.pMain.visible = false
      else
        m.pMain.jumpTo = m.channelPosition
        m.pMain.visible = true
      end if
      handled = true
    else if key = "down" then ' EPG
      print ghLogHead();"onKeyEvent ***** EPG ******************"
      if m.top.state = "buffering" then
        m.pEPG.visible = false
      else
        m.pEPG.jumpTo = m.channelPosition
        m.pEPG.visible = true
      end if
    else if key = "up" then ' LISTA DE CANALES
      print ghLogHead();"onKeyEvent ***** LISTA DE CANALES *****"
      if m.top.state = "buffering" then
        m.pChannels.visible = false
      else
        m.pChannels.jumpTo = m.channelPosition
        m.pChannels.visible = true
      end if
      handled = true
    else if key = "play" or key = "right" or key = "left" or key = "fastforward" or key = "rewind" or key = "replay" then
      handled = true ' NO HAGO NADA
    end if
  end if

  return handled
end function

sub onChangeVisibleScreen(event)
  screen = event.getData()
  ' print ghLogHead();"onChangeVisibleScreen > ";screen
  if screen = "" then
    m.pMain.available = true ' solo si no hay otra abierta
  else
    m.pMain.available = false
  end if
end sub

' LISTADO DE CANALES
' ------------------------------------------
sub onChannelVisibleChange(event)
  data = event.getData()

  print ghLogHead();"onChannelVisibleChange -- data=";data

  if data then
    m.top.visibleScreen = "channel"
  else
    m.top.visibleScreen = invalid
    m.pChannels.visible = false
    m.pChannels.setFocus(false)
    m.top.setFocus(true)
  end if
end sub

' EPG
' ------------------------------------------
sub onGridVisibleChange(event)
  data = event.getData()

  if data then
    m.top.visibleScreen = "epg"
  else
    m.top.visibleScreen = invalid
    m.pEPG.visible = false
    m.pEPG.setFocus(false)
    m.top.setFocus(true)
  end if
end sub

' MINI EPG
' ------------------------------------------
sub onMainVisibleChange(event)
  data = event.getData()

  print ghLogHead();"onMainVisibleChange -- data=";data

  if data then
    m.top.visibleScreen = "miniepg"
    m.pMain.SetFocus(true)
  else
    m.top.visibleScreen = invalid
    m.pMain.SetFocus(false)
    m.top.SetFocus(true)
  end if
end sub
sub onCmd(event)
  cmd = event.getData()

  print ghLogHead();"CMD :: ";cmd

  if cmd.cmd <> invalid then ' manejo de comandos
    if cmd.cmd = "changeChannel" then
      program = cmd.program
      channel = cmd.channel

      m.top.selected = {
        group_id: ghGetChild(channel, "group_id"),
        name: ghGetChild(channel, "title"),
        number: ghGetChild(channel, "number"),
        channel_id: ghGetChild(channel, "id"),
        channelPosition: ghGetChild(channel, "channelPosition"),
        program: program
        actionType: "change"
      }
    else if cmd.cmd = "showOptions" then
      showOptions(cmd)
    end if
  end if

  m.pMain.cmd = {} ' limpio el valor
end sub

sub showOptions(data)
  channel = data.channel
  program = data.program

  channels = ghGetChild(m.global, "parental.channels", {})
  groupId = ghGetChild(channel, "group_id", "")

  options = []
  if channels[groupId] <> invalid and channels[groupId] <> "" then
    options.push({
      title: ghTranslate("live_panel_options_desbloquear", "Desbloquear canal"),
      actionType: "unblock",
      data: channel,
      icon: ghGetAsset("playingLive_alert_unlockChannel_icon", ghGetImageByMode("unblock.png")) ' falta key
    })
  else
    options.push({
      title: ghTranslate("live_panel_options_bloquear", "Bloquear canal")
      actionType: "block",
      data: channel,
      icon: ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))
    })
  end if

  favorites = ghGetChild(m.global, "favorites.channels", {})
  groupId = ghGetChild(channel, "group_id", "")

  if favorites[groupId] <> invalid and favorites[groupId] <> "" then
    options.push({
      title: ghTranslate("live_panel_options_delfavorite", "Eliminar canal de favoritos")
      actionType: "unfavorited",
      data: channel,
      icon: ghGetAsset("playingLive_alert_channelFavorite_icon")
    })
  else
    options.push({
      title: ghTranslate("live_panel_options_addfavorite", "Añadir canal a favoritos")
      actionType: "favorited",
      data: channel
      icon: ghGetAsset("playingLive_alert_channelFavorite_icon")
    })
  end if

  for each item in m.languagesFinal
    options.push({
      title: item.label_large
      actionType: "changeAudio",
      data: item
      icon: ghGetAsset("playingLive_alert_channelFavorite_icon")
    })
  end for

  m.options.options = options
  m.options.channel = channel
  m.options.program = program
  m.options.visible = true
end sub

sub onOptionVisibleChange(event)
  data = event.getData()

  if data then
    m.top.visibleScreen = "options"
  else
    m.top.visibleScreen = invalid
    m.top.setFocus(true)
  end if
end sub

sub onOptionSelected(event)
  info = event.getData()

  if info <> invalid then
    if ghGetChild(info, "actionType") = "changeAudio" then
      m.logger.debug("cambiando audio", { info: info })
      data = info.data

      if data.itemActivacion = true then
        m.top.globalCaptionMode = data.activate
        ' cambio el titulo al item activar/desactivar subtitulo, de la lista de idiomas
        ls = m.audioPanel.langData
        for each l in ls
          if l.itemActivacion = true then
            if m.top.globalCaptionMode = "On" then
              l.activate = "Off"
              l.label_large = "Desactivar subtitulos"
            else
              l.activate = "On"
              l.label_large = "Activar subtitulos"
            end if
          end if
        end for
        m.options.options = ls
      else
        ' guardo la preferencia de audio y subtitulo
        ghSetRegistry("preference_live_audio", LCase(ghGetChild(data, "audio", "")), "user")
        ghSetRegistry("preference_live_subtitle", LCase(ghGetChild(data, "subtitle", "")), "user")

        if data.audio_track = invalid and data.subtitle_track = invalid then
          m.logger.debug("cambiando single audio ")
          ' single audio, continuo para llamar a getMedia
          m.top.selected = data
        else
          ' tengo audio o subtitulo
          ' cambio los track sin llamar a la getMedia
          if data.audio_track <> invalid and data.audio_track <> "" then
            m.logger.debug("cambiando multiple audio ")
            m.top.audioTrack = data.audio_track
          end if

          if data.subtitle_track <> invalid and data.subtitle_track <> "" then
            m.logger.debug("cambiando subtitulo ")
            m.top.subtitleTrack = data.subtitle_track

            m.top.globalCaptionMode = "On"
            setGlobalCaption("On")
          else
            m.top.globalCaptionMode = "Off"
            setGlobalCaption("Off")
          end if
        end if
      end if
    else
      if ghGetChild(info, "actionType") <> "change" then
        m.pEPG.refresh = true
      end if

      name = ghGetChild(info, "data.title", "")
      if name = "" then
        name = ghGetChild(info, "data.name", "")
      end if

      ' por si viene un node o un assocarray
      selected = {
        actionType: ghGetChild(info, "actionType")
        channelposition: ghGetChild(info, "data.channelposition")
        id: ghGetChild(info, "data.id")
        group_id: ghGetChild(info, "data.group_id")
        name: name
        number: ghGetChild(info, "data.number")
        data: info.data
      }

      m.top.selected = selected
    end if
  end if
end sub

sub onKeyPressed(event)
  data = event.getData()

  m.top.keypressed = data
end sub

' sub onAvailableAudioTracks() ' event
'   m.logger.debug("onAvailableAudioTracks")

'   LogicaMacabra()
' end sub

' sub onAvailableSubtitleTracks(event)
'   data = event.getData()

'   m.logger.debug("onAvailableSubtitleTracks", { data: data })

'   if data.Count() > 0 then
'     LogicaMacabra()
'   end if
' end sub

' sub handleLanguages(event)
'   data = event.getData()

'   m.logger.debug("handleLanguages", { data: data })

'   m.languages = data
' end sub

' sub LogicaMacabra()
'   if m.languages = invalid then
'     m.logger.debug("aun no tengo la info de los idiomas")
'   end if

'   if m.top.availableAudioTracks = invalid then
'     m.logger.debug("aun no tengo la info de los audios")
'   end if

'   if m.top.availableSubtitleTracks = invalid then
'     m.logger.debug("aun no tengo la info de los subtitulos")
'   end if

'   m.logger.debug("languages", { options: ghGetChild(m.languages, "options", []), audio: ghGetChild(m.languages, "audios"), subtitles: ghGetChild(m.languages, "subtitles") })
'   m.logger.debug("available audios/Subtitle", { subtitles: m.top.availableSubtitleTracks, audios: m.top.availableAudioTracks })

'   subtitleOptions = ghGetChild(m.languages, "subtitles.options", {})
'   audioOptions = ghGetChild(m.languages, "audios.options", {})

'   m.logger.debug("subtitleOptions", subtitleOptions)
'   m.logger.debug("audioOptions", audioOptions)

'   finalLanguages = []

'   if m.top.globalCaptionMode = "On" then
'     itemActivate = { is_current: false, itemActivacion: true, activate: "Off", label_large: "Desactivar subtitulos" }
'   else
'     itemActivate = { is_current: false, itemActivacion: true, activate: "On", label_large: "Activar subtitulos" }
'   end if
'   finalLanguages.push(itemActivate)

'   ' agrego a la lista de idiomas los tacks de audio y subtitulos
'   ' asi al elegir de la lista, si tiene track no llamo a la getMedia
'   for each item in ghGetChild(m.languages, "options", [])
'     m.logger.debug("current item", item)

'     if item.audio <> invalid then
'       m.logger.debug("audio seleccionado", { item_audio: item.audio, option_seleccionado: audioOptions[item.audio] })

'       if audioOptions[item.audio] <> invalid then
'         for each audio in m.top.availableAudioTracks
'           m.logger.debug("audio track", { audio: audio, compara: audioOptions[item.audio] })
'           if audio.Language = audioOptions[item.audio] then
'             item.audio_track = audio.Track
'             exit for
'           end if
'         end for
'       end if
'     end if

'     if item.subtitle <> invalid then
'       m.logger.debug("subtitle seleccionado", { item_subtitle: item.subtitle, option_seleccionado: subtitleOptions[item.subtitle] })

'       ' en las opciones de subtitulos de la get Media viene clave/valor que tiene el codigo ej: "es"
'       ' busco en las opciones de subtitulos del player que la propiedad Language sea igual al codigo clave/valor que viene en la getMedia
'       if subtitleOptions[item.subtitle] <> invalid then
'         for each subtitle in m.top.availableSubtitleTracks
'           if subtitle.Language = subtitleOptions[item.subtitle].internal then
'             item.subtitle_track = subtitle.TrackName
'             exit for
'           end if
'         end for
'       end if
'     end if

'     if item.is_current then
'       m.top.audioTrack = item.audio_track
'       m.top.subtitleTrack = item.subtitle_track
'     end if

'     finalLanguages.push(item)
'   end for

'   m.logger.debug("final languages", finalLanguages)

'   m.languagesFinal = finalLanguages
' end sub