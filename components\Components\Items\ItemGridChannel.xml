<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemGridChannel" extends="Group">
  <script type="text/brightscript" uri="ItemGridChannel.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>

    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" onChange="showfocus"/>
    <field id="rowHasFocus" type="boolean" onChange="showfocus"/>

    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Rectangle id="background" translation="[0,0]" width="273" height="154" color="#1A1A1A" visible="true">
      <Poster id="itemPoster"/>
      <Poster id="candado" visible="false" />
      <Poster id="corazon" visible="false" />
      <Label id= "channelNumber" translation="[5,10]" text="123" width="60" height="50" color="#FFFFFF" visible="true"/>
    </Rectangle>

  </children>

</component>