<?xml version="1.0" encoding="utf-8"?>
<component name="CustomMarkGrid" extends="MarkupGrid">
	<script type="text/brightscript" uri="CustomMarkGrid.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
	<interface>
		<!-- Grid configuration -->
		<field id="gridType" type="string" value="service" onChange="onGridDataChanged" />
		<field id="gridData" type="array" onChange="onGridDataChanged" />
		<field id="centerGrid" type="boolean" value="true" />
		<field id="gridX" type="integer" value="0" />
		<field id="gridY" type="integer" value="150" />
		<field id="numColumns" type="integer" value="4" />
		<field id="numRows" type="integer" value="2" />
		<field id="itemWidth" type="integer" value="400" />
		<field id="itemHeight" type="integer" value="200" />
		<field id="itemSpacingX" type="integer" value="30" />
		<field id="itemSpacingY" type="integer" value="30" />
		<field id="selected" type="string" />
		<field id="debug" type="boolean" value="false" />
	</interface>
</component>
