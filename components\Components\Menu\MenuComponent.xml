<?xml version="1.0" encoding="utf-8" ?>

<component name="MenuComponent" extends="Group">

  <script type="text/brightscript" uri="MenuComponent.brs" />
  <script type="text/brightscript" uri="MenuComponentFunctions.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Screen.brs" />

  <interface>
    <field id="menuSelected" type="string" value="" alwaysNotify="true" />
    <field id="action" type="assocarray" />
    <field id="backSelected" alwaysNotify="true" type="boolean" value="false" />
    <field id="refresh" alwaysNotify="true" type="boolean" onChange="changeNav" />
    <field id="reloadHome" alwaysNotify="true" type="boolean" />

    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
		<function name="getMenuSelected" />
  </interface>

  <children>
    <GHMenuRowList id="menu" translation="[0,0]" />
  </children>

</component>
