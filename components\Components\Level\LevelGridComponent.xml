<?xml version="1.0" encoding="utf-8" ?>

<component name="LevelGridComponent" extends="Page">

  <script type="text/brightscript" uri="LevelGridComponent.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="node" type="string" value="invalid" />
    <field id="reload" type="boolean" value="false" alwaysNotify="true" onChange="handleReload" />
    <field id="cintasLevel" type="array" onChange="updateLevel" />
    <field id="title" type="string" value="" onChange="OnTitleChange" alwaysNotify="true" />
    <!-- interfaz de salida -->
    <field id="contenido" type="assocarray" alwaysNotify="true" />
    <field id="itemFocused" type="assocarray" alwaysNotify="true" />
    <field id="longOkCintas" type="assocarray" />
  </interface>

  <children>
    <Label id="title" text="" translation="[0,-15]" width="1180" height="60" horizAlign = "center" vertAlign = "center"/>
    <GHRowList id="theGrid" />
  </children>

</component>
