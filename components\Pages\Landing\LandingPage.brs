' LANDINGpage
'

sub Init()
  m.top.getScene().updateTheme = m.global.config.theme
  ' general de pantalla
  m.map = { "b01": { "up": invalid, "right": invalid, "down": invalid, "left": invalid, "default": true } }
  ' botonera
  m.b01 = m.top.findNode("b01")
  m.b01.ObserveField("selected", "OnButtonSelected")
  m.b01.ObserveField("backSelected", "OnExitChannel")
  ghSetBackground(ghGetAssetByMode("landing_access_background", "")) 'de momento faltaría tener el key y nombre de la imagen
  m.logo = m.top.findNode("logo")
  m.logo.uri = ghGetAssetByMode("landing_head_logoClarovideo")
  ' texts
  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(40, "bold")
  m.title.text = ghTranslate("landing_menu_title_label", "¡Todo lo que te gusta en sólo lugar!", {})
  m.descrip = m.top.findNode("descrip")
  m.descrip.font = ghGetFont(22, "bold") 'revisar el tema del tamaño en pantalla, el invision pide 24px pero se corta
  m.descrip.text = ghTranslate("landing_menu_description_label", "El mejor contenido, canales de TV, películas y series.", {})
  m.btnRegister = m.top.findNode("btnRegister")
  m.btnRegister.text = ghTranslate("landing_menu_option_button_register", "REGÍSTRATE", {})
  m.btnLogin = m.top.findNode("btnLogin")
  m.btnLogin.text = ghTranslate("landing_menu_option_button_login", "INICIA SESIÓN", {})
  ' ----------------------------
  ' ATENCION!
  ' Si estoy en esta pagina
  ' tengo que estas deslogueado
  ' ----------------------------
  ghSetRegistry("isLoggedIn", "false")
  ghDeleteSectionRegistry("user")
  ' print ghListSectionData()
  ' print ghListSectionData("user")
  ' ----------------------------
end sub

sub onWasShown() ' event
  m.top.signalBeacon("AppDialogInitiate")
  turnFocusTo("b01")
  eventBody = {
    parent_id: ghGetRegistry("parent_id", "user")
    user_id: ghGetRegistry("user_id", "user")
    screen_name: "landing",
    screen_class: "/login"
  }
  GA4Event("screen_view", eventBody)
end sub

sub updateFieldFocus() ' donde me paro cuando arranca la pantalla
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
  turnFocusTo("b01")
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
end sub
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if m.top.debug then print ghLogHead();"OnButtonSelected -- "
  if child.selected then
    child.selected = false
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
    m.top.signalBeacon("AppDialogComplete")
    m.top.routerChild = { page: child.value }
  end if
end sub
sub OnExitChannel() ' event
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%%%%  OUT OF CHANNEL  %%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  m.top.GetScene().exitChannel = true
end sub


' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
  end if
  return handled
end function
function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function

