<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="LoginPage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="LoginPageLite" extends="Page">

  <script type="text/brightscript" uri="LoginPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <!-- <script type="text/brightscript" uri="pkg:/components/DeepLinking.brs" /> -->

  <interface>
    <field id="inFlow" type="boolean" value="false" />
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />
    <Label id="title" focusable="false" translation="[416,64]" width="504" height="48" text="Inicia sesión" />
    <Label id="descrip" focusable="false" translation="[416,136]" width="504" height="32" text="¿Cuál es tu correo electrónico?" />
    <!-- Botonera -->
    <GHButtonGroup id="botonera" layout="map" orientation="vertical" handleKey="false">
      <GHInput id="user" placeholder="Usuario" translation="[400,200]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="false" width="504" height="72" title="Inicia sesión" titleColor="0xFFFFFF" message="¿Cuál es tu correo electrónico?" messageColor="0xFFFFFF"/>
      <GHInput id="pass" placeholder="Password" translation="[400,280]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="true" width="504" height="72" title="Inicia sesión" titleColor="0xFFFFFF" message="Contraseña" messageColor="0xFFFFFF"/>
      <GHButton value="Login" id="login" text="SIGUIENTE" translation="[400,424]" color="0xFFFFFF" selColor="#FFFFFF" width="504" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
      <GHButton value="Back" id="cancel" text="CANCELAR" translation="[400,487.33]" color="0xFFFFFF" selColor="#FFFFFF" width="504" height="72" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" />
      <GHButton value="RegisterPage" id="toRegister" text="¿Nuevo en Claro video? Regístrate" translation="[656,576]" width="248" height="72" color="0xFFFFFF" selColor="0xFFFFFF" selBackColor="#212224" focusColor="0xFFFFFF" backColor="#212224" />
    </GHButtonGroup>
    <!-- PopUps -->
    <GHError id="error" />
    <GHLoading id="loading" visible="false"/>
  </children>

</component>
