<component name="ItempaymentOption" extends="Group">
	<script type="text/brightscript" uri="ItempaymentOption.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
	<interface>
		<field id="width" type="float" />
		<field id="height" type="float" />
		<field id="itemContent" type="node" onChange="showContent" />
		<field id="debug" type="boolean" value="false" />
	</interface>
	<children>
		<!-- Background Rectangle -->
		<Rectangle id="container" color="#2E303D" translation="[0,0]" />
		<Group id="contentGroup" translation="[0,0]">
			<Label id="label" horizAlign="center" vertAlign="center" />
			<Poster id="image" loadSync="true" />
		</Group>
	</children>
</component>
