' GHLivePanelMiniEPGItem
' ------------------------------

sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init ** "
  ' componentes
  m.border = m.top.findNode("border")
  m.backpanel = m.top.findNode("backpanel")

  m.card = m.top.findNode("card")

  m.image = m.top.findNode("image")
  m.title = m.top.findNode("title")
  m.from = m.top.findNode("from")
  m.separator = m.top.findNode("separator")
  m.to = m.top.findNode("to")
  m.avance = m.top.findNode("avance")
  m.avance.backColor = "#2C2C2C"
  m.avance.width = "216"
  m.tag = m.top.findNode("tag")
  m.recordatorio = m.top.findNode("recordatorio")
  m.data = m.top.findNode("data")


  ' eventos generales
  m.top.ObserveField("width", "Draw")
  m.top.ObserveField("height", "Draw")
  ' acciones
  InitialDraw()
end sub

' FORMATO
' ------------------------------
sub InitialDraw()
  if m.top.debug then print ghLogHead();"InitialDraw ** "
  m.image.setFields({
    uri: ghGetAsset("mini_epg_event_default", "pkg:/images/Placeholder_Epg_event.png") ' acá va la key
    width: 152
    height: 86
    loadDisplayMode: "scaleToFit"
  })
  m.tag.setFields({
    uri: "pkg:/images/tag_ya_emitido.png"
    'height: 24
    'width: 70
  })
  m.recordatorio.setFields({
    uri: "pkg:/images/ic_epg_watch.png"
    height: 24
    visible: false
  })
  m.title.setFields({
    text: ghTranslate("epg_loading", "Title")
    font: ghGetFont(24, "regular")
    color: "#FFFFFF"
  })
  m.from.setFields({
    text: "from"
    font: ghGetFont(18, "regular")
    color: "#FFFFFF"
  })
  m.to.setFields({
    text: "to"
    font: ghGetFont(18, "regular")
    color: "#FFFFFF"
  })
  m.separator.setFields({
    text: " -"
    font: ghGetFont(18, "latFrom")
    color: "#FFFFFF"
  })
end sub
sub Draw()
  if m.top.debug then print ghLogHead();"Draw ** "
  ' card
  m.card.setFields({
    layoutDirection: "horiz"
    horizAlignment: "left"
    vertAlignment: "top"
    itemSpacings: 10
    translation: [m.top.backPaddingX + m.top.cardPaddingX, m.top.backPaddingY + m.top.cardPaddingY]
  })
  ' border
  m.border.setFields({
    width: 484
    height: 133.8
    translation: [-10.4, -14]
    uri: m.top.border_url
  })
  if m.top.debug then print ghLogHead();"Draw border -- ";m.border.width;" ";m.border.height;" ";m.border.uri;" ";m.border.loadStatus
  ' back
  m.backpanel.setFields({
    color: m.top.back_color
    width: 456
    height: 104
    translation: [m.top.backPaddingX, m.top.backPaddingY]
  })
  ' tag
  if m.top.debug then print ghLogHead();"Draw -- tag ";m.top.tag
  ' if m.top.tag = "ahora" then
  '   m.tag.uri = "pkg:/images/tv_ahora.png"
  '   m.tag.visible = true
  ' else if m.top.tag = "mas tarde" then
  '   m.tag.uri = "pkg:/images/tv_mas_tarde.png"
  '   m.tag.visible = true
  ' else
  '   m.tag.visible = false
  ' end if
  ' recordatorio
  if m.top.debug then print ghLogHead();"Draw -- recordatorio ";m.top.recordatorio
  ' m.recordatorio.setFields({
  '   visible: m.top.recordatorio
  ' })
  if m.top.debug then print ghLogHead();"Draw backpanel -- ";m.backpanel.color;" ";m.backpanel.width;" ";m.backpanel.height
end sub
' EVENTS
' ------------------------------
sub onContentChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onContentChange -- ";data
  ' if m.top.debug then print ghLogHead();"onContentChange -- ";data.item
  ' data
  m.title.text = ghGetChild(data, "title", "")
  m.from.text = ghGetTime(ghGetChild(data, "item.date_begin"))
  m.to.text = ghGetTime(ghGetChild(data, "item.date_end"))
  ' imagen

  img = ghGetChild(data, "item.ext_eventimage_name", "")
  ' fijarse si img tiene algo
  ' si no tiene lo levanto de m.top.channelImage


  if img <> "" then
    m.image.uri = img
  else if m.top.channelImage <> "" then
    m.image.uri = m.top.channelImage
  else
    m.image.uri = ghGetAsset("mini_epg_event_default", "pkg:/images/Placeholder_Epg_event.png")
  end if
  ' tag
  m.avance.visible = false
  m.tag.visible = true
  tag = ghGetChild(data, "id", "")
  if m.top.debug then print ghLogHead();"onContentChange -- ";tag
  if tag = "ahora" then
    if ghGetDisplayMode() = "FHD" then
      m.tag.uri = ghGetAsset("", "pkg:/images/tag_ya_emitido.png")
    else
      m.tag.uri = ghGetAsset("", "pkg:/images/tv_ahora.png")
    end if
    drawProgress(data)
  else if tag = "masTarde"
    if ghGetDisplayMode() = "FHD" then
      m.tag.uri = ghGetAsset("", "pkg:/images/tag_mas_tarde.png")
    else
      m.tag.uri = ghGetAsset("", "pkg:/images/tv_mas_tarde.png")
    end if
  else
    m.tag.visible = false
  end if
end sub
' utilities
function ghGetTime(data)
  if data = invalid then
    return ""
  else
    arr = ghSplit(data, " ")
    res = ""
    if arr.Count() > 1 then res = Left(arr[1], 5)
    return res
  end if
end function
sub drawProgress(data)
  try
    calculo = {
      tFrom: ghGetChild(data, "PLAYSTART")
      tCur: CreateObject("roDateTime").AsSeconds()
      total: ghGetChild(data, "PLAYDURATION")
    }
    if calculo.total > 0 then ' solo si no tengo 0 como total
      calculo.avance = calculo.tCur - calculo.tFrom
      calculo.porcentaje = calculo.avance / calculo.total * 100
      if m.top.debug then
        print ghLogHead();"Calculo de avance > ";calculo
      end if
      m.avance.value = calculo.porcentaje
      m.avance.visible = true
    end if
  catch error
    m.avance.visible = false
    print ghLogHead();"ERROR : ";error
  end try
end sub