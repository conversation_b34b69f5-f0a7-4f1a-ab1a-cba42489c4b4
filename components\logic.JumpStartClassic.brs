sub classic_jumpStart(ipaddr)
  m.logger.debug("jumpStartClassic - ipaddr", { ip: ipaddr })

  m.apiIsLoggedIn = ghCallApi("IsLoggedInLite", "classic_IsLoggedInOk", "classic_IsLoggedInError", false)
  if ipaddr <> invalid and m.iniciado = false then
    m.logger.debug("enviando ip", { ip: ipaddr, iniciado: m.iniciado })
    ' para que deje desloguear
    m.iniciado = true

    m.apiIsLoggedIn.ipaddr = ipaddr
  end if
  m.apiIsLoggedIn.control = "run"
end sub

sub classic_IsLoggedInOk()
  m.logger.debug("classic_IsLoggedInOk")

  ' informacion de super-highlight
  ghCallApi("SuperHighlightLite")

  'informacion de subscripciones y metodos de pago
  ghCallApi("ProfileLite")

  isLoggedIn = ghGetChild(m.apiIsLoggedIn, "content.isLoggedIn")
  acceptedTerms = ghGetChild(m.apiIsLoggedIn, "content.accepted_terms")

  m.logger.debug("isLoggedIn ", { isLoggedIn: isLoggedIn, acceptedTerms: acceptedTerms })

  if isLoggedIn <> invalid and isLoggedIn then
    ghCallApi("PushSession")

    if acceptedTerms = 0 then
      classic_callApa("FlujoALosTerminosYCondiciones")
    else
      classic_callApa("FlujoALaHome")
    end if
  else
    FlujoALaLanding()
  end if
end sub

sub classic_IsLoggedInError()
  m.logger.debug("classic_IsLoggedInError")

  FlujoALaLanding()
end sub

sub classic_callApa(resultOk)
  m.logger.debug("classic_callApa", { result: resultOk })

  m.resultOk = resultOk
  m.apiApa = ghCallApi("Apa", "classic_ApaOk", "classic_ApaError")
end sub

sub classic_ApaError()
  m.logger.error("Error en la llamada a API APA")

  ErrorInicializacion("CAPA01")
end sub

sub classic_ApaOk()
  m.logger.debug("classic_ApaOk")

  m.apiNav = ghCallApi("Nav", m.resultOk, "classic_NavError")
end sub

sub classic_NavError()
  m.logger.error("Error en la llamada a API NAV")

  ErrorInicializacion("CNAV00")
end sub