# GHCheckBox

## Propiedades

| Nombre       | Tipo    | Default                   | Descripción                                                           |
| ------------ | ------- | ------------------------- | --------------------------------------------------------------------- |
| text         | string  | MyButton                  | texto de la etiqueta del checkbox.                                    |
| textWrap     | string  | true                      | el texto de la etiqueta debe partirse en múltiples líneas?.           |
| tildeFig     | string  | pkg:/images/Check_HD.png  | Imagen del tilde del checkbox.                                        |
| translation  | string  | [0,0]                     | posición del componente.                                              |
| width        | string  | 350                       | ancho total del componente.                                           |
| height       | string  | 60                        | alto total del componente.                                            |
| horizAlign   | string  | center                    | alineación horizontal de la etiqueta del checkbox.                    |
| vertAlign    | string  | center                    | alineación vertical de la etiqueta del checkbox.                      |
| color        | string  | 0xFFFFFFFF                | color del texto de la etiqueta del checkbox.                          |
| selColor     | string  | 0xFFFFFFFF                | color del texto de la etiqueta del checkbox cuando está seleccionado. |
| backColor    | string  | 0x212224FF                | color de fondo del checkbox.                                          |
| selBackColor | string  | 0x212224FF                | color de fondo del checkbox cuando está seleccionado.                 |
| padding      | string  | 5                         | padding del checkbox.                                                 |
| focusMap     | string  | pkg:/images/focus01.9.png | imagen tipo 9 para el borde del checkbox.                             |
| focusColor   | string  | 0xFFFFFF                  | color del border del checkbox.                                        |
| focus        | boolean | false                     | el componente tiene foco?                                             |
| selected     | boolean | false                     | el componente ha sido seleccionado?                                   |
| checked      | boolean | false                     | el checkbox está tildado?                                             |
| value        | string  | mybutton                  | valor a devolver                                                      |
| debug        | boolean | false                     | enciende los logs del componente.                                     |

## Ejemplo

Declaración del grupo en el xml.

```xml
<GHCheckBox id="check"
            text="Acepto los Términos y Condiciones"
            width="248"
            height="72"
            translation="[400,352]"
            backColor="#212224"
/>
```

Manejo del grupo con `layout` tipo `map`.

```basic
' check
m.check = m.top.findNode("check")
m.check.text = gh Translate("register_access_termsConditions_label", "Acepto los términos y condiciones", {})
```
