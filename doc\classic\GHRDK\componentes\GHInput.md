# GHInput

## Propiedades

| Nombre           | Tipo    | Default                   | Descripción                                        |
| ---------------- | ------- | ------------------------- | -------------------------------------------------- |
| value            | string  |                           | valor del componente. Valor inicial.               |
| placeholder      | string  | MyLabel                   | valor en el caso de que el valor esté vacío.       |
| maxTextLength    | string  | 15                        | cantidad máxima de caracteres admitidos            |
| color            | string  | 0xFFFFFF                  | color del texto del input.                         |
| selColor         | string  | 0xFFFFFF                  | color del texto de input cuando tiene foco.        |
| placeholdercolor | string  | 0xFFFFFF                  | color del texto de placeholder.                    |
| title            | string  | Title                     | titulo del popup del teclado.                      |
| message          | string  |                           | mensaje del popup del teclado.                     |
| titleColor       | string  | 0x00FFFF                  | color del texto del título del popup del teclado.  |
| messageColor     | string  | 0x00FFFF                  | color del texto del mensaje del popup del teclado. |
| password         | boolean | false                     | mostrar `*` en lugar de los caracteres.            |
| translation      | string  | [0,0]                     | posición del componente.                           |
| width            | string  | 0                         | ancho del componente.                              |
| height           | string  | 0                         | alto del componente.                               |
| focusPadding     | string  | 12                        | padding desde el borde de foco al componente.      |
| focusMap         | string  | pkg:/images/focus01.9.png | imagen para el borde de foco.                      |
| focusColor       | string  | 0xFFFFFF                  | color del borde de foco.                           |
| focus            | boolean | false                     | tiene foco el componente?                          |
| selected         | boolean | false                     | ha sido seleccionado el componente?                |
| debug            | boolean | false                     | prende los logs del componente.                    |

## Ejemplo

Declaración del grupo en el xml.

```xml
<GHInput id="user"
         placeholder="Usuario"
         translation="[400,200]"
         color="0xFFFFFF"
         selColor="#7F8086"
         focusColor="0xFFFFFF"
         placeholdercolor="#7F8086"
         password="false"
         width="504"
         height="72"
         title="Inicia sesión"
         titleColor="0xFFFFFF"
         message="¿Cuál es tu correo electrónico?"
         messageColor="0xFFFFFF"
/>

<GHInput id="pass"
         placeholder="Password"
         translation="[400,280]"
         color="0xFFFFFF"
         selColor="#7F8086"
         focusColor="0xFFFFFF"
         placeholdercolor="#7F8086"
         password="true"
         width="504"
         height="72"
         title="Inicia sesión"
         titleColor="0xFFFFFF"
         message="Tu contraseña debe incluir: Mayúsculas y minúsculas, Al menos un número, 8 o más caracteres"
         messageColor="0xFFFFFF"
/>
```

Manejo del componente.

```basic
' seteo inicial
m.user = m.top.findNode("user")
m.user.placeholder = gh Translate("", "Correo electrónico")
m.pass = m.top.findNode("pass")
m.pass.placeholder = gh Translate("", "Contraseña")
' recuperacion del valor
m.apiLogin.username = m.top.findNode("user").value
m.apiLogin.password = m.top.findNode("pass").value
```
