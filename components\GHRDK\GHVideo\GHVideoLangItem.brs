
function init()
  m.title = m.top.findNode("title")
  m.icon = m.top.findNode("icon")
  m.check = m.top.findNode("check")
  m.fondo = m.top.findNode("fondo")
end function

function itemContentChanged()
  m.fondo.setFields({
    width: m.top.width,
    height: 65,
    translation: [0, 0],
  })

  m.check.setFields({
    translation: [340, 10]
    width: 40
    uri: ""
  })
  iconUri = ghGetAsset("language_panel_language_icon", "pkg:/images/40_idioma.png")
  width = 40
  height = 30

  isSubtitle = ghGetChild(m.top.itemContent, "data.trackType", "")
  if isSubtitle = "subtitle" then
    iconUri = ghGetAsset("language_panel_subtitle_icon", "pkg:/images/40_subtitulo.png")
    width = 36
    height = 30
  end if
  m.icon.setFields({
    translation: [10, 15]
    width: width
    height: height
    uri: iconUri
  })
  m.title.setFields({
    translation: [60, 20]
    text: ghGetChild(m.top.itemContent.data, "label_large")
    font: ghGetFont(21, "regular")
  })

  recalcItem(false)
end function

sub onFocus(event)
  recalcItem(event.getData())
end sub

sub recalcItem(status = false)
  m.fondo.visible = m.top.itemHasFocus ' prendo y apago el fondo

  if status then
    m.title.color = "#FFFFFF"
    'm.icon.uri = "pkg:/images/40_idioma.png"
    'm.icon.height= 30
    'm.icon.width= 40
  else
    m.title.color = "#FFFFFF"
    if m.top.itemContent <> invalid and ghGetChild(m.top.itemContent.data, "is_current") then
      m.check.uri = ghGetAsset("language_panel_check_icon", "pkg:/images/Check_HD.png")
      m.check.height = 40
      m.check.width = 40
    end if
  end if
end sub
