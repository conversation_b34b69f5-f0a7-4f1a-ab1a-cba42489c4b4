<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemEmptywithmessage" extends="Group">
  <script type="text/brightscript" uri="ItemEmptywithmessage.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" />
    <field id="height" type="float" />
    <!-- alias="fondo.width" alias="fondo.height"  -->
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <!-- <Rectangle id="fondo" translation="[0,0]" /> -->
    <LayoutGroup id="tira" translation="[10,10]" layoutDirection="horiz" horizAlignment="left" itemSpacings="20" addItemSpacingAfterChild="true">
      <Label id="message" translation="[10,10]" color="#CCCCCC" width="350" height="250" horizAlign="left" vertAlign="center" wrap="true" lineSpacing="0" />
      <Poster id="img1" uri="pkg:/images/loading_square.png" loadSync="true" height="230" width="230" />
      <Poster id="img2" uri="pkg:/images/loading_square.png" loadSync="true" height="230" width="230" />
      <Poster id="img3" uri="pkg:/images/loading_square.png" loadSync="true" height="230" width="230" />
    </LayoutGroup>
  </children>

</component>