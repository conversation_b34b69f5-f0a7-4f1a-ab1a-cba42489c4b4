<?xml version="1.0" encoding="utf-8"?>
<!-- <component name="LoginPage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="multiPackPage" extends="Page">
	<script type="text/brightscript" uri="multiPackPage.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<interface>
		<field id="data" type="array" onChange="handleData" />
		<field id="selected" type="assocarray" />
		<field id="value" type="string" />
	</interface>
	<children>
		<Label id="titleLabel" height="48" translation="[400, 100]" />
		<GHRowList id="multiSubscriptionList" />
		<GHButtonGroup id="botonera1" layout="map" orientation="horizontal" handleKey="false">
			<GHButton visible="false" value="StarLabel" id="starLabel" text="* ¿QUÉ INCLUYE?" width="200" height="50" color="0xFFFFFF" backcolor="#2E303D" focusColor="0xFFFFFF" />
		</GHButtonGroup>
	</children>
</component>
