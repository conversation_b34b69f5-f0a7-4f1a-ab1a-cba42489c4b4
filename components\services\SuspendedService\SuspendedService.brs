sub Init()
  ' m.top.debug = true
  m.scene = m.top.getScene()
  m.activo = false
  m.diag = invalid

  m.pantalla = m.top.findNode("pantalla")
  initDialogScreen()
  m.isLoggedIn = false
  m.isShowingScreen = false ' se esta mostrando la pantalla?
  m.isSuspended = false ' esta suspendido el usuario?


  m.panel = m.top.findNode("panel")
  print ghLogHead("SUSPENDED");"INIT **"
  drawDebugScreen()
end sub
sub drawDebugScreen()
  translation = [650, 20]
  height = 300
  width = 600
  m.top.findNode("background").setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    color: "0x005500AA"
  })
  m.panel.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    infoText: "SuspendedService" '	string	none	READ_WRITE	The text to be displayed in the label.
    textColor: "#FFFFFF" '		color	0xFFFFFFFF	READ_WRITE	The color of the text displayed in the label.
    bulletText: [] '		array of strings	none	READ_WRITE	List of strings preceded by a bullet (for example, ["Bullet 1","Bullet 2"]).
    infoText2: "No tengo eventos." '		string	none	READ_WRITE	A second text string that can be offset from the first.
    infoText2Color: "#FFFFFF" '		color	0xFFFFFFFF	READ_WRITE	Specifies the infoText2 string color
    infoText2BottomAlign: true '		boolean	false	READ_WRITE	Specifies whether the infoText2 string is vertically aligned to the bottom of the info pane
  })
end sub
sub initDialogScreen()
  back = m.top.findNode("pantBackground")
  back.setFields({
    color: "#121212"
    translation: [0, 0]
    width: 1280
    height: 720
  })
  m.top.findNode("logoclaro").setFields({
    translation: [61, 35]
    width: 163
    height: 70 '33
    uri: ghGetAsset("landing_head_logoClarovideo", "pkg://images/HD/logoClaro.png")
  })
  m.top.findNode("title").setFields({
    width: 1280,
    height: 100
    translation: [0, 126]
    font: ghGetFont(31, "bold")
    horizAlign: "center"
    vertAlign: "center"
    text: ghTranslate("suspended_modal_title_label", "Cuenta Suspendida")
  })
  m.top.findNode("description").setFields({
    width: 1280,
    height: 300
    translation: [0, 160]
    font: ghGetFont(21, "regular")
    horizAlign: "center"
    vertAlign: "center"
    text: ghReplaceStr(ghTranslate("suspended_modal_description_label", "Tu cuenta está suspendida debido a falta de pago. {br}{br} Para ponerte al corriente con tus pagos y seguir {br} disfrutando de los beneficios de Claro video {br} llama al centro de atención:"), "{br}", "+chr(10)+")
  })
  m.top.findNode("text").setFields({
    width: 1280,
    height: 100
    translation: [0, 395]
    font: ghGetFont(24, "bold")
    horizAlign: "center"
    vertAlign: "center"
    text: ghTranslate("suspended_modal_number_callCenter_label", "{01 800 252 9999}")
  })

  m.top.findNode("textDos").setFields({
    width: 1280,
    height: 100
    translation: [0, 474]
    font: ghGetFont(21, "regular")
    horizAlign: "center"
    vertAlign: "center"
    text: ghTranslate("AlertaSuspensiondeCuenta_TextoDescriptivo1", "Una vez realizado el pago el servicio puede tardar")
  })
  m.top.findNode("textTres").setFields({
    width: 1280,
    height: 100
    translation: [0, 495]
    font: ghGetFont(21, "regular")
    horizAlign: "center"
    vertAlign: "center"
    text: ghTranslate("AlertaSuspensiondeCuenta_TextoDescriptivo2", "un par de horas en restablecerse.")
  })
  print ghLogHead("SUSPENDED");"initDialogScreen **"
end sub

' SERVICIO
sub onTick() ' looping
  if m.top.debug then print ghLogHead("SUSPENDED");"onTick **"
  m.isLoggedIn = ghGetRegistry("isLoggedIn") = "true"
  if m.top.debug then print ghLogHead("SUSPENDED");"onTick -- userLogged? ";m.isLoggedIn
  if m.isLoggedIn then ProcessInit()
  panelRefresh()
end sub
' PROCESO
sub ProcessInit() ' 1 : inicio
  if m.top.debug then print ghLogHead("SUSPENDED");"ProcessInit >> UserSuspended [ProcessGotUserSuspendedData,ProcessError]"
  ghCallApi("UserSuspended", "ProcessGotUserSuspendedData", "ProcessError")
end sub
sub ProcessGotUserSuspendedData(event) ' 2 : vuelve de chequeo
  data = event.getData()
  if m.top.debug then print ghLogHead("SUSPENDED");"ProcessGotUserSuspendedData << UserSuspended -- OK"
  isSuspended = ghGetChild(data, "isSuspended")

  ' isSuspended = not m.isSuspended ' HARDCO sacar !!!!
  ' isSuspended = true  ' HARDCO sacar !!!!

  if m.top.debug then print ghLogHead("SUSPENDED");"ProcessGotUserSuspendedData -- isSuspended ";isSuspended
  if isSuspended then
    processIsSuspended()
  else
    processNotSuspended()
  end if
  ' processIsSuspended() ' HARDCO
end sub
sub processIsSuspended() ' 3 : si esta suspendido
  if m.top.debug then print ghLogHead("SUSPENDED");"processIsSuspended **"

  ' si hay algún dialogo, lo cierro
  dialog = m.top.GetScene().dialog
  if dialog <> invalid then
    print "FIX +++++++++ CIERRO EL DIALOGO que se esta mostrando."
    dialog.close = true
  end if

  m.isSuspended = true
  m.isShowingScreen = true
  m.pantalla.visible = true
  bloquear()
end sub
sub processNotSuspended() ' 3 : si no esta suspendido
  if m.top.debug then print ghLogHead("SUSPENDED");"processNotSuspended **"
  m.isSuspended = false
  m.isShowingScreen = false
  m.pantalla.visible = false
  desbloquear()
end sub
sub ProcessError(event) '  MENSAJE DE ERROR INTERNO
  data = event.getData()
  print "================================"
  print "ERROR"
  print "================================"
  print data
  print "================================"
end sub

' DEBUG
sub panelRefresh() ' debug
  if m.top.debug then
    log = []
    if m.isLoggedIn then log.push("isLoggedIn") ' esta logeado?
    if m.isShowingScreen then log.push("isShowingScreen") ' se esta mostrando la pantalla?
    if m.isSuspended then log.push("isSuspended") ' esta suspendido?
    m.panel.bulletText = log
    ' ---- abajo
    date = CreateObject("roDateTime")
    date.ToLocalTime()
    m.panel.infoText2 = date.ToISOString()
  end if
end sub
sub onDebugTurn(event)
  data = event.getData()
  print ghLogHead("SERVICE");"onDebugTurn -- ";data
  m.top.findNode("display").visible = data
end sub

sub bloquear()
  if m.top.debug then print ghLogHead("SUSPENDED");"bloquear **"
  m.currFocus = m.global.currFocus
  m.top.setFocus(true)
end sub
sub desbloquear()
  if m.top.debug then print ghLogHead("SUSPENDED");"desbloquear **"
  m.top.setFocus(false)
  if m.currFocus <> invalid then m.currFocus.setFocus(true)
end sub

function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead("SUSPENDED");"onKeyEvent -- key=";key;" press=";press
  handled = true
  if press then
    if key = "back" then
      END
    end if
  end if
  return handled
end function
