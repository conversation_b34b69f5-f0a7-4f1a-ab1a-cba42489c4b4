sub DataInit()
  m.top.debug = false
  m.api.url = m.config.mfwk.host + "/services/player/v1/getpreferences"

  m.api.query.delete("api_version")

  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user"),
    "region": ghGetRegistry("region"),
    "group_id": m.top.group_id,
  })

end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  preferences = ghGetChild(response, "#0.Preferrences", {})
  print "response getPreferences: ";preferences

  ' preferences = {
  '   "Audio": "ORIGINAL",
  '   "Subtitle": "PT"
  ' }

  m.top.content = preferences
end sub