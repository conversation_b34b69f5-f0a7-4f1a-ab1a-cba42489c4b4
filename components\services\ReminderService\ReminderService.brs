sub Init()
  ' m.top.debug = true
  m.scene = m.top.getScene()
  m.activo = false
  m.diag = invalid

  m.panel = m.top.findNode("panel")

  m.remindersList = {
    cant: 0
    reminders: []
  }

  print ghLogHead("REM");"INIT **"
  drawDebugScreen()
end sub
sub drawDebugScreen()
  translation = [650, 320]
  height = 300
  width = 600
  m.top.findNode("background").setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    color: "0x550000AA"
  })
  m.panel.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    infoText: "RemiderService" '	string	none	READ_WRITE	The text to be displayed in the label.
    textColor: "#FFFFFF" '		color	0xFFFFFFFF	READ_WRITE	The color of the text displayed in the label.
    bulletText: [] '		array of strings	none	READ_WRITE	List of strings preceded by a bullet (for example, ["Bullet 1","Bullet 2"]).
    infoText2: "No tengo eventos." '		string	none	READ_WRITE	A second text string that can be offset from the first.
    infoText2Color: "#FFFFFF" '		color	0xFFFFFFFF	READ_WRITE	Specifies the infoText2 string color
    infoText2BottomAlign: true '		boolean	false	READ_WRITE	Specifies whether the infoText2 string is vertically aligned to the bottom of the info pane
  })
end sub

' SERVICIO
sub onTick() ' looping
  print ghLogHead("REM");"onTick **"
  refreshData() ' actualizo datos
  checkReminders() ' chequeo y muestro
end sub

' REFRESH
sub refreshData() ' actualizo datos
  print ghLogHead("REM");"refreshData -- Calling..."
  ghCallApi("ReminderList", "refreshDataOk", "refreshDataError")
end sub
sub refreshDataOk(event)
  data = event.getData()
  print ghLogHead("REM");"refreshDataOk -- OK."
  m.remindersList = data
  panelRefresh() ' actualizo debug
end sub
sub refreshDataError(event)
  data = event.getData()
  print ghLogHead("REM");"refreshDataError -- ERROR."
  print "****************************"
  print "****************************"
  print "ERROR !!"
  print data
  print "****************************"
  print "****************************"
end sub

' HAY REMINDERS
sub checkReminders() ' chequeo
  print ghLogHead("REM");"checkReminders -- Init."
  cantR = m.remindersList.cant
  listR = m.remindersList.reminders

  print ghLogHead();"checkReminders -- cant: ";cantR
  if cantR > 0 then
    r = listR[0]
    dataR = {
      ' --
      prog_name: r.data.name
      prog_description: r.data.description
      prog_image: r.data.image_base_square
      ' --
      time_begin: r.data.begintime
      time_end: r.data.endtime
      ' --
      channel_num: r.data.channel_number
      channel_name: r.data.channel_name
      channel_image: r.data.channel_image
      ' --
    }
    showDialog(dataR)

    ' ---------------------
    ' ---------------------
    ' ---------------------
    ' ---------------------
    date = CreateObject("roDateTime")
    date.ToLocalTime()
    nowUnix = date.AsSeconds()
    nowTimestamp = date.ToISOString()
    print "recordatorios: (";nowTimestamp;")"

    ' recorro los pendientes.
    for r = 0 to cantR - 1

      re = listR[r]
      remdata = re.data

      ' limpieza
      if remdata.channel_number = invalid then
        if m.top.debug then print "Borrando ";re.id;" ";re.exp_date;" > ";remdata.begintime;" | ";remdata.channel_name;" > ";remdata.name;" (";remdata.unix_begin;")"
        ' hasRemindersModifications = true
        ' DeleteReminder(re.id)
        return
      end if

      ' sayDebug("XXXXXXXX", re, nowUnix)
      ' if m.top.debug then
      '   print re.id;" ";re.exp_date;" > ";
      '   print remdata.begintime;" | ";remdata.channel_name;" > ";remdata.name;" (";remdata.unix_begin;")"
      ' end if

      if remdata.unix_begin < nowUnix ' ya empezo
        if nowUnix < remdata.unix_end then ' todavia no termino
          sayDebug(r, "YA EMPEZO!", re, nowUnix)
          ' if m.top.debug then
          '   print "--------- AHORA !!! "
          '   print re.id;" ";re.exp_date;" > ";
          '   print remdata.begintime;" | ";remdata.channel_name;" > ";remdata.name;" (";remdata.unix_begin;")"
          '   print "           ";remdata.unix_begin;" < ";nowUnix;" and ";nowUnix;" < ";remdata.unix_end
          ' end if
          ' -----------------------
          ' MOSTRAR EL AVISO!!!
          ' -----------------------
        else
          sayDebug(r, "YA PASO!", re, nowUnix)
          ' if m.top.debug then
          '   print "SE PASO !! ";re.id;" ";re.exp_date;" > ";remdata.begintime;" | ";remdata.channel_name;" > ";remdata.name;" (";remdata.unix_begin;")"
          '   print "           ";remdata.unix_begin;" < ";nowUnix;" and ";nowUnix;" < ";remdata.unix_end
          ' end if
          ' -----------------------
          ' MOSTRAR EL AVISO!!!
          ' -----------------------
          ' DeleteReminder(re.id)
        end if
      else
        sayDebug(r, "FALTA!", re, nowUnix)
        ' if m.top.debug then
        '   print "FALTA   !! ";re.id;" ";re.exp_date;" > ";remdata.begintime;" | ";remdata.channel_name;" > ";remdata.name;" (";remdata.unix_begin;")"
        '   print "           ";remdata.unix_begin;" < ";nowUnix;" and ";nowUnix;" < ";remdata.unix_end
        ' end if
      end if
    end for

    ' ---------------------
    ' ---------------------
    ' ---------------------
    ' ---------------------

    ' end for
  end if
  print ghLogHead("REM");"checkReminders -- END."
end sub

' REMIND
sub showDialog(data) ' muestro dialogo
  print ghLogHead("REM");"showDialog -- Init."

  print " "
  print "-----------------"
  print "|DIALOG >>"
  print data
  print "-----------------"
  print " "
  ' {
  '   channel_image: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWHORIZONTAL.jpg?size=290x163"
  '   channel_name: "LA NACIÓN +"
  '   channel_num: 18
  '   prog_description: "Toda la información de la mano de Luis Novaresio, junto a Guadalupe Vazquez y Pancho Olivera."
  '   prog_image: ""
  '   prog_name: "Buen Dia Nacion"
  '   time_begin: "2023/07/20 10:00:00"
  '   time_end: "2023/07/20 13:00:00"
  ' }

  if not m.activo then
    m.activo = true

    m.diag = CreateObject("roSGNode", "GHToastDialog")

    m.diag.title = "Recordatorio"
    m.diag.message = ["recordaste que"]
    m.diag.bulletText = [
      "Canal " + str(data.channel_num) + " - " + data.channel_name,
      "Programa " + data.prog_name,
      "[" + data.time_begin + " - " + data.time_end + "]"
    ]
    ' m.diag.bulletType = "numbered"
    m.diag.bottomMessage = ["¿Querés ir al canal?"]
    m.diag.buttons = ["IR A ALGUN LADO", "DESCARTAR"]

    m.diag.observeFieldScoped("buttonSelected", "onDialogSelected")
    m.diag.observeFieldScoped("wasClosed", "onDialogWasClosed")
    ' show
    m.scene.dialog = m.diag

  else
    print ghLogHead("REM");"showDialog -- YA HAY OTRO DIALOGO ABIERTO."
  end if

  print ghLogHead("REM");"showDialog -- END."
end sub
sub onDialogSelected(event)
  data = event.getData()
  print ghLogHead();"onDialogSelected --";data
  m.activo = false
  print " "
  print "*******************************"
  print "SELECTED! opcion ";data
  print "*******************************"
  print " "
end sub
sub onDialogWasClosed()
  print ghLogHead();"onDialogWasClosed --"
  m.activo = false
end sub

' DEBUG
sub panelRefresh() ' debug
  if m.top.debug then
    log = ["Nada."]
    if m.remindersList.cant <> invalid then
      log = []
      for l = 0 to m.remindersList.cant - 1
        r = m.remindersList.reminders[l].data
        log.push(str(r.channel_number) + "-" + r.channel_name + "(" + r.begintime + ")")
      end for
    end if
    m.panel.bulletText = log
    ' ---- abajo
    date = CreateObject("roDateTime")
    date.ToLocalTime()
    m.panel.infoText2 = date.ToISOString() + ": (" + str(m.remindersList.cant) + ")."
  end if
end sub
sub sayDebug(num, msg, re, nowUnix)
  if m.top.debug then
    d = re.data
    print " "
    print "--------- ";num;" ---------"
    print ">> ";msg
    print "id:";re.id;"  exp_date:";re.exp_date
    print "UNIX   : ";d.unix_begin;" < (";nowUnix;") < ";d.unix_end
    print "Channel: ";d.channel_number;" - ";d.channel_name;" (";d.channel_id;")"
    print "Program: ";d.name;" (";d.id;")"
    print "         ";d.description
    print "         ";d.begintime;" a ";d.endtime;" (";d.duration;")"
    print "--------- ";num;" ---------"
  end if
end sub

sub onDebugTurn(event)
  data = event.getData()
  print ghLogHead("SERVICE");"onDebugTurn -- ";data
  m.top.findNode("display").visible = data
end sub

' ****************************
' ****************************
' REMINDERS LIST
' <Component: roAssociativeArray> =
' {
'     cant: 1
'     reminders: <Component: roArray>
' }
' <Component: roArray> =
' [
'     <Component: roAssociativeArray>
' ]
' ****************************
' PRIMERO
' <Component: roAssociativeArray> =
' {
'     channel_id: "9306288"
'     data: <Component: roAssociativeArray>
'     event_alf_id: "c24b3dd429d8"
'     event_id: "**********"
'     exp_date: 20230720100000
'     group_id: ""
'     id: "1689857742"
'     other_data: ""
'     type: "epg_serie"
'     user_id: "73156552"
' }
' PRIMERO.DATA
' <Component: roAssociativeArray> =
' {
'     aud_dolby: "0"
'     aud_stereo: "0"
'     begintime: "2023/07/20 10:00:00"
'     channel_group: <Component: roAssociativeArray>
'     channel_group_id: "764182"
'     channel_id: "9306288"
'     channel_image: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWHORIZONTAL.jpg?size=290x163"
'     channel_liveref: "lnmas"
'     channel_name: "LA NACIÓN +"
'     channel_number: 18
'     channel_provider_metadata_id: 2
'     channel_provider_metadata_name: "NAGRA"
'     confirmado: invalid
'     description: "Toda la información de la mano de Luis Novaresio, junto a Guadalupe Vazquez y Pancho Olivera."
'     duration: "03:00:00"
'     dvb_content: "Noticias"
'     endtime: "2023/07/20 13:00:00"
'     event_alf_id: "c24b3dd429d8"
'     ext_actors: invalid
'     ext_catchup: "1"
'     ext_country: "ARG"
'     ext_director: invalid
'     ext_ep_original_name: invalid
'     ext_episode_id: "401"
'     ext_eventimage_name: ""
'     ext_eventimage_name_base: ""
'     ext_language: "spa"
'     ext_ncont_id: "106022275944"
'     ext_nevt_id: "c24b3dd429d8"
'     ext_original_name: "Buen Dia Nacion"
'     ext_recordable: "1"
'     ext_season_id: invalid
'     ext_serie_desc: invalid
'     ext_serie_short_desc: invalid
'     ext_series_id: "106022245543"
'     ext_startover: "1"
'     ext_year: "2022"
'     group_id: invalid
'     group_rel: invalid
'     id: "**********"
'     id_empleado: invalid
'     image_base_horizontal: ""
'     image_base_square: ""
'     image_base_vertical: ""
'     language: "esp"
'     name: "Buen Dia Nacion"
'     parental_rating: "+7"
'     talent: "Luis Novaresio, Rifle Varela, Guadalupe Vázquez, Pancho Olivera"
'     tms_id: invalid
'     type: "0"
'     unix_begin: 1689858000
'     unix_end: 1689868800
'     user_content: invalid
'     vid_black_and_white: "0"
' }
' PRIMERO.DATA.CHANNEL_GROUP.COMMON
' <Component: roAssociativeArray> =
' {
'     channel_number: "18"
'     date: "20180213130549"
'     description: "LA NACIÓN + TV EVERYWHERE"
'     description_large: "LA NACIÓN +"
'     duration: invalid
'     encoder_tecnology: <Component: roAssociativeArray>
'     episode_number: invalid
'     format_types: "susc"
'     id: "764182"
'     image_background: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACIN_e-1280x720.jpg"
'     image_base_horizontal: "http://clarovideocdn7.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWHORIZONTAL.jpg"
'     image_base_square: "http://clarovideocdn5.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWCUADRADO.jpg"
'     image_base_vertical: "http://clarovideocdn5.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWVERTICAL.jpg"
'     image_clean_horizontal: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACINWHORIZONTAL.jpg"
'     image_clean_square: "http://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACINWCUADRADO.jpg"
'     image_clean_vertical: "http://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACINWVERTICAL.jpg"
'     image_external: invalid
'     image_frames: "http://clarovideocdn7.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SPRITES/LANACIN-00h-00m-00s-00f.jpg"
'     image_large: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWHORIZONTAL.jpg?size=675x380"
'     image_medium: "http://clarovideocdn8.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWVERTICAL.jpg?size=200x300"
'     image_small: "http://clarovideocdn0.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SS/LANACINWHORIZONTAL.jpg?size=290x163"
'     image_sprites: "http://clarovideocdn1.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/SPRITES/LANACIN-SPRITEBAR.jpg"
'     image_still: invalid
'     image_trickplay: ""
'     is_series: false
'     live_enabled: "1"
'     live_ref: "lnmas"
'     live_type: "1"
'     preview: "false"
'     proveedor_code: "cvperu"
'     proveedor_name: "CLARO VIDEO PERU"
'     rating_code: "G"
'     recorder_technology: <Component: roAssociativeArray>
'     resource_name: invalid
'     rollingcreditstime: invalid
'     rollingcreditstimedb: invalid
'     season_number: invalid
'     short_description: invalid
'     timeshift: invalid
'     title: "LA NACIÓN +"
'     title_episode: invalid
'     title_original: "LA NACIÓN +"
'     title_uri: "LA-NACION-"
'     url_imagen_t1: "http://clarovideocdn1.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACINWVERTICAL.jpg?size=200x300"
'     url_imagen_t2: "http://clarovideocdn3.clarovideo.net/CVPERU/PELICULAS/LANACIN/EXPORTACION_WEB/CLEAN/LANACINWHORIZONTAL.jpg?size=290x163"
'     votes_average: 4
'     year: invalid
' }
' ****************************
' ****************************



