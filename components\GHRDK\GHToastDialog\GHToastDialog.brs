

sub Init()
  print "Creating SampleMessageDialog"

  ' m.top.title = tr("StandardMessageDialog Test")
  ' m.top.message = [tr("Message line 1"), tr("Message line 2")]
  ' m.top.bulletText = [tr("Bullet 1"), tr("Bullet 2")]
  ' m.top.bulletType = "numbered"
  ' m.top.bottomMessage = [tr("Bottom Message")]
  ' m.top.buttons = [tr("OK"), tr("Cancel")]

  m.top.observeFieldScoped("buttonFocused", "printFocusButton")
  m.top.observeFieldScoped("buttonSelected", "printSelectedButtonAndClose")
  m.top.observeFieldScoped("wasClosed", "wasClosedChanged")
end sub

sub printFocusButton()
  print "m.buttonArea button ";m.top.buttons[m.top.buttonFocused];" focused"
end sub

sub printSelectedButtonAndClose()
  print "m.buttonArea button ";m.top.buttons[m.top.buttonSelected];" selected"
  m.top.close = true
end sub

sub wasClosedChanged()
  print "SimpleProgressDialog Closed"
end sub