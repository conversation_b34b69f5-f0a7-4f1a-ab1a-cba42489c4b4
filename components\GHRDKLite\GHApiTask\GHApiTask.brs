' GHApiTask
' -----
' Task Simple

'
' ghApiCall
' https://developer.roku.com/es-ar/docs/references/brightscript/events/rourlevent.md
' GetResponseCode()
' GetFailureReason()
' -----------------------------
sub Init()
  m.internalDebug = false

  m.top.functionName = "GetData"
  m.config = m.global.config
  m.lastResponse = {} ' codigo y headers de la llamada
  m.errTable = [
    { code: -1, name: "CURLE_UNSUPPORTED_PROTOCOL", description: "" }
    { code: -2, name: "CURLE_FAILED_INIT", description: "" }
    { code: -3, name: "CURLE_URL_MALFORMAT", description: "" }
    { code: -4, name: "CURLE_NOT_BUILT_IN", description: "" }
    { code: -5, name: "CURLE_COULDNT_RESOLVE_PROXY", description: "" }
    { code: -6, name: "CURLE_COULDNT_RESOLVE_HOST", description: "" }
    { code: -7, name: "CURLE_COULDNT_CONNECT", description: "" }
    { code: -8, name: "CURLE_FTP_WEIRD_SERVER_REPLY", description: "" }
    { code: -9, name: "CURLE_REMOTE_ACCESS_DENIED", description: "A service was denied by the server due to lack of access - when login fails this is not returned" }
    { code: -11, name: "CURLE_FTP_WEIRD_PASS_REPLY", description: "" }
    { code: -13, name: "CURLE_FTP_WEIRD_PASV_REPLY", description: "" }
    { code: -14, name: "CURLE_FTP_WEIRD_227_FORMAT", description: "" }
    { code: -15, name: "CURLE_FTP_CANT_GET_HOST", description: "" }
    { code: -16, name: "CURLE_HTTP2", description: "" }
    { code: -17, name: "CURLE_FTP_COULDNT_SET_TYPE", description: "" }
    { code: -18, name: "CURLE_PARTIAL_FILE", description: "" }
    { code: -19, name: "CURLE_FTP_COULDNT_RETR_FILE", description: "" }
    { code: -21, name: "CURLE_QUOTE_ERROR", description: "Quote command failure" }
    { code: -22, name: "CURLE_HTTP_RETURNED_ERROR", description: "" }
    { code: -23, name: "CURLE_WRITE_ERROR", description: "" }
    { code: -25, name: "CURLE_UPLOAD_FAILED", description: "Failed upload command" }
    { code: -26, name: "CURLE_READ_ERROR", description: "Could open/read from file" }
    { code: -27, name: "CURLE_OUT_OF_MEMORY", description: "" }
    { code: -28, name: "CURLE_OPERATION_TIMEDOUT", description: "The timeout time was reached" }
    { code: -30, name: "CURLE_FTP_PORT_FAILED	FTP PORT", description: "operation failed" }
    { code: -31, name: "CURLE_FTP_COULDNT_USE_REST", description: "The REST command failed" }
    { code: -33, name: "CURLE_RANGE_ERROR	RANGE", description: "command didn't work" }
    { code: -34, name: "CURLE_HTTP_POST_ERROR", description: "" }
    { code: -35, name: "CURLE_SSL_CONNECT_ERROR", description: "Wrong when connecting with SSL" }
    { code: -36, name: "CURLE_BAD_DOWNLOAD_RESUME", description: "Couldn't resume download" }
    { code: -37, name: "CURLE_FILE_COULDNT_READ_FILE", description: "" }
    { code: -38, name: "CURLE_LDAP_CANNOT_BIND", description: "" }
    { code: -39, name: "CURLE_LDAP_SEARCH_FAILED", description: "" }
    { code: -41, name: "CURLE_FUNCTION_NOT_FOUND", description: "" }
    { code: -42, name: "CURLE_ABORTED_BY_CALLBACK", description: "" }
    { code: -43, name: "CURLE_BAD_FUNCTION_ARGUMENT", description: "" }
    { code: -45, name: "CURLE_INTERFACE_FAILED", description: "CURLOPT_INTERFACE failed" }
    { code: -47, name: "CURLE_TOO_MANY_REDIRECTS", description: "Catch endless re-direct loops" }
    { code: -48, name: "CURLE_UNKNOWN_TELNET_OPTION", description: "User specified an unknown option" }
    { code: -49, name: "CURLE_TELNET_OPTION_SYNTAX", description: "Malformed telnet option" }
    { code: -51, name: "CURLE_PEER_FAILED_VERIFICATION", description: "Peer's certificate or fingerprint wasn't verified fine" }
    { code: -52, name: "CURLE_GOT_NOTHING", description: "When this is a specific error" }
    { code: -53, name: "CURLE_SSL_ENGINE_NOTFOUND", description: "SSL crypto engine not found" }
    { code: -54, name: "CURLE_SSL_ENGINE_SETFAILED", description: "Can not set SSL crypto engine as default" }
    { code: -55, name: "CURLE_SEND_ERROR", description: "Failed sending network data" }
    { code: -56, name: "CURLE_RECV_ERROR", description: "Failure in receiving network data" }
    { code: -58, name: "CURLE_SSL_CERTPROBLEM", description: "Problem with the local certificate" }
    { code: -59, name: "CURLE_SSL_CIPHER", description: "Couldn't use specified cipher" }
    { code: -60, name: "CURLE_SSL_CACERT", description: "Problem with the CA cert (path?)" }
    { code: -61, name: "CURLE_BAD_CONTENT_ENCODING", description: "Unrecognized transfer encoding" }
    { code: -62, name: "CURLE_LDAP_INVALID_URL", description: "Invalid LDAP URL" }
    { code: -63, name: "CURLE_FILESIZE_EXCEEDED", description: "Maximum file size exceeded" }
    { code: -64, name: "CURLE_USE_SSL_FAILED", description: "Requested FTP SSL level failed" }
    { code: -65, name: "CURLE_SEND_FAIL_REWIND", description: "Sending the data requires a rewind that failed" }
    { code: -66, name: "CURLE_SSL_ENGINE_INITFAILED", description: "Failed to initialize ENGINE" }
    { code: -67, name: "CURLE_LOGIN_DENIED", description: "User, password or similar was not accepted and we failed to login" }
    { code: -68, name: "CURLE_TFTP_NOTFOUND", description: "File not found on server" }
    { code: -69, name: "CURLE_TFTP_PERM", description: "Permission problem on server" }
    { code: -70, name: "CURLE_REMOTE_DISK_FULL", description: "Out of disk space on server" }
    { code: -71, name: "CURLE_TFTP_ILLEGAL", description: "Illegal TFTP operation" }
    { code: -72, name: "CURLE_TFTP_UNKNOWNID", description: "Unknown transfer ID" }
    { code: -73, name: "CURLE_REMOTE_FILE_EXISTS", description: "File already exists" }
    { code: -74, name: "CURLE_TFTP_NOSUCHUSER", description: "No such user" }
    { code: -75, name: "CURLE_CONV_FAILED", description: "Conversion failed" }
    { code: -86, name: "CURLE_CONV_REQD", description: "Caller must register conversion callbacks using curl_easy_setopt options CURLOPT_CONV_FROM_NETWORK_FUNCTION, CURLOPT_CONV_TO_NETWORK_FUNCTION, and CURLOPT_CONV_FROM_UTF8_FUNCTION" }
    { code: -77, name: "CURLE_SSL_CACERT_BADFILE", description: "Could not load CACERT file, missing or wrong format" }
    { code: -78, name: "CURLE_REMOTE_FILE_NOT_FOUND", description: "Remote file not found" }
    { code: -79, name: "CURLE_SSH", description: "Error from the SSH layer, somewhat generic so the error message will be of interest when this has happened" }
    { code: -80, name: "CURLE_SSL_SHUTDOWN_FAILED", description: "Failed to shut down the SSL connection" }
  ]
  BaseDataInit()
end sub
sub BaseDataInit()
  m.api = {
    name: "unknown",
    async: true,
    method: "GET",
    url: "",
    query: {
      ' config
      "authpn": m.config?.mfwk.authpn,
      "authpt": m.config?.mfwk.authpt,
      "device_type": m.config?.mfwk.device_type,
      "device_model": m.config?.mfwk.device_model
      "device_manufacturer": m.config?.mfwk.device_manufacturer
      "device_category": m.config?.mfwk.device_category
      "device_id": m.global?.device_id
      "api_version": m.config?.api?.versions?.default ' m.config.mfwk.api_version
      "format": m.config?.mfwk?.format
      ' registry
      "HKS": ghGetRegistry("HKS")
      ' "user_id": ghGetRegistry("user_id", "user")
      "region": ghGetRegistry("region"),
    },
    payload: {}
    body: "",
    headers: m.config?.api?.headers,
    timeout: m.config?.api?.timeout
  }
  m.logger = CreateLogger()
  if m.internalDebug then
    m.logger.debug("BaseDataInit", { method: m.api.method, url: m.api.url, query: m.api.query, body: m.api.body, headers: m.api.headers })
  end if
end sub

sub GetData() ' firstTry = true)
  ' por defecto, se inicializan los parametros, no en la segunda vuelta
  ' if firstTry then
  '   if m.top.debug then print ghLogHead();"GetData MODE = INIT "
  '   DataInit()
  ' else
  '   if m.top.debug then print ghLogHead();"GetData MODE = RETRY "
  '   DataRetry()
  ' end if
  DataInit()
  http = PrepareObject()
  ' if m.api.async then
  if m.internalDebug then print ghLogHead();"GetData ***** ASYNC *****"
  GetAsync(http)
  ' else
  '   if m.top.debug then print ghLogHead();"GetData ***** SYNC *****"
  '   GetSync(http)
  ' end if
end sub
sub GetAsync(http)
  if m.internalDebug then print ghLogHead();"GetAsync -- init"
  ' clean
  m.raw = invalid
  m.body = invalid
  m.headers = invalid
  ' method ----------------
  if m.api.method = "GET" then
    retorno = http.AsyncGetToString()
  else
    retorno = http.AsyncPostFromString(m.api.body)
  end if
  ' -----------------------
  ' si funciono -----------
  if retorno then
    if m.internalDebug then print ghLogHead("API");"GetAsync -- WORKED!"
    msg = m.port.waitMessage(m.api.timeout)

    if msg = invalid then
      ' -------------------------
      print ghLogHead("API");"GetAsync -- TIMEOUT! ";m.api.timeout, msg
      ProcessError(m.api.name, m.api.url)
    else if type(msg) = "roUrlEvent" then
      ' -------------------------
      m.headers = msg.GetResponseHeaders()
      m.response = msg.GetResponseCode()
      m.lastResponse = {
        headers: m.headers,
        code: m.response
      }
      if m.internalDebug then
        ' no se imprime body o raw -- se rompe si el m.response es muy grande por ejemplo apaTerminos
        print ghLogHead("API");"Response Code = ";m.response
        print ghLogHead("API");"Headers = ";m.headers
      end if
      if m.response < 0 then ' errores de llamado
        if m.internalDebug then
          print ghLogHead("API");"GetAsync -- RESPONSE <0 = ";m.response
          print ghLogHead("API");"GetAsync -- reason==";getErrorData(m.response)
        end if
        ProcessError(m.api.name, m.api.url, getErrorData(m.response))
      else if m.response >= 0 and m.response < 399 then ' xx 1xx 2xx 3xx
        if m.internalDebug then print ghLogHead("API");"GetAsync -- RESPONSE = ";m.response
        m.raw = msg.getString()
        if m.raw <> invalid and Len(m.raw) > 0 then
          m.body = ParseJson(m.raw)
        else
          m.body = ""
        end if
        ' if m.top.flag = 0 then
        '   m.top.flag = 1
        '   print ghLogHead();"EEEEEEEEEE NEW FLAG : ";m.top.flag
        '   HandleCode4xx()
        '   print ghLogHead();"EEEEEEEEEE BACK FLAG : ";m.top.flag
        ' else
        ProcessData(m.body, m.raw)
        ' end if
      else if m.response >= 400 and m.response < 500 then ' 4xx
        if m.internalDebug then
          print ghLogHead("API");"GetAsync -- RESPONSE 4xx = ";m.response
        end if
        m.raw = msg.getString()
        m.body = ParseJson(m.raw)
        HandleCode4xx()
      else
        m.raw = msg.getString()
        if m.internalDebug then
          print ghLogHead("API");"GetAsync -- RESPONSE >500 = ";m.response
          print ghLogHead("API");"GetAsync -- RAW = ";m.raw
        end if
        m.body = ParseJson(m.raw)
        ProcessError(m.api.name, m.api.url, m.response)
      end if
    else
      m.logger.error("::GOOSE PANIC(!):::::::::::::::::::::::::::::::", { "type": type(msg), msg: msg, api: m.api, query: m.api.query, body: m.api.body })
    end if
  else
    m.logger.error("GetAsync -- NOT WORKING! ", { response: m.response })
    ProcessError(m.api.name, m.api.url)
  end if
  ' catch error
  '   ' se produjo un error interno con algo...
  '   m.top.error = ghErrorNetwork(m.api.name, m.api.url)
  ' end try
end sub

function PrepareObject()

  ' mostrando curl
  CreateCurlCommand(m.api)

  ' PARAMETROS
  ' -----------------------------------
  ' agrego el ?
  if Instr(1, m.api.url, "?") > 0 then
    headChar = ""
  else
    headChar = "?"
  end if
  params = ghArray2Query(m.api.query, headChar)
  if m.api.url.inStr("claropagos") >= 0
    m.api.url = m.api.url
  else
    m.api.url = m.api.url + params
  end if

  ' -----------------------------------

  m.logger.info("PrepareObject", { method: m.api.method, url: m.api.url, body: m.api.body, headers: m.api.headers })

  ' OBJETO BASE
  ' -----------------------------------
  m.port = createObject("roMessagePort")
  http = createObject("roUrlTransfer")
  http.SetCertificatesFile("common:/certs/ca-bundle.crt")
  http.AddHeader("X-Roku-Reserved-Dev-Id", "")
  http.InitClientCertificates()
  http.setRequest(m.api.method)
  http.RetainBodyOnError(true)
  http.setMessagePort(m.port)
  if m.api.url = "" then
    ' se produjo un error interno con algo...
    m.top.error = ProcessError(m.api.name, m.api.url)
  else
    http.setUrl(m.api.url)
  end if
  ' headers ---------------
  for each item in m.api.headers
    http.AddHeader(item, m.api.headers[item])
  end for
  return http
end function
' VIRTUAL
' -------------------------------
sub ProcessData(res, raw)
  m.logger.error("ProcessData -- ERROR - Should implement ProcessData(res,raw)", { raw: Left(raw, 20) })
end sub
sub DataInit()
  m.logger.info("DataInit -- ERROR - Should implement DataIni() !!!")
end sub
sub DataRetry()
  m.logger.info("DataInit -- WARNING - Can implement DataRetry() !!!")
end sub
' ERRORS
' -------------------------------
function getErrorData(err, byWhat = "code")
  for each item in m.errTable
    if item[byWhat] = err then
      return item
    end if
  end for
  return invalid
end function
sub ProcessError(api, url, response = invalid)
  m.logger.error("ProcessError **", { api: api, url: url, response: response })
  ' nError = ghErrorNetwork(api, url, response)
  if m.body = invalid then m.body = {}
  m.body.netError = ghErrorNetwork(api, url, response)
  ProcessData(m.body, m.raw)
end sub


' TOKEN
' -----
sub HandleCode4xx()
  if m.internalDebug then print ghLogHead("API");"HandleCode4xx Code = ";m.lastResponse.code

  shouldRetry = false

  if m.response = 401 then
    print "tengo 401."
    shouldRetry = true
    ' errors = m.body.errors
    ' if errors <> invalid then
    '   print "hay errores."
    ' end if
  end if

  ' shouldRetry = true ' HARCDO
  print ghLogHead();"HandleCode4xx -- shouldRetry=";shouldRetry
  if shouldRetry then
    RenewToken()
  else
    ProcessData(m.body, m.raw)
  end if
end sub

sub RenewToken()
  if m.top.debug then print ghLogHead("API");"RenewToken >>"
  ghCallApi("RefreshTokenLite", "TokenOK", "TokenFail")
end sub

sub TokenOK() ' event
  ' data = event.getData()
  if m.top.debug then print ghLogHead("API");"TokenOK "
  ' rellamado
  if m.top.debug then print ghLogHead("API");"TokenOK RECALL >>> ";m.top.flag
  GetData()
end sub

sub TokenFail()
  if m.top.debug then print ghLogHead("API");"TokenFail"
  ProcessData(m.body, m.raw)
end sub

' UTILIDADES
function getUrlObject(origin)
  res = { _origin: origin }
  url = ghSplit(origin, "?")
  if url.Count() > 1 then
    ' info limpia
    res.strUrl = url[0]
    res.strQuery = url[1]
    ' convierto a objeto
    objQuery = {}
    queryStrings = ghSplit(url[1], "&")
    for i = 0 to queryStrings.Count() - 1
      value = ghSplit(queryStrings[i], "=")
      objQuery.addReplace(value[0], value[1])
    next
    res.queryObject = objQuery
  else
    res.strUrl = origin
    res.strQuery = ""
    res.queryObject = {}
  end if
  return res
end function

function replaceQueryParams(origin, replacements = {})
  in = getUrlObject(origin)
  res = {
    _origin: origin
    urlIn: in.strUrl
    queryIn: in.strQuery
  }
  if in.queryObject.Count() > 1 then
    ' url limpia
    ' res.urlBase = url[0]
    ' res.queryStringIn = url[1]
    ' consigo parametros
    ' queryStrings = ghSplit(url[1], "&")
    ' res.queryStringsIn = queryStrings
    ' convierto a objeto
    ' queryObjectIn = {}
    ' for i = 0 to queryStrings.Count() - 1
    '   value = ghSplit(queryStrings[i], "=")
    '   queryObjectIn[value[0]] = value[1]
    ' next
    ' res.queryObjectIn = queryObjectIn
    ' reemplazos
    queryObjectOut = in.queryObject
    for each item in replacements
      ' queryObjectOut.addReplace(item, replacements[item])
      queryObjectOut[item] = replacements[item]
    end for
    res.queryObjectOut = queryObjectOut
    ' query salida
    queryOut = ""
    for each item in queryObjectOut
      queryOut += item + "=" + queryObjectOut[item] + "&"
    end for
    queryOut = left(queryOut, len(queryOut) - 1)
    ' salida
    res.queryOut = queryOut
    res.urlOut = in.strUrl + "?" + queryOut
  end if
  return res
end function