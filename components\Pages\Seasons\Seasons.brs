sub Init()
  setConstants()

  m.itemSeason = 0 ' ir a item por defecto
  m.title = m.top.findNode("title")
  m.spinner = m.top.findNode("spinner")
  m.carousel = m.top.findNode("seasons")
  m.carousel.focus = true
end sub

sub onWasShown() ' event
  CargarDatos(m.top.data)
end sub

sub setConstants()
  if m.top.debug then print ghLogHead();"setConstants *** ---------------------"
  m.rowConfig = {
    "_BASE_": { ' para todas las cintas
      itemComponentName: "ItemPolymorphic"
      numRows: 1
      itemSize: [1420, 210] ' tamaño de toda la cinta -- 1280 + 140 (offset izquierda)
      itemSpacing: [0, 0]
      rowLabelOffset: [0, 15]
      showRowLabel: [true]
      focusXOffset: [1]
      rowFocusAnimationStyle: "fixedFocusWrap"
      rowCounterRightOffset: 0
      variableWidthItems: [false]
      focusBitmapUri: ghGetImageByMode("4px_Focus.9.png")
      loadingBitmapUri: "pkg:/images/loading.png"
      loadingBitmapOpacity: 1
    }
    "seasons": {
      "rowItemSize": [[373, 209]],
      "rowItemSpacing": [[21, 0]]
      "rowHeights": [400],
      "rowSpacings": [70]
      "focusXOffset": [200],
      "rowLabelOffset": [[200, 10]],
      "showRowLabel": [true],
      "showRowCounter": [false]
    },
  }
end sub

sub CargarDatos(data = [])
  result = CreateObject("roSGNode", "GHContent")
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.id = "Temporadas"

  aItems = []

  title = ""
  for each index in data
    infoCard = data[index.Tostr()]
    infoCard.rowType = "CarrouselSeason"
    title = infoCard.serieTitle
    aItems.push(infoCard)
  end for

  ' para ordenar
  for i = 0 to aItems.count() - 1
    item = aItems[i]
    if item.number.toStr().len() = 1 then
      aItems[i].order = "0" + aItems[i].number
    else
      aItems[i].order = aItems[i].number
    end if
  end for
  aItems.sortBy("order")

  for i = 0 to aItems.count() - 1
    season = aItems[i]
    ' para posicion en season de episodio de vcard
    ' tomar index y no el numero, por si faltan seasons
    if season.number.toStr() = ghGetChild(m.top.item, "season").toStr() then
      ' m.itemSeason = item.number.toInt() - 1
      m.itemSeason = i
    end if

    item = CreateObject("roSGNode", "GHContent")
    ghUtils_ForceSetFields(item, {
      data: aItems[i]
    })
    obCinta.appendChild(item)
  end for

  result.appendChild(obCinta)

  chargeCarousel("seasons", result)

  ' titulo
  m.title.setFields({
    font: ghGetFont(32, "medium")
    horizAlign: "center"
    text: title
  })

  m.spinner.visible = false

end sub

sub chargeCarousel(nameCarousel, cintas)
  carousel = m.top.findNode(nameCarousel)
  cantItems = cintas.getChild(0).getChildCount() ' para el cambio de foco por pocos items
  if cantItems > 0 then
    carousel.ObserveField("value", "OnCardSelect")
    carousel.setFields(ghGetChild(m.rowConfig, "_BASE_", {})) ' seteos básicos
    carousel.setFields(ghGetChild(m.rowConfig, nameCarousel, {})) ' manejo de items ' lo pego completo. Si no hay, no hay
    carousel.content = cintas ' content
    carousel.visible = true

    carousel.jumpToRowItem = [0, m.itemSeason]
  end if
end sub

sub OnCardSelect(event)
  data = event.getData()

  if data <> invalid
    group_id = ghGetChild(data, "data.data.group_id", invalid)
    if group_id = invalid then
      group_id = ghGetChild(data, "data.data.id", invalid)
    end if

    m.top.group_id = group_id

    m.top.close = true
  end if
end sub

