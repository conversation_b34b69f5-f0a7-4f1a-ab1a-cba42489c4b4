' ProfileRead
'
' https://mfwkweb-api.clarovideo.net
' /services/user/profile/read
' ?
' device_id=web&
' device_category=web&
' device_model=web&
' device_type=web&
' device_so=Chrome&
' format=json&
' device_manufacturer=generic&
' authpn=webclient&
' authpt=tfg1h3j4k6fd7&
' api_version=v5.92&
' region=mexico&
' HKS=web60806ec5a3fe2&
' user_id=42781937&
' lasttouch=1619014569&
' user_token=eyJ0e...rwKFvzrIp-tZMk&
' gamification_id=59a432eea7b01c39b81ad009

sub DataInit()
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/profile/read"

  print ghLogHead();"DataInit -- ";m.global.lasttouch
  m.api.query.Append({
    ' parametros
    "user_id": ghGetRegistry("user_id", "user"),
    "gamification_id": ghGetRegistry("gamification_id", "user"),
    ' "lasttouch": ghGetRegistry("lasttouch_seen", "user"),
    "lasttouch": ghGetChild(m.global, "lastTouch.seen"),
    "user_token": ghGetRegistry("user_token", "user"),
    ' manifest
    "appversion": ghGetAppVersion(), ' &appversion=1.0.0
    "region": ghGetRegistry("region"),
    "api_version": ghGetChild(m.global.config, "api.version.ProfileRead", m.global.config.api.versions.default), 'config.mfwk.api_version ' &api_version=v5.86
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  content = {}
  response = ghGetChild(res, "response", [])

  if res.errors = invalid then ' ================= OK


    if m.top.debug then print ghLogHead();"response.data = ";response.data
    if m.top.debug then print ghLogHead();"response.data.members = ";response.data.members
    for i = 0 to response.data.members.Count() - 1
      if m.top.debug then print ghLogHead();"response.data.members = ";i;"=";response.data.members[i]
    end for

    print "************* FALTA GUARDAR LOS DATOS DE SOCIAL *******************"

    ' social = ghGetChild(response, "socialNetworks", [])
    ' if m.top.debug then print ghLogHead();"social = ";social
    ' if social.Count() > 0 then
    '   if m.top.debug then print ghLogHead();"social[0] = ";response.socialNetworks[0]
    ' end if


    ' content.addReplace("isLoggedIn", true)
    ' ' global
    ' ' ----------------------------------------------------
    ' ghSetRegistry("isLoggedIn", "true")
    ' if m.top.debug then print ghLogHead();"ProcessData -- Check Region -- ";ghGetRegistry("region");" <> ";response.region
    ' if ghGetRegistry("region") <> response.region then
    '   content.addReplace("ReloadTranslations", true)
    '   ghSetRegistry("region", response.region)
    ' else
    '   content.addReplace("ReloadTranslations", false)
    ' end if
    ' user
    ' ----------------------------------------------------
    ' ghSetRegistry("user_id", response.user_id, "user")

  else ' ==================================================== FALLO

    if m.top.debug then print ghLogHead();"errors = ";res.errors
    if m.top.debug then print ghLogHead();"errors.error = ";res.errors.error
    err = res.errors.error
    msgErrors = ""
    for e = 0 to err.Count() - 1
      msgErrors += err[e] + "> "
      if m.top.debug then print ghLogHead();"errors = ";err[e]
    end for

    content.addReplace("isSocialOk", false)
    content.error = true
    content.error_code = msgErrors

    ' global
    ' ----------------------------------------------------
    ghDeleteSectionRegistry("social") ' no deberia

  end if

  ' ----------------------------------------------------
  if m.top.debug then
    print "++++++++++++++++++++++++++++++++++++++ social"
    print "++++++++++++++++++++++++++++++++++++++"
    print ghListSectionData("social")
    print "++++++++++++++++++++++++++++++++++++++"
    print "++++++++++++++++++++++++++++++++++++++ content"
    print content
    print "++++++++++++++++++++++++++++++++++++++"
  end if
  ' ----------------------------------------------------

  m.top.content = content
end sub
