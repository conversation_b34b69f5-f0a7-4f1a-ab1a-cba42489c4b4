# Tracking



## Dudas

##### Para MX

- [ ] De dónde sacamos el porcentaje de visualización para disparar el `completion`.
- [ ] Qué cosas hay que tomar de APA? porque no tenemos una lista, y ahora sabemos que vienen los segundos del conteo del próximo episodio.

##### Roku

- [x] Tengo qualitychange en roku? >> streamingSegment >> rate bps
- [x] Tengo onleaving app o algo parecido?





General
--------------
jwt.io

- bookmark.
- métrica de reproducción.



### getmedia

getmedia -- post -- version 5.92 -- hay que mandar el `user_token` y el `payway_token` por formulario.

`user_token` lo obtengo del `login` / `isloggedin`.

`payway_token` lo obtengo del `purchasebuttoninfo`.

En la getMedia estan los `tracks`.





## Ticks

![track_0_gen](img/track_0_gen.png)



- Las `urls` se deben tomar de la getMedia, no se pueden poner en la config.
- El intervalo del `tick` se define en el resultado de la getMedia, no puede ser hardcode.

```json
"tracking": {
	"policies": {
		"tick_interval": 60,
		"retries": 999
	},
	urls": {
	  "tick": "https://.../services/track/tick?stream_...",
	  "stop": "https://.../services/track/stop?stream_...",
	  "timecode": "https://.../services/track/tick?stream_...",
	  "completion": "https://.../services/track/completion?stream_...",
	  "interval": "https://.../services/track/tick?stream_...",
	  "dubsubchange": "https://.../services/track/dubsubchange?stream_...",
	  "qualitychange": "https://.../services/track/qualitychange?stream_...",
	  "episodechange": "https://.../services/track/episodechange?stream_...",
	  "seek": "https://.../services/track/seek?stream_...",
	  "pause": "https://.../services/track/pause?stream_...",
	  "resume": "https://.../services/track/resume?stream_...",
	  "view": "https://.../services/track/start?stream_...",
	  "credits": "https://.../services/track/credits?stream_...",
	  "error": "https://.../services/track/error?stream_..."
	}
}
```



### tick

*Se dispara cuando*:  cada N cantidad de segundos, que se define en la getMedia para cada contenido

Se debe incluir el `timecode` con el avance del usuario. en segundos.

![track_1_tick](img/track_1_tick.png)



### stop

*Se dispara cuando:* cuando sale de la pantalla

- **ojo si sale de la app !! si no se manda vive 3min**

![track_2_stop](img/track_2_stop.png)



### timecode

NO SE USA?



### completion

*Se dispara cuando*:  en el fin del contenido

**(!)** También cuando salto de la serie

![track_2_stop](img/track_2_stop.png)



### episodechange

*Se dispara cuando:*  llego al fin player o cambio de episodio.

Antes de llamar a la getmedia.

![track_2_stop](img/track_2_stop.png)



### interval

NO SE USA?



### dubsubchange

*Se dispara cuando:*  cambia de idioma o sub

En single audio hay que disparar esta mas la `stop`

Cuando vuelve el `stop`, recién hago la `getmedia`.

![track_2_stop](img/track_2_stop.png)



### qualitychange

**????** lo tengo en roku

![track_4_qual](img/track_4_qual.png)



### seek

*Se dispara cuando:*  cuando avanza o retrocede

![track_5_seek](img/track_5_seek.png)



### pause

*Se dispara cuando:*  hace pausa...

![track_3_playpause](img/track_3_playpause.png)

**(!)** tiene que seguir el `track` porque sino muere la sesión....

![track_1_tick](img/track_1_tick.png)



### resume

*Se dispara cuando:*  vuelve a play.

![track_3_playpause](img/track_3_playpause.png)



### view

*Se dispara cuando:*  ya esta listo para mostrar el primer frame.



### credits

*Se dispara cuando:*  cuando aparecen los créditos.

Se calcula con un valor en negativo de segundos contra cantidad de segundos totales, `rollingcreditstime`.

**(!)** La cantidad de segundos de conteo viene en APA para conteo regresivo !!!!!

```json
"media": {
  . . .
  "rollingcreditstime": "-6",
  . . .
}
```



### error

*Se dispara cuando:*  cuando se produce un error.

Después se tiene que con

![track_6_error](img/track_6_error.png)




---





