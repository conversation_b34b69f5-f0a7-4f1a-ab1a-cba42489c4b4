' Copyright (c) 2018 Roku, Inc. All rights reserved.

sub Init()
    m.titleLabel = m.top.findNode("title")
    m.titleLabel.color = "#FFFFFF"
    m.titleLabel.font = ghGetFont(40, "regular")
    m.descriptionLabel = m.top.findNode("description")
    m.descriptionLabel.color = "#FFFFFF"
    m.descriptionLabel.font = ghGetFont(19, "medium")
    m.blending = m.top.findNode("blending")
    m.blending.translation = "[-100,-100]"
    m.layoutTalents = m.top.findNode("layoutTalents")
    m.layoutGenre = m.top.findNode("layoutGenre")
end sub

sub onMaxWidthChange()
    m.titleLabel.width = m.top.maxWidth
    m.descriptionLabel.width = m.top.maxWidth
end sub

sub onContentSet()
    content = m.top.content
    m.titleLabel.text = ""
    m.descriptionLabel.text = ""
    cant = m.layoutTalents.getChildCount()
    m.layoutTalents.removeChildrenIndex(cant, 0)
    cant = m.layoutGenre.getChildCount()
    m.layoutGenre.removeChildrenIndex(cant, 0)
    if content <> invalid then
        item = content.item
        titleLabelText = content.title
        if content.releaseDate <> invalid and Len(content.releaseDate) > 0 then
            titleLabelText += " | " + content.releaseDate
        end if
        if content.StarRating <> invalid and content.StarRating > 0 then
            titleLabelText += " | " + generateStarsRating(content.StarRating)
        end if
        if content.item <> invalid then
            empiezaArray = ghSplit(content.item.date_begin, " ")
            empieza = empiezaArray[0]
            if Len (empieza) > 1 then
                empieza = empiezaArray[1]
            end if
            terminaArray = ghSplit(content.item.date_end, " ")
            termina = terminaArray[0]
            if Len (termina) > 1 then
                termina = terminaArray[1]
            end if
            m.titleLabel.text = titleLabelText
            m.descriptionLabel.text = content.description
            m.talent = item.talent
            m.rating = item.parental_rating
            m.director = item.ext_director
            m.country = item.ext_country
            m.genre = item.dvb_content
            m.year = item.ext_year

            'setRole(gh Translate("", "Director:"), ghGetChild(m, "director", ""))
            'setRole(gh Translate("", "Protagonistas:"), ghGetChild(m, "talent", ""))
            'setRole(gh Translate("", "País:"), ghGetChild(m, "country", ""))

            if ghGetChild(m, "genre", "") <> "" then
                createChild({
                    type: "label",
                    props: {
                        text: ghGetChild(m, "genre", "")
                        font: ghGetFont(18, "regular")
                    }
                }, m.layoutGenre)
            end if

            if ghGetChild(m, "year", "") <> "" then
                createChild({
                    type: "label",
                    props: {
                        text: ghGetChild(m, "year", "")
                        font: ghGetFont(18, "regular")
                    }
                }, m.layoutGenre)
            end if

            if ghGetChild(m, "rating", "") <> "" then
                createChild({
                    type: "GHTag",
                    props: {
                        font: ghGetFont(16, "regular")
                        backMap: ghGetImageByMode("2px_back.9.png")
                        color: "0xFFFFFFFF"
                        text: ghGetRatingLabel(ghGetChild(m, "rating", ""))
                        horizPadding: 10
                        vertPadding: 6
                    }
                }, m.layoutGenre)
            end if

            createChild({
                type: "label",
                props: {
                    text: ghFormatDurationEpg(empieza) + " a " + ghFormatDurationEpg(termina)
                    font: ghGetFont(18, "regular")
                }
            }, m.layoutGenre)

            if ghGetChild(content, "item.duration", "") <> "" then
                createChild({
                    type: "label",
                    props: {
                        text: ghFormatDuration(ghGetChild(content.item, "duration", invalid))
                        font: ghGetFont(18, "bold")
                    }
                }, m.layoutGenre)
            end if
        end if
    end if

    ' CONFIGURACIÓN PARA EL LAYOUT DE TALENTOS
    setRole({
        text: ghTranslate("", "Director:")
        font: ghGetFont(17, "bold")
    }, {
        text: ghGetChild(m, "director", "")
        font: ghGetFont(17, "regular")
    })
    setRole({
        text: ghTranslate("", "Protagonistas: ")
        font: ghGetFont(17, "bold")
    }, {
        text: ghGetChild(m, "talent", "")
        font: ghGetFont(17, "regular")
    })
    setRole({
        text: ghTranslate("", "País: ")
        font: ghGetFont(17, "bold")
    }, {
        text: ghGetChild(m, "country", "")
        font: ghGetFont(17, "regular")
    })

end sub
'setRole(gh Translate("", "Director:"), ghGetChild(m, "director", ""))

sub setRole(propsLabel, propsText)

    if propsLabel.text <> "" and propsText.text <> "" then

        group = CreateObject("roSGNode", "LayoutGroup")
        group.setFields({ layoutDirection: "horiz", vertAlignment: "center", itemSpacings: "[4]" })

        'en negrita
        prompt = CreateObject("roSGNode", "label")
        prompt.setFields(propsLabel)

        ' en normal
        text = CreateObject("roSGNode", "label")
        text.setFields(propsText)



        group.appendChild(prompt)
        group.appendChild(text)
        m.layoutTalents.appendChild(group)


    end if
end sub
'sub setRole(label, text)
'    if text <> "" then
'        createChild({
'            type: "label",
'            props: {
'                text: label,
'                font: ghGetFont(18, "bold")
'            }
'        }, m.layoutTalents)
'        createChild({
'            type: "label",
'            props: {
'                text: text
'                font: ghGetFont(18, "regular")
'            }
'        }, m.layoutTalents)
'    end if
'end sub

sub createChild(object, layout)
    op = CreateObject("roSGNode", object.type)
    op.setFields(object.props)
    layout.appendChild(op)
end sub

function generateStarsRating(starRating as integer, maxRating = 100 as integer, maxCount = 5 as integer) as string
    stars = {
        empty: chr(9734)
        full: chr(9733)
    }
    result = ""
    pointsPerStar = maxRating / maxCount
    fullCount = starRating / pointsPerStar
    if fullCount - int(fullCount) > 0.5 then
        fullCount = int(fullCount) + 1
    else
        fullCount = int(fullCount)
    end if
    emptyCount = maxCount - fullCount

    while fullCount > 0
        result += stars.full
        fullCount--
    end while
    while emptyCount > 0
        result += stars.empty
        emptyCount--
    end while

    return result
end function
