sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/search/vertical"

  m.api.query.Append({
    "value": m.top.value,
    "provider_id": m.top.provider
    "field": "TALENT",
    "from": "0",
    "quantity": "50",
    ' "tenant_code": "netnow",
    "api_version": ghGetChild(m.global.config, "api.version.Vertical", m.global.config.api.versions.default),
    "region": ghGetRegistry("region"),
    "filterlist": getFilter(ghGetRegistry("region")),
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

function getFilter(region = "mexico")
  filters = m.global.filter_list

  return ghGetChild(filters, region + ".filterlist")
end function

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub