<?xml version="1.0" encoding="utf-8" ?>

<component name="BuyViewV2" extends="Page">

	<!-- PARTES -->
	<script type="text/brightscript" uri="BuyViewV2.brs" />
	<script type="text/brightscript" uri="messages/BuyMessages.brs" />
	<!-- STATES -->
	<script type="text/brightscript" uri="states/stateStart/stateStart.brs" />
	<script type="text/brightscript" uri="states/stateLogin/stateLogin.brs" />
	<script type="text/brightscript" uri="states/stateAlreadyHas/stateAlreadyHas.brs" />
	<script type="text/brightscript" uri="states/statePurchase/statePurchase.brs" />
	<script type="text/brightscript" uri="states/stateTicket/stateTicket.brs" />
	<script type="text/brightscript" uri="states/stateOut/stateOut.brs" />
	<script type="text/brightscript" uri="states/stateBuyPin/stateBuyPin.brs" />
	<script type="text/brightscript" uri="states/stateCheckout/stateCheckout.brs" />
	<!-- PURCHASES -->
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<script type="text/brightscript" uri="states/statePurchase/amcogate/Purchase_amcogate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/promogate/Purchase_promogate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/telmex/Purchase_telmexmexicogate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/hubgate/Purchase_hubgate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/rokugate/Purchase_rokugate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/hubfacturafijagate/purchase_fijagate.brs" />
	<script type="text/brightscript" uri="states/statePurchase/Tarjeta/purchase_claropagosgate.brs" />


	<interface>
		<!-- interfaz de entrada -->
		<field id="buyB" type="assocarray" />
		<field id="accessCode" type="assocarray" />
		<field id="data" type="assocarray" />
		<field id="contentId" type="string" />
		<field id="content_name" type="string" />
		<field id="content_type" type="string" />
		<field id="content_category" type="string" />
		<!-- diferencia entre VOD=G o Suscripcion=A -->
		<field id="object_type" type="string" value="G"/>

		<!-- para refresh la vcard -->
		<field id="refresh" type="boolean" value="false" />
		<field id="node_id" type="string"/>
	</interface>

	<children>

		<GHLoading/>
		<Rectangle id="fondo" color="0x00000000" width="1280" height="780" translation="[0,0]" />

	</children>
</component>
