' INTROCARD
' ------------------------
sub Init()
  ' m.top.debug = true
  ' boton
  m.botonera = m.top.findNode("botonera")
  m.btnNext = m.top.findNode("btnNext")
  'm.txtBoton = "OMITIR INTRO ({@SEG})"
  m.txtBoton = ghTranslate("skip_intro_button_text", "OMITIR INTRO")
  ' eventos
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnBackSelected")
  m.top.ObserveField("visible", "OnShow")
  initTimer()
end sub

sub OnShow() ' event
  if m.top.visible then
    if m.top.debug then print ghLogHead();"OnShow -- Jumping"
    m.top.wasClosed = false ' reinicio
    m.top.value = invalid
    ghFocusJumpTo("botonera")
    setTimerCount()
    m.tmrSeguir.control = "start"
  end if
end sub

' BOTONERA
sub OnButtonSelected() ' event
  if m.top.debug then print ghLogHead();"OnButtonSelected -- boton=";m.botonera.value
  m.top.value = "JUMP"
  close()
end sub
sub OnBackSelected() ' event
  if m.top.debug then print ghLogHead();"OnBackSelected -- boton=";m.botonera.value
  m.top.value = "CANCEL"
  close()
end sub
sub close()
  if m.top.debug then print ghLogHead();"OnBackSelected -- close"
  m.tmrSeguir.control = "stop"
  m.top.visible = false
end sub

function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "back" then
      OnBackSelected()
    else
      OnBackSelected()
    end if
  end if
  return handled
end function


' TIMER
sub initTimer()
  m.tmrSeguir = CreateObject("roSGNode", "Timer")
  m.tmrSeguir.ObserveField("fire", "tmrTriggerSeguir")
  m.tmrSeguir.repeat = true ' chequea varias veces...
  m.tmrSeguir.duration = 1 ' 1 sec
  m.tmrCount = ghGetChild(m.global, "timers.skipIntro.time", 10)
end sub

sub setTimerCount()
  m.tmrCount = ghGetChild(m.global, "timers.skipIntro.time", 10)
  m.btnNext.text = ghReplaceStr(m.txtBoton, "{@SEG}", m.tmrCount.toStr())
  if m.top.debug then print ghLogHead();"setTimerCount -- tmrCount= ";m.tmrCount
end sub
sub tmrTriggerSeguir()
  if m.tmrCount > 0 then
    m.tmrCount -= 1 ' contamos
    if m.tmrCount < 1 then
      if m.top.debug then print ghLogHead();"tmrTriggerSeguir -- NEXT!"
      OnBackSelected()
    else
      if m.top.debug then print ghLogHead();"tmrTriggerSeguir -- counting ";m.tmrCount
      m.btnNext.text = ghReplaceStr(m.txtBoton, "{@SEG}", m.tmrCount.toStr())
    end if
  else
    m.tmrSeguir.control = "stop"
  end if
end sub
