<?xml version="1.0" encoding="utf-8" ?>

<component name="ScrBuyGenericMessage" extends="Page">

    <script type="text/brightscript" uri="ScrBuyGenericMessage.brs" />

    <interface>
        <!-- interfaz de entrada -->
        <field id="mode" type="string" value="scrError" />

        <field id="title" type="string" alias="lblTitle.text" />
        <field id="message" type="string" alias="lblDescrip.text" />
        <field id="accept" type="string" alias="btnAccept.text" />
        <field id="fwd" type="assocarray" />
        <field id="back" type="assocarray" />
        <!-- valores de salida -->
        <field id="value" type="assocarray" />

    </interface>

    <children>
        <!-- <Rectangle id="fondo" color="#FF000066" translation="[0,0]" width="1280" height="720"/> -->

        <Group id="scrError">
            <Label id="lblTitle" translation="[0,210]" width="1280" height="48" text="*" />
            <Label id="lblDescrip" translation="[328,256]" width="624" height="136" horizAlign="center" text="*" wrap="true" />
            <GHButton id="btnAccept" translation="[392,412]" value="OK" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
        </Group>
    </children>

</component>
