' GHToast
' byGoose(20203 <EMAIL>

function init()
  ' m.top.debug = true
  ' seteos generales
  m.top.focusable = false
  ' objects

  m.background = m.top.findNode("background")
  m.texts = m.top.findNode("texts")

  m.top.ObserveField("visible", "onVisibleChange")

  buildVTimer()

  if m.top.debug then print ghLogHead("WIDGET");"Init "
end function
sub config()
  ' quitar todos los childs existentes
  cant = m.texts.getChildCount()
  m.texts.removeChildrenIndex(cant, 0)

  if m.top.iconPosition = "left" then
    rawImage()
  end if

  label = CreateObject("roSGNode", "Label")

  ' font = ghGetComponentFont("GHErrorMessage")
  ' if m.top.font <> invalid then
  '   font = m.top.font
  ' end if

  label.setFields({
    color: m.top.titleColor
    font: ghGetFont(24, "bold")
    text: m.top.title
  })
  m.texts.appendChild(label)

  if m.top.iconPosition = "middle" then
    rawImage()
  end if

  rawDivisor()

  body = CreateObject("roSGNode", "Label")
  body.setFields({
    color: "0xFFFFFF"
    font: ghGetComponentFont("GHErrorMessage")
    text: m.top.body
  })
  m.texts.appendChild(body)

  Redraw()
end sub
sub rawDivisor()
  if m.top.divisor <> "" then
    separatorL = CreateObject("roSGNode", "Rectangle")
    separatorL.setFields({
      width: m.top.separatorWidth
      color: "0xFFFFFF00"
    })
    m.texts.appendChild(separatorL)

    divisor = CreateObject("roSGNode", "Label")
    divisor.setFields({
      color: "0xFFFFFF"
      font: ghGetComponentFont("GHErrorMessage")
      text: m.top.divisor
    })
    m.texts.appendChild(divisor)

    separatorR = CreateObject("roSGNode", "Rectangle")
    separatorR.setFields({
      width: m.top.separatorWidth
      color: "0xFFFFFF00"
    })
    m.texts.appendChild(separatorR)
  end if
end sub
sub rawImage()
  separatorL = CreateObject("roSGNode", "Rectangle")
  separatorL.setFields({
    width: m.top.separatorWidth
    color: "0xFFFFFF00"
  })
  m.texts.appendChild(separatorL)

  icon = CreateObject("roSGNode", "Poster")
  if m.top.iconImage <> invalid then
    icon.setFields({
      uri: m.top.iconImage
      height: m.top.iconHeight
      width: m.top.iconWidth
    })
  end if

  if m.top.id = "LongOkMensaje" then
    icon.setFields({
      uri: m.top.iconImage
      height: 40
      width: 40
    })
  end if
  m.texts.appendChild(icon)

  separatorR = CreateObject("roSGNode", "Rectangle")
  separatorR.setFields({
    width: m.top.separatorWidth
    color: "0xFFFFFF00"
  })
  m.texts.appendChild(separatorR)
end sub

sub buildVTimer()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer --"
  m.VTimer = CreateObject("roSGNode", "Timer")
  m.VTimer.ObserveField("fire", "VTimerTrigger")
  m.VTimer.repeat = false
  if m.top.time > 0 then
    m.VTimer.duration = m.top.time
    m.VTimer.control = "start"
  end if
  if m.top.debug then print ghLogHead("WIDGET");"buildKeyTimer -end- "
end sub
sub VTimerTrigger()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer -- GO!"
  m.top.wasClosed = true ' importante para limpiar memoria
  m.top.visible = false
end sub

sub onVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onVisibleChange -- ";data
  if data then m.VTimer.control = "start"
end sub

sub onTimeChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onTimeChange -- ";data
  m.VTimer.control = "stop"
  if m.top.time > 0 then
    m.VTimer.duration = data
    m.VTimer.control = "start"
  end if

  config()
end sub
sub Redraw()
  if m.top.debug then print ghLogHead("WIDGET");"Redraw -- GO! alignement="m.top.alignement

  m.texts.translation = [22, -260]
  m.texts.horizAlignment = m.top.alignement
  rec = m.texts.boundingRect()

  bW = rec.width + (m.top.contentOffset[0] * 2)
  bH = rec.height + (m.top.contentOffset[1] * 2)
  bX = rec.x - m.top.contentOffset[0]
  bY = rec.y - m.top.contentOffset[1]
  m.background.setFields({
    width: bW,
    height: bH,
    translation: [bX, bY],
    blendColor: m.top.backgroundColor
  })

  if m.top.debug then
    print ghLogHead("WIDGET");"Redraw BoundRec=";" ";rec.x;" ";rec.y;" ";rec.width;" ";rec.height
    print ghLogHead("WIDGET");"Redraw Background=";bX;" ";bY;" ";bW;" ";bH
  end if
end sub