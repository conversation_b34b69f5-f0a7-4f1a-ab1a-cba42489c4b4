' GHVideoPanel
' --------------------------
sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init ***"
  ' focus
  m.mapFocus = "" ' inicialmente nada
  m.map = {
    "barSeek": { "up": invalid, "right": invalid, "down": "playbuttons", "left": invalid }
    "playbuttons": { "up": "barSeek", "right": invalid, "down": invalid, "left": "ointro" }
    "ointro": { "up": "barSeek", "right": "playbuttons", "down": invalid, "left": invalid }
  }
  ' general
  m.top.ObserveField("visible", "onPanelVisibleChange")
  m.top.ObserveField("focusedChild", "focusHandle")

  ' info
  m.isSeek = false
  m.isSerie = false
  '  ointro
  m.oIntro = m.top.findNode("ointro")
  ' m.oIntro.backSelEnable = false
  m.oIntro.ObserveField("selected", "oIntroSelected") ' apreto algun boton
  ' playbuttons
  m.playbuttons = m.top.findNode("playbuttons")
  m.playbuttons.backSelEnable = false
  m.playbuttons.ObserveField("selected", "playButtonsSelected") ' apreto algun boton
  ' barseek
  m.barSeekCurrent = m.top.findNode("barSeekCurrent")
  m.barSeekCurrent.font = ghGetFont(16, "regular")
  m.barSeekTotal = m.top.findNode("barSeekTotal")
  m.barSeekTotal.font = ghGetFont(16, "regular")
  m.barSeek = m.top.findNode("barSeek")
  m.barSeek.ObserveField("newValue", "onSeekBar") ' cambio de posicion
  m.barSeek.ObserveField("focus", "focusBar") ' cambio de posicion
  m.barSeek.ObserveField("selected", "handleContinue") ' OK en la barra sale
  m.barSeek.ObserveField("focus", "handleSeekFocus") ' OK en la barra sale
  m.trickpics = m.top.findNode("trickpics")
  m.tpickU = m.top.findNode("tpickU")
  m.infoCard = m.top.findNode("infoCard")

  ' -----
  setFormat()
  TimerInit()
end sub

sub focusBar(event)
  data = event.getData()
  if data = true then
    timerPause()
    m.isSeek = true
  else
    timerReset()
    TimerGo()
    m.isSeek = false
  end if
end sub

sub handleSeekFocus(event)
  data = event.getData()
  print ghLogHead();"handleSeekFocus ";data
  if data = true then
    ' refreshPosition()
    refreshTrickPlay()
    m.tpickU.visible = true
    m.infoCard.visible = false
  else
    m.tpickU.visible = false
    m.infoCard.visible = true
  end if
end sub
sub setFormat()
  ' trickPics
  'm.tpic = [m.top.findNode("tpic-1"), m.top.findNode("tpic-2"), m.top.findNode("tpic-3"), m.top.findNode("tpic-4"), m.top.findNode("tpic-5")] de momento quieren uno solo, queda comentado por si piden más
  m.tpic = [m.top.findNode("tpickU")]

  for t = 0 to 0
    m.tpic[t].setFields({ "uri": "pkg:/images/icon_hd.jpg" })
  end for
  ' if m.top.debug then print "********** ";m.tpic[0].id;" <<<"
  'm.tpic[0].setFields({
  '  padding: [12, 12],
  '  uriBorder: ghGetImageByMode("2px_back.9.png")
  '  colorBorder: "0xFFFFFFFF"
  '  widthBorder: 264
  '  heightBorder: 204
  '})
  m.barSeek.setFields({
    knotUri: "pkg:/images/knotknot1.png"
    knotWidth: 20
    knotHeight: 20
    knotSelUri: "pkg:/images/knotknot2.png"
    knotSelWidth: 20
    knotSelHeight: 20
  })
end sub
sub drawInfo()
  if m.top.debug then print ghLogHead();"drawInfo --";m.top.info
  ' info = m.top.findNode("infoCard")
  ghClearChilds(m.infoCard)
  ' title
  infoTitle = CreateObject("roSGNode", "Label")
  infoTitle.setFields({
    id: "infoTitle"
    font: ghGetFont(30, "bold")
    text: m.top.info.title
    color: "0xFFFFFF"
  })
  m.infoCard.appendChild(infoTitle)

  ' para acomodar el texto del capitulo
  bounding = infoTitle.boundingRect()
  m.widthTitle = bounding["width"]
end sub
sub drawButtons()
  if m.top.debug then print ghLogHead();"drawButtons --";m.top.info
  ghClearChilds(m.playbuttons) ' limpio cualquier boton existente

  ' m.btnPlay = CreateObject("roSGNode", "GHButtonImg")
  ' m.btnPlay.setFields({
  '   id: "btnPlay"
  '   value: "play"
  '   width: 100
  '   height: 120
  '   icon: ghGetAsset("player_pause_icon", "")
  '   iconWidth: 56
  '   iconHeight: 56
  '   iconOver: ghGetAsset("player_pause_icon", "")
  '   iconWidthOver: 56
  '   iconHeightOver: 56
  '   foco: ghGetImageByMode("player/player_focus_round.png")
  '   focoWidth: 76
  '   focoHeight: 76
  '   text: gh Translate("player_pause", "Pausa")
  '   'text: "Play"
  '   textColor: "0xFFFFFFFF"
  '   textOffset: 10
  ' })
  ' m.playbuttons.appendChild(m.btnPlay)

  ' lenguajes
  m.btnLang = CreateObject("roSGNode", "GHButtonImg")
  m.btnLang.setFields({
    id: "btnLang"
    value: "lang"
    width: 100
    height: 120
    icon: ghGetAsset("player_language_icon", "")
    iconWidth: 56
    iconHeight: 56
    foco: ghGetImageByMode("player/player_focus_round.png")
    focoWidth: 76
    focoHeight: 76
    text: ghTranslate("player_language", "Idiomas")
    textOffset: 10
  })
  m.playbuttons.appendChild(m.btnLang)
  ' temporadas
  if m.isSerie then
    ' 900 es para que quede centrado
    withInfo = 900 - m.widthTitle

    epiInfoTemp = CreateObject("roSGNode", "Label")
    epiInfoTemp.setFields({
      id: "epiInfoTemp"
      font: ghGetFont(21, "regular")
      text: ghTranslate("", "Temp. ") + m.top.info.season + "| " + "Ep. " + m.top.info.episodenumber + ": "
      color: "0xFFFFFF"
    })
    m.infoCard.appendChild(epiInfoTemp)

    epiInfoEpisodeTitle = CreateObject("roSGNode", "Label")
    epiInfoEpisodeTitle.setFields({
      id: "epiInfoEpisodeTitle"
      font: ghGetFont(21, "bold")
      text: m.top.info.titleepisode
      width: withInfo
      color: "0xFFFFFF"
    })
    m.infoCard.appendChild(epiInfoEpisodeTitle)

    if m.global.trailerSet <> "trailer"
      m.btnSeason = CreateObject("roSGNode", "GHButtonImg")
      m.btnSeason.setFields({
        id: "btnSeason"
        value: "season"
        width: 100
        height: 120
        icon: ghGetAsset("player_season_icon", "")
        iconWidth: 56
        iconHeight: 56
        foco: ghGetImageByMode("player/player_focus_round.png")
        focoWidth: 76
        focoHeight: 76
        text: ghTranslate("player_season", "Temporadas")
        textOffset: 10
      })
      m.playbuttons.appendChild(m.btnSeason)
    end if
  end if

  ' agregar a mi lista
  m.btnMiLista = CreateObject("roSGNode", "GHButtonImg")
  m.btnMiLista.setFields({
    id: "btnMiLista"
    value: "lista"
    width: 100
    height: 120
    icon: ghGetAsset("player_add_list_icon", "")
    iconWidth: 56
    iconHeight: 56
    foco: ghGetImageByMode("player/player_focus_round.png")
    focoWidth: 76
    focoHeight: 76
    text: ghTranslate("player_add_list", "Agregar a Mi Lista")
    textOffset: 10
  })

  if m.top.info.favorited = true then
    m.btnMiLista.text = ghReplaceStr(ghTranslate("player_remove_list", "Quitar de" + chr(10) + "Mi lista"), "{br}", chr(10))
    m.btnMiLista.icon = ghGetAsset("player_remove_list_icon")
  else
    m.btnMiLista.text = ghReplaceStr(ghTranslate("player_add_list", "Agregar a" + chr(10) + "Mi lista"), "{br}", chr(10))
    m.btnMiLista.icon = ghGetAsset("player_add_list_icon")
  end if

  m.playbuttons.appendChild(m.btnMiLista)
end sub
' EVENTS
' --------------------------
sub focusHandle(event)
  data = event.getData()

  ' cuando cierro lang o seasons, mando el foco a playerPanel
  ' y entra en esta funcion. donde si el foco lo tiene el panel lo mando a id guardado en m.mapFocus
  if m.top.visible = true and data <> invalid and data.id <> invalid then
    if data.id = "playerPanel" then
      turnFocusTo(guessFocusTo(m.mapFocus))
    end if
  end if
end sub

sub updateFieldFocus(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus << ";data
  if data then
    if m.mapFocus = "playbuttons" then
      m.playbuttons.setFocus(true)
      m.playbuttons.focus = true
    end if
  else
  end if
end sub
sub onInTo(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onInTo << ";data
  m.top.visible = true
  ' primero el omitir intro
  if m.oIntro.visible then
    turnFocusTo("ointro")
    return
  end if
  ' voy ahi...
  focusTo = ghGetChild(data, "focusTo", "playbuttons") ' a donde, con default
  if m.top.debug then print ghLogHead();"onInTo >> ";focusTo
  turnFocusTo(focusTo)
  ' manejo de timer
  timerStatus = ghGetChild(data, "timer", invalid)
  if m.top.debug then print ghLogHead();"timer >> ";timerStatus
  if timerStatus.toStr() = "pause" then TimerPause()
  ' campos a destino
  fieldsTo = ghGetChild(data, "fieldsTo", invalid)
  if m.top.debug then print ghLogHead();"onInTo >> ";fieldsTo
  if fieldsTo <> invalid then m.top.findNode(focusTo).setFields(fieldsTo)

  action = ghGetChild(data, "action", invalid)
  if action <> invalid then
    m.top.cmd = { "command": action }
  end if
end sub
sub onPanelVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onPanelVisibleChange -- ";data
  if data then ' entro----
    m.playbuttons.backSelected = false
    m.barSeek.visible = true
    TimerGo()
  else ' salgo ------------------
    m.barSeek.setFocus(false)
    m.playbuttons.setFocus(false)
    TimerPause()
    ' changeButtonPlay(true)
  end if
end sub

sub onInfoUpdate(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onInfoUpdate -- actualizando datos de la pelicula "
  m.isSerie = data.season <> ""

  drawInfo()
  drawButtons() ' redibujo botones!

  ' si frefreshco info ( favorited ) pongo el foco donde estaba
  if m.top.visible = true and m.mapFocus <> invalid and m.mapFocus <> "" then
    turnFocusTo(guessFocusTo(m.mapFocus))
  end if
end sub
sub onStateChange(event)
  state = event.getData()
  if state = "playing" then
    print ghLogHead();"onStateChange -- Estoy en PLAY."
    ' AudioPanelRefresh()
  else if state = "paused" then
    print ghLogHead();"onStateChange -- Estoy en PAUSED."
  end if
end sub
sub onContentChange() ' event
  ' data = event.getData()
  if m.top.debug then print ghLogHead();"onContentChange -- NADA!"
  ' if data <> invalid then
  ' end if
end sub
' ACTIONS
' --------------------------
sub handleContinue() ' event = invalid
  if m.top.debug then print ghLogHead();"handleContinue -- <<<"
  m.top.cmd = { "command": "ok" }
end sub
sub playButtonsBack() ' event
  if m.top.debug then print ghLogHead();"playButtonsBack ** "
  playButtonsDispatch("play")
end sub
sub playButtonsSelected() ' event
  if m.top.debug then print ghLogHead();"playButtonsSelected -- ";m.playbuttons.value
  playButtonsDispatch(m.playbuttons.value)
end sub
sub playButtonsDispatch(btn)
  seek_step = 120
  if btn = "seek0" then ' inicio..
    if m.top.debug then print ghLogHead();"playButtonsSelected -- SEEK 0"
    _setPosition(0)
  else if btn = "seek-" then ' hacia atras
    if m.top.debug then print ghLogHead();"playButtonsSelected -- SEEK -"
    _movePosition(-seek_step)
  else if btn = "seek+" then ' hacia adelante
    if m.top.debug then print ghLogHead();"playButtonsSelected -- SEEK +"
    _movePosition(seek_step)
    ' else if btn = "play" then
    '   if m.top.state = "playing" then
    '     ' changeButtonPlay(false)
    '     m.top.cmd = { "command": "pause" }
    '     TimerPause()
    '   else
    '     ' changeButtonPlay(true)
    '     m.top.cmd = { "command": "exit" }
    '   end if
    '   if m.top.debug then print ghLogHead();"playButtonsSelected -- PLAY!" ' play y cierre
    '   ' handleContinue()
  else if btn = "season" then
    if m.top.debug then print ghLogHead();"playButtonsSelected -- SEASON!"
    m.top.cmd = { "command": "showSeasonPanel" }
    TimerPause()
  else if btn = "lang" then ' lenguaje
    if m.top.debug then print ghLogHead();"playButtonsSelected -- IDIOMAS!"
    m.top.cmd = { "command": "showAudioPanel" }
    TimerPause()
  else if btn = "lista" then
    if m.top.debug then print ghLogHead();"playButtonsSelected -- SEASON!"
    m.top.cmd = { "command": "lista" }
  end if

  infoEvent = {
    content_id: ghGetChild(m.top.content, "groupid")
    content_name: ghGetChild(m.top.content, "title", "")
    content_type: "movie"
    content_category: ghGetChild(m.top.content, "ybgenres", "")

    content_availability: "by subscription"
    interaction_type: "language",
    content_episode: "not apply",
    content_list: "not apply",
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "play content",
    screen_class: "/play-content",
  }
  if m.isSerie then

    infoEvent.content_episode = m.top.info.episodenumber
    infoEvent.content_list = m.top.info.season
    infoEvent.content_type = "series"

  end if

  GA4Event("interaction_player", infoEvent)
  refreshPosition()
end sub
' SEEKBAR
' --------------------------
sub onSeekBar(event)
  'acá ver
  m.isSeek = true
  m.top.keypressed = "x"
  data = event.getData()
  if m.top.debug then print ghLogHead();"onSeekBar -- new position ";data
  _setPosition(data)
  m.barSeekCurrent.text = _posToTime(data)
end sub
sub refreshTrickPlay()
  if m.top.debug then print ghLogHead();"refreshTrickPlay ** init"
  if m.top.trickBif <> invalid then
    if m.top.debug then print ghLogHead();"refreshTrickPlay ** fpos ";m.top.position, m.barSeek.curValue
    fPos = [m.top.position]
    for i = 0 to 0
      m.top.trickBif.getNearestFrame = fPos[i]
      m.tpic[i].uri = m.top.trickBif.nearestFrame

      screenWidth = ghGetGlobalWH().w
      imgWidth = m.tpic[0].width
      seekBarWidth = m.barSeek.width
      Lmargin = (screenWidth - seekBarWidth) / 2

      try
        porcPeli = m.barSeek.curValue / m.top.duration
      catch catchError
        print "ERROR refreshTrickPlay >>>>";catchError
        porcPeli = 0
      end try

      pixelesAvance = seekBarWidth * porcPeli
      translation = [Lmargin + pixelesAvance - (imgWidth / 2), 380]
      m.tpic[0].translation = translation
      ' print "------------------------"
      ' print "screenWidth=";screenWidth;" seekBarWidth=";seekBarWidth;" Lmargin=";Lmargin
      ' print m.top.position;"/";m.top.duration;" porcPeli=";porcPeli;" pixelesAvance=";pixelesAvance
      ' print "translation=";translation
      ' print "------------------------"
      ' print " "
      ' avance de la pelicula -- 1000 % visto 450px
      ' tpic3.translation=[ (140+450) - (ancho de imagen/2),400]
      if m.top.debug then print "trickImg -- ";i;" ";m.tpic[i].uri;" > [";m.tpic[i].loadStatus;"]"
    end for
  else
    if m.top.debug then print "!! INVALID TRICK"
  end if
  if m.top.debug then print ghLogHead();"refreshTrickPlay ** end"
end sub
sub refreshPosition()
  if m.top.debug then print ghLogHead();"refreshPosition -- ";m.top.position
  m.barSeekCurrent.text = _posToTime(m.top.position)
  m.barSeekTotal.text = _posToTime(m.top.duration)
  if m.top.duration <> 0 then
    m.barSeek.setFields({ maxValue: m.top.duration, curValue: m.top.position })
    refreshTrickPlay()
  end if
end sub

sub refreshPositionPlayer(event)
  position = event.getData()

  if m.isSeek = false then
    m.barSeekCurrent.text = _posToTime(position)
    if m.top.duration <> 0 then
      m.barSeek.setFields({ maxValue: m.top.duration, curValue: position })
    end if
  end if
end sub

' FOCUS
' -----------------------------
function onKeyEvent(key, press) as boolean
  print "ACAKEY"; key
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  if press then
    if key = "back" then ' back no actualiza posicion !!!
      handled = true
      ' m.top.visible = false
      m.top.cmd = { "command": "exit" }
    else if key = "play" then ' pido pausa
      ' manejo en ghVideo
      handled = false
    else
      TimerReset()
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
    m.top.keypressed = key
  end if
  return handled
end function
function guessFocusTo(direction) as string
  if m.mapFocus = invalid then
    focusTo = "playbuttons"
  else ' a donde voy?
    if m.map[m.mapFocus][direction] <> invalid then
      focusTo = m.map[m.mapFocus][direction]
      if focusTo = "ointro" and not m.oIntro.visible then
        focusTo = m.mapFocus
      end if
    else
      focusTo = m.mapFocus
    end if
  end if
  return focusTo
end function
sub turnFocusTo(id)
  ' if m.mapFocus <> id then
  if m.top.debug then print ghLogHead();"turnFocusTo -- "; m.mapFocus; " -> "; id
  oFrom = m.top.findNode(m.mapFocus)
  oTo = m.top.findNode(id)
  if oFrom <> invalid then ' apago el actual
    oFrom.setFocus(false)
    oFrom.focus = false
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- no encuentro";m.mapFocus
  end if
  if oTo <> invalid then
    m.mapFocus = id
    oTo.focus = true
    oTo.setFocus(true)
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- no encuentro";id
  end if
  ' else
  '   if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; m.mapFocus
  ' end if
end sub
' TIMER - Close
' -----------------------------
sub TimerInit()
  if m.top.debug then print ghLogHead();"TimerInit **"
  m.tmrClose = CreateObject("roSGNode", "Timer")
  m.tmrClose.ObserveField("fire", "TimerTrigger")
  m.tmrClose.repeat = true ' una vez
  m.tmrClose.duration = 1
  TimerReset()
end sub
sub TimerGo()
  if m.top.debug then print ghLogHead();"TimerGo **"
  TimerReset()
  m.tmrClose.control = "start"
end sub
sub TimerPause()
  if m.top.debug then print ghLogHead();"TimerPause **"
  m.tmrClose.control = "stop"
  TimerReset()
end sub
sub TimerReset()
  m.isSeek = false
  if m.top.debug then print ghLogHead();"TimerReset **"
  m.timerCount = ghGetChild(m.global, "timers.panels.player", 10)
end sub
sub TimerTrigger()
  m.timerCount -= 1
  if m.timerCount < 1 then
    if m.top.debug then print ghLogHead();" TIMER - GO !!"
    TimerReset()
    ' handleContinue()
    m.top.cmd = { "command": "exit" }
  end if
end sub
' UTILITIES
' -----------------------------
sub _setPosition(newval) ' salto absoluto de tiempo
  if newval <> invalid then
    if m.top.debug then print ghLogHead();"_setPosition -- ";newval
    if newval > m.top.duration then newval = m.top.duration
    if newval < 0 then newval = 0
    if m.top.debug then print ghLogHead();"_setPosition -- ";newval
    m.top.position = newval
    refreshPosition()
  end if
end sub
sub _movePosition(offset = 0) ' salto relativo de tiempo
  _setPosition(m.top.position + offset)
end sub
function _posToTime(position) as string ' formato de tiempos
  segundos = position mod 60
  minutos = Int(position / 60) ' integer
  horas = Int(minutos / 60)
  minutos = minutos - (horas * 60)
  if horas > 0 then ' ------------
    txtHor = ""
    if horas < 10 then txtHor += "0"
    txtHor += horas.toStr() + ":"
  else
    txtHor = "00:"
  end if
  if minutos > 0 then ' ------------
    txtMin = ""
    if minutos < 10 then txtMin += "0"
    txtMin += minutos.toStr() + ":"
  else
    txtMin = "00:"
  end if
  txtSec = "" ' ------------
  if segundos < 10 then txtSec += "0"
  txtSec += segundos.toStr()
  ' ------------
  time = txtHor + txtMin + txtSec
  if m.top.debug then print ghLogHead();"_posToTime -- ";position;" > ";time
  return time
end function
' OMITIR INTRO
' -----------------------------
sub checkOIntro()
  if m.top.showOmitirIntro = true then
    m.oIntro.visible = true
  else
    m.oIntro.visible = false
    turnFocusTo("playbuttons")
  end if
end sub

sub oIntroSelected()
  m.top.cmd = { "command": "omitirIntro" }
end sub

' ---------------------
' REFERENCIA
' ---------------------
' m.btnLang.setFields({
'   id: "btnLang"
'   value: "lang"
'   width: 100
'   height: 120
'   icon: ghGetImageByMode("player/player_btn_idiomas_base.png")
'   ' iconColor: "0x0000FF"
'   iconWidth: 60
'   iconHeight: 60
'   ' iconOver: ghGetImageByMode("player/player_btn_fwd_over.png")
'   ' iconColorOver: "0xFF0990"
'   ' iconWidthOver: 60
'   ' iconHeightOver: 60
'   foco: ghGetImageByMode("player/player_focus_round.png")
'   ' focoColor: "0x00FFFF"
'   focoWidth: 76
'   focoHeight: 76
'   text: gh Translate("player_lang", "Idiomas")
'   textColor: "0xFFFFFFFF"
'   textOffset: 10
' })





' sub changeButtonPlay(play = true)
'   if m.btnPlay <> invalid then
'     if play = true then
'       m.btnPlay.text = gh Translate("player_pause", "Pausa") 'falta key
'       m.btnPlay.icon = ghGetAsset("player_pause_icon", "")
'       m.btnPlay.iconOver = ghGetAsset("player_pause_icon", "")
'     else
'       m.btnPlay.setFields({
'         text: gh Translate("player_play", "Play") 'falta key
'         icon: ghGetAsset("player_play_icon", "")
'         iconOver: ghGetAsset("player_play_icon", "")
'       })
'     end if
'   end if
' end sub