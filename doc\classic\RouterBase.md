# BaseRouter

## Show

@Public

- Developer should extend BaseScene and work in it's context.
- Function show(args) should be overrided in channel.

@Sample: // MainScene.xml

```xml
<?xml version="1.0" encoding="UTF-8"?>
<component name="MainScene" extends="BaseScene">
  <script type="text/brightscript" uri="pkg:/components/MainScene.brs" />
</component>
```

```
  // MainScene.brs
  sub Show(args)
      homeGrid = CreateObject("roSGNode", "GridView")
      homeGrid.content = GetContentNodeForHome() ' implemented by developer
      homeGrid.ObserveField("rowItemSelected","OnGridItemSelected")
      'this will trigger job to show this View
      m.top.ComponentController.callFunc("show", {
          view: homeGrid
      })
  end sub
```

## Theme

@Public

Theme is used to customize the appearance of all SGDEX views.
For common fields see [SGDEXComponent](#SGDEXComponent#fields#theme)
For view specific views see view documentation

- [GridView](#GridView#fields#theme)
- [DetailsView](#DetailsView#fields#theme)
- [VideoView](#VideoView#fields#theme)
- [CategoryListView](#CategoryListView#fields#theme)

<b>Theme can be set to several levels</b>

<b>Any view:</b>

```json
scene.theme = {
  global: {
    textColor: "FF0000FF"
  }
}
```

Set's all text colors to red
<b>Type of view</b>

```json
scene.theme = {
  gridView: {
    textColor: "FF0000FF"
  }
}
```

Set's all grids text color to red
<b>Instance specific:</b>
use view's theme field to set it's theme

```javascript
view = CreateObject("roSGNode", "GridView");
view.theme = {
  textColor: "FF0000FF",
};
```

this grid will only have text color red
All theme fields are combined and used by view when created, so you can set

```json
scene.theme = {
  global: {
    textColor: "FF0000FF"
  }
  gridView: {
    textColor: "00FF00FF"
  }
}
view1 = CreateObject("roSGNode", "GridView")
view2 = CreateObject("roSGNode", "GridView")
view.2theme = {
  textColor: "FFFFFFFF"
} detailsView= CreateObject("roSGNode", "DetailsView")
```

In this case

- view1 - will have texts in 00FF00FF
- view2 - will have FFFFFFFF
- detailsView - will take textColor from global and it will be FF0000FF

## updateTheme

@Public
Field to update themes by passing the config

Structure of config is same as for [theme](#BaseScene#fields#theme) field.

You should only pass fields that should be updated not all theme fields.

Note. if you want to change a lot of fields change them with as smaller amount of configs as you can, it wouldn't redraw views too often then.
