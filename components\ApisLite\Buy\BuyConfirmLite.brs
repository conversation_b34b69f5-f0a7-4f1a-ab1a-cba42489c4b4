' BuyConfirm
' ---------------------------

sub DataInit()
  m.top.debug = true

  if Instr(1, m.top.link, "buyconfirm") > 0 then
    m.llamado = "buyconfirm"

    m.api.method = "POST"
    m.api.url = m.config.mfwk.host + m.top.link

    m.api.query.delete("api_version")

    m.api.query.Append({
      "user_id": ghGetRegistry("user_id", "user"),
    })
    m.api.timeout = 30000

    ' default
    if m.top.extra_params <> invalid then
      extra_params = FormatJson(m.top.extra_params)
    else
      extra_params = ""
    end if

    m.api.body = ghArray2Query({
      "token": ghGetRegistry("user_token", "user")
      "buyToken": m.top.buyToken
      "extra_params": extra_params
    }, "")
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"body = ";res.errors
    print ghLogHead();"body.entry = ";ghGetChild(res, "entry", "! no existe")
    print ghLogHead();"body.response = ";ghGetChild(res, "response", "! no existe")
    print ghLogHead();"body.response.extra_data";ghGetChild(res, "response.extra_data", "! no existe")
  end if


  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' armo la respuesta ok
  content = ghGetChild(res, "response", res)
  data_code = ghGetChild(res, "response.code", "Mensaje por defecto") ' TODO: DEFAULTS!!!!
  titYCont = ghTranslate(data_code, data_code)
  titYContSeparados = ghDividirTitCont(titYCont, "¡Transacción exitosa!", "Su transacción ha sido exitosa.")
  content.msgContent = titYContSeparados.content ' por ahora hardco
  content.msgTitle = titYContSeparados.title

  updateGlobalArray("lasttouch", {
    purchased: ghGetChild(res, "response.lastTouch")
  })

  m.top.content = content
end sub

' ******************************************
' OTRO
' ******************************************
sub Otro()
  try
    m.port = createObject("roMessagePort")
    http = createObject("roUrlTransfer")
    http.SetCertificatesFile("common:/certs/ca-bundle.crt")
    http.AddHeader("X-Roku-Reserved-Dev-Id", "")
    http.InitClientCertificates()

    config = m.global.config
    ' path = "/services/payway/workflowstart"
    params = {
      "authpn": config.mfwk.authpn,
      "authpt": config.mfwk.authpt,
      "api_version": config.mfwk.api_version
    }
    ' armado
    m.url = config.mfwk.host + m.top.link + ghArray2Query(params, "")
    if m.top.debug then print ghLogHead();"URL=";m.url
    ' ----------------------------------
    http.setUrl(m.url)
    http.setMessagePort(m.port)
    if http.AsyncGetToString() then
      msg = m.port.waitMessage(0)
      if type(msg) = "roUrlEvent" then
        ' -------------------------
        headers = msg.GetResponseHeaders()
        response = msg.GetResponseCode()
        if m.top.debug then print ghLogHead();"Headers = ";headers
        if m.top.debug then print ghLogHead();"Response Code = ";response
        if response = 200 then
          body = ParseJson(msg.getString())
          if m.top.debug then print ghLogHead();"Body = ";body
          Otro_DataProcess(body)
        else
          m.top.content = ghErrorGetJson({
            number: response,
            message: "NETWORK ERROR"
          })
        end if
        ' -------------------------
      end if
    end if
  catch error
    ghErrorDump(error)
    m.top.content = ghErrorGetJson(error)
  end try
end sub
sub Otro_DataProcess(body)
  if m.top.debug then print "*********************************"
  if m.top.debug then print "*********************************"
  if m.top.debug then print ghLogHead();"body = ";body
  if m.top.debug then print ghLogHead();"body.response = ";ghGetChild(body, "response")

  if body.msg = "ERROR" then

    if m.top.debug then print ghLogHead();"body.response.errors = ";ghGetChild(body, "errors")
    m.top.error = body

  else

    m.top.content = body

  end if


  if m.top.debug then print "*********************************"
  if m.top.debug then print "*********************************"
end sub
