<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->

<component name="ProfileSelectPageLite" extends="Page">
  <script type="text/brightscript" uri="ProfileSelectPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="data" type="assocarray" onChange="refreshData" />
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <Poster id="logo" translation="[64,32]" width="123" height="24" loadDisplayMode="scaleToFit"/>
    <Label id="title" translation="[0,160]" width="1280" vertAlign="center" horizAlign="center" text="" />
    <GHRowList id="theGrid" translation="[0, 200]" visible="true"/>
    <!-- <Label id="msgNoMethod" translation="[400,292]" visible="false" width="480" height="120" horizAlign="center" text="msgNoMethod" wrap="true" /> -->
    <!-- <GHButton id="cancel" translation="[400,50]" value="CANCEL" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" /> -->
    <GHButton id="administrar" visible="false" translation="[468,540]" value="ADMINISTRAR" width="344" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
    <GHLoading id="spinner" visible="true" />
    <!-- <GHButton id="acept" translation="[600,412]" value="ACEPT" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />-->
  </children>

</component>
