sub lite_jumpStart(ipaddr)
  ' TODO hardcodeado para pruebas
  ' ghSetRegistry("region", "mexico")

  m.logger.debug("Inicio de aplicacion Lite - ipaddr:", { ip: ipaddr })

  m.apiIsLoggedIn = ghCallApi("IsLoggedInLite", "IsLoggedInLiteOk", "errorIsLoggedInLite", false)
  if ipaddr <> invalid then
    m.apiIsLoggedIn.ipaddr = ipaddr
  end if
  m.apiIsLoggedIn.control = "run"
end sub

sub errorIsLoggedInLite()
  m.logger.error("Error en la llamada a API IsLoggedInLite")

  ErrorInicializacion("LILI00")
end sub

sub IsLoggedInLiteOk()
  isLoggedIn = ghGetRegistry("isLoggedIn")

  m.logger.debug("IsLoggedInLiteOk - isLoggedIn:", { isLoggedIn: isLoggedIn })

  if isLoggedIn = "true" then
    if m.skipProfile = invalid or m.skipProfile <> true then
      m.logger.debug("Flujo a ProfileSelector, skipProfile:", { skipProfile: m.skipProfile })
      FlujoAProfileSelector()
      return
    end if
  end if

  headSession()
end sub

sub headSession()
  m.logger.debug("llamada a API PushSession")
  m.apiPushSession = ghCallApi("PushSession")

  headProfile()
end sub

sub headProfile()
  logged = ghGetRegistry("isLoggedIn")

  m.logger.debug("headProfile - logged:", { logged: logged })

  ' informacion de super-highlight
  ghCallApi("SuperHighlightLite")

  if logged = "true" then
    m.logger.debug("llamada a API ProfileLite")
    m.apiProfile = ghCallApi("ProfileLite", "headNav", "errorProfileLite")
  else
    headNav()
  end if
end sub

sub errorProfileLite()
  m.logger.error("Error en la llamada a API ProfileLite")

  ErrorInicializacion("LPRF00")
end sub

sub headNav()
  m.logger.debug("llamada a API NavLite")

  m.apiProfile = ghCallApi("NavLite", "headFinal", "errorNavLite")
end sub

sub errorNavLite()
  m.logger.error("Error en la llamada a API NavLite")

  ErrorInicializacion("LNAV00")
end sub

sub headFinal()
  m.logger.debug("headFinal - wrNode:", m.wrNode)

  if m.wrNode <> invalid then
    menuOption = ghGetItemByProperty(m.global.nav.items, "id", m.wrNode)

    if menuOption = invalid then
      menuOption = ghGetItemByProperty(m.global.nav.childs, "id", m.wrNode)
    end if

    if menuOption <> invalid then
      menuCode = menuOption.code
      m.global.navSelect = menuCode
    end if
  end if

  isLoggedIn = m.apiIsLoggedIn.content?.isLoggedIn
  acceptedTerms = m.apiIsLoggedIn.content?.accepted_terms

  m.logger.debug("headFinal", { isLoggedIn: isLoggedIn, acceptedTerms: acceptedTerms })

  if isLoggedIn <> invalid and isLoggedIn then
    if acceptedTerms = 0 then
      FlujoALosTerminosYCondiciones()
      return
    end if
  end if

  if m.skipHome = invalid or m.skipHome = false then
    FlujoALaHome()
  else
    setLoading(false)
  end if
end sub

sub FlujoAProfileSelector()
  enabled = ghGetChild(m.global, "profiles_config.enable", false)

  m.logger.debug("chequeando flujo a home o seleccion de perfiles, profiles_config.enabled:", { enalbed: enabled })

  if enabled then
    m.logger.debug("Flujo a pantalla ProfileSelectPageLite")

    ' saca el logo de claro ( que se muestra al cargar la app )
    m.top.backgroundUri = ""

    pagina = CreateObject("roSGNode", "ProfileSelectPageLite")
    pagina.id = "ProfileSelectPageLite"
    pagina.ObserveField("value", "BackFromProfileSelector")
    pagina.setFields({})
    m.top.ComponentController.allowCloseChannelOnLastView = false
    m.top.ComponentController.callFunc("show", { view: pagina })
  else
    headSession()
  end if
end sub

sub BackFromProfileSelector()
  m.logger.debug("saliendo ProfileSelectPageLite, volviendo a la home")

  setLoading(true)
  headSession()
end sub