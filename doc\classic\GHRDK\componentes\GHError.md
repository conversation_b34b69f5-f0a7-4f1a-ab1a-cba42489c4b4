# GHError

## Propiedades

| Nombre      | Tipo    | Default  | Descripción                                 |
| ----------- | ------- | -------- | ------------------------------------------- |
| title       | string  | Alert    | título del popup                            |
| descrip     | string  |          | descripción del error                       |
| translation | string  | [0,0]    | posición del popup                          |
| width       | string  | 1280     | ancho del popup                             |
| height      | string  | 720      | alto del popup                              |
| backColor   | string  | 0xFFFFFF | color del fondo (puede tener transparencia) |
| focus       | boolean | false    | si el popup está en foco                    |
| value       | string  |          | valor que devuelve el popup                 |
| wasClosed   | boolean | false    | el popup fue cerrado                        |
| debug       | boolean | false    | prende los logs de debug                    |

## Ejemplo

Declaración del grupo en el xml.

```xml
<GHError id="error" />
```

Manejo del error.

```basic
' declaracion
m.error = m.top.findNode("error")
m.error.ObserveField("wasClosed", "BackFromError")

' prende el error
m.error.title = "Error"
m.error.descrip = "(!) No aceptó los Términos y Condiciones."
turnFocusTo("error")

' para la vuelta
sub BackFromError()
  turnFocusTo("botonera")
end sub

```