<?xml version="1.0" encoding="utf-8"?>

<component name="PurchaseCodigoPromo" extends="Page">

  <script type="text/brightscript" uri="PurchaseCodigoPromo.brs" />

  <interface>
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />

    <!-- interfaz interna -->
    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus"
    alwaysNotify="true" /> -->
    <!-- <field id="debug" type="boolean" value="false" /> -->
  </interface>

  <children>
    <Label id="title" focusable="false" translation="[0,164]" width="1280" height="48" text="*" />
    <Label id="descrip" focusable="false" translation="[0,236]" width="1280" height="32" text="*" />

    <GHButtonGroup id="botonera" layout="map" orientation="vertical">
      <GHInput
        id="cvv"
        maxTextLength="21"
        placeholder="CVV"
        translation="[400,300]"
        color="0xFFFFFF"
        selColor="#7F8086"
        focusColor="0xFFFFFF"
        placeholdercolor="#7F8086"
        password="false"
        width="504"
        height="72"
        titleColor="0xFFFFFF"
        messageColor="0xFFFFFF"
      />

      <GHButton id="accept" text="ACCEPT" value="ACCEPT" translation="[400,424]" color="0xFFFFFF"
        selColor="#FFFFFF" width="504" height="72" backcolor="#981C15" selBackColor="#981C15"
        focusColor="0xFFFFFF" />

      <GHButton id="cancel" text="CANCEL" value="CANCEL" translation="[400,487.33]" color="0xFFFFFF"
        selColor="#FFFFFF" width="504" height="72" backcolor="#2E303D" selBackColor="#2E303D"
        focusColor="0xFFFFFF" />
    </GHButtonGroup>

    <GHError id="error" />

  </children>
</component>