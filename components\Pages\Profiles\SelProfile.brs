' LANDINGpage
'

sub Init()
  m.top.getScene().updateTheme = m.global.config.theme
  ' ghSetBackground("http://cdn.ripoll.ar/roku/landingbg_HD.png") 'de momento faltaría tener el key y nombre de la imagen
  ' general de pantalla
  m.map = { "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid } }
  ' botonera
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnExitToHome")
  ' texts
  m.fondo = m.top.findNode("fondo")
  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(40, "bold")
  m.btn1 = m.top.findNode("btn1")
  m.btn2 = m.top.findNode("btn2")
  ' api
  ' m.apiProfileAvatars = ghCallApi("ProfileAvatars", "forfun") ' API Apa
end sub

sub updateFieldFocus(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
  if data then
    Refresh()
    turnFocusTo("botonera")
  end if
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
end sub
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if child.selected then
    child.selected = false
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
    if child.value = "back" then
      OnExitToHome()
    else ' if child.value = "page_player" then
      print "BUTTON: %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% | ";child.value
      m.top.routerReset = {
        page: "HomePage",
        fields: { nodo: "" }
      } ' recargo la home forzada.
    end if
  end if
end sub
sub OnExitToHome() ' event
  print ghLogHead();"OnExitToHome"
  m.top.routerClose = true
end sub
' DATA
' -----------------------------
sub forfun(event)
  ghDumpEvent(event)
end sub
' REDIBUJO
' -----------------------------
sub Refresh()
  print ghLogHead();"Refresh. w/";m.top.contenido
  m.title.text = "Perfiles"
  m.fondo.uri = ghGetChild(m.top.contenido, "data.hdposterurl", "")
  ' m.btn1.text = gh Translate("vcard_button_player>????", "player")
  ' m.btn2.text = gh Translate("vcard_button_volver>????", "volver")
end sub
' EVENTS
' -----------------------------
function onKeyEvent(key, press)
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    end if
  end if
  return handled
end function
function guessFocusTo(direction) as string
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function
' -----------------------------
