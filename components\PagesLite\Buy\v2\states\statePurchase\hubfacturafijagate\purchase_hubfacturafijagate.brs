sub hubfacturaFlow()
  m.top.debug = false

  ' m.top.getScene().updateTheme = m.global.config.theme

  ' m.title = m.top.findNode("title")
  ' m.title.font = ghGetFont(40, "regular")
  ' m.title.text = ghTranslate("MDP_Formulario_DoubleOption_TextoTitulo_{gateway}", "")
  ' '   m.title.text = ghTranslate("login_access_title_label", "Inicia sesión")

  ' m.descrip = m.top.findNode("descrip")
  ' m.descrip.font = ghGetFont(20, "regular")
  ' m.descrip.text = "Código de sucursal + 2 últimos dígitos de la cédula de identidad. Obtén tu código de sucursal en tu estado de cuenta"

  ' m.user = m.top.findNode("user")
  ' m.user.placeholder = "Código de sucursal + 2 últimos dígitos de la cédula de identidad"

  ' m.loading = m.top.findNode("loading")

  ' m.botonera = m.top.findNode("botonera")
  ' m.botonera.ObserveField("backSelected", "BackTo")
  ' m.botonera.ObserveField("selected", "OnBotoneraSelected")
  ' m.botonera.map = {
  '   "user": { "up": invalid, "right": invalid, "down": "user2", "left": invalid },
  '   "user2": { "up": "user", "right": invalid, "down": "continueButton", "left": invalid },
  '   "continueButton": { "up": "user2", "right": invalid, "down": "cancelButton", "left": invalid },
  '   "cancelButton": { "up": "continueButton", "right": invalid, "down": invalid, "left": invalid } }

  m.checkoutFieldType = m.top.findNode("checkoutFieldType")
  m.checkoutFieldType = "hubfacturafijagate"
  ' m.continueButton = m.checkoutFields.findNode("continueButton")
  ' m.cancelButton = m.checkoutFields.findNode("cancelButton")
  ' m.continueButton.ObserveField("selected", "OnContinueSelected")
  ' m.cancelButton.ObserveField("selected", "OnCancelSelected")

  ' m.user = m.top.findNode("user")
  ' m.user2 = m.top.findNode("user2")
  ' print "*** Setting up user2 button observer ***"
  ' print "user2 button found: " m.user2 <> invalid

  ' ' Since user2 is inside GHButtonGroup (botonera), we need to observe the group's selected field
  ' ' and check which button was selected in the handler
  ' print "*** Setting up botonera observer for button selection ***"
  ' ' Note: m.botonera.ObserveField("selected", "OnButtonSelected") is already set up above
  ' ' We need to modify the existing OnButtonSelected handler or create a new one

  ' m.error = m.top.findNode("error")
  ' m.error.ObserveField("wasClosed", "BackFromError")
end sub


' ' sub onWasShown(event)
' '   data = event.getData()
' '   if data then
' '     turnFocusTo("botonera")
' '   end if
' ' end sub

' ' sub updateFieldFocus()
' '   if m.top.focus then
' '     turnFocusTo("botonera")
' '   end if
' ' end sub

' ' Handler for botonera (GHButtonGroup) selection events
' ' sub OnBotoneraSelected(event)
' '   print "*** OnBotoneraSelected called ***"
' '   child = event.getRoSGNode()
' '   value = child.value
' '   print "Selected button value: " value

' '   ' Check which button was selected based on its value
' '   if value = "user2" then
' '     ' The user2 button was selected, open the markup grid
' '     OpenMarkupGrid()
' '   end if
' ' end sub

' ' Function to open the markup grid
' ' sub OpenMarkupGrid()
' '   print "*** Opening markup grid for user2 button ***"

' '   ' Reset button state
' '   m.user2.selected = false

' '   ' Try a simple test first - create a basic markup grid without JSON
' '   print "*** Creating CustomMarkGrid component ***"
' '   ScrCodigo = CreateObject("roSGNode", "CustomMarkGrid")

' '   if ScrCodigo <> invalid then
' '     print "*** CustomMarkGrid created successfully ***"
' '     ScrCodigo.id = "markupGrid"

' '     ' Create test data with service_id property as required by CustomMarkGrid
' '     jsonData = loadJsonFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubfacturafijagate/hubfacturafijagate.json")

' '     ScrCodigo.gridData = jsonData.response.account_data
' '     ScrCodigo.gridType = "service"
' '     ScrCodigo.ObserveField("wasClosed", "onMarkupGridClosed")
' '     ScrCodigo.ObserveField("itemSelected", "onMarkupGridRowItemSelected")

' '     print "*** Opening markup grid via routerChild ***"
' '     print "*** ScrCodigo object: " ScrCodigo
' '     print "*** ScrCodigo.id: " ScrCodigo.id

' '     ' Try using the component object directly
' '     m.top.routerChild = { page: ScrCodigo }
' '     print "*** routerChild set with component object ***"

' '     ' Alternative: Try using string name if object doesn't work
' '     ' m.top.routerChild = { page: "CustomMarkGrid" }
' '     ' print "*** routerChild set with string name ***"
' '   else
' '     print "*** ERROR: Could not create CustomMarkGrid component ***"
' '     ShowInputError()
' '   end if
' ' end sub

' ' sub OnContinueSelected(event)
' '   button = event.getRoSGNode()
' '   ' Validate input format before proceeding
' '   userInput = m.user.value
' '   print "userInput " userInput
' '   if ValidateFijaGateInput(userInput) then
' '     m.top.value = {
' '       opcion: "SELECT"
' '       data: userInput
' '     }
' '     m.top.visible = false
' '   else
' '     ' data = loadJsonFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubfacturafijagate/hubfacturafijagate.json")
' '     ' print "hubfacturaData " data.response.account_data
' '     ' ScrCodigo = CreateObject("roSGNode", "CustomMarkGrid")
' '     ' ScrCodigo.id = "markupGrid"
' '     ' ScrCodigo.gridData = data.response.account_data
' '     ' ScrCodigo.gridType = "service"
' '     ' ScrCodigo.ObserveField("wasClosed", "FijaGate_Return")
' '     ' m.top.routerChild = { page: ScrCodigo }
' '     ShowInputError()
' '   end if
' ' end sub

' ' sub OnCancelSelected(event)
' '   button = event.getRoSGNode()
' '   m.top.value = {
' '     opcion: "BACK"
' '     data: ""
' '   }
' '   m.top.close = true
' ' end sub



' ' sub BackFromError()
' '   turnFocusTo("botonera")
' ' end sub

' sub BackTo()
'   m.top.routerClose = true
' end sub

' ' Handler for when markup grid is closed
' ' sub onMarkupGridClosed(event)
' '   print "Markup grid closed, returning focus to user2 button"
' '   turnFocusTo("botonera")
' ' end sub

' ' Handler for when an item is selected from markup grid
' ' sub onMarkupGridRowItemSelected(event)
' '   selectedIndex = event.getData()
' '   if selectedIndex <> invalid and type(selectedIndex) = "roInt" then

' '     gridContent = event.getRoSGNode().content

' '     if gridContent <> invalid and gridContent.getChildCount() > selectedIndex then
' '       selectedItem = gridContent.getChild(selectedIndex)

' '       if selectedItem <> invalid then
' '         itemTitle = ghGetChild(selectedItem, "title", "")
' '         event.getRoSGNode().visible = false
' '         ScrCodigo = CreateObject("roSGNode", "hubfacturafijagate")
' '         user2 = ScrCodigo.findNode("user2")
' '         user2.text = itemTitle
' '         m.top.routerChild = { page: ScrCodigo }

' '         turnFocusTo("botonera")
' '       end if
' '     end if
' '   end if
' ' end sub

' ' Handler for FijaGate return (used in OnContinueSelected)
' ' sub FijaGate_Return(event)
' '   print "FijaGate markup grid closed, returning focus"
' '   turnFocusTo("botonera")
' ' end sub

' ' Validation function for FijaGate input format
' ' Expected format: 11 digits + "+" + 2 digits (e.g., 12345678910+12)
' ' function ValidateFijaGateInput(input as string) as boolean
' '   if input = invalid or input = "" then
' '     return false
' '   end if

' '   ' Remove any whitespace
' '   cleanInput = input.trim()

' '   ' Check if input contains exactly one "+" symbol
' '   plusIndex = inStr(1, cleanInput, "+")
' '   if plusIndex = 0 then
' '     return false
' '   end if

' '   ' Split by "+" symbol
' '   beforePlus = Left(cleanInput, plusIndex - 1)
' '   afterPlus = Mid(cleanInput, plusIndex + 1)

' '   ' Validate: 11 digits before "+" and 2 digits after "+"
' '   if Len(beforePlus) = 11 and Len(afterPlus) = 2 then
' '     ' Check if all characters before "+" are digits
' '     for i = 1 to Len(beforePlus)
' '       char = Mid(beforePlus, i, 1)
' '       if char < "0" or char > "9" then
' '         return false
' '       end if
' '     end for

' '     ' Check if all characters after "+" are digits
' '     for i = 1 to Len(afterPlus)
' '       char = Mid(afterPlus, i, 1)
' '       if char < "0" or char > "9" then
' '         return false
' '       end if
' '     end for

' '     return true
' '   end if

' '   return false
' ' end function

' ' ' Show error message for invalid input
' ' sub ShowInputError()
' '   m.error.title = "Formato incorrecto"
' '   m.error.descrip = "Ingresa 11 dígitos del número de servicio + símbolo '+' + 2 últimos dígitos de tu cédula. Ejemplo: 12345678910+12"
' '   m.error.visible = true
' '   turnFocusTo("error")
' ' end sub

' function onKeyEvent(key, press) as boolean
'   handled = false

'   return handled
' end function

' Validate input as user types
' sub validateInput(event)
'   textField = event.getRoSGNode()
'   userInput = textField.text

'   ' Enable/disable continue button based on validation
'   if ValidateFijaGateInput(userInput) then
'     ' Valid input - enable continue button
'     if m.continueButton <> invalid then
'       m.continueButton.opacity = 1.0
'       m.continueButton.enabled = true
'     end if
'   else
'     ' Invalid input - disable continue button
'     if m.continueButton <> invalid then
'       m.continueButton.opacity = 0.5
'       m.continueButton.enabled = false
'     end if
'   end if
' end sub