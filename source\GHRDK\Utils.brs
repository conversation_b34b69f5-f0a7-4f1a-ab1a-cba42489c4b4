' ------------------------------------------------
' UTILS
' byGoose(2020) (<EMAIL>)
' ------------------------------------------------

' TRANSLATION
' --------------------------------------------------------------
function ghVal2Trans(x, y)
  punto = ghXYtoAbstract(x, y)
  return "[" + str(punto.x) + "," + str(punto.y) + "]"
end function
function ghXYtoAbstract(x, y)
  info = CreateObject("roDeviceInfo").getDisplaySize()
  ' print ghLogHead();"ghXTtoAbstract [";info.w",";info.h;"] (";x;",";y;")";
  if info.w = 1920 then
    x = (x / 1280) * 1920
    y = (y / 720) * 1080
  else if info.w = 1280
    x = x
    y = y
  else
    x = (x / 1280) * 1920
    y = (y / 720) * 1080
  end if
  ' print " => (";x;",";y;")"
  return { "x": x, "y": y }
end function
function ghXtoAbstract(x)
  info = ghGetGlobalWH()
  ' print ghLogHead();"ghXtoAbstract [";info.w;"] (";x;")";
  if info.w = 1920 then
    x = (x / 1280) * 1920
  else if info.w = 1280
    x = x
  else
    x = (x / 1280) * 1920
  end if
  ' print " => (";x;")"
  return x
end function
function ghYtoAbstract(y)
  info = ghGetGlobalWH()
  ' print ghLogHead();"ghYTtoAbstract [";info.h;"] (";y;")";
  if info.w = 1920 then
    y = (y / 720) * 1080
  else if info.w = 1280
    y = y
  else
    y = (y / 720) * 1080
  end if
  ' print " => (";y;")"
  return y
end function
function ghGetGlobalWH()
  try
    res = CreateObject("roDeviceInfo").getDisplaySize()
  catch tryError
    print "ERROR ghGetGlobalWH >>> " ' ;tryError
    res = { w: 1280, h: 720 } ' por defecto HD
  end try
  return res
end function
function ghDumpTrans(trans) as string
  return "[" + StrI(trans[0]) + "," + StrI(trans[1]) + "]"
end function

function ghTranslate(key as string, default as string, data = {} as object)
  newData = invalid

  try
    translation = m.global.translations
  catch tryError
    print "ERROR ghTranslate >>>";tryError
    translation = {} ' por defecto, nada.
  end try

  'Simulacion del nodo translation
  ' translation = {
  '   landing_menu_title_label: "Porque viste {@pelicula} te recomendamos"
  ' }

  if translation <> invalid then
    newData = translation.LookupCI(key)
  end if
  if newData = invalid then ' No encontre la key
    ' newData = default ' GOOSE -- No quieren defaults -- JAN
    ' newData = " "
    ' newData = "[" + key + "] " + default
    newData = key
  end if
  for each field in data ' reemplazo
    busco = "{@" + field + "}"
    newData = ghReplaceStr(newData, busco, data[field])
  end for
  newData = ghReplaceStr(newData, "{br}", chr(10))

  ' reemplazo en gral, si la traduccion tiene la variable @EMAIL
  email = ghGetChild(m.global, "user.email", "")
  newData = ghReplaceStr(newData, "{@EMAIL}", email)
  newData = ghTrim(newData)

  return ghDecodeHTML(newData.toStr())
end function

' DISPLAY MODES
' --------------------------------------------------------------
function ghGetDisplayMode() as string
  ' print "GetDisplayProperties ";CreateObject("roDeviceInfo").GetDisplayProperties()
  ' print "GetUIResolution ";CreateObject("roDeviceInfo").GetUIResolution()
  uiResName = CreateObject("roDeviceInfo").GetUIResolution()?.name
  return uiResName
end function
function ghGetImageByMode(file)
  return "pkg:/images/" + ghGetDisplayMode() + "/" + file
end function

' TEXT
' --------------------------------------------------------------
function ghCapitaliceText(texto) as string
  textoFinal = Mid(UCase(texto), 0, 1) + Mid(LCase(texto), 2)
  return textoFinal
end function
function ghReplaceStr(origen, busco, reemplazo)
  if origen <> invalid and reemplazo <> invalid then
    res = origen
    posicion = Instr(1, res, busco)
    while posicion <> 0
      antes = Mid(res, 1, posicion - 1)
      despues = Mid(res, posicion + Len(busco))
      res = antes + reemplazo + despues
      posicion = Instr(1, res, busco)
    end while
    return res
  end if
end function
function ghDividirTitCont(ori, defaultTitle, defaultMsg)
  ' print ghLogHead();"ghDividirTitCont -- ";ori, defaultTitle, defaultMsg
  dividido = ghSplit(ori, "|#|")
  ' print ghLogHead();"ghDividirTitCont -- dividido=";dividido.Count(), dividido
  salida = {
    title: defaultTitle,
    content: defaultMsg
  }
  if dividido.Count() = 2 then ' me viene titulo y mensaje
    salida.title = dividido[0]
    salida.content = dividido[1]
  else if dividido.Count() = 1 then ' solo me viene contenido
    salida.content = dividido[0]
  end if
  ' ya tengo los valores
  salida.title = ghDecodeHTML(salida.title)
  salida.content = ghDecodeHTML(salida.content)
  salida.content = ghReplaceStr(salida.content, "{br}", chr(10))
  ' print ghLogHead();"ghDividirTitCont -- salida=";salida
  return salida
end function
function ghSplit(var, separator = " ")
  if Len(separator) > 1 then
    var = ghReplaceStr(var, separator, chr(187))
    separator = chr(187) ' ╗
  end if
  result = []
  word = ""
  for i = 1 to Len(var)
    car = Mid(var, i, 1)
    if car <> separator then
      word += car
    else
      result.push(word)
      word = ""
    end if
  end for
  result.push(word)
  return result
end function
function ghDecodeHTML(ori)
  reemplazos = [
    ["&aacute;", "á"], ["&eacute;", "é"], ["&iacute;", "í"], ["&oacute;", "ó"], ["&uacute;", "ú"],
    ["&Aacute;", "Á"], ["&Eacute;", "É"], ["&Iacute;", "Í"], ["&Oacute;", "Ó"], ["&Uacute;", "Ú"],
    ["&aacute", "á"], ["&eacute", "é"], ["&iacute", "í"], ["&oacute", "ó"], ["&uacute", "ú"],
    ["&Aacute", "Á"], ["&Eacute", "É"], ["&Iacute", "Í"], ["&Oacute", "Ó"], ["&Uacute", "Ú"],
    ["&ntilde;", "ñ"], ["&Ntilde;", "Ñ"],
    ["&lsquo;", "`"], ["&rsquo;", "´"], ["&apos;", "'"],
    ["&nbsp;", " "], ["&iexcl;", "¡"], ["&iquest;", "¿"], ["&mdash;", "—"], ["&ndash;", "–"], ["&amp;", "&"],
    ["<br/>", " "], ["<br>", " "], ["<b>", " "], ["</b>", " "]
  ]
  res = ori
  for r = 0 to reemplazos.Count() - 1
    res = ghReplaceStr(res, reemplazos[r][0], reemplazos[r][1])
    ' print "[";reemplazos[r][0] + "]";
  end for
  ' print
  ' print ghLogHead();"ghDecodeHTML -- ", ori, res
  return res
end function
function ghTrim(str as string) as string
  st = CreateObject("roString")
  st.SetString(str)
  return st.Trim()
end function
function ghConvertToStringAndJoin(dataArray as object, divider = " | " as string, cant = 0) as string
  result = ""
  contador = 0
  if Type(dataArray) = "roArray" and dataArray.Count() > 0
    for each item in dataArray
      if cant <> 0 and cant = contador
        ' salir
        exit for
      end if
      if item <> invalid
        strFormat = invalid
        if GetInterface(item, "ifToStr") <> invalid
          strFormat = item.Tostr()
        else if GetInterface(item, "ifArrayJoin") <> invalid
          strFormat = item.Join(", ")
        end if
        if strFormat <> invalid then
          if result.Len() > 0 then result += divider
          result += strFormat
        end if
      end if
      contador = contador + 1
    end for
  end if
  return result
end function

' DATE / TIME
' --------------------------------------------------------------
function ghNow() as string
  date = CreateObject("roDateTime")
  date.ToLocalTime()
  return date.ToISOString() + "[" + StrI(date.GetMilliseconds()) + "]"
end function
function ghFormatDuration(data = invalid) as object
  'Actualmente dice 00:52 min debería ser 52 min. Cuando es en horas debe figurar así: 2 h 30 min
  if data <> invalid and data <> "" then
    constHora = " h "
    constMin = " min"
    ' print ghLogHead();"UTILS:ghFormatTime -- orig=";data
    partes = ghSplit(data, " ")
    ' print ghLogHead();"UTILS:ghFormatTime -- partes=";partes
    horamin = ghSplit(partes[0], ":")
    ' print ghLogHead();"UTILS:ghFormatTime -- horamin=";horamin
    hora = Val(horamin[0])
    min = Val(horamin[1])
    formatedTime = ""
    if hora > 0 then
      formatedTime = Str(hora) + constHora
    end if
    formatedTime += Str(min) + constMin
  else
    formatedTime = ""
  end if
  ' print ghLogHead();"UTILS:ghFormatTime -- RES=";formatedTime
  return formatedTime
end function
function ghFormatDurationEpg(data = invalid) as object
  'Actualmente dice 00:52 min debería ser 52 min. Cuando es en horas debe figurar así: 2 h 30 min
  if data <> invalid and data <> "" then
    constHora = ":"
    constMin = "hs"
    ' print ghLogHead();"UTILS:ghFormatTime -- orig=";data
    partes = ghSplit(data, "")
    ' print ghLogHead();"UTILS:ghFormatTime -- partes=";partes
    horamin = ghSplit(partes[0], ":")
    ' print ghLogHead();"UTILS:ghFormatTime -- horamin=";horamin
    hora = Val(horamin[0])
    min = Val(horamin[1])
    if Len(min.Tostr()) = 1 then
      min = "0" + min.Tostr()
    end if
    formatedTime = ""
    if hora > 0 then
      formatedTime = Str(hora) + constHora
    end if
    formatedTime += min.Tostr() + constMin
  else
    formatedTime = ""
  end if
  ' print ghLogHead();"UTILS:ghFormatTime -- RES=";formatedTime
  return formatedTime
end function

' LOGs
' --------------------------------------------------------------
function ghLogHead(selector = "") as string
  if selector <> "" then selector = " <" + selector + "> "
  return ghNow() + selector + "[" + m.top.subType() + "]{" + m.top.id + "} : "
end function
sub ghDumpEvent(event)
  print "================================================"
  print event
  print "..getData......................"
  print event.getData()
  print "..getField......................"
  print event.getField()
  print "..getRoSGNode......................"
  print event.getRoSGNode()
  print "..getNode......................"
  print event.getNode()
  print "..getInfo......................"
  print event.getInfo()
  print "================================================"
end sub
function ghDumpObject(obj)
  response = ""
  for each i in obj.Items()
    response += i.key + "=" + Str(i.value) + ", "
  end for
  return Left(response, Len(response) - 2)
end function
sub ghErrorDumpToLog(api = "", extra = {}, error = {})
  obj = {
    api: api,
  }
  backtrace = ""
  if error.backtrace <> invalid then
    for each item in error.backtrace
      backtrace += item.function + "(" + Str(item.line_number) + ") in " + item.filename + "; "
    end for
  end if
  if backtrace <> "" then obj.backtrace = backtrace
  if error.number <> invalid then obj.error_code = error.number
  if error.message <> invalid then obj.error_msg = error.message
  for each item in extra
    obj[item] = extra[item]
  end for
  ghLogDump(obj, "error")
end sub
sub ghLogDump(obj, tipo = "info")
  ' ejemplo de uso
  ' ghLogDump({"prueba": "todo bien con json :D"})
  ' ghLogDump("log string", "error")
  try
    if type(obj) = "roAssociativeArray" then
      obj = FormatJson(obj)
    else
      obj = obj.toStr()
    end if
    ' Call
    sendLog = ghCallApi("SendLog", "LogOK", "LogError", false)
    sendLog.type = tipo
    sendLog.description = obj
    sendLog.control = "run"
  catch error
    print ghLogHead();"ghLogDump -- ERROR: ";error
    print ghLogHead();"ghLogDump -- TYPE ERROR?: " + type(obj) + " not supported in ghLogDump."
  end try
end sub
sub LogOk() ' event
  ' print ghLogHead();"LOG-SENT:::OK"
end sub
sub LogError() ' event
  ' print ghLogHead();"LOG-SENT:::ERROR"
end sub
sub LogClear(rows = 50)
  for a = 1 to rows
    print " "
  end for
end sub

' ERRORs
' --------------------------------------------------------------
sub ghErrorDump(error)
  print
  print "----ERROR---------------------------------------------------------------"
  print "Number : ";error.number
  print "Message: ";error.message
  print "CallStack:"
  for each item in error.backtrace
    print item.function;" (";item.line_number;") in ";item.filename
  end for
  print "----ERROR---------------------------------------------------------------"
  print
end sub
function ghErrorGetJson(error) as object
  backtrace = []
  if error.backtrace <> invalid then
    for each item in error.backtrace
      backtrace.push({
        "func": item.function,
        "line": item.line_number,
        "file": item.filename
      })
    end for
  end if
  return {
    "error": true,
    "error_code": error.number,
    "error_msg": error.message,
    "error_trace": backtrace
  }
end function
function ghErrorNetwork(api, url, response = invalid)
  ghErrorDumpToLog(api, { url: url, message: "NETWORK ERROR" })
  return ghErrorGetJson({ number: response, message: "NETWORK ERROR" })
end function

' FOCUS
' --------------------------------------------------------------
sub ghFocusJumpTo(id)
  print ghLogHead();"ghFocusJumpTo to id=";id
  if m.top.focusedChild <> invalid and m.top.focusedChild.id <> invalid and m.top.focusedChild.id <> "" then
    if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
      m.top.findNode(m.top.focusedChild.id).focus = false
    end if
  end if
  m.top.findNode(id).focus = true
end sub

' REGISTRY (LocalStorage)
' --------------------------------------------------------------
function ghGetRegistry(key as string, section = "Default" as string) as string
  reg = CreateObject("roRegistrySection", section)
  result = ""
  if reg.Exists(key) then
    result = reg.Read(key)
  end if
  return result
end function
function ghListSections() as dynamic
  reg = CreateObject("roRegistry")
  result = reg.GetSectionList()
  return result
end function
function ghListSectionData(section = "Default") as dynamic
  reg = CreateObject("roRegistrySection", section)
  keys = reg.GetKeyList()
  result = {}
  for each k in keys
    result.addReplace(k, reg.Read(k))
  end for
  return result
end function
function ghSetRegistry(key, val, section = "Default")
  ' invalid check
  if key = invalid then return false
  if val = invalid then val = ""
  ' key
  if type(key) <> "String" then
    return false
  end if
  ' val
  if type(val) <> "String" then
    if type(val) = "roAssociativeArray" or type(val) = "roArray" then
      val = FormatJson(val)
    else if type(val) = "Integer" or type(val) = "Float" then
      val = Str(val)
    else
      return false
    end if
  end if
  ' section
  if type(section) <> "String" then
    return false
  end if

  reg = CreateObject("roRegistrySection", section)
  reg.Write(key, val)
  reg.Flush()
  return true
end function
function ghDeleteRegistry(key as string, section = "Default" as dynamic)
  reg = CreateObject("roRegistrySection", section)
  reg.Delete(key)
  reg.Flush()
end function
function ghDeleteSectionRegistry(section = "Default")
  reg = CreateObject("roRegistry")
  print ghLogHead();"ghDeleteSectionRegistry: delete section " + section
  reg.Delete(section)
  reg.Flush()
end function
function ghDeleteALLRegistry()
  reg = CreateObject("roRegistry")
  sections = reg.GetSectionList()
  for each section in sections
    print ghLogHead();"ghDeleteALLRegistry: delete section " + section
    reg.Delete(section)
  next
  reg.Flush()
end function

' MANIFEST
' --------------------------------------------------------------
function ghGetAppVersion() as string
  app = CreateObject("roAppInfo")
  return app.GetValue("major_version") + "." + app.GetValue("minor_version") + "." + app.GetValue("build_version")
end function
function ghGetFirmwareVersion() as string
  dInfo = CreateObject("roDeviceInfo")
  fw = dInfo.GetOSVersion()
  return fw.major + "-" + fw.minor + "-" + fw.revision + "-" + fw.build
end function
function ghGetDeviceId() as string
  dInfo = CreateObject("roDeviceInfo")
  return dInfo.GetChannelClientId()
end function

' ASSETS
' --------------------------------------------------------------
function ghGetAsset(key as string, default = "" as string)
  ' print ghLogHead();"ghGetAsset ";key;" default: ";default

  try
    assets = m.global.assets
  catch tryError
    print "ERROR ghGetAsset >>>";tryError
    assets = {} ' por defecto, nada.
  end try

  if key <> "" and assets <> invalid then
    res = assets.LookupCI(key)
    if res = invalid then
      print ghLogHead();"ghGetAsset no encontre la key [";key;"]"
      res = default
    end if
  else
    res = default
  end if
  return res
end function
function ghGetAssetByMode(key as string, default = "" as string)
  ' Ejemplo de la ghGetAssetByMode
  ' m.itemPoster.uri = ghGetAsset("select_payment_method_telmex", "pkg:/images/Placeholder_SelectorMDP-FHD.png")
  ' m.itemPoster.uri = ghGetAssetByMode("select_payment_method_telmex", "pkg:/images/Placeholder_SelectorMDP-FHD.png")
  ' select_payment_method_telmex > select_payment_method_telmex_FHD
  ' return ghGetAsset(key + "_" + ghGetDisplayMode(), default)
  return ghGetAsset(ghGetDisplayMode() + "_" + key, default)
end function
function ghGetRatingLabel(ratKey)
  ' rating_G
  ' rating_TV-G
  ' rating_PG
  ' rating_PG-13
  ' rating_TV-14
  ' rating_R
  ' rating_NC-17
  if left(ratKey, 1) = "+" then
    ratKey += " " + ghTranslate("key_del_anio", "key_del_anio") ' GOOSE cambiar la key! anio
  end if
  ' return ghTranslate("rating_" + ratKey, ratKey) + sufix
  return ratKey
end function

function ghGetRatingLabelV2(rat_id)
  '10_rating_description_label = Todos
  '20_rating_description_label =+7 años
  '30_rating_description_label =+12 años
  '40_rating_description_label =+16 años
  '50_rating_description_label =+18 años
  key = rat_id + "_rating_description_label"
  ratKey = ghTranslate(key, "key_del_anio") ' GOOSE cambiar la key! anio
  return ratKey
end function

' APIS
' --------------------------------------------------------------
function ghCallApi(api as string, okCallback = invalid, errorCallback = invalid, doRun = true as boolean) as object
  ' ApaSession ------------
  apiObject = CreateObject("roSGNode", api)
  ' print ghLogHead();"ghCallApi -- callback ";callback
  if okCallback <> invalid then
    apiObject.ObserveField("content", okCallback)
  end if
  if errorCallback <> invalid then
    apiObject.ObserveField("error", errorCallback)
  end if
  ' print ghLogHead();"ghCallApi -- doRun ";doRun
  if doRun then
    apiObject.control = "run"
  end if
  return apiObject
end function
function ghInsertVersion(path, module)
  newPath = path
  version = ghGetChild(m.global.config, "api.versions." + module, "vxx")
  newPath = ghReplaceStr(path, "{@version}", version)
  ' print ghLogHead();"ghInsertVersion -- ";module;" >> ";version;" >> ";path" >> ";newPath
  return newPath
end function

' SCREEN
' --------------------------------------------------------------
sub ghTurnLoading(status as boolean, loadingElement, currentElement)
  currentElement.enabled = not status ' prendo el response del foco del grupo
  loadingElement.visible = status ' apago el loading
end sub

' THEME
' --------------------------------------------------------------
function ghPushTheme(theme) as object
  print ghLogHead();"UTILS:ghPushTheme."
  oldTheme = FormatJSON(m.top.getScene().theme)
  m.top.getScene().updateTheme = theme
  return oldTheme
end function
sub ghPopTheme(theme as string)
  print ghLogHead();"UTILS:ghPopTheme."
  if theme <> "null" then
    m.top.getScene().updateTheme = ParseJSON(theme)
  end if
end sub

' CHILDREN -- manoseo de children
' --------------------------------------------------------------
function ghGetChildrenAsArray(obj)
  childs = []
  ch = obj.getChildren(-1, 0)
  for each item in ch
    childs.push(item)
  end for
  return childs
end function
sub ghReplaceChildren(obj, children)
  cant = obj.getChildCount()
  obj.removeChildrenIndex(cant, 0)
  obj.appendChildren(children)
end sub
sub ghClearChilds(obj) ' limpia childs de un nodo
  bCant = obj.getChildCount()
  if bCant > 0 then obj.removeChildrenIndex(bCant, 0)
end sub
function ghGetChildNodeById(obj, id) ' limpia childs de un nodo
  cant = obj.getChildCount()
  for c = 0 to cant
    ch = obj.getChild(c)
    if ch.id = id then return ch
  end for
  return invalid
end function
function ghGetItemByProperty(options, keyname, value, childElement = "childs")
  res = invalid
  cant = options.Count()
  for i = 0 to cant - 1
    op = options[i]
    ' print "* ";keyname;"=";value;" >> ";op[keyname]
    if value = options[i][keyname] then
      ' print "*** LO ENCONTRE!"
      return options[i]
    else
      if op[childElement] <> invalid then
        if op[childElement].Count() > 0
          r = ghGetItemByProperty(op[childElement], keyname, value)
          if r <> invalid then return r ' busco en los childs
        end if
      end if
    end if
  end for
  return res
end function

' OTRAS COSAS
' --------------------------------------------------------------
sub ghUtils_forceSetFields(node as object, fieldsToSet as object)
  ' ghPOCForceSetFields
  ' if not fw_isSGNode(node) or not fw_isAssociativeArray(fieldsToSet) then return
  existingFields = {}
  newFields = {}
  'AA of node read-only fields for filtering'
  fieldsFilterAA = {
    focusedChild: "focusedChild"
    change: "change"
    metadata: "metadata"
  }
  for each field in fieldsToSet
    if node.hasField(field)
      if not fieldsFilterAA.doesExist(field) then existingFields[field] = fieldsToSet[field]
    else
      newFields[field] = fieldsToSet[field]
    end if
  end for
  node.setFields(existingFields)
  node.addFields(newFields)
end sub
function ghGetChild(ori, path, default = invalid)
  try
    if ori = invalid then return default
    levels = ghSplit(path, ".")
    base = ori
    for each level in levels
      if Left(level, 1) = "#" then
        lvl = val(mid(level, 2))
      else
        lvl = level
      end if
      if base[lvl] <> invalid then
        base = base[lvl]
      else
        return default
      end if
    end for

    ' retorno valor con el mismo type que el type del default
    if type(default) = "String" or type(default) = "roString" and not(type(base) = "roString" or type(base) = "String") then
      if type(base) = "roBoolean" or type(base) = "roArray" or type(base) = "roAssociativeArray" then
        base = ""
      else
        base = base.Tostr()
      end if
    else if type(default) = "Boolean" or type(default) = "roBoolean" and not(type(base) = "roBoolean" or type(base) = "Boolean") then
      if base.Tostr() = "false"
        base = false
      else if base.Tostr() = "true"
        base = true
      else
        base = false
      end if
    else if type(default) = "Integer" or type(default) = "roInt" and not(type(base) = "roInt" or type(base) = "Integer") then
      base = base.toStr().ToInt()
    else if type(default) = "Float" or type(default) = "roFloat" and not(type(base) = "roFloat" or type(base) = "Float") then
      base = base.toStr().toFloat()
    else if type(default) = "AssociativeArray" or type(default) = "roAssociativeArray" and not(type(base) = "roAssociativeArray" or type(base) = "AssociativeArray") then
      if type(base) = "roArray" or type(base) = "Array" then
        if base.Count() = 1 then
          base = base[0]
        else
          base = {}
        end if
      else
        base = {}
      end if
    else if type(default) = "Array" or type(default) = "roArray" and not(type(base) = "roArray" or type(base) = "Array") then
      if type(base) = "roAssociativeArray" or type(base) = "AssociativeArray" then
        aBase = []
        for each item in base
          aBase.push(item)
        end for
        base = aBase
      else
        base = []
      end if
    end if

    return base
  catch error
    print ghLogHead();"ghGetChild -- ERROR: "
    ghErrorDump(error)
    return default
  end try
end function
function findInArray(dataArray, text) ' array donde buscar, texto o array de texto a buscar
  ' buscar
  find = []
  result = []

  if Type(text) = "roArray" and text.Count() > 0
    for i = 0 to text.Count() - 1
      find.push(text[i])
    end for
  else
    find.push(text)
  end if

  if type(dataArray) = "roAssociativeArray" or Type(dataArray) = "roArray" and dataArray.Count() > 0
    for i = 0 to find.Count() - 1
      for i2 = 0 to dataArray.Count() - 1
        if dataArray[i2] = find[i]
          result.push(dataArray[i2])
        end if
      end for
    end for
  end if

  return result
end function
' -------------------------------
function getIdMisAlquileres(name)
  result = false

  if type(name) = "roString" then
    name = LCase(name)
    if Lcase(name) = "mis-alquileres" or Lcase(name) = "mis_alquileres" then
      result = true
    end if
  end if

  return result
end function
function isNodoMisContenidos(name)
  result = false
  if name = LCase(name) then
    if Lcase(name) = "mis-contenidos" or Lcase(name) = "mis_contenidos" or Lcase(name) = "miscontenidos" then
      result = true
    end if
  end if

  return result
end function
function getIdContinuarViendo(name)
  result = false

  if type(name) = "roString" then
    name = LCase(name)
    if Lcase(name) = "continua-viendo" or Lcase(name) = "continua_viendo" or Lcase(name) = "seguir-viendo" or Lcase(name) = "seguir_viendo" then
      result = true
    end if
  end if

  return result
end function
function getIdMiLista(name)
  result = false

  if type(name) = "roString" then
    name = LCase(name)
    if LCase(name) = "mi-lista" or LCase(name) = "mi_lista" then
      result = true
    end if
  end if

  return result
end function

' ANALYTICS
' --------------------------------------------------------------
sub GA4Event(name, params = {})
  if m.global.GA4_plugin <> invalid then
    params.parent_id = ghGetRegistry("parent_id", "user")
    params.user_id = ghGetRegistry("user_id", "user")
    params.country = ghGetRegistry("country_code", "user")
    ' params.country = ghGetRegistry("country_code", "user")
    print "*********GA4_EVENT*********"
    print "GA4_EVENT_NAME: "; name
    print "GA4_EVENT_BODY: "; FormatJson(asocEventFormat(params))
    print "*********GA4_EVENT*********"
    m.global.GA4_plugin.setField("event", { name: name, params: params })
  end if
end sub

'
function getUserTypeGA4(needParsedValues = false)
  user_type = "registrado"
  if UCase(m.global.model) = UCase("Lite") then
    nav = ghGetChild(m.global, "nav.type")
    ' si necesita que lo parseen a fremium o premium
    if needParsedValues = true then
      if nav = "nav_freemium_not_logged" then
        user_type = "freemium"
      else if nav = "nav_premium" then
        user_type = "premium"
      end if
      ' si no lo necesita devolvemos la nav
    else
      user_type = nav
    end if
  end if
  return user_type
end function
' LASTTOUCH
' --------------------------------------------------------------
sub updateGlobalArray(name, values)
  if values <> invalid then ' TODO and type(values) = "object" a veces el values no es object
    arr = m.global[name]
    if arr = invalid then
      m.global.AddField(name, "assocarray", false)
      arr = {}
      m.global[name] = arr
    end if
    for each item in values
      arr[item] = values[item]
    end for
    m.global[name] = arr
  end if
end sub

' CHANNELS - carga de canales al inicial la app o al volver a hacer login
' --------------------------------------------------------------
sub initializeChannels()
  ghCallApi("EpgVersion", "handleEpgVersion", "handleError")
end sub
sub handleEpgVersion(event)
  data = event.getData()
  m.epgVersion = data.epg_version

  ghCallApi("EpgMenu", "handleEpgMenu", "handleError")
end sub
sub handleEpgMenu() ' event
  ghCallApi("Lineup")
  ghCallApi("Channels")
end sub
sub handleError(error = {}) ' event
  print ghLogHead();"handleError *** ";error
end sub

' LOGIN
' --------------------------------------------------------------
sub ghDoLoginLiteInFlow(returnTo)
  isLoggedIn = ghGetRegistry("isLoggedIn")
  if m.buy.debug then print ghLogHead();"ghDoLoginInFlow -- isLoggedIn=";isLoggedIn;" returnTo=";returnTo
  LoginPageLite = CreateObject("roSGNode", "LoginPageLite")
  LoginPageLite.id = "LoginPageLite"
  LoginPageLite.ObserveField("wasClosed", returnTo)
  LoginPageLite.SetFields({ inFlow: true })
  m.top.routerChild = { page: LoginPageLite } ' modo router
end sub



sub useCallback(callback, params = invalid, fncCallback = "")
  m.logger.debug("useCallback", { callback: callback, params: params, fncCallback: fncCallback })

  apiObject = CreateObject("roSGNode", "TaskCallback")
  apiObject.ObserveField("ok", callback)
  apiObject.fncCallback = fncCallback
  if Type(params) = "roArray" then
    apiObject.params = params
  end if
  apiObject.control = "run"
end sub

sub ghCheckAuthentication(returnTo, fncChangeAuth, params)
  m.logger.debug("ghCheckAuthentication", { returnTo: returnTo, fncChangeAuth: fncChangeAuth, params: params })

  m.returnTo = returnTo
  m.changeAuth = fncChangeAuth
  m.params = params

  if ghGetRegistry("isLoggedIn") <> "true" then
    m.logger.debug("ghCheckAuthentication isLoggedIn false")

    LoginPageLite = CreateObject("roSGNode", "LoginPageLite")
    LoginPageLite.id = "LoginPageLite"
    LoginPageLite.ObserveField("wasClosed", "closeLogin")
    LoginPageLite.SetFields({ inFlow: true })
    m.top.routerChild = { page: LoginPageLite }
  else
    m.logger.debug("ghCheckAuthentication isLoggedIn true")

    useCallback(returnTo, params)
  end if
end sub

sub closeLogin()
  m.logger.debug("closeLogin")

  if ghGetRegistry("isLoggedIn") = "true" then
    scene = m.top.getScene()
    scene.callFunc("warmReboot", {
      skipHome: true,
      sameProfile: true,
      model: "lite",
    })

    ' si tiene que llamar a una funcion intermedia
    ' por ejemplo para cargar los datos de nuevo (vcard)
    ' o si directamente llama a la funcion final del callback
    if m.changeAuth <> invalid and m.changeAuth <> "" then
      m.logger.debug("llamando a la funcion con parametros ", { call: m.changeAuth, params: m.params, return: m.returnTo })
      useCallback(m.changeAuth, m.params, m.returnTo)
    else
      m.logger.debug("llamando a la funcion con parametros ", { call: m.returnTo, params: m.params, return: m.returnTo })
      useCallback(m.returnTo, m.params)
    end if
  end if
end sub


' para crear un curl command desde GHApiTask y GHApiCall
sub CreateCurlCommand(api = {})
  method = api.method
  url = api.url

  if url = invalid then
    return
  end if

  headers = api.headers
  if headers = invalid then
    headers = {}
  end if

  query = api.query
  if query = invalid then
    query = {}
  end if

  body = api.body
  if body = invalid then
    body = ""
  end if

  queryString = ""
  for each key in query
    if query[key] <> invalid then
      ' Verifica si el valor es booleano
      if type(query[key]) = "roBoolean" then
        if query[key] = true then
          query[key] = "true"
        else
          query[key] = "false"
        end if
      else if Type(query[key]) = "roInt" or Type(query[key]) = "roInteger" or Type(query[key]) = "roFloat" or Type(query[key]) = "roBoolean" or Type(query[key]) = "roString" or Type(query[key]) = "roDateTime" or Type(query[key]) = "roByteArray" or Type(query[key]) = "String"
        query[key] = query[key].toStr()
      end if

      if queryString = "" then
        queryString = key + "=" + query[key]
      else
        queryString = queryString + "&" + key + "=" + query[key]
      end if
    end if
  end for

  separador = "?"
  if Instr(1, url, "?") > 0 then
    separador = "&"
  end if

  curlCommand = "curl --request " + method + " \" + chr(10)
  curlCommand += "  --url '" + url + separador + queryString + "' \" + chr(10)

  headerCount = headers.Count()
  currentHeader = 0
  for each key in headers
    currentHeader += 1
    if currentHeader = headerCount
      curlCommand += "  --header '" + key + ": " + headers[key] + "'"
    else
      curlCommand += "  --header '" + key + ": " + headers[key] + "' \" + chr(10)
    end if
  end for

  if body <> "" then
    ' cuando hacemos el ghArray2Query
    ' parsedBody = ParseQueryStringToJson(body)
    ' if parsedBody <> invalid then
    '   body = parsedBody
    ' end if
    curlCommand += " \" + chr(10) + "  --data '" + body + "'"
  end if

  print ghLogHead();">>>>> [";m.top.subType();"] CURL generado: ";chr(10);curlCommand;chr(10);">>>>>"
end sub

function ParseQueryStringToJson(queryString as string) as string
  ' Eliminar si es '&'
  if Left(queryString, 1) = "&" then
    queryString = Mid(queryString, 2)
  end if

  items = ghSplit(queryString, "&")
  json = "{" + chr(10)
  for each item in items
    keyValue = ghSplit(item, "=")
    if keyValue.Count() = 2 then
      key = keyValue[0]
      value = keyValue[1]
      if json.len() > 2 then
        json += "," + chr(10)
      end if
      json += """" + key + """: """ + value + """"
    else
      return invalid
    end if
  end for
  json += chr(10) + "}"
  return json
end function


' CAPTIONS
function setGlobalCaption(mode = invalid)
  if mode <> invalid then
    info = CreateObject("roDeviceInfo")
    print "============================="
    print "CAPTIONS"
    print "============================="
    print "anterior=";info.GetCaptionsMode()
    info.SetCaptionsMode(mode)
    print "actual=";info.GetCaptionsMode()
    print "============================="
  end if
end function

function getCountryCode(country)

  code = country
  ' Mapa de países y sus códigos ISO de 2 letras
  countryCodes = {
    "Argentina": "ar",
    "Bolivia": "bo",
    "Chile": "cl",
    "Colombia": "co",
    "Costa Rica": "cr",
    "Dominicana": "do",
    "Ecuador": "ec",
    "El Salvador": "sv",
    "Guatemala": "gt",
    "Honduras": "hn",
    "México": "mx",
    "Mexico": "mx",
    "Nicaragua": "ni",
    "Panamá": "pa",
    "Panama": "pa",
    "Paraguay": "py",
    "Perú": "pe",
    "Peru": "pe",
    "Uruguay": "uy",
    "Venezuela": "ve"
  }

  ' Convertir el país a minúsculas para facilitar la comparación
  if country = invalid or country = "" then
    country = ghGetRegistry("region")
    country = UCase(country)

    ' Buscar el código en el mapa
    code = ghGetChild(countryCodes, country, "")
    if code <> "" then
      code = LCase(code)
    end if
  end if

  ' Convertir el país a minúsculas para facilitar la comparación
  if country = invalid then
    country = ghGetRegistry("region")
    country = UCase(country)

    ' Buscar el código en el mapa
    code = ghGetChild(countryCodes, country, "")
    if code <> "" then
      code = LCase(code)
    end if
  end if

  return code
end function

function getContentSectionName(node)

  sectionCodes = {
    "homeuser": "home start"
    "miscontenidos": "home miscontenidos"
    "peliculas": "home movies"
    "seriesnv": "home series"
    "niniosnv": "home kids"
    "rentanv": "home renta"
    "clarosports": "home clarosports"
    "homeuser_lite": "home start"
    "premium_lite": "home premium"
    "telcel": "home telcel"
    "telmex": "home telmex",
  }

  sectionName = ghGetChild(sectionCodes, node, ghReplaceStr(node, "_", " "))

  return sectionName
end function

function getContentListFormatName(text)
  text = LCase(text)
  listFormatCodes = {
    "superdestacados": "superdestacado"
    "destacados": "superdestacado"
  }

  sectionName = ghGetChild(listFormatCodes, text, ghReplaceStr(text, "_", " "))

  return sectionName
end function

function asocEventFormat(aIn)
  aOut = createObject("roAssociativeArray")
  for each item in aIn
    ori = aIn[item]
    if type(ori) = "roString" or type(ori) = "String" then
      aOut[item] = LCase(ori)
    else if type(ori) = "roAssociativeArray" then
      aOut[item] = asocEventFormat(ori)
    else
      aOut[item] = ori ' Mantener otros tipos de datos sin cambios
    end if
  end for
  return aOut
end function

function sortPaymentMethods(methods as object) as object
  ' Ordenamos el array por el valor del campo "gateway"
  ' Primero "rokupay" y luego los demás alfabéticamente.

  ' Creamos dos arrays para los diferentes tipos de gateway
  rokupayMethods = []
  otherMethods = []

  ' Separamos los métodos de pago en dos grupos
  For Each method In methods
    if LCase(method.gateway) = "rokupay" then
      rokupayMethods.Push(method)
    else
      otherMethods.Push(method)
    end if
  next

  ' Ordenamos el array de otros métodos alfabéticamente por "gateway"
  otherMethods.SortBy("gateway")

  ' Creamos un nuevo array para los métodos ordenados
  sortedMethods = []

  ' Añadimos los métodos "rokupay"
  sortedMethods.Append(rokupayMethods)

  ' Añadimos los otros métodos ordenados
  sortedMethods.Append(otherMethods)

  return sortedMethods
end function

function CalcularDiasRestantes(fechaExpiracion as string) as integer
  ' Crear objetos de fecha
  fechaActual = CreateObject("roDateTime")
  fechaExp = CreateObject("roDateTime")

  ' Configurar la fecha de expiración usando FromISO8601String()
  ' La fecha ya está en formato ISO8601: "2025-04-25T18:50:21"
  fechaExp.FromISO8601String(fechaExpiracion)

  ' Obtener los segundos desde el epoch para ambas fechas
  segundosActual = fechaActual.AsSeconds()
  segundosExp = fechaExp.AsSeconds()

  ' Calcular la diferencia en segundos y convertir a días
  ' Para obtener días transcurridos (positivo cuando la fecha ya pasó)
  diferenciaSegundos = segundosActual - segundosExp

  ' 86400 segundos = 1 día
  diferenciaDias = Int(diferenciaSegundos / 86400)

  return diferenciaDias
end function

function handlingSizeForHD(value as integer)
  finalValue = value * (2 / 3)
  return finalValue
end function

sub NavCheckoutFieldsEntry(paymentType as dynamic, screenID as string)
  CheckoutFieldsEntry = CreateObject("roSGNode", "CheckoutFieldsEntry")
  CheckoutFieldsEntry.id = screenID
  m.top.routerChild = {
    page: CheckoutFieldsEntry,
    fields: {
      accessCode: ""
      data: ""
      checkoutFieldType: paymentType
    }
  }
end sub

function handleBackspace(currentString as string) as string
  if currentString.Len() > 0 then
    return currentString.Left(currentString.Len() - 1)
  else
    ' String is already empty, nothing to do
    return ""
  end if
end function

function getCurrentTimeHHMMSS() as dynamic
  dateTime = CreateObject("roDateTime")
  hour = dateTime.GetHours()
  minute = dateTime.GetMinutes()
  second = dateTime.GetSeconds()
  hourString = Str(hour)
  if hour < 10 then hourString = "0" + hourString

  minuteString = Str(minute)
  if minute < 10 then minuteString = "0" + minuteString

  secondString = Str(second)
  if second < 10 then secondString = "0" + secondString

  timeString = hourString + ":" + minuteString + ":" + secondString
  return timeString.Replace(" ", "")
end function