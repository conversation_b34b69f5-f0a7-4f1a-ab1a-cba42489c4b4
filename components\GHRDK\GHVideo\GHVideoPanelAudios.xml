<?xml version="1.0" encoding="utf-8" ?>

<component name="GHVideoPanelAudios" extends="Group">
  <script type="text/brightscript" uri="GHVideoPanelAudios.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="langData" type="array" onChange="onLangDataUpdate" alwaysNotify="true" />
    <field id="info" type="assocarray" onChange="onInfoUpdate" alwaysNotify="true" />

    <field id="selected" type="assocarray" alwaysNotify="true" />
    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="panelLeft">
      <Label id="title"/>
      <Label id="time"/>
      <Label id="description" wrap= "true"/>
    </Rectangle>
    <LayoutGroup id="infoSerie" translation="[80,258]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[10]" visible="false">
      <Label id="infoSeason" text="infoSeason"/>
      <Label id="infoBar2" text="|"/>
      <Label id="infoEpisode" text="infoEpisode"/>
      <Label id="infoBar3" text=":"/>
      <Label id="infoEpisodeTitle" text="infoEpisodeTitle"/>
    </LayoutGroup>
    <Rectangle id="panelRight">
      <Label id="langLabel" height= "24" width="272"/>
      <MarkupGrid id="languages" />
    </Rectangle>
  </children>

</component>
