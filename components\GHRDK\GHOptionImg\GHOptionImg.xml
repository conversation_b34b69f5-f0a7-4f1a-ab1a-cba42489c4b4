<?xml version="1.0" encoding="utf-8" ?>
<component name="GHOptionImg" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHOptionImg.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <!-- imagen normal -->
    <field id="imageURI" type="string" value="pkg:/images/HD/avatar.png" onChange="refresh" alias="image.uri"/>
    <field id="imageWidth" type="float" value="40" onChange="refresh" alias="image.width" />
    <field id="imageHeight" type="float" value="40" onChange="refresh" alias="image.height" />
    <field id="imageHorizAlign" type="string" value="center" onChange="refresh" />
    <field id="imageVertAlign" type="string" value="center" onChange="refresh" />
    <field id="imageTranslation" type="intarray" value="[0,0]" onChange="refresh" />
    <!-- el fondo del over -->
    <field id="overImageURI" type="string" value="circulodeprueba.9.png"/>
    <!-- el fondo del selected -->
    <field id="selectedImageURI" type="string" value="circulodeprueba.9.png"/>

    <!-- posicion -->
    <!-- <field id="translation" type="string" value="[0,0]" onChange="refresh" alias="image.translation" /> -->
    <field id="width" type="float" value="50" onChange="refresh" alias="fondo.width"/>
    <field id="height" type="float" value="40" onChange="refresh" alias="fondo.height"/>
    <!-- color -->
    <field id="color" type="string" value="#656767" onChange="recalcColors" />
    <field id="selColor" type="string" value="#FFFFFF" onChange="recalcColors" />

    <!-- ok button -->
    <field id="menuSelected" type="boolean" value="false" alwaysNotify="true" />
    <field id="menuFocused" type="boolean" value="false" alwaysNotify="true" />
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="value" type="string" value="mybutton"/>
    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="fondo" color="0x00FF0000" translation="[0,0]" />
    <Poster id="imageFondo" uri="" width="0.0" height="0.0" translation="[0,0]" />
    <Poster id="image" uri="" width="0.0" height="0.0" translation="[0,0]" />
    <!-- <Label id="label" text="MyLabel" color="0x000000" translation="[0,0]" width="0" height="0" vertAlign="center" horizAlign="center" /> -->
  </children>

</component>
