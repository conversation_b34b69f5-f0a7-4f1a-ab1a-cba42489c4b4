sub Init()
    m.top.debug = true
    if m.top.debug then print "CustomMarkGrid: Init"
    m.top.setFields({
        numColumns: 4
        numRows: 3
        translation: [130, 160]
        itemSpacing: [20, 20]
        itemSize: [200, 100]
        itemComponentName: "CustomMarkGridItem"
        vertFocusAnimationStyle: "fixedFocus"
    })
    m.numberOfItems = 0

    ' m.top.ObserveField("gridType", "setGridType")
    ' m.top.ObserveField("gridData", "onGridDataChanged")
end sub

sub setGridType()
    print "gridType :" m.top.gridType, m.top.gridData
end sub

sub onGridDataChanged()
    if m.top.debug then print "onGridDataChanged : " m.top.gridType
    if m.top.gridType = "year"
        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(32)
        m.top.itemSpacingY = handlingSizeForHD(39)
    else if m.top.gridType = "month"
        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(31)
        m.top.itemSpacingY = handlingSizeForHD(38)
    else if m.top.gridType = "service"
        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(32)
        m.top.itemSpacingY = handlingSizeForHD(39)
    else if m.top.gridType = "country"
        m.top.itemWidth = handlingSizeForHD(275)
        m.top.itemHeight = handlingSizeForHD(107)
        m.top.itemSpacingX = handlingSizeForHD(15)
        m.top.itemSpacingY = handlingSizeForHD(15)
    else if m.top.gridType = "documentType"
        print "inside gridType"
        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(30)
        m.top.itemSpacingY = handlingSizeForHD(30)
    else if m.top.gridType = "multiLineGrid"


        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(30)
        m.top.itemSpacingY = handlingSizeForHD(30)
    else


        m.top.itemWidth = handlingSizeForHD(410)
        m.top.itemHeight = handlingSizeForHD(233)
        m.top.itemSpacingX = handlingSizeForHD(32)
        m.top.itemSpacingY = handlingSizeForHD(39)
    end if
    m.top.setFields({
        itemSpacing: [m.top.itemSpacingX, m.top.itemSpacingY]
        itemSize: [m.top.itemWidth, m.top.itemHeight]
        itemComponentName: "CustomMarkGridItem"
        vertFocusAnimationStyle: "fixedFocus"
    })
    m.top.ObserveField("itemSelected", "selectedGridItem")
    m.top.content = buildMarkupGrid(m.top.gridData)
    if m.numberOfItems < 8
        m.top.numRows = 2
    end if
    m.top.translation = calculateGridPosition()
    m.top.setFocus(true)
end sub

sub selectedGridItem(event)
    data = event.getData()
    node = event.getRoSGNode()
    item = node.content.getChild(data)
    m.top.selected = item.title
    m.top.routerClose = true
end sub


function buildMarkupGrid(arrVal) as object
    contentRoot = createObject("roSGNode", "ContentNode")
    children = []
    for each arr in arrVal
        arrType = Type(arr)
        if arrType = "roAssociativeArray"
            itemNode = createObject("roSGNode", "ContentNode")
            itemNode.title = arr.service_id.toStr()
            itemNode.id = m.top.gridType
            if arr.description <> invalid and arr.description <> ""
                itemNode.description = arr.description
            end if
            if arr.service_name <> invalid and arr.service_name <> ""
                itemNode.shortDescriptionLine1 = arr.service_name
            end if

        else
            itemNode = createChildNode(arr)
        end if
        children.push(itemNode)
    end for
    m.numberOfItems = children.count()
    contentRoot.Update({ children: children }, true)
    return contentRoot
end function

function createChildNode(data as string) as object
    childNode = {
        title: data,
        type: m.top.gridType
    }
    return childNode
end function

' Calculate grid position for centering
function calculateGridPosition() as object
    x = m.top.gridX
    y = m.top.gridY

    '  if m.top.centerGrid and m.top.gridData <> invalid then
    'if m.top.centerGrid and m.top.gridData <> invalid then
    ' Calculate center position based on number of columns
    itemCount = m.top.content.getChildCount()

    actualRows = ceil(itemCount / m.top.numColumns)
    if actualRows >= m.top.numRows
        actualRows = m.top.numRows
    end if

    totalWidth = (m.top.itemSize[0] * m.top.numColumns) + (m.top.itemSpacing[0] * (m.top.numColumns - 1))
    totalHeight = (m.top.itemSize[1] * actualRows) + (m.top.itemSpacing[1] * (actualRows - 1))
    x = (1280 - totalWidth) / 2
    y = (720 - totalHeight) / 2

    ' Ensure minimum margin
    if x < 50 then x = 50
    if y < 50 then y = 50
    ' end if

    if m.top.debug then print "CustomMarkGrid: Grid position ["; x; ","; y; "]"
    ' x = (1280 - m.top.boundingRect().width) / 2
    ' y = (720 - m.top.boundingRect().height) / 2
    return [x, y]
end function


function ceil(value as float) as integer
    rounded = int(value)
    if value > rounded then
        rounded = rounded + 1
    end if
    return rounded
end function

