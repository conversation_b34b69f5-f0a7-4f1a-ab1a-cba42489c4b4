' GHLiveLangItem
' ---------------------------

function init()
  m.title = m.top.findNode("title")
  m.icon = m.top.findNode("icon")
  m.fondo = m.top.findNode("fondo")
end function

function itemContentChanged()
  print ghLogHead();"itemContentChanged -- ";m.top.itemContent.data

  m.fondo.setFields({
    width: m.top.width,
    height: 65,
    translation: [0, 0],
  })

  m.icon.setFields({
    translation: [10, 20]
    width: 40
    uri: ""
  })
  m.title.setFields({
    translation: [60, 20]
    text: ghGetChild(m.top.itemContent.data, "label_large")
    font: ghGetFont(18, "regular")
  })

  recalcItem(false)
end function

sub onFocus(event)
  recalcItem(event.getData())
end sub

sub recalcItem(status = false)
  m.fondo.visible = m.top.itemHasFocus ' prendo y apago el fondo

  if status then
    m.title.color = "#FFFFFFFF"
    m.icon.uri = ghGetAsset("language_panel_language_icon","pkg:/images/40_idioma.png")
    m.icon.height = 40
    m.icon.width = 40
  else
    m.title.color = "#FFFFFF55"
    m.icon.uri = ""
    m.icon.height = 40
    m.icon.width = 40

    if m.top.itemContent <> invalid and ghGetChild(m.top.itemContent.data, "is_current") then
      m.icon.uri = ghGetAsset("language_panel_subtitle_icon","pkg:/images/40_subtitulo.png")
      m.icon.height = 40
      m.icon.width = 40
    end if
  end if
end sub
