sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/user/seendel"

  m.api.query.Append({
    "object_id": m.top.group_id,
    "region": ghGetRegistry("region"),
    "user_hash": ghGet<PERSON>egistry("session_userhash", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  response = res.response

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' ghSetRegistry("lasttouch_seen", res.lasttouch.seen, "user")
  updateGlobalArray("lasttouch", { seen: res.lasttouch.seen })

  m.top.content = {}
end sub