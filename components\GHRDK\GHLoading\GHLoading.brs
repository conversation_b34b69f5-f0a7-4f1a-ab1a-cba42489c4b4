' GHButton
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  m.background = m.top.findNode("background")
  m.spinner = m.top.findNode("spinner")
  m.spinner.poster.observeField("loadStatus", "ShowSpinner")
  m.spinner.poster.uri = "pkg:/images/loading.png"
  m.top.observeField("visible", "updateFieldVisible")
end function
sub ShowSpinner()
  if(m.spinner.poster.loadStatus = "ready")
    RecalcSpinner()
    m.spinner.visible = true
  end if
end sub

' TEXT
sub updateFieldWidth()
  recalcSpinner()
end sub
sub updateFieldVisible(event)
  if event.getData() then
    if m.top.debug then print ghLogHead();"Visible true"
    if m.top.debug then print ghLogHead();"focusedChild : ";m.global.focusedChild
  else
    if m.top.debug then print ghLogHead();"Visible false"
  end if
end sub
' FOCUS
sub updateFieldFocus() ' event
  if m.top.focus then
    if m.top.debug then print ghLogHead();"Focus true"
    m.top.visible = true
    port = CreateObject("roMessagePort")
    while true
      ' msg = wait(0, port) ' wait for a message
      wait(0, port) ' wait for a message
    end while
  else
    if m.top.debug then print ghLogHead();"Focus false"
    m.top.visible = false
  end if
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus;" visible ";m.top.visible
end sub
sub RecalcSpinner()
  if m.top.debug then print ghLogHead();"RecalcSpinner";m.top.width;" ";m.top.height
  if(m.spinner.poster.loadStatus = "ready")
    centerx = (m.background.width - m.spinner.poster.bitmapWidth) / 2
    centery = (m.background.height - m.spinner.poster.bitmapHeight) / 2
    m.spinner.translation = [centerx, centery]
    if m.top.debug then print ghLogHead();"RecalcSpinner x=";centerx;"y=";centery
  end if
end sub

