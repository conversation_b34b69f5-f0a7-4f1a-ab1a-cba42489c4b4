sub Init()
  m.top.debug = true
  m.background = m.top.findNode("background")
  m.panel = m.top.findNode("panel")
  ' queue
  m.queue = []
  m.temporalQueue = []
  m.isQueueEnabled = true
  m.isQueueBusy = false
  drawDebugScreen()
end sub
sub drawDebugScreen()
  translation = [650, 10]
  height = 700
  width = 600
  m.background.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    color: "0x00AAFFCC"
  })
  m.panel.setFields({
    translation: translation
    width: width '		integer	0	READ_WRITE	The width of the label.
    height: height '		integer	0	READ_WRITE	The height of the label.
    visible: true
    infoText: "GA4 - Eventos" '	string	none	READ_WRITE	The text to be displayed in the label.
    textColor: "0xFFFFFFFF" '		color	0xFFFFFFFF	READ_WRITE	The color of the text displayed in the label.
    bulletText: [" "] '		array of strings	none	READ_WRITE	List of strings preceded by a bullet (for example, ["Bullet 1","Bullet 2"]).
    infoText2: "No tengo eventos." '		string	none	READ_WRITE	A second text string that can be offset from the first.
    infoText2Color: "#FF0000" '		color	0xFFFFFFFF	READ_WRITE	Specifies the infoText2 string color
    infoText2BottomAlign: true '		boolean	false	READ_WRITE	Specifies whether the infoText2 string is vertically aligned to the bottom of the info pane
  })
end sub
' TICK
sub onTick()
  if m.queue.Count() > 0 or m.temporalQueue.Count() > 0 then
    if m.top.debug then print ghLogHead("GA4");"workerTimerTrigger: ";m.queue.Count();" events."
    workerSend()
  else
    if m.top.debug then print ghLogHead("GA4");"workerTimerTrigger: NO events."
  end if
end sub
' WORKER
sub workerSend()
  if m.top.debug then
    print "workerSend."
    print "<< QUEUE:trabajando >> ";m.queue.Count();" > ";m.temporalQueue.Count()
  end if
  m.isQueueBusy = true ' flag
  for e = 0 to m.queue.Count() - 1
    if m.top.debug then print e;" <";
    m.temporalQueue.push(m.queue.pop())
  end for
  if m.top.debug then
    if m.top.debug then print " << QUEUE:lista. >> ";m.queue.Count();" > ";m.temporalQueue.Count()
  end if
  m.isQueueBusy = false ' flag

  ' mandar queue
  clientId = ghGetRegistry("user_id", "user")
  if clientId = "" then clientId = "0"
  sendEvent({
    client_id: clientId,
    events: m.temporalQueue
  })
end sub
' EVENT
sub onEvent(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onEvent -- " data, FormatJson(data)

  ' Flag !
  while m.isQueueBusy
    Sleep(100)
  end while
  ' Queue append
  data.params.append(getDefaultParams())
  'm.queue.push(data)
  m.queue.push(asocEventFormat(data))
  'hacer que haga un lcase a todas las propiedades y hacer un utils para hacerlo en el print
  if m.top.debug then print "EVENT)) ";data.name;" ";data.params.screen_class;" ";data.params.screen_name
  panelRefresh()

  ' Queue send
  clientId = ghGetRegistry("user_id", "user")
  if clientId = "" then clientId = "0"
  if not m.isQueueEnabled then
    sendEvent({
      client_id: clientId,
      events: [data]
    })
  end if
end sub
function getDefaultParams()
  defaultParams = {
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    "region": ghGetRegistry("region"),
    "user_id": 0
    "device": "roku"
  }
  if ghGetRegistry("isLoggedIn") = "true" then
    defaultParams["user_id"] = ghGetRegistry("user_id", "user")
    'defaultParams["country"] = ghGetRegistry("country_code", "user")
  end if
  if m.top.debug then print ghLogHead();"onEvent -- LOGGED ";defaultParams

  return defaultParams
end function
sub onDebugTurn(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("SERVICE");"onDebugTurn -- ";data
  m.top.findNode("display").visible = data
end sub
' SEND Event
sub sendEvent(event)
  if m.top.debug then print ghLogHead();"sendEvent -- sending ";m.temporalQueue.Count();" eventos."
  e = ghCallApi("GA4Event", "sendEventOk", "sendEventError", false)
  e.event = event
  e.control = "run"
  if m.top.debug then print ghLogHead();"sendEvent -- api=";e
end sub
sub sendEventOk(event)
  data = event.getData()
  if m.top.debug then print "EVENT)) >>----> eventos enviados."
  m.temporalQueue = [] ' ahora puedo limpiar la queue
  panelRefresh()
  if m.top.debug then print ghLogHead();"sendEventOk -- "; FormatJson(data)
end sub
sub sendEventError(event)
  data = event.getData()
  panelRefresh()
  if m.top.debug then print ghLogHead();"sendEventError -- "; FormatJson(data)
end sub
' DEBUG
sub panelRefresh()
  m.panel.infoText2 = "Eventos: " + Str(m.queue.Count()) + " en buffer / " + Str(m.temporalQueue.Count()) + " encolados."
  log = []
  for l = m.queue.Count() - 1 to m.queue.Count() - 12 step -1
    if l >= 0 then
      ' print "EVENT)) ";m.queue[l].params.screen_class, m.queue[l].params.screen_name
      ' log.push(FormatJson(m.queue[l].params.screen_class + " > " + m.queue[l].params.screen_name))
      log.push(m.queue[l].params.screen_class + " > " + m.queue[l].params.screen_name)
    end if
  end for
  m.panel.bulletText = log
end sub

'function asocEventFormat(aIn)
'  aOut = aIn
'  for each item in aIn
'    ori = aIn[item]
'    ' print "It < ";item;" = ";aIn[item];" [";type(ori);"]"
'    if type(ori) = "roString" or type(ori) = "String" then
'      ' print "!!! entre a un STR ";ori
'      aOut[item] = LCase(ghReplaceStr(ori, " ", "_"))
'      ' print ori
'    end if
'    if type(ori) = "roAssociativeArray" then
'      ' print "!!! entre a un ASSOC"
'      aOut[item] = asocEventFormat(ori)
'    end if
'    ' print "It > ";item;" = ";aOut[item];" [";type(aOut[item]);"]"
'  end for
'  return aOut
'end function

