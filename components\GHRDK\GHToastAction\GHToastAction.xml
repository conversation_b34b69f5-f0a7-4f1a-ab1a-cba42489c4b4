<?xml version="1.0" encoding="utf-8" ?>

<component name="GHToastAction" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHToastAction.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz entrada -->
    <field id="time" type="int" value="12" onChange="onTimeChange" />
    <field id="title" type="string" value="***" onChange="onTitleChange" />
    <!-- <field id="divisor" type="string" value=" | " alias="divisorText.text" /> -->
    <field id="body" type="string" value="***" onChange="onBodyChange" />
    <field id="backgroundImage" type="string" value="pkg:/images/toast_back_gradient.9.png" alias="background.uri"/>
    <field id="iconImage" type="string" value="" onChange="onIconChange" />
    <field id="contentOffset" type="array" value="[25,15]" onChange="Redraw"/>
    <field id="alignement" type="string" value="center" alias="card.horizAlignment" />
    <!-- interfaz de salida -->
    <field id="onOk" type="string" onChange="onOkChange" />
    <field id="onCancel" type="string" onChange="onCancelChange" />

    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="wasClosed" type="boolean" value="false" alwaysNotify="true" />
  </interface>

  <children>
    <Poster id="background" translation="[0,0]" />
    <LayoutGroup id="card" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" itemSpacings="[10]">
      <Poster id="icon" />
      <LayoutGroup id="texts" layoutDirection="vert" horizAlignment="left" vertAlignment="top" itemSpacings="[0]">
        <Label id="titleText" focusable="false" color="0xFFFFFF" visible="false" />
        <!-- <Label id="divisorText" focusable="false" color="0xFFFFFF" visible="false" /> -->
        <Label id="bodyText" focusable="false" color="0xFFFFFF" visible="false" />
      </LayoutGroup>
      <GHButtonGroup id="botonera" layout="childs" orientation="horizontal" debug="true">
        <GHButton id="btnNext" focusMap="" width= "281" height= "72" selColor="#FFFFFF" selBackColor="#981C15" backcolor="#981C15" color="0xFFFFFF" text="VER AHORA" value="btnNext" translation="[920,544]" />
      </GHButtonGroup>
    </LayoutGroup>
  </children>

</component>
