sub DataInit()
  m.top.debug = true

  m.api.url = m.config.mfwk.host + "/services/apa/metadata"
  m.api.query.Append({
    "sessionKey": m.config.mfwk.appKey + "-" + ghGetRegistry("region")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- api="m.api.query
  end if

  ' assets
  ghCallApi("ApaAssets", "assetOk", "assetError")
end sub
sub DataRetry()
  if m.top.debug then print ghLogHead();"DataRetry -- PASE!"
end sub
' ASSETS
' ---------------------------
sub assetOk()
  if m.top.debug then print "api assets ok"
end sub
sub assetError()
  print "api assets error"
end sub
' PROCESOS
' ---------------------------
sub ProcessData(res, raw)

  if m.top.debug then print ghLogHead();"ProcessData"

  errors = ghGetChild(res, "errors")
  if errors = invalid then
    errors = ghGetChild(res, "result.error")
  end if
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  try
    ' TIMERS - en el mismo archivo
    getTimersConfig(res)

    translations = ParseJson(res.translations)
    translationsFinal = ghGetChild(translations, "language." + ghGetRegistry("region"), {})

    nodes = ParseJson(ghGetChild(res, "nodes_configuration", "{}"))
    filter = ParseJson(ghGetChild(res, "byr_filterlist_configuration", "{}"))
    providers = ParseJson(ghGetChild(res, "providers_label_configuration", "{}"))
    search = ParseJson(ghGetChild(res, "search_priority", "{}"))
    searchNew = ParseJson(ghGetChild(res, "search", "{}"))
    itemTitle = ParseJson(ghGetChild(res, "ItemTitle", "{}"))
    youbora = ParseJson(ghGetChild(res, "youbora_options", "{}"))
    tvConfig = ParseJson(ghGetChild(res, "tv_epg_menu_nodo", "{}"))
    ga4 = ParseJson(ghGetChild(res, "ga4", "{}"))

    finalSearch = search
    if searchNew.count() > 0 then
      finalSearch = searchNew
    end if

    ' model
    ' -----
    ' cv_advertising_config: {
    '   "default": {
    '     "enable": false
    '   },
    '   "mexico": {
    '     "enable": true,
    '     "skip_offset": 5,
    '     "experience_hour_refresh": 4
    '   }
    ' }
    cv_advertising_config = getByRegion(res, "cv_advertising_config")
    ' HARDCO
    ' -----
    ' cv_advertising_config = {
    '   "enable": true,
    '   "skip_offset": 5,
    '   "experience_hour_refresh": 4
    ' }

    m.global.setFields({
      cv_advertising_config: cv_advertising_config
      translations: translationsFinal,
      filter_list: filter,
      search: ghGetChild(finalSearch, "default"),
      nodes: nodes,
      providers: ghGetChild(providers, "default"),
      youbora: youbora
      ItemTitle: itemTitle
      tvConfig: tvConfig,
      ga4: ga4,
    })
  catch error
    m.top.error = { response: raw }
    return
  end try

  try
    if m.top.debug then print "YOUBORA ++++++++++++++++++++++++++++++++++"
    InitYoubora(true) ' FORCED !!!!!!!!!!!!!
    if m.top.debug then print "YOUBORA ++++++++++++++++++++++++++++++++++"
  catch error
    print error
  end try

  m.top.content = res
end sub

' TIMERS
sub getTimersConfig(res)

  theTimers = ghGetChild(m.global, "config.timers")
  if theTimers = invalid then
    print ghLogHead();"Using defaults from CODE (!) -- missing configuration."
    theTimers = {
      "panels": {
        "player": 300,
        "miniEPG": 300
      },
      "skipIntro": {
        "enable": true,
        "time": 7
      },
    }
  end if

  ' controlPlayerHide: ParseJson(ghGetChild(res, "control_player_hide", ghGetChild(m.global, "timers.controlPlayerHide", "{}")))
  panels_player = getByRegion(res, "control_player_hide")
  panels_player = ghGetChild(panels_player, "time")
  if panels_player <> invalid then theTimers.panels.player = panels_player
  if m.top.debug then print "Setting panels_player", theTimers.panels.player

  ' mini_epg_auto_hide_seconds miniEPGAutoHide: ParseJson(ghGetChild(res, "mini_epg_auto_hide", ghGetChild(m.global, "timers.miniEPGAutoHide", "{}")))
  panels_miniEPG = getByRegion(res, "mini_epg_auto_hide_seconds")
  panels_miniEPG = ghGetChild(panels_miniEPG, "time")
  if panels_miniEPG <> invalid then theTimers.panels.miniEPG = panels_miniEPG
  if m.top.debug then print "Setting panels_miniEPG", theTimers.panels.miniEPG

  ' skipIntroConfig: ParseJson(ghGetChild(res, "skip_intro_configuration", ghGetChild(m.global, "timers.skipIntroConfig", "{}")))
  ' print "!!!!!!!", ghGetChild(res, "skip_intro_configuration")
  skipIntro = getByRegion(res, "skip_intro_configuration")
  ' skipIntro = {
  '   "time": 7
  '   "enable": true
  ' }
  if skipIntro <> invalid then
    if skipIntro.enable <> invalid then theTimers.skipIntro.enable = skipIntro.enable
    if skipIntro.time <> invalid then theTimers.skipIntro.time = skipIntro.time
    if m.top.debug then print "Setting skipIntro", theTimers.skipIntro.enable;" >> ";theTimers.skipIntro.time
  else
    theTimers.skipIntro.enable = false
    theTimers.skipIntro.time = 1
  end if
  ' SETTING
  if not m.global.hasField("timers") m.global.AddField("timers", "assocarray", false)
  m.global.setFields({ timers: theTimers })

  if m.top.debug then
    print " "
    print "+-------------------------------------"
    print "| Configuracion de timers"
    print "+-------------------------------------"
    for each item in m.global.timers.items(): print item.key;" : ";item.value: end for
    print "+-------------------------------------"
    print " "
  end if
end sub
' getByRegion
' -----
' devuelve una configuracion por pais, o la default
function getByRegion(origin, path)
  if m.top.debug then print ghLogHead();"getByRegion -- ";path, Type(origin)
  try
    ini = ParseJson(ghGetChild(origin, path))
    if ini <> invalid then ' si tengo la key
      res = ghGetChild(ini, ghGetRegistry("region")) ' por region
      if res = invalid then res = ghGetChild(ini, "default") ' el default
      if res <> invalid then
        if m.top.debug then print ghLogHead();"getByRegion -- ";path, res
        return res
      end if
    end if
  catch error
    print ghLogHead();"ERROR -- ";error
  end try
  if m.top.debug then print ghLogHead();"INVALID -- ";path
  return invalid
end function