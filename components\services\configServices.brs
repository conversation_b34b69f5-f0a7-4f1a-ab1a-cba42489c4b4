' CONFIGURACION DE SERVICIOS
' ---------------------------------
'
' Parar servicio
' m.global.ServiceManager.stop = "REM"
'
' Restartear servicio
' m.global.ServiceManager.restart = "GA4"
'
' ---------------------------------


' RegisterServiceManager
' ------------------------------------------
' Inicializa el manejador de servicios de la APP
sub RegisterServiceManager(model = "classic")
  print ghLogHead("SERVICE");"RegisterServiceManager. -- ";model
  ' si estoy reentrando
  if m.global.hasField("ServiceManager") then
    print ghLogHead("SERVICE");"ServiceManager -- YA EXISTE!"
    m.global.ServiceManager.shutdown = true
    print ghLogHead("SERVICE");"ServiceManager -- SHUTDOWN!!"
  else
    ' definicion
    print ghLogHead("SERVICE");"ServiceManager -- CREATING !!"
    service_manager = CreateObject("roSGNode", "ServiceManager")
    service_manager.id = "service_manager"
    ' global
    m.global.AddField("ServiceManager", "node", false)
    m.global.ServiceManager = service_manager
    ' insert
    m.top.appendChild(service_manager)
    print ghLogHead("SERVICE");"ServiceManager -- CREATED !!"
  end if

  ' ----------
  'config
  m.global.ServiceManager.debug = false
  ' ----------
  RegisterServices(model)
end sub

sub RegisterServices(model)
  print ghLogHead("SERVICE");"RegisterServices. model=[";model;"]"
  ' ----------
  ' servicios
  if model = "classic" then
    InitWidgets()
    InitAnalytics()
    ' InitRemider()
    InitSuspended()
    InitChannels()
    InitRokupay()
    ' ----------
  end if
  if model = "lite" then
    InitWidgets()
    InitSuspended()
    InitExperience()
    InitChannels()
    InitAnalytics()
    InitRokupay()
  end if
end sub

' ANALYTICS
' ----------------------------
sub InitWidgets()
  m.global.ServiceManager.add = {
    component: "WidgetService",
    id: "Wid",
    loop: 1,
    global: "WIDGET"
    ' debug: true
  }
end sub
sub InitAnalytics()
  m.global.ServiceManager.add = {
    component: "GA4Service",
    id: "GA4",
    loop: 2,
    global: "GA4_plugin"
    debug: false
  }
end sub
sub InitRemider()
  m.global.ServiceManager.add = {
    component: "ReminderService",
    id: "REM",
    loop: 1,
    global: "REM_plugin"
    ' debug: true
  }
end sub
sub InitSuspended()
  m.global.ServiceManager.add = {
    component: "SuspendedService",
    id: "SUS",
    loop: 240, '240 1 hora * 60 min * 4(15sec) = 240
    global: "SUS_plugin"
    initialTick: true
    debug: false
  }
end sub
sub InitExperience()
  m.global.ServiceManager.add = {
    component: "ExperienceService",
    id: "Exp",
    loop: 240, ' 240  ' 1 hora * 60 min * 4(15sec) = 240
    global: "EXPERIENCE"
    debug: false
  }
end sub
sub InitChannels()
  m.global.ServiceManager.add = {
    component: "ChannelsService",
    id: "Chan",
    loop: 240, ' 240  ' 1 hora * 60 min * 4(15sec) = 240
    global: "CHAN"
    debug: false
  }
end sub
sub InitRokupay()
  m.global.ServiceManager.add = {
    component: "RokupayService",
    id: "RPay",
    loop: 1, ' 240  ' 1 hora * 60 min * 4(15sec) = 240
    global: "RPAY"
    debug: true
  }
end sub