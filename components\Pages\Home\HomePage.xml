<?xml version="1.0" encoding="utf-8" ?>

<!--  <component name="HomePage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd">  -->
<component name="HomePage" extends="Page">
  <script type="text/brightscript" uri="HomePage.brs"/>
  <interface>
    <!--  interfaz de entrada  -->
    <field id="deeplink" type="assocarray" onChange="OnDeepLink"/>
    <!--  interfaz de accion  -->
    <field id="title" type="string" value="" onChange="OnTitleChange" alwaysNotify="true"/>
    <field id="nodo" type="string" value="" onChange="OnNodoChange" alwaysNotify="true"/>
    <field id="order" type="integer" value="0"/>
    <field id="contenido" type="assocarray"/>
  </interface>
  <children>
    <!--  Header Alert Group  -->
    <HomeHeaderAlert id="headerAlert" translation="[0,0]"/>
    <!--  Main Content Group  -->
    <Poster id="fondo" translation="[0,70]" width="1280" height="720"/>
    <MenuComponent id="menu" translation="[40,90]"/>
    <LevelComponent id="clientZone" translation="[0,170]"/>
  </children>
</component>
