' CampaignLite
' https://app.swaggerhub.com/apis/ClaroVideo/campaign/1.0.0#/Campaign/post_services_campaign_v1__group_id_

sub DataInit()
  m.top.debug = false
  m.api.url = m.config.mfwk.host + "/services/campaign/v1/" + m.top.group_id

  m.api.method = "POST"

  m.api.query.delete("api_version")
  m.api.query.delete("HKS")
  m.api.query.delete("user_id")

  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })
  m.api.query.Append({
    "group_id": m.top.group_id,
  })
  m.api.body = formatJson({
    "payway_token": m.top.payway_token,
    "output": "xml_vmap1"
  })
end sub

sub ProcessData(res, raw)
  data = ghGetChild(res, "data")

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("campaign errors:", { errors: errors })

    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = data
end sub