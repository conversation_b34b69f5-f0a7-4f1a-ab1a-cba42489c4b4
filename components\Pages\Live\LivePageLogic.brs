sub VivoPage()
  print ghLogHead("VIVO PAGE");"VivoPage *** "

  ' si se produce un error con el streamType, intenta con otro solo si error es false
  ' si es otro error, vuelvo al canal anterior
  m.channelOld = {}
  m.channel = {}
  m.error = false
  m.initializeWithCarousel = false
  m.group_id = ""


  ' cargo la info de canales bloqueados
  controlPinList()

  ' cargo los favoritos
  ghCallApi("GetFavoritesLive")

  ' si no tengo cargado los canales, intento de nuevo cargas los mismos
  ' si ya estan cargados, busco los payway_token
  if ghGetChild(m.global, "channels.list") = invalid then
    ghCallApi("EpgVersion", "handleEpgVersion", "handleErrorEpgVersion")
  else
    getChannelsPayway("handleLinealChannel")
  end if
end sub



sub controlPinList()
  ' actualizo variable global, con canales que tienen pin parental
  ghCallApi("ListPin")
  'modifiqué acá para que pueda ver que hace la api
  'ghCallApi("ModifyControlPin")
end sub

'sub controlModifyPin()
'llamo por las dudas que no estén cargados los estados de los pines
'ghCallApi("ListPin")
'actualizo variable global, o pin , con canales que tienen pin parental
'ghCallApi("ModifyControlPin")
'modifiqué acá
'end sub

sub handleEpgVersion() ' event
  ' data = event.getData()
  ghCallApi("EpgMenu", "handleEpgMenu", "handleErrorEpgMenu")
end sub

sub handleEpgMenu() ' event
  ' data = event.getData()
  ' lineup = ghCallApi("Lineup", "getChannelsPayway", "handleError")
  ghCallApi("Lineup", "getChannelsPayway", "handleErrorLineUp")
end sub

sub getChannelsPayway(event)
  print ghLogHead("VIVO PAGE");"getChannelsPayway *** "

  ' si viene de un callApi, o si es un pedido para obtener nuevos payway token
  if type(event) = "roSGNodeEvent" then
    ghCallApi("LinealChannels", "handleLinealChannel", "handleErrorLinealChannels")
  else
    ghCallApi("LinealChannels", event, "handleErrorLinealChannels")
  end if
end sub


sub reintentar() ' event ' ' reintento si da mensaje de payway token
  setPlayer(m.channel)
end sub

sub handleLinealChannel() ' event
  print ghLogHead("VIVO PAGE");"handleLinealChannel *** "
  m.top.channels = ghGetChild(m.global, "channels.list") ' lista de canales
  ' ya tengo listado de canales
  ' sintonizo canal, mientras lleno epg
  playFirstChannel()
end sub

sub handleErrorLinealChannels() ' event
  m.logger.error("Error en la carga lineal channels")
  showError({ title: ghTranslate("channel_error_generic_title	", "Hubo un error inesperado"), message: ghTranslate("channel_error_generic_description", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde."), onAccept: "OnExitToHome" })
end sub

sub handleErrorLineUp() ' event
  m.logger.error("Error en la carga de lineUp")
  showError({ title: ghTranslate("channel_error_generic_title	", "Hubo un error inesperado"), message: ghTranslate("channel_error_generic_description", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde."), onAccept: "OnExitToHome" })
end sub

sub handleErrorEpgVersion() ' event
  m.logger.error("Error en la carga de epg version")
  showError({ title: ghTranslate("channel_error_generic_title	", "Hubo un error inesperado"), message: ghTranslate("channel_error_generic_description", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde."), onAccept: "OnExitToHome" })
end sub

sub handleErrorEpgMenu() ' event
  m.logger.error("Error en la carga de epg Menu")
  showError({ title: ghTranslate("channel_error_generic_title	", "Hubo un error inesperado"), message: ghTranslate("channel_error_generic_description", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde."), onAccept: "OnExitToHome" })
end sub

sub playFirstChannel()
  payway = ""
  blocked = false
  channel = {}
  nextChannel = invalid

  ' seleccion si existe el canal guardado en el usuario
  group_id = ghGetChild(m.top, "group_id", "") ' para recibir desde la home

  ' solo si viene al live sin especificar un canal
  if group_id = "" then
    group_id = ghGetRegistry("channel", "user")
    m.logger.debug("channel from registry: ", { groupId: group_id })
  else
    m.initializeWithCarousel = true
  end if

  m.logger.debug("playFirstChannel - group_id: ", { groupId: group_id, initializeWithCarousel: m.initializeWithCarousel })

  if group_id <> "" and group_id <> invalid then
    channelsBlock = ghGetChild(m.global, "parental.channels", {})

    ' si esta bloqueado no selecciono canal guardado
    if channelsBlock[group_id] <> invalid then
      m.logger.debug("channel blocked: ", { groupId: group_id })
      blocked = true
    end if

    listChannels = ghGetChild(m.global, "channels.list", [])
    listPayway = ghGetChild(m.global, "channels.payway", {})

    for i = 0 to listChannels.Count() - 1
      item = listChannels[i]
      ' si el canal guardado no esta en la lista de canales, no selecciono canal guardado
      if ghGetChild(item, "group_id", "") = group_id then
        payway = ghGetChild(listPayway[item.group_id], "payway_token")
        ' si no tiene payway token, no selecciono canal guardado
        if payway <> "" and payway <> invalid then
          m.logger.debug("tengo payway token", { item: item })
          channel = item
        end if

        ' exit for
      else if ghGetChild(channel, "group_id", "") <> "" and channelsBlock[item.group_id] = invalid and nextChannel = invalid then
        ' siguiente canal no bloqueado
        nextPayway = ghGetChild(listPayway[item.group_id], "payway_token")
        if nextPayway <> "" and nextPayway <> invalid then
          m.logger.debug("preparando variable con canal no bloqueado", { item: item })
          nextChannel = item
        end if
      end if
    end for
  end if
  ' ===================================================

  ' si no hay seleccionado ningun canal
  if m.initializeWithCarousel = false and ghGetChild(channel, "group_id", "") = "" then
    m.logger.debug("sin initializeWithCarousel y sin channel")

    channelsBlock = ghGetChild(m.global, "parental.channels", {})

    listChannels = ghGetChild(m.global, "channels.list", [])
    listPayway = ghGetChild(m.global, "channels.payway", {})
    ' buscar primero con paywayToken
    for i = 0 to listChannels.Count() - 1
      item = listChannels[i]
      payway = ghGetChild(listPayway[item.group_id], "payway_token")
      if payway <> "" and payway <> invalid then
        m.logger.debug("selecciono primer canal con payway token")
        ' channel = item
        if channelsBlock[item.group_id] <> invalid then
          m.logger.debug("channel blocked: ", { groupId: item.group_id })
          blocked = true
        else
          channel = item
          blocked = false
          m.logger.debug("channel blocked: ", { groupId: item.group_id })
          exit for
        end if
        ' exit for
      end if
    end for

    ' si entra desde un carousel y no tiene payway token, muestro msj y salgo
  else if ghGetChild(m.top, "group_id", "") <> "" and (payway = "" or payway = invalid) and blocked = false then
    m.logger.debug("vengo de un carousel y no tengo payway token")
    print "global data : doPlansOfferV2 " group_id
    doPlansOfferV2(group_id)
    m.channel = { group_id: group_id }


    '  showError({ onAccept: "OnExitToHome", title: ghTranslate("channel_error_not_purchased_title", "Transacción no disponible"), message: ghTranslate("channel_error_not_purchased_description", "Adquiere este contenido desde la web o app de Claro video. Una vez adquirido podrás disfrutarlo en tu Roku") })

    ' si viene de un carousel y el canal esta bloquedo, sigo con ese canal bloqueado para que pida pin
  else if ghGetChild(m.top, "group_id", "") <> "" and blocked = true then
    m.logger.debug("vengo de un carousel y el canal esta bloqueado", { channel: channel })

    ' si esta bloqueado, agarro el siguiente canal que tenga payway token y no este bloqueado
  else if ghGetChild(channel, "group_id", "") <> "" and blocked = true then
    m.logger.debug("channel blocked, selecciono siguiente canal")
    channel = nextChannel

  end if


  if ghGetChild(channel, "group_id", "") <> "" then
    m.logger.debug("channel selected group_id: ", { groupId: ghGetChild(channel, "group_id", "") })
    setPlayer(channel)
  else
    ' showGridChannels()
  end if
end sub

sub setPlayer(channel)
  print ghLogHead("VIVO PAGE");"setPlayer *** "

  groupId = ghGetChild(channel, "group_id", "sin_id")

  ' si quiere cambiar al mismo canal que esta reproduciondo, no hago nada, solo oculto grilla o epg
  if groupId = ghGetChild(m.video, "content.groupid", "") then
    ' mismo canal, oculto grilla o epg
    m.video.panelVisible = false
  else
    listPayway = ghGetChild(m.global, "channels.payway", {})
    payway = ghGetChild(listPayway[groupId], "payway_token")

    if payway = "" or payway = invalid then
      ' mensaje de compra ?
      m.logger.error("no tiene payway token", { groupId: groupId })
      ' showGridChannels()
      ' save channel to change after purchase
      m.channel = channel
      doPlansOfferV2(groupId)
      ' showError({ title: ghTranslate("channel_error_not_purchased_title", "Transacción no disponible"), message: ghTranslate("channel_error_not_purchased_description", "Adquiere este contenido desde la web o app de Claro video. Una vez adquirido podrás disfrutarlo en tu Roku") })
    else
      m.channel = channel
      m.channel.payway_token = payway
      m.channel.offerid = ghGetChild(listPayway[groupId], "offerid")
      m.channel.purchaseid = ghGetChild(listPayway[groupId], "purchaseid")

      ' canales bloqueados
      channels = ghGetChild(m.global, "parental.channels", {})

      ' si no esta bloqueado sigo, sino pido pin
      if channels[groupId] = invalid or channels[groupId] = "" then
        ' guardo canal que estoy intentado mostrar, para reintentar en caso de error

        ' TODO (ver si funciona bien) mando track-stop antes de la getMedia
        trackSendStop("onGenericTrackOk", "onGenericTrackError")

        attach = ghCallApi("Attach", "attackResponse", "attackResponse", false)
        attach.group_id = groupId
        attach.purchase_id = ghGetChild(listPayway[groupId], "purchaseid")
        attach.control = "run"

        iniciarReproduccion({ groupId: groupId, payway: payway })
      else
        ' pantalla de pin parental
        m.pinCallback = { "ok": "iniciarReproduccion", "error": "handlerErrorPin", parameters: { groupId: groupId, payway: payway, type: "smooth_streaming" } }
        getPin()
      end if

    end if
  end if
end sub

sub showError(data = { message: "", title: "", accept: "OK", onAccept: "" })
  ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
  ScrGenericMessage.id = "errorLive"
  ScrGenericMessage.SetFields({
    title: ghGetChild(data, "title", ghTranslate("channel_error_generic_title", "Hubo un error inesperado"))
    message: ghGetChild(data, "message", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (LPL-01)"),
    accept: ghGetChild(data, "accept", ghTranslate("buypin_error_button_accept", "OK"))
  })
  ScrGenericMessage.ObserveField("wasClosed", ghGetChild(data, "onAccept", ""))
  m.top.routerChild = {
    page: ScrGenericMessage,
    fields: {}
  }
end sub

function onKeyEvent(key, press) as boolean
  print ghLogHead("VIVO PAGE");"onKeyEvent *** "

  handled = false
  if press then
    if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press

    if key = "back" then
      OnExitToHome()
      handled = true
    end if
  end if
  return handled
end function

sub getPin(title = invalid, subTitle = invalid, accept = invalid, hideSendpin = false)
  'accept es la key de apa para el texto del boton aceptar si es que queremos modificar el valor por defecto
  ScrOptions = CreateObject("roSGNode", "ScrPinInputV2")
  ScrOptions.id = "srcPinInput"
  ScrOptions.ObserveField("wasClosed", "BuyPin_Return")

  textTitle = ghTranslate("channel_pin_label_title", "Canal bloqueado")
  textSubTitle = ghReplaceStr(ghTranslate("channel_pin_label_description", "Ingresa tu PIN de seguridad para {br} desbloquear este canal."), "{br}", "+chr(10)+")
  if title <> invalid
    textTitle = title
  end if
  if subTitle <> invalid
    textSubTitle = subTitle
  end if

  m.top.routerChild = {
    page: ScrOptions,
    fields: {
      textTitle: textTitle
      textSubTitle: textSubTitle
      textButton: accept
      hideSendpin: hideSendpin
    }
  }
end sub

sub BuyPin_Return(event)
  scr = event.getRoSGNode()


  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    ' si viene de un carousel, el canal esta bloqueado y pone cancelar, salgo del live
    if m.initializeWithCarousel = true then
      OnExitToHome()
    else
      ' si no se esta viendo nada, abro grilla de canales
      m.video.focus = true
      if m.video.content = invalid then
        showGridChannels()
      end if
    end if
  else if scr.value.opcion = "SELECT" then
    m.top.loading = true
    hasPin = ghGetChild(m.global.status_pin, "has_a_pin", false)
    print hasPin
    print "el valor de el pin es"
    print m.global.status_pin.has_a_pin
    if hasPin = false then 'este es el flujo de un usuario nuevo que configura su pin
      m.source = scr.value.data
      apiModifyPin = ghCallApi("ModifyControlPin", "modifyResOK", "modifyResError", false)
      apiModifyPin.pin = m.source
      apiModifyPin.isCreatePin = true
      apiModifyPin.control = "run"
      m.top.loading = false
    else
      apiCheckControlPin = ghCallApi("CheckControlPin", ghGetChild(m.pinCallback, "ok"), ghGetChild(m.pinCallback, "error"), false)
      apiCheckControlPin.parametersCallback = ghGetChild(m.pinCallback, "parameters", {})
      apiCheckControlPin.controlPIN = scr.value.data
      apiCheckControlPin.control = "run"
    end if
  end if
end sub

sub handlerErrorPin() ' event
  m.top.loading = false

  m.logger.error("Error en el pin", { iniciado: m.iniciado })

  ' si ya se esta viendo un canal, no muestro mensaje de error, ya que queda en el canal que estaba antes de pedir pin
  if m.iniciado = false then
    m.video.showError = {
      title: ghReplaceStr(ghTranslate("error_channel_block", "El canal {canal} esta bloqueado"), "{canal}", ghGetChild(m.channel, "name", "") + " " + ghGetChild(m.channel, "number", ""))
      image: ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))
    }
  end if

  showError({ title: ghTranslate("buypin_error_title", "Error pin"), message: ghTranslate("buypin_error_description", "Pin invalido"), onAccept: "Buypin" })
end sub

sub showGridChannels() ' event = invalid
  m.top.loading = false
  m.video.showGridChannels = true
end sub

sub checkPinMessage(data = { message: "", title: "", accept: "OK", onAccept: "", group_id: "" })
  m.group_id = data.group_id
  ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
  ScrGenericMessage.id = "hasNotPin"
  ScrGenericMessage.SetFields({
    title: ghTranslate("setupPin_confirmation_title_label", "")
    message: ghTranslate("setupPin_confirmation_detail_label", "Debes configurar un pin primero"),
    accept: ghTranslate("setupPin_confirmation_option_button_continue", "CONTINUAR")
  })
  ScrGenericMessage.ObserveField("wasClosed", "inputChannelRouter")
  m.top.routerChild = {
    page: ScrGenericMessage,
    fields: {}
  }
end sub

sub inputChannelRouter(event)
  scr = event.getRoSGNode()
  title = ghTranslate("setupPin_confirmation_title_label", "Elige un pin de 6 números")
  description = ghTranslate("setupPin_confirmation_option_button_crear", "Crear")
  if scr.value.opcion = "FWD" then
    'para reutilizar el componente del get pin se le agrega una clavede apa que el componente
    'va a validar para el boton aceptar y un booleano para saber si hace hidden el de recuperar
    'pin
    getPin(title, description, "setupPin_confirmation_option_button_aceptar", true)
  end if
end sub

sub modifyResOK()
  print "la modify respondio OK"
  apiCheckControlPin = ghCallApi("ControlPinAdd", "pinAddOk", "pinAddError", false)
  apiCheckControlPin.group_id = m.group_id
  apiCheckControlPin.control = "run"
  m.top.loading = false
  ' cargo la info de canales bloqueados
  controlPinList()
end sub

sub modifyResError()
  print "hubo un error de el pin"
  m.top.loading = false
end sub

'lOGIC FOR CHANNEL SUBSCRIPTION FLOW

sub doPlansOfferV2(group_id)
  print ghLogHead("VIVO PAGE");"doPlansOfferV2 Group ID: ";group_id
  setLoading(true)
  getProviderCode(group_id)
  offerApi = ghCallApi("PbiLite", "OfferOk", "offerError", false)
  offerApi.setFields({
    group_id: group_id
  })
  offerApi.control = "run"
end sub

function getProviderCode(group_id = "")
  if m.global.channelProvider <> invalid and m.global.channelProvider.result.getChildCount() <> 0 then
    for i = 0 to m.global.channelProvider.result.getChildCount() - 1
      item = m.global.channelProvider.result.getChild(i)
      if item.group_id = group_id then
        m.selectedChannel = item
        exit for
      end if
    end for

    m.providerCode = ghGetChild(m.selectedChannel, "proveedor_code", "")
    print ghLogHead("VIVO PAGE");"selectedChannel : ";m.selectedChannel
    print ghLogHead("VIVO PAGE");"getProviderCode : ";m.providerCode
  end if
end function

sub OfferOk(event)
  data = event.getData()

  m.accessCode = ghGetChild(data, "accessCode")

  setLoading(false)

  botones = ghGetChild(data, "listButtons.button", [])
  purchaseInfo = []
  for each item in botones
    if item.purchasable = true
      purchaseInfo.push(item)
    end if
  end for

  if purchaseInfo.Count() > 1
    multiPackPage = CreateObject("roSGNode", "multiPackPage")
    multiPackPage.id = "multiPackPage"
    multiPackPage.ObserveField("wasClosed", "handleCloseSubscription")

    m.top.routerChild = {
      page: multiPackPage,
      fields: {
        data: purchaseInfo
      }
    }
    return
  end if

  if purchaseInfo.Count() = 1
    singlePackPage = CreateObject("roSGNode", "singlePackPage")
    singlePackPage.id = "singlePackPage"
    singlePackPage.ObserveField("wasClosed", "handleCloseSubscription")

    m.top.routerChild = {
      page: singlePackPage,
      fields: {
        data: purchaseInfo[0],
      }
    }
    return
  end if

  callCenterPage = CreateObject("roSGNode", "callCenterPage")
  callCenterPage.id = "callCenterPage"
  callCenterPage.ObserveField("wasClosed", "handleCloseSubscription")

  m.top.routerChild = {
    page: callCenterPage,
    fields: {
      data: m.selectedChannel
      providerCode: m.providerCode
    }
  }
end sub

sub handleCloseSubscription(event)
  data = event.getRoSGNode()

  m.logger.debug("handleSubscription", { data: data.selected })

  if data.selected <> invalid then
    buyValue(data.selected)
  else
    m.logger.debug("close subscription screen", { initializeWithCarousel: m.initializeWithCarousel })

    if m.initializeWithCarousel = true then
      OnExitToHome()
    end if
  end if
end sub

function buyValue(info)
  buyView = CreateObject("roSGNode", "BuyViewV2")
  buyView.id = "BuyView"
  buyView.ObserveField("wasClosed", "handleCloseBuy")

  m.top.routerChild = {
    page: buyView,
    fields: {
      buyB: info
      accessCode: m.accessCode
      ' accessCode: {
      '   enabled: false
      '   msgAccessCode: "Protege tus pagos y activa el control parental con tu PIN de protección. Configúralo desde tu computadora en el menú de tu perfil."
      ' }
      data: {}
    }
  }
end function

sub handleCloseBuy(event)
  data = event.GetRoSGNode()

  m.logger.debug("handleCloseBuy", { data: data })

  refresh = ghGetChild(data, "refresh", false)
  if refresh = true then
    'buy sucess

    setLoading(true)
    ghCallApi("LinealChannels", "handleLinialChannelBuy", "handleLinialChannelBuy")

  else
    ' cancel

    if m.initializeWithCarousel = true then
      OnExitToHome()
    end if
  end if
end sub

sub handleLinialChannelBuy()
  m.logger.debug("handleLinialChannelBuy")
  setLoading(false)
  listChannels = ghGetChild(m.global, "channels.list", [])
  listPayway = ghGetChild(m.global, "channels.payway", {})
  for i = 0 to listChannels.Count() - 1
    item = listChannels[i]
    if ghGetChild(item, "group_id", "") = m.channel.group_id then
      m.logger.debug("purchased token payway token", { channelPosition: i })
      payway = ghGetChild(listPayway[item.group_id], "payway_token")
      if payway <> "" and payway <> invalid then
        channel = item
        exit for
      end if
    end if
  end for
  m.channel = channel
  setPlayer(m.channel)
end sub

sub offerError(event)
  data = event.getData()
  m.logger.error("Error en la carga de ofertas", { data: data })
  showError({ title: ghTranslate("channel_error_generic_title", "There was an unexpected error"), message: ghTranslate("channel_error_generic_description", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (LPL-01)"), onAccept: "OnExitToHome" })
  setLoading(false)
end sub