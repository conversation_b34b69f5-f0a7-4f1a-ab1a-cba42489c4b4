sub DataInit()
  async = true
  if m.top.mode <> "async" then
    async = false
  end if

  m.api.async = async
  m.api.url = m.top.url
  ' m.api.url = m.top.url + ghArray2Query({ "timecode": m.top.timecode }, "")
  m.api.query = {}
  m.api.query.Append({
    "timecode": m.top.timecode
    "device_id": "f7785395-3dc0-5ca4-b2bd-b4e6346221e3"
  })

  if m.top.purchase_id <> invalid and m.top.purchase_id <> "" then
    m.api.query.Append({
      purchase_id: m.top.purchase_id
    })
  end if
  if m.top.preferred_audio <> invalid and m.top.preferred_audio <> "" then
    m.api.query.Append({
      offer_id: m.top.offer_id
    })
  end if
  if m.top.preferred_audio <> invalid and m.top.preferred_audio <> "" then
    m.api.query.Append({
      preferred_audio: m.top.preferred_audio
    })
  end if
  if m.top.preferred_subtitle <> invalid and m.top.preferred_subtitle <> "" then
    m.api.query.Append({
      preferred_subtitle: m.top.preferred_subtitle
    })
  end if

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
  end if
end sub

sub ProcessData(res, raw)
  textHtml = Instr(1, raw, "<HTML>")

  result = {}
  if res <> invalid then
    result = res
  end if

  if textHtml > 0 then
    m.top.error = {
      raw: raw
    }
  else
    result.trkMessage = m.top.message
    if ghGetChild(result, "status") = 200 then
      m.top.content = result
    else
      m.top.error = result
    end if
  end if
end sub