<?xml version="1.0" encoding="utf-8" ?>

<component name="LevelListComponent" extends="Page">

  <script type="text/brightscript" uri="LevelListComponent.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="back" type="boolean" value="false" alwaysNotify="true" />
    <field id="node" type="string" value="invalid" />
    <field id="title" type="string" value="" onChange="OnTitleChange" alwaysNotify="true" />
    <field id="cintasLevel" type="array" onChange="updateLevel" />
    <!-- interfaz de salida -->
    <field id="contenido" type="assocarray" />
  </interface>

  <children>
    <Label id="title" text="" translation="[0,0]"  width="1180" height="60" horizAlign = "center" vertAlign = "center"/>
    <GHOrderButtons id="theFilters" translation= "[90,0]" backSelEnable="false" handleKey="false" />
    <GHRowList id="theList" />

    <GHLoading id="spinner" visible="true" translation="[-50, -100]"/>
  </children>

</component>
