' ProfileLite
' https://app.swaggerhub.com/apis/ClaroVideo/ProfileRead/1.0.0
' -----------------------

sub DataInit()
  ' m.top.debug = true
  m.logger.debug("DataInit **")

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/v1/profile/read"
  m.api.query.delete("api_version") ' sin api version
  ' m.api.query.delete("user_id") ' sin user id
  ' m.api.query.delete("HKS") ' sin user id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    "user_token": ghGetRegistry("user_token", "user")
    ' "lasttouch": ghGetRegistry("lasttouch_profile", "user")
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw= ", { raw: raw })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  m.logger.debug("ProcessData -- res=", { res: res })
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' manejo de errores
  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    m.logger.error("ProcessData -- ERROR ", { error: errors })
    res.raw = raw
    m.top.error = res
    return
  end if

  data = res.data ' raiz de datos

  currGId = ghGetRegistry("gamification_id")
  cantPerf = data.members.Count()
  for p = 0 to cantPerf - 1
    prof = data.members[p]
    m.logger.debug("ProcessData -- member ", { username: prof.username, gamificaciontId: prof.gamification_id })
    if prof.gamification_id = currGId
      m.logger.debug("ProcessData -- !ES ESTE! member ", { username: prof.username, gamificaciontId: prof.gamification_id })
      ' ghSetRegistry("profile", FormatJson(prof), "user")
      m.global.profiles_current = prof
    end if
  end for

  print "|PROF| >> ";m.global.profiles_current

  ' respuesta
  m.top.content = {
    data: data
  }

end sub
