sub Init()
  m.top.debug = false

  m.top.ObserveField("visible", "onPanelVisibleChange")

  m.map = {
    "languages": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }

  ' Panel Left
  ' -----------------------------
  ' m.panL = m.top.findNode("panelLeft")
  m.infoSeason = m.top.findNode("infoSeason")
  m.infoEpisode = m.top.findNode("infoEpisode")
  m.infoEpisodeTitle = m.top.findNode("infoEpisodeTitle")
  ' m.panL.setFields({
  '   translation: [0, 0]
  '   width: 1080
  '   height: 1920
  '   ' color: "#000000"
  ' })
  m.title = m.top.findNode("title")
  m.title.setFields({
    translation: [81, 187]
    font: ghGetFont(43, "regular")
    width: 664
    height: 55
  })
  m.time = m.top.findNode("time")
  m.time.setFields({
    translation: [50, 100]
    font: ghGetFont(21, "regular")
    width: 500
    height: 32
  })
  m.desc = m.top.findNode("description")
  m.desc.setFields({
    translation: [81, 340]
    font: ghGetFont(21, "regular")
    width: 728
    height: 112
  })
  ' -----------------------------
  ' Panel Right
  ' -----------------------------
  m.panR = m.top.findNode("panelRight")
  m.panR.setFields({
    translation: [880, 0]
    width: 401
    height: 720
    color: "#282828"
  })
  m.langLabel = m.top.findNode("langLabel")
  m.langLabel.setFields({
    translation: [50, 100]
    text: ghTranslate("", "IDIOMA")
    color: "#999999"
    font: ghGetFont(21, "regular")
  })
  m.languages = m.top.findNode("languages")
  m.languages.setFields({
    translation: [00, 190]
    itemComponentName: "GHLiveLangItem"
    numColumns: 1
    numRows: 7
    color: "#FFFFFF"
    itemSize: [500, 30]
    itemSpacing: [0, 50]
    drawFocusFeedback: false
    vertFocusAnimationStyle: "floatingFocus"
  })
  m.languages.ObserveField("itemSelected", "onItemSelected")
  ' -----------------------------
end sub

sub onPanelVisibleChange(event)
  data = event.getData()
  if data then ' entro----
    position = 0
    for each l in m.top.channels
      if l.is_current then
        exit for
      end if
      position = position + 1
    end for
    m.languages.jumpToItem = position

    m.languages.setFocus(true)
  else ' salgo ------------------
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub

' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  if m.top.visible = true then
    if press then
      if key <> "back" then
        turnFocusTo(guessFocusTo(key))
        handled = true
        if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
      else
        handled = true
        ' m.top.focus = false
        m.top.visible = false
      end if
    end if
  end if
  return handled
end function

function guessFocusTo(direction)
  focusTo = invalid
  current = getCurrentFocus()
  if current <> invalid then
    ' a donde voy?
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    else
      focusTo = current
    end if
  end if
  return focusTo
end function

sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
      m.top.findNode(id).setFocus(true)
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub

function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  ' if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function

sub onInfoUpdate(event)
  data = event.getData()
  m.title.text = ghGetChild(data, "title", "")
  m.time.setFields({
    font: ghGetFont(24, "regular")
    text: ghFormatDuration(ghGetChild(data, "duration", invalid))
    translation: [80, 292]
  })
  m.desc.text = ghGetChild(data, "description", "")
  if ghGetChild(data, "serie_id") <> invalid then
    m.infoSeason.setFields({
      font: ghGetFont(24, "regular")
      text: ghTranslate("Temporada", "Temporada") + " " + ghGetChild(data, "season")
    })
    m.infoEpisode.setFields({
      font: ghGetFont(16, "regular")
      text: ghTranslate("Episodio", "Episodio") + " " + ghGetChild(data, "episodenumber")
    })
    m.infoEpisodeTitle.setFields({
      font: ghGetFont(16, "bold")
      text: ghGetChild(data, "titleEpisode")
    })

    m.top.findNode("infoSerie").visible = true
  else
    m.top.findNode("infoSerie").visible = false
  end if
end sub

sub onDataUpdate(event)
  ls = event.getData()

  langs = createObject("RoSGNode", "GHContent")
  for each l in ls
    lang = createObject("RoSGNode", "GHContent")
    lang.data = l
    langs.appendChild(lang)
  end for

  m.languages.content = langs
end sub

sub onItemSelected(event)
  data = event.getData()
  if data <> invalid then
    print ghLogHead();"onItemSelected >>----> ";m.languages.content.getChild(data)
    print ghLogHead();"onItemSelected >>----> ";data
    data = ghGetChild(m.languages.content.getChild(data), "data")
    m.top.selected = data
  end if
end sub