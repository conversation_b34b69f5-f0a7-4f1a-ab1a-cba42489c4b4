' Mensaje dinamico
' gdripoll - 2023

' INIT
' ---------
sub Init()
  m.top.debug = true
  if m.top.debug then print ghLogHead();"Init"
  initialFormat()
end sub
sub initialFormat()
  if m.top.debug then print ghLogHead();"initialFormat ***"
  ' -----
  if m.top.debug then print ghLogHead();"pantalla --"
  m.pantalla = m.top.findNode("pantalla")
  m.pantalla.setFields({
    width: 1920
    height: 1080
    color: "#121212"
  })
  ' -----
  if m.top.debug then print ghLogHead();"imagenes --"
  m.imagenes = m.top.findNode("imagenes")
  m.top.images = [' array of Poster
    {
      uri: "pkg://images/logo.png"
      translation: [50, 50]
    }
  ]

  m.columna = m.top.findNode("columna")
  m.columna.setFields({
    translation: [640, 200],
    horizAlignment: "center"
    vertAlignment: "top"
    itemSpacings: [20]
  })
  ' -----
  m.iconos = m.top.findNode("iconos")
  m.iconos.setFields({
    layoutDirection: "horiz"
    itemSpacings: 30
  })
  m.top.icons = [
    {
      uri: "pkg://images/HD/peligro.png"
    }
  ]
  ' -----
  m.titulos = m.top.findNode("titulos")
  m.titulos.setFields({
    layoutDirection: "vert"
    horizAlignment: "center"
    itemSpacings: 5
  })
  m.top.titles = [' array de Labels
    {
      text: "Titulo"
      font: ghGetFont(26, "bold")
    }
  ]
  ' -----
  m.mensajes = m.top.findNode("mensajes")
  m.mensajes.setFields({
    layoutDirection: "vert"
    horizAlignment: "center"
    itemSpacings: 5
  })
  m.top.messages = [' array de Labels
    {
      text: "un mensaje"
      font: ghGetFont(16, "medium")
    }
  ]
  ' -----
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnBackSelected")
  m.botonera.setFields({
    layoutDirection: "vert"
    horizAlignment: "center"
    ' -----
    layout: "childs"
    orientation: "vertical"
  })
  m.top.buttons = [' array de ghButtons
    {
      id: "btnAceptar"
      text: "ACEPTAR"
      value: "accept"
    }
  ]
end sub
' BUILD
' ---------
sub buildImagenes(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"buildImagenes ----------"
  _clearChilds(m.imagenes)
  for each i in data
    m.imagenes.appendChild(_buildObject("Poster", i))
  end for
end sub
sub buildIconos(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"buildIconos ----------"
  _clearChilds(m.iconos)
  for each i in data
    m.iconos.appendChild(_buildObject("Poster", i))
  end for
end sub
sub buildTitulos(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"buildTitulos ----------"
  _clearChilds(m.titulos)
  for each i in data
    m.titulos.appendChild(_buildObject("Label", i))
  end for
end sub
sub buildMensajes(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"buildMensajes ----------"
  _clearChilds(m.mensajes)
  for each i in data
    m.mensajes.appendChild(_buildObject("Label", i))
  end for
end sub
sub buildBotonera(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"buildBotonera ----------"
  _clearChilds(m.botonera)
  for each i in data
    m.botonera.appendChild(_buildObject("GHButton", i))
    print m.botonera.getChildCount()
  end for
end sub
' EVENTS
' ---------------------
sub onWasShown(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onWasShown -- ";data
  if data then
    if m.top.debug then print ghLogHead();"onWasShown -- FOCUS!"
    m.botonera.focus = true
    m.botonera.setFocus(true)
  else
    if m.top.debug then print ghLogHead();"onWasShown -- OFF FOCUS!"
  end if
end sub
sub OnButtonSelected()
  if m.top.debug then print ghLogHead();"OnButtonSelected -- ";m.botonera.value
  exitScreen(m.botonera.value)
end sub
sub OnBackSelected()
  if m.top.debug then print ghLogHead();"OnBackSelected -- ";m.botonera.value
  exitScreen(m.top.resultOnBack)
end sub
' ACTIONS
' ---------------------
sub exitScreen(value)
  if m.top.debug then print ghLogHead();"exitScreen -- value=";value
  options = {}
  options[m.top.resultProperty] = value
  m.top.routerReturn = options
end sub
' INTERNALS
' ---------------------
function _buildObject(tipo, data)
  if type(data) = "roSGNode" then return data ' si viene algo, pongo eso.
  obj = CreateObject("roSGNode", tipo)
  obj.setFields(data)
  return obj
end function
sub _clearChilds(obj)
  obj.removeChildrenIndex(obj.getChildCount(), 0)
end sub

' PORLAS
' ----------------------------
' sub showMessage(event)
'   data = event.getData()
'   if m.top.debug then print ghLogHead();"showMessage :: DATA ", data
'   ' construyo el mensaje
'   buildLogo(data.logo)
'   buildIcon(data.icon)
'   buildTitles(data.titles)
'   buildMessages(data.messages)
'   buildButtons(data.buttons)
' end sub
' sub buildLogo(data)
'   if data <> invalid then
'     if m.top.debug then print ghLogHead();"buildLogo ", data
'     if data.object <> invalid then
'       m.imagenes.appendChild(data.object)
'     else ' lo dibujo yo
'       if data.uri <> invalid then
'         logo = CreateObject("roSGNode", "Poster")
'         logo.uri = data.uri
'         if data.campos <> invalid then logo.setFields(data.campos)
'         m.imagenes.appendChild(logo)
'       end if
'     end if
'   else
'     if m.top.debug then print ghLogHead();"buildLogo INVALID"
'   end if
' end sub
' sub buildIcon(data)
'   if data <> invalid then
'     if m.top.debug then print ghLogHead();"buildIcon ", data

'     if data.object <> invalid then
'       m.imagenes.appendChild(data.object)
'     else ' lo dibujo yo
'       if data.uri <> invalid then
'         icon = CreateObject("roSGNode", "Poster")
'         icon.uri = data.uri
'         if data.campos <> invalid then icon.setFields(data.campos)
'         m.imagenes.appendChild(icon)
'       end if
'     end if

'   else
'     if m.top.debug then print ghLogHead();"buildIcon INVALID"
'   end if
' end sub
' sub buildTitles(data)
'   if data <> invalid then
'     if m.top.debug then print ghLogHead();"buildTitles ", data

'     for each t in data
'       if t.object <> invalid then
'         m.titles.appendChild(t.object)
'       else
'         if t.text <> invalid then
'           text = CreateObject("roSGNode", "Label")
'           text.text = t.text
'           if t.campos <> invalid then text.setFields(t.campos)
'           m.titles.appendChild(text)
'         end if
'       end if
'     end for

'   else
'     if m.top.debug then print ghLogHead();"buildTitles INVALID"
'   end if
' end sub
