' CVV
'

sub Init()
  m.top.debug = false
  ' components
  m.title = m.top.findNode("lblTitle")
  m.descrip = m.top.findNode("lblDescrip")
  m.accept = m.top.findNode("btnAccept")
  m.accept.ObserveField("selected", "OnAccept")

  m.title.setFields({
    font: ghGetFont(28, "bold")
    text: m.top.title
    horizAlign: "center"
  })
  m.descrip.setFields({
    font: ghGetFont(24, "regular")
    text: m.top.message
    horizAlign: "center"
  })
  m.accept.setFields({
    text: m.top.accept
  })
end sub
sub updateFieldFocus()
  if m.top.debug then print ghLogHead();"updateFieldFocus ***"
  if m.top.focus then
    m.prevTheme = ghPushTheme(m.global.config.theme) ' mi tema
    m.accept.focus = true
  else
    ghPopTheme(m.prevTheme) ' restauro el tema anterior
    m.accept.setFocus(false)
    ' closeScreen()
  end if
end sub
sub OnAccept() ' event
  if m.top.debug then print ghLogHead();"OnAccept >> ";m.top.fwd
  m.top.value = { opcion: "FWD", data: m.top.fwd }
  m.top.routerClose = true
end sub
sub BackTo()
  if m.top.debug then print ghLogHead();"BackTo *** >> ";m.top.back
  m.top.value = { opcion: "BACK", data: m.top.back }
  m.top.routerClose = true
end sub
' FOCUS
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key = "back" then
      BackTo()
    end if
  end if
  return handled
end function