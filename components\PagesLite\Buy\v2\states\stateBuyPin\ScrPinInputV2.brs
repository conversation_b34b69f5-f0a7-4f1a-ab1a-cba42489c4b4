' ScrPinInput
' -----------------------------

sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init ***"
  ' components
  m.title = m.top.findNode("title")
  m.descrip = m.top.findNode("descrip")

  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.setFields({
    map: {
      "cvv": { "up": invalid, "right": invalid, "down": "accept", "left": invalid },
      "accept": { "up": "cvv", "right": invalid, "down": "sendpin", "left": invalid }
      "sendpin": { "up": "accept", "right": invalid, "down": invalid, "left": invalid }
    }
  })
  m.cvv = m.top.findNode("cvv")
  m.accept = m.top.findNode("accept")
  m.sendpin = m.top.findNode("sendpin")
  componentsInit()
end sub
sub componentsInit()
  if m.top.debug then print ghLogHead();"componentsInit ***"

  textTitle = m.top.textTitle
  textSubTitle = m.top.textSubTitle
  ' para no tocar pagos
  if m.top.textTitle = "" then
    textTitle = ghTranslate("buypin_label_title", "Protección de pagos")
  end if
  if m.top.textSubTitle = "" then
    textSubTitle = ghTranslate("buypin_label_description", "Tienes activado el PIN de protección de Pagos, ingrésalo para acceder al contenido")
  end if

  'si tengo que ocultar el boton de recuperar pin 
  if m.top.hideSendpin then
    m.sendpin.visible= false
    m.botonera.setFields({
      map: {
        "cvv": { "up": invalid, "right": invalid, "down": "accept", "left": invalid },
        "accept": { "up": "cvv", "right": invalid, "down": invalid, "left": invalid }    
      }
    })
  end if
    


  m.title.setFields({
    font: ghGetFont(40, "regular")
    text: textTitle
    horizAlign: "center"
  })

  m.descrip.setFields({
    font: ghGetFont(28, "regular")
    text: textSubTitle
    horizAlign: "center"
  })

  m.cvv.setFields({
    'placeholder: ghTranslate("buypin_label_placeholder", "CVV")
    title: ghTranslate("buypin_keyboard_title", "Ingresa tu PIN")
    message: ghTranslate("buypin_keyboard_description", "Recuerda que es de 4 a 6 caracteres")
  })
  print m.top.textButton
  print "el valor del button"
  'para poder controlar el valor del texto del boton de la pantalla
  textButton = ghTranslate("buypin_option_button_next", "ACEPTAR")
  if m.top.textButton <> "" then
    textButton = ghTranslate(m.top.textButton, "CONTINUAR")
  end if
  m.accept.setFields({ text: textButton })

  m.sendpin.setFields({ text: ghTranslate("buypin_option_button_cancel", "RECUPERA TU PIN") })
end sub
sub SGDEX_SetTheme() ' theme
  if m.top.debug then print ghLogHead();"SGDEX_SetTheme ***"
end sub
sub OnButtonSelected(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"OnButtonSelected *** data=";data
  if data then
    child = event.getRoSGNode()
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
    if child.value = "SENDPIN" then
      SendPin()
    else
      m.top.value = {
        opcion: "SELECT"
        data: m.cvv.value
      }
      closeScreen()
    end if
  else
    if m.top.debug then print ghLogHead();"OnButtonSelected *** NO ACTION ";data
    ' m.top.routerStatus = true
    ' m.top.routerSetFocus = { page: "ScrPinInput" }
  end if
end sub
' SENDPIN
' -----------------------------
sub SendPin()
  if m.top.debug then print ghLogHead();"SendPin ***"
  m.apiCallRemind = ghCallApi("Remind", "SendPin_Return")
end sub
sub SendPin_Return() ' event
  if m.top.debug then print ghLogHead();"Remind_Return ***"
  ' data = event.getData()
  ScrBuyMessage = CreateObject("roSGNode", "ScrBuyMessageLiteV2")
  ScrBuyMessage.ObserveField("wasClosed", "SendPin_ShowMessage_Return")
  ScrBuyMessage.SetFields({
    title: "",
    message: ghTranslate("buypin_reminder_msg", "Te hemos enviado un correo electrónico con tu PIN de Protección."),
    accept: ghTranslate("buypin_option_button_next", "ACEPTAR"),
    fwd: "outfail",
    back: "outfail"
  })
  m.top.routerChild = { page: ScrBuyMessage }
end sub
sub SendPin_ShowMessage_Return() ' event
  if m.top.debug then print ghLogHead();"ShowMessage_Return ***"
  BackTo()
end sub
sub BackTo() ' event
  if m.top.debug then print ghLogHead();"BackTo ***"
  m.top.value = {
    opcion: "BACK"
    data: ""
  }
  closeScreen()
end sub
sub closeScreen()
  if m.top.debug then print ghLogHead();"closeScreen ***"
  m.top.routerClose = true
end sub
sub updateFieldFocus() ' event
  if m.top.debug then print ghLogHead();"updateFieldFocus ***";m.top.focus
  if m.top.focus then ghFocusJumpTo("botonera")
end sub
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent *** key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
    else
      turnFocusTo("botonera")
      BackTo()
    end if
  end if
  return handled
end function
function guessFocusTo(direction) as string
  if m.top.debug then print ghLogHead();"guessFocusTo *** direction=";direction
  current = getCurrentFocus()
  ' a donde voy?
  if m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
  else
    focusTo = current
  end if
  return focusTo
end function
sub turnFocusTo(id)
  if m.top.debug then print ghLogHead();"turnFocusTo *** id=";id
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus *** current=";current
  return current
end function
