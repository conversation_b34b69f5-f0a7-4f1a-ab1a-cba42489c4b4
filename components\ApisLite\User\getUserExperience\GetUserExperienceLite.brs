' GetUserExperienceLite
' https://app.swaggerhub.com/apis-docs/ClaroVideo/getuserexperience/1.0.0
' -----------------------

sub DataInit()
  ' m.top.debug = true

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/payway/paymentservice/v1/getuserexperience"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })
  m.api.query.Append({
    region: ghGetRegistry("region")
    module_version: "v2"
  })

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' if m.top.debug then
  '   print "****************************************"
  '   print "API GETUSEREXPERIENCE"
  '   print "****************************************"
  '   print ghLogHead();"ProcessData -- Body= ";res
  '   if raw <> invalid then
  '     ' print ghLogHead();"ProcessData -- raw= ";raw
  '     print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  '   else
  '     print "No hay raw"
  '   end if
  '   if res <> invalid then
  '     if res.experience <> invalid then
  '       print ghLogHead();"ProcessData -- experience= ";res.experience
  '     else
  '       print ghLogHead();"ProcessData -- No hay .experience"
  '     end if
  '   end if
  '   print "****************************************"
  '   print "****************************************"
  ' end if

  err = res.errors
  response = res.experience
  if err <> invalid then ' dio un error
    m.logger.error("ProcessData -- ERROR = ", { error: err })
    m.top.error = err
  else
    m.global.user_experience = response
    m.top.content = response
  end if
end sub
