' HOMEpage
'

sub Init()
  ' m.top.debug = true

  LogClear()
  m.logger.debug("Init")
  m.focusId = invalid ' para terminos y condiciones

  m.hayModificacion = false

  ' general de pantalla
  m.map = {
    "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid },
    "error": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }
  m.logo = m.top.findNode("logo")
  m.title = m.top.findNode("title")
  m.botonera = m.top.findNode("botonera")
  m.avatar = m.top.findNode("avatar")
  m.name = m.top.findNode("name")
  m.kid = m.top.findNode("kid")
  m.changeImage = m.top.findNode("changeImage")
  m.cancel = m.top.findNode("cancel")
  m.save = m.top.findNode("save")
  m.delete = m.top.findNode("delete")
  m.loading = m.top.findNode("loading")
  m.error = m.top.findNode("error")
  m.overInput = m.top.findNode("overInput")
  m.overInput.font = ghGetFont(20, "bold")
  m.overInput.text = ghTranslate("updateProfile_access_nameProfile_label", "Nombre de perfil")

  m.toolTip = m.top.findNode("toolTip")
  m.top.checkboxUri = ghGetImageByMode("fondoCheckboxProfiles.png")
  m.top.titleCheckboxText = ghTranslate("kids_tooltip_title_label", "Al activar kids en este perfil:")
  m.top.titleCheckboxFont = ghGetFont(18, "bold")
  m.top.checkboxText1Text = ghTranslate("kids_tooltip_parentalRestriction_label_validation", "•Sólo se podrá ver contenido" + chr(10) + " apto para 12 años o menor.")
  m.top.checkboxText1Font = ghGetFont(18, "regular")
  m.top.checkboxText2Text = ghTranslate("kids_tooltip_additionalRestrictions_label_validation", "•No se podrán hacer transacciones ni " + chr(10) + " acceder a configuraciones y ayuda.")
  m.top.checkboxText2Font = ghGetFont(18, "regular")
  m.toolTip.visible = false
  m.chkError = m.top.findNode("chkError")
  m.chkError.visible = false
  m.mode = "new" ' new / edit ...
  m.pinCallback = { "ok": "handleOkPin", "error": "handlerErrorPin", parameters: {} }
  drawComponents() ' parámetros de los componentes
  controlPinList()
  m.kid.ObserveField("focus", "OnCheckFocus")
  'm.kid.ObserveField("selected", "OnCheckChange")
  refresh()
end sub

sub OnCheckFocus(event)
  prendido = event.getData()
  m.logger.debug("OnCheckFocus visible=", { visible: m.kid.visible, prendido: prendido, checked: m.kid.checked })
  if m.kid.visible then
    if prendido = true and not m.kid.checked then
      m.toolTip.visible = true
      'm.chkError.visible = true
    else
      m.toolTip.visible = false
      'm.chkError.visible = false
    end if
  end if
end sub

sub OnCheckChange(event)
  'controlPinList()
  estado = event.getData()
  print "AAAA ";estado
  m.toolTip.visible = estado
  ' llamamos la pantalla
  ScrOptions = CreateObject("roSGNode", "ScrPinInputV2")
  ScrOptions.id = "ScrPinInput"
  ScrOptions.ObserveField("wasClosed", "BuyPin_Return")
  ScrOptions.SetFields({
    textTitle: ghTranslate("lockPIN_access_subtitleKids_label", "PIN de seguridad"),
    textSubtitle: ghTranslate("lockPIN_editProfile_subtitleKids_label", "Ingresa tu PIN de seguridad para continuar.")
  textPin: ghTranslate("lockChannel_access_option_button_recoveryPIN", "¿OLVIDASTE TU PIN DE SEGURIDAD?") })
  'ScrOptions.SetFields({ data: m.buy })
  m.top.routerChild = { page: ScrOptions }
end sub

sub drawComponents()
  m.logger.debug("drawComponents.")
  ' logo
  m.logo.uri = ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
  ' title
  m.title.setFields({
    font: ghGetFont(38, "bold")
    text: ghTranslate("addProfile_access_title_label", "Agregar perfil")
    width: "1280"
    height: "48"
    translation: "[0,110]"
    vertAlign: "center"
    horizAlign: "center"

  })
  ' --FORMULARIO---------------------
  ' avatar
  m.avatar.setFields({
    translation: [410, 200]
    width: "80"
    height: "80"
    uri: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar01.png"
  })
  ' changeImage
  m.changeImage.setFields({
    value: "changeImage"
    text: ghTranslate("updateProfile_access_option_button_avatarSelector", "CAMBIAR IMAGEN", {})
    translation: [499, 205]
    width: "385"
    height: "72"
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    selBackColor: "#2E303D"
    focusColor: "0xFFFFFF"
    backColor: "#2E303D"
    font: ghGetFont(20, "bold")
  })
  ' name
  m.name.setFields({
    placeholder: ghTranslate("updateProfile_access_nameProfile_label", "Nombre", {})
    translation: [397, 340]
    color: "0xFFFFFF"
    selColor: "#7F8086"
    focusColor: "0xFFFFFF"
    placeholdercolor: "#7F8086"
    password: "false"
    width: "485"
    height: "72"
    title: "Selecciona un nombre"
    titleColor: "0xFFFFFF"
    message: "¿Cuál es tu nombre?"
    messageColor: "0xFFFFFF"
    backColor: "#FFFFFF"
    selBackColor: "#FFFFFF"
  })
  m.name.ObserveField("value", "onTextChange")
  m.kid.setFields({
    value: "kid"
    text: ghTranslate("updateProfile_access_checkKids_label", "Kids", {})
    width: "248"
    height: "72"
    translation: [400, 410]
    backColor: "#212224"
  })
  m.kid.ObserveField("checked", "onCheckedChange")
  ' botonera
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.setFields({
    layout: "map"
    map: {
      "changeImage": { "up": invalid, "right": invalid, "down": "name", "left": invalid },
      "name": { "up": "changeImage", "right": invalid, "down": "kid", "left": invalid },
      "kid": { "up": "name", "right": invalid, "down": "save", "left": invalid },
      "save": { "up": "kid", "right": invalid, "down": "cancel", "left": invalid }
      "cancel": { "up": "save", "right": invalid, "down": "delete", "left": invalid }
      "delete": { "up": "cancel", "right": invalid, "down": invalid, "left": invalid }
    }
  })
  ' cancel
  m.cancel.setFields({
    value: "cancel"
    text: ghTranslate("updateProfile_access_option_button_cancel", "CANCELAR", {})
    translation: [401, 545]
    width: "480"
    height: "72"
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    backcolor: "#2E303D"
    selBackColor: "#2E303D"
    focusColor: "0xFFFFFF"
  })
  ' save
  m.save.setFields({
    value: "save"
    text: ghTranslate("updateProfile_access_option_button_update", "XXXGUARDAR", {})
    translation: [401, 480]
    width: "480"
    height: "72"
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    backcolor: "#4B1512"
    selBackColor: "#981C15"
    focusColor: "0xFFFFFF"
  })
  'delete
  m.delete.setFields({
    value: "delete"
    text: ghTranslate("updateProfile_access_option_button_deleteProfile", "ELIMINAR PERFIL", {})
    translation: [400, 545]
    width: "480"
    height: "72"
    color: "0xFFFFFF"
    selColor: "0xFFFFFF"
    focusColor: "0xFFFFFF"
    backcolor: "#2E303D"
    selBackColor: "#2E303D"
  })
  ' error
  m.error.ObserveField("wasClosed", "BackFromError")
  ' hayModificacion
end sub
sub onProfileEdit(event)
  data = event.getData()
  m.logger.debug("onProfileEdit data=", { data: data })
  refresh()
end sub

sub refresh()
  data = m.top.profile
  m.logger.debug("refresh =", { data: data })
  if data <> invalid then ' -------------
    m.logger.debug("Estoy en EDICION")
    ' titulos
    m.title.text = ghTranslate("updateProfile_access_title_label", "Editar perfil", {})
    ' campos
    m.avatar.uri = data.user_image
    m.name.value = data.username
    m.kid.checked = data.is_kids
    ' botones
    m.save.text = ghTranslate("updateProfile_access_option_button_update", "GUARDAR", {})
    m.save.width = 242
    m.save.translation = [638, 480]
    m.cancel.width = 242
    m.cancel.translation = [401, 480]

    ' boton delete prende apaga.
    print data
    if data.rol <> "admin" then
      m.delete.visible = true
      m.kid.visible = true
      m.botonera.setFields({
        layout: "map"
        map: {
          "changeImage": { "up": invalid, "right": invalid, "down": "name", "left": invalid },
          "name": { "up": "changeImage", "right": invalid, "down": "kid", "left": invalid },
          "kid": { "up": "name", "right": invalid, "down": "save", "left": invalid },
          "save": { "up": "kid", "right": invalid, "down": "delete", "left": "cancel" }
          "cancel": { "up": "kid", "right": "save", "down": "delete", "left": invalid }
          "delete": { "up": "cancel", "right": invalid, "down": invalid, "left": invalid }
        }
      })
    else
      m.delete.visible = false
      m.kid.visible = false
      m.botonera.setFields({
        layout: "map"
        map: {
          "changeImage": { "up": invalid, "right": invalid, "down": "name", "left": invalid },
          "name": { "up": "changeImage", "right": invalid, "down": "save", "left": invalid },
          "save": { "up": "name", "right": invalid, "down": "cancel", "left": "cancel" }
          "cancel": { "up": "name", "right": "save", "down": invalid, "left": invalid }
        }
      })
    end if
    ' modo
    m.mode = "edit"
    'm.botonera.setFields({
    '  layout: "map"
    '  map: {
    '    "changeImage": { "up": invalid, "right": invalid, "down": "name", "left": invalid },
    '    "name": { "up": "changeImage", "right": invalid, "down": "kid", "left": invalid },
    '    "kid": { "up": "name", "right": invalid, "down": "cancel", "left": invalid },
    '    "save": { "up": "kid", "right": invalid, "down": "delete", "left": "cancel" }
    '    "cancel": { "up": "kid", "right": "save", "down": "delete", "left": invalid }
    '    "delete": { "up": "cancel", "right": invalid, "down": invalid, "left": invalid }
    '  }
    '})
  else ' --------------------------------
    m.logger.debug("Estoy en NEW")
    'titulos
    m.title.text = ghTranslate("addProfile_access_title_label", "Agregar perfil", {})
    ' campos * hay que limpiar
    m.avatar.uri = ghGetImageByMode("ic_avatar.png")
    m.name.value = ""
    m.kid.value = false
    ' botones
    m.save.text = ghTranslate("addProfile_access_option_button_save", "GUARDAR", {})
    m.delete.visible = false
    'no solo se saca el delete visible sino que hay que mapear el cancelar sino se pierde el foco
    m.botonera.setFields({
      layout: "map"
      map: {
        "changeImage": { "up": invalid, "right": invalid, "down": "name", "left": invalid },
        "name": { "up": "changeImage", "right": invalid, "down": "kid", "left": invalid },
        "kid": { "up": "name", "right": invalid, "down": "save", "left": invalid },
        "save": { "up": "kid", "right": invalid, "down": "cancel", "left": invalid }
        "cancel": { "up": "save", "right": invalid, "down": invalid, "left": invalid }
        '"delete": { "up": "cancel", "right": invalid, "down": invalid, "left": invalid }
      }
    })
    ' modo
    m.mode = "new"
  end if
  m.logger.debug("refresh to ", { node: m.mode })
  onHayModificacion(false) ' este es el cambio inicial
end sub

' EVENTOS
' ----------------------------
sub onWasShown() ' event
  m.logger.debug("onWasShown.")

  turnFocusTo("botonera")
  ' if m.loading <> true then
  '   if m.focusId <> invalid then
  '     turnFocusTo(m.focusId)
  '     m.focusId = invalid
  '   else
  '     turnFocusTo("botonera")
  '   end if
  ' end if
  m.logger.debug("onWasShown. END")
end sub
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if child.selected then
    child.selected = false ' lo primero es apagarme!
    opcion = child.value
    print "***** la opcion es: "; opcion
    if opcion = "0"
      m.kid.checked = not m.kid.checked

      hasPin = ghGetChild(m.global.status_pin, "has_a_pin", false)
      print hasPin
      print "el valor de el pin es"
      if hasPin = false then 'este es el flujo de un usuario nuevo que configura su pin
        m.eventValue = event
        MessageView("createNewPin", {}, "OnCheckChangeNotPin")
      else
        OnCheckChange(event)
        print "entré acá"
      end if
    else if opcion = "cancel" then
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      BackTo()
    else if opcion = "save" then
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      if m.mode = "new" addProfile() else editProfile()
    else if opcion = "changeImage"
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      selectAvatar()
    else if opcion = "delete" then
      m.logger.debug("OnButtonSelected -- value", { option: opcion })
      deleteProfile()
    end if
  end if
end sub
' BACK
' ----------------------------
sub BackTo() ' event vuelta a la landing
  m.logger.debug("BackTo")
  m.top.focus = false
  m.top.wasClosed = false
  m.top.close = true
end sub
sub BackFromError() ' vuelvo desde error
  turnFocusTo("botonera")
end sub

' ----------------------------
' EDIT
' ----------------------------
sub editProfile()
  m.logger.debug("editProfile.")
  apiEdit = ghCallApi("ProfileUpdateLite", "editProfileOk", "editProfileFail", false)
  apiEdit.setFields({
    "gamification_id": m.top.profile.gamification_id
    "user_id": m.top.profile.partnerUserId
    "firstname": m.name.value
    "user_image": m.avatar.uri
    "is_kids": m.kid.checked
  })
  apiEdit.control = "run"
end sub
sub editProfileOk(event)
  data = event.getData()
  m.logger.debug("editProfileOk [OK] data=", { data: data })
  'm.global.profile_img = m.avatar.uri
  BackTo()
end sub
sub editProfileFail(event)
  data = event.getData()
  print " "
  print "*****************************************"
  print "*****************************************"
  print "ERROR"
  print data
  print "*****************************************"
  print "*****************************************"
  print " "
  errors = ghGetChild(data, "errors", {})
  m.logger.debug("editProfileFail [Error] data=", { data: data })
  MessageView("editProfileFail", errors)

  BackTo()
end sub
' ----------------------------
' CREATE
' ----------------------------
sub addProfile()
  m.logger.debug("addProfile.")
  apiEdit = ghCallApi("ProfileCreateLite", "createProfileOk", "createProfileFail", false)
  apiEdit.setFields({
    "gamification_id": ghGetChild(m.top.profile, "gamification_id")
    "user_id": ghGetChild(m.top.profile, "partnerUserId")
    "firstname": m.name.value
    "user_image": m.avatar.uri
    "is_kids": m.kid.checked
  })
  apiEdit.control = "run"
end sub
sub createProfileOk(event)
  data = event.getData()
  m.logger.debug("editProfileOk [OK] data=", { data: data })
  BackTo()
end sub
sub createProfileFail(event)
  data = event.getData()
  '{
  '  errors: <Component: roArray>
  '  raw: "{"errors":[{"code":"USR_GAM_00009","detail":"Error: 'GROUP_IS_ALREADY_AT_MAX_MEMBERS' ","source":"L2luZXRwdWIvc2VydmljZXMvc3JjL0FtY28vQnVzaW5lc3MvVXNlci9Qcm9maWxlL0hlbHBlci9Vc2VyVmFsaWRhdGVTZXJ2aWNlLnBocCAzOTE="}]}"
  '}
  print " "
  print "*****************************************"
  print "ERROR"
  print data
  print "*****************************************"
  print " "
  errors = ghGetChild(data, "errors", {})
  m.logger.debug("createProfileFail [Error] data=", { data: data })
  MessageView("createProfileFail", errors)
  BackTo()
end sub
' ----------------------------
' DELETE
' ----------------------------
sub deleteProfile()
  m.logger.debug("deleteProfile.")
  m.deleteProfile = CreateObject("RoSGNode", "ProfileDeletePageLite")
  m.deleteProfile.setFields({
    id: "ProfileDeletePageLite"
    profile: gatherCurrentData()
  })
  m.deleteProfile.ObserveField("result", "onDeleteProfileBack")
  m.top.routerChild = { page: m.deleteProfile } ' modo router
end sub
sub onDeleteProfileBack(event)
  data = event.getData()
  m.logger.debug("onDeleteProfileBack [OK] data=", { data: data })
  BackTo()
  '   if data = "deleted" then ' solo si realmente se borro
  '   m.logger.debug("onDeleteProfileBack CIERRO VENTANA")
  '   ' BackTo()
  '   m.top.close = true
  ' end if
end sub
' ----------------------------
' AVATAR
' ----------------------------
sub selectAvatar()
  m.logger.debug("deleteProfile.")
  m.selectAvatar = CreateObject("RoSGNode", "ProfileAvatarSelectPageLite")
  m.selectAvatar.setFields({
    id: "ProfileAvatarSelectPageLite"
    profile: gatherCurrentData()
  })
  m.selectAvatar.ObserveField("result", "onSelectAvatarBack")
  m.top.routerChild = { page: m.selectAvatar } ' modo router
end sub
sub onSelectAvatarBack(event)
  data = event.getData()
  m.logger.debug("onSelectAvatarBack [OK] data=", { data: data })
  if data.cmd = "select" then
    img = ghGetChild(data, "data.user_image")
    if img <> invalid then
      m.avatar.uri = img
      onHayModificacion(true)
    end if
  end if
end sub
' ----------------------------
' PIN
' ----------------------------
sub BuyPin_Return(event)
  scr = event.getRoSGNode()
  print scr.value.opcion
  print "el valor de scr es"; scr.value.opcion

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    ' si viene de un carousel, el canal esta bloqueado y pone cancelar, salgo del live
    m.top.wasClosed = false
    m.kid.checked = not m.kid.checked
    BackTo()
    ' BackFromError()
  else if scr.value.opcion = "SELECT" then
    m.loading = true
    hasPin = ghGetChild(m.global.status_pin, "has_a_pin", false)
    print hasPin
    print "el valor de el pin es"
    '   print m.global.status_pin.has_a_pin
    if hasPin = false then 'este es el flujo de un usuario nuevo que configura su pin
      m.source = scr.value.data
      apiModifyPin = ghCallApi("ModifyControlPin", "modifyResOK", "modifyResError", false)
      apiModifyPin.pin = m.source
      apiModifyPin.isCreatePin = true
      apiModifyPin.control = "run"
      m.loading = false
    else
      apiCheckControlPin = ghCallApi("CheckControlPin", "handleOkPin", "handleErrorPin", false)
      apiCheckControlPin.parametersCallback = ghGetChild(m.pinCallback, "parameters", {})
      apiCheckControlPin.controlPIN = scr.value.data
      apiCheckControlPin.control = "run"
    end if
  end if
end sub
sub handleOkPin(event)
  m.kid.checked = not m.kid.checked
  print "PIN CORRECTO"
end sub
sub handleErrorPin(event)
  MessageView("pinError")
  print "PIN INCORRECTO"
end sub
sub OnCheckChangeNotPin(event)
  estado = event.getData()
  m.toolTip.visible = estado
  ' llamamos la pantalla
  ScrOptions = CreateObject("roSGNode", "ScrPinInputV2")
  ScrOptions.id = "ScrPinInput"
  ScrOptions.ObserveField("backSelected", "BackTo")
  ScrOptions.ObserveField("wasClosed", "BuyPin_Return")
  ScrOptions.SetFields({
    textTitle: ghTranslate("", "PIN de Seguridad"),
    textSubtitle: ghTranslate("profiles_pin_setUp_label", "Elegí un PIN de seguridad de 6 números para continuar.")
  })
  m.top.routerChild = { page: ScrOptions }
end sub
' ----------------------------
' COMUNES
' ----------------------------
function gatherCurrentData()
  data = {
    "gamification_id": ghGetChild(m.top.profile, "gamification_id")
    "user_id": ghGetChild(m.top.profile, "partnerUserId")
    "firstname": m.name.value
    "user_image": ghGetChild(m.top.profile, "user_image")
    "is_kids": m.kid.checked
  }
  m.logger.debug("gatherCurrentData data=", { data: data })
  return data
end function



' DEFAULTS a optimizar
' ----------------------------
function onKeyEvent(key, press)
  m.logger.debug("onKeyEvent ", { key: key, press: press })
  handled = false
  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
      handled = true
      m.logger.debug("onKeyEvent -- focusedChild", { focus: m.top.focusedChild.id })
    else
      m.logger.debug("onKeyEvent -- BACK!")
      BackTo()
    end if
  end if
  return handled
end function

sub modifyResOK(event)
  print "la modify respondio OK"
  apiCheckControlPin = ghCallApi("ControlPinAdd", "pinAddOk", "pinAddError", false)
  apiCheckControlPin.group_id = m.group_id
  apiCheckControlPin.control = "run"
  m.loading = false
  ' cargo la info de canales bloqueados
  controlPinList()
end sub
sub modifyResError(event)
  print "hubo un error de el pin"
  m.loading = false
end sub
sub controlPinList()
  ' actualizo variable global, con canales que tienen pin parental
  ghCallApi("ListPin")
  'modifiqué acá para que pueda ver que hace la api
  'ghCallApi("ModifyControlPin")
end sub

sub MessageView(typeMessage, data = {}, nextFunction = invalid)
  titleMessage = ""
  translationMsg = []
  textMessage = ""
  iconsUri = ""

  if typeMessage = "pinError"
    titleMessage = ghTranslate("****pinErrorTitle", "Error de pin", {})
    textMessage = ghTranslate("****pinErrorMessage", "El pin ingresado es incorrecto")
    iconsUri = "pkg://images/HD/peligro.png"
    translationMsg = [0, -80]
  else if typeMessage = "editProfileFail"
    code = ghGetChild(data, "code")
    detail = ghGetChild(data, "detail")
    print "MECA data ";
    if code = "USR_GAM_00009" and Instr(1, detail, "NO_CHANGE_WAS_MADE_IN_USER") <> 0
      titleMessage = ghTranslate("****editPerfilErrorTitle", "Error", {})
      textMessage = ghTranslate("****editPerfilMessage", "Tienes que hacer por lo menos una modificación en el perfil")
      iconsUri = "pkg://images/HD/peligro.png"
      translationMsg = [0, -80]
    end if
  else if typeMessage = "createProfileFail"
    '{"code":"USR_GAM_00009","detail":"Error: 'GROUP_IS_ALREADY_AT_MAX_MEMBERS' ",
    code = ghGetChild(data, "code")
    detail = ghGetChild(data, "detail")
    if code = "USR_GAM_00009" and detail = "GROUP_IS_ALREADY_AT_MAX_MEMBERS"
      titleMessage = ghTranslate("****createPerfilErrorTitle", "Error", {})
      textMessage = ghTranslate("****pinErrorMessage", "Tienes el máximo de perfiles permitido")
      iconsUri = "pkg://images/HD/peligro.png"
      translationMsg = [0, -80]
    else
      titleMessage = ghTranslate("****createPerfilErrorTitle", "Error", {})
      textMessage = ghTranslate("****pinErrorMessage", "Hubo un error en la creación de perfiles")
      iconsUri = "pkg://images/HD/peligro.png"
      translationMsg = [0, -80]
    end if
  else if typeMessage = "createNewPin"
    '{"code":"USR_GAM_00009","detail":"Error: 'GROUP_IS_ALREADY_AT_MAX_MEMBERS' ",
    titleMessage = ghTranslate("profiles_pin_modal_title", "Elegí un PIN de 6 números", {})
    textMessage = ghTranslate("profiles_pin_modal_label", "Con tu PIN podrás configurar la protección de pago para tus alquileres y suscripciones y las restricciones de contenido por edad para cada perfil. También podrás bloquear y desbloquear los canales de televisión para todos tus perfiles.")
    iconsUri = ""
    translationMsg = [0, -80]
  end if


  ' ver july
  msg = CreateObject("roSGNode", "DynamicMessage")
  logo = CreateObject("roSGNode", "Poster")
  logo.uri = "pkg://images/logo.png"
  logo.translation = [50, 50]
  icons = CreateObject("roSGNode", "Poster")
  'icons.uri = ""'"pkg://images/HD/peligro.png"
  'icon.translation = [500, 10]
  titulo = CreateObject("roSGNode", "Label")
  titulo.text = titleMessage
  titulo.font = ghGetFont(42, "bold")
  msg.SetFields({
    translation: translationMsg
    logo: { object: logo },
    icons: [
      { object: icons },
      { uri: iconsUri },
      { translation: [500, 10] }],
    titles: [
      { object: titulo },
      { text: titleMessage, font: ghGetFont(42, "bold") }],
    messages: [
      { text: textMessage, font: ghGetFont(27, "regular"), height: 160, width: 698, wrap: "true", lineSpacing: "0", vertAlign: "center", horizAlign: "center" },
      '{ text: "un mensaje corto" }
    ],
    buttons: [
      { id: "accept", text: ghTranslate("profiles_pin_modal_button_execute", "ACEPTAR"), backColor: "#981C15", selBackColor: "#981C15", color: "#FFFFFF", selColor: "#FFFFFF", value: "accept" },
      { id: "cancel", text: ghTranslate("profiles_pin_modal_button_cancel", "CANCELAR"), backColor: "#2E303D", selBackColor: "#2E303D", color: "#FFFFFF", selColor: "#FFFFFF", default: true },
      '{ id: "otro", text: "OTRO BOTON" }
    ]
  })
  msg.ObserveField("backSelected", "BackTo")
  if nextFunction = "OnCheckChangeNotPin" then
    msg.ObserveField("wasClosed", "onWasClosed")
  end if
  m.top.routerChild = { page: msg } ' modo router
end sub

sub onWasClosed(event)
  child = event.getRoSGNode()
  result = ghGetChild(child, "routerReturn.DynamicMessageResult")
  print "TEST"
  print result
  if result = "accept" then
    OnCheckChangeNotPin(event)
  end if
end sub

' ----------------------------
' BOTON SAVE
' ----------------------------
sub onHayModificacion(modificacion = invalid)
  if modificacion <> invalid then m.hayModificacion = modificacion
  m.logger.debug("onHayModificacion hayModificacion=", { hayModificacion: m.hayModificacion })
  if m.hayModificacion then
    m.logger.debug("onHayModificacion CON modificacion")
    m.save.SetFields({
      backcolor: "#981C15"
      selBackColor: "#981C15"
    })
  else
    m.logger.debug("onHayModificacion SIN modificacion")
    m.save.SetFields({
      backcolor: "#4B1512"
      selBackColor: "#4B1512"
    })
  end if
end sub
sub onTextChange(event)
  data = event.getData()
  m.logger.debug("onTextChange value=", { data: data })
  onHayModificacion(true)
end sub
sub onCheckedChange(event)
  data = event.getData()
  m.logger.debug("onCheckedChange checked=", { data: data })
  onHayModificacion(true)
end sub


' ' FLUJO de registracion
' ' ----------------------------
' sub FlujoRegistracion_Error(event as object) ' flujo con error
'   res = event.GetData()

'   ghTurnLoading(false, m.loading, m.botonera)
'   m.error.title = res.error_code
'   m.error.descrip = res.error_msg
'   turnFocusTo("error")
' end sub
' sub saliDelApi() ' event volvi del pushSession
'   if m.top.debug then print "*******************************************"
' end sub

' function guessFocusTo(direction) as string
'   current = getCurrentFocus()
'   ' a donde voy?
'   if m.map[current][direction] <> invalid then
'     focusTo = m.map[current][direction]
'   else
'     focusTo = current
'   end if
'   return focusTo
' end function
' sub turnFocusTo(id)
'   current = getCurrentFocus()
'   if current <> id then
'     if current <> invalid then
'       m.top.findNode(current).focus = false ' apago el actual
'     end if
'     if m.top.findNode(id).focus <> invalid then
'       if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
'       m.top.findNode(id).focus = true
'     end if
'   else
'     if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
'   end if
' end sub
' function getCurrentFocus()
'   current = invalid
'   if m.top.focusedChild <> invalid then
'     if m.top.focusedChild.id <> "" then
'       if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
'         current = m.top.focusedChild.id
'       end if
'     end if
'   end if
'   if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
'   return current
' end function

