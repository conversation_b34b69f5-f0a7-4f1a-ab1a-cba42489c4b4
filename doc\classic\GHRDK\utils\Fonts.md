# Fonts.brs

[toc]



## ghGetFont

Devuelve un nodo tipo font con la configuración solicitada.



<u>Definición:</u>

```basic
function ghGetFont(size = 20, mode = "regular") as object
```



<u>Parámetros:</u>

| Nombre | Tipo   | Default | Descripción                             |
| ------ | ------ | ------- | --------------------------------------- |
| size   | int    |         | tamaño de la letra.                     |
| mode   | string | regular | tipo de letra `regular | medium | bold` |

---

## ghGetComponentFont

Obtiene una configuración estandarizada para un determinado componente. Esta función se utiliza dentro de los componentes para poder obtener una configuración estandarizada de los fonts a utilizar. En el caso de cambiar el tipo de font de un componente específico, se puede cambiar dentro de esta función y no en cada uno de los componentes.

<u>Definición:</u>

```basic
function ghGetComponentFont(component) as object
```

<u>Parámetros:</u>

| Nombre    | Tipo   | Default | Descripción                               |
| --------- | ------ | ------- | ----------------------------------------- |
| component | string |         | nombre del componente al que proveer font |

---