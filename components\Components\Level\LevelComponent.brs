' LevelComponent
' -------------------
sub Init()
  m.versionSuffix = ""
  if ghGetChild(m.global, "model", "") = "lite" then
    m.versionSuffix = "Lite"
  end if

  ' m.top.debug = true
  m.top.getScene().updateTheme = m.global.config.theme
  m.typeLevel = invalid ' tipo de componente que estoy mostrando
  ' componentes
  m.grilla = m.top.findNode("grilla")
  m.grilla.ObserveField("contenido", "onCardSelect")
  m.grilla.ObserveField("itemFocused", "onItemFocused")
  m.lista = m.top.findNode("lista")
  m.lista.ObserveField("contenido", "onCardSelect")
  m.top.ObserveField("reloadLevel", "handleReloadLevel")
  m.top.ObserveField("reloadLevelUser", "handleReloadLevelUser")

  ' por default, para el foco directo
  m.top.vista = "grilla"
end sub

sub SGDEX_SetTheme() ' theme
  ' print ghLogHead();"SGDEX_SetTheme"
end sub
sub handleReload()
  ' TODO ver si es necesario antes de la level o levelUser
  if m.top.debug then print ghLogHead();"handleReload -- recargo grilla."
  ghCallApi("LastTouch", "doHandleReload")
end sub
sub onItemFocused(event)
  m.top.itemFocused = event.getData()
end sub

sub handleReloadLevelUser()
  ghCallApi("LastTouch", "reloadUser")
end sub

sub reloadUser()
  vista = getCurrent()
  vista.reload = true
end sub

sub handleReloadLevel()
  m.apiContenido = ghCallApi("Level" + m.versionSuffix, "LevelOK", "LevelERROR", false)
  m.apiContenido.node = m.top.node
  m.apiContenido.control = "run"
end sub

' EVENTS
' -------------------
sub updateNode(event)
  node = event.getData()
  if m.top.debug then print ghLogHead();"updateNode -- new node=[";node;"]"
  if node <> invalid then
    handleReloadLevel()

    GA4Event("screen_view", {
      screen_class: "/level"
      screen_name: node,
    })
  end if
end sub
sub updateFieldFocus(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- data=";data
  if data then
    if m.top.debug then print ghLogHead();"updateFieldFocus -- RESET groupId"
    m.top.contenido = invalid
  end if
  TurnFocusTo(data)
  if m.top.debug then print ghLogHead();"updateFieldFocus -- listo!"
end sub
sub OnCardSelect(event)
  data = event.getData()
  m.top.contenido = data ' soy un pasamanos !!!
end sub
' LEVEL
' -------------------
sub LevelOK(event)
  data = event.getData()
  m.typeLevel = ghGetChild(data, "tipo") ' que tengo que mostrar

  if m.top.debug then
    print ghLogHead();"LevelOK. Tipo [";data.tipo;"]"
    print ghLogHead();data
  end if

  if m.typeLevel = "listadoinfinito" then
    CargarLevelListComponent(data.cintas)
  else ' todo lo demas
    'Align submenu based on initial row type
    if data.cintas <> invalid and data.cintas.count() >= 2 then
      if data.cintas[0].type <> "Highlight" and data.cintas[1].type = "navchilds" then
        temp = data.cintas[0]
        data.cintas[0] = data.cintas[1]
        data.cintas[1] = temp
      end if
    end if
    CargarLevelGridComponent(data.cintas)
  end if
end sub
sub levelERROR()
  print ghLogHead();"levelERROR."
  print ghLogHead();"ERROR => ";ghGetChild(m.apiContenido, "error.error")
  CargarLevelErrorComponent(ghGetChild(m.apiContenido, "error.error"))
end sub
' CARGADORES
' -------------------
sub CargarLevelGridComponent(cintas = invalid)
  if m.top.debug then print ghLogHead();"CargarLevelGridComponent -- GO! ----- ";cintas.count();" cintas."
  if cintas <> invalid then
    m.top.vista = "grilla"
    m.grilla.node = m.top.node
    m.grilla.title = m.top.title
    m.grilla.cintasLevel = cintas
  else
    CargarLevelErrorComponent()
  end if
  if m.top.debug then print ghLogHead();"CargarLevelGridComponent -- END -----"
end sub
sub CargarLevelListComponent(cintas)
  if m.top.debug then print ghLogHead();"CargarLevelListComponent -- GO! ----- ";cintas.count();" cintas."
  if m.top.debug then print ghLogHead();"LevelOK. Hago la MAGIA INFINITA.";cintas
  m.top.vista = "lista"
  m.lista.node = m.top.node
  m.lista.title = m.top.title
  m.lista.cintasLevel = cintas
  if m.top.debug then print ghLogHead();"CargarLevelListComponent -- END -----"
end sub
' Modo ERROR
' -------------------
sub CargarLevelErrorComponent(error = invalid)
  if m.top.debug then print ghLogHead();"CargarLevelErrorComponent -- GO! ----- "
  print
  print
  print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  print "GOOSE PANIC: LevelComponent: FALTA PANTALLA DE ERROR!!!!"
  print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  if error <> invalid then
    print error
  end if
  print
  print
  if m.top.debug then print ghLogHead();"CargarLevelErrorComponent -- END -----"
end sub
' MANEJADOR
' -------------------
sub TurnVista(event)
  data = event.getData()
  cant = m.top.getChildCount()
  if m.top.debug then print ghLogHead();"TurnVista to  ";data;" of ";cant
  for v = 0 to cant - 1
    c = m.top.getChild(v)
    if c.id = data then
      c.visible = true
    else
      c.visible = false
    end if
  end for
end sub
sub TurnFocusTo(data)
  f = getCurrent()
  if f <> invalid then
    f.focus = data
    f.setFocus(data)
  end if
end sub
function getCurrent()
  return m.top.findNode(m.top.vista)
end function