sub Init()
  m.rowConfig = getConfigurationCards()

  m.selectedSeasonsIndex = 0

  m.top.ObserveField("visible", "onVisibleChange")

  m.title = m.top.findNode("title")

  m.seasons = m.top.findNode("seasons")
  m.seasons.focusBitmapUri = ghGetImageByMode("focoCarruselTemporadas.9.png")
  m.seasons.ObserveField("value", "OnTemporadaSelect")
  m.seasons.ObserveField("rowItemSelected", "onRowItemSelected")
  m.seasons.ObserveField("rowItemFocused", "onRowItemFocused")

  m.episodes = m.top.findNode("episodesPlayer")
  m.episodes.ObserveField("rowItemSelected", "onRowItemSelected")
  m.episodes.ObserveField("rowItemFocused", "onRowItemFocused")


  m.map = {
    "seasons": { "up": invalid, "right": invalid, "down": "episodesPlayer", "left": invalid }
    "episodesPlayer": { "up": "seasons", "right": invalid, "down": invalid, "left": invalid }
  }
end sub

sub onRowItemSelected()
  m.top.keypressed = "x"
end sub
sub onRowItemFocused()
  m.top.keypressed = "x"
end sub

sub onVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onVisibleChange << ";data

  if data = true then
    turnFocusTo("seasons")
    turnFocusTo("episodesPlayer")
  else
    m.episodes.setFocus(false)
    m.seasons.setFocus(false)
  end if
end sub

sub OnTemporadaSelect(event)
  data = event.getData()

  info = data.data

  m.selectedSeasonsIndex = info.index

  chargeCarouselNew(m.aSeasons, info.index)
  ' CargarEpisodes(m.aSeasons[info.index].episodes, info.index)
  CargarEpisodes(m.aSeasons[info.index].episodes)

  turnFocusTo("episodesPlayer")
end sub

sub OnEpisodeSelect(event)
  data = event.getData()

  if data <> invalid
    group_id = ghGetChild(data, "data.data.group_id", invalid)
    if group_id = invalid then
      group_id = ghGetChild(data, "data.data.id", invalid)
    end if

    item = ghGetChild(data, "data.data")
    item.content_id = invalid
    item.group_id = group_id

    m.top.selected = item
  end if

end sub

sub onInfoUpdate(event)
  m.dataInfo = event.getData()

  CargarDatos()
end sub

sub onSeasonsUpdate(event)
  m.dataSeason = event.getData()

  CargarDatos()
end sub

sub CargarDatos()
  data = m.dataSeason
  info = m.dataInfo

  if data = invalid or info = invalid then
    return
  end if

  aItems = []

  title = ""
  for each index in data
    item = data[index.Tostr()]
    if item.id <> invalid then
      aItems.push(item)
    end if
  end for

  ' para ordenar
  for i = 0 to aItems.count() - 1
    item = aItems[i]
    if item.number.toStr().len() = 1 then
      aItems[i].order = "0" + aItems[i].number
    else
      aItems[i].order = aItems[i].number
    end if
  end for
  aItems.sortBy("order")

  m.selectedSeasonsIndex = 0

  for i = 0 to aItems.count() - 1
    season = aItems[i]
    if season.number.toStr() = ghGetChild(info, "season").toStr() then
      m.selectedSeasonsIndex = i
    end if
  end for

  ' titulo
  m.title.setFields({
    font: ghGetFont(32, "medium")
    horizAlign: "center"
    text: title
  })

  m.aSeasons = aItems
  chargeCarouselNew(m.aSeasons, m.selectedSeasonsIndex)
  ' CargarEpisodes(m.aSeasons[m.selectedSeasonsIndex].episodes, m.selectedSeasonsIndex)
  CargarEpisodes(m.aSeasons[m.selectedSeasonsIndex].episodes)
end sub

function chargeCarouselNew(data, selected)
  content = CreateObject("roSGNode", "ContentNode")
  row = CreateObject("rosgnode", "ContentNode")

  cant = data.count()
  jumpTo = 0
  for i = 0 to cant - 1
    info = data[i]
    text = ghTranslate("vcard_access_abbreviationSeason_label", "Temporada") + " " + info.number.toStr()

    item = row.CreateChild("ContentNode")
    item.addField("HDItemWidth", "float", false)
    item.addField("selected", "boolean", false)
    item.addField("index", "integer", false)
    item.addField("number", "string", false)
    item.addField("data", "assocarray", false)
    item.title = text
    item.index = i
    item.number = info.number.toStr()
    item.data = info

    if i = selected then
      item.selected = true
      jumpTo = i
    else
      item.selected = false
    end if

  end for

  content.AppendChild(row)

  m.seasons.content = content
  m.seasons.jumpToRowItem = [0, jumpTo]
end function

sub CargarEpisodes(data = []) ' , temporada = "1"
  if m.top.debug then print ghLogHead();"CargarEpisodes *** ---------------------"
  result = CreateObject("roSGNode", "GHContent")
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.id = "Episodios"

  jumpToItem = 0
  cantidad = data.count()
  for i = 0 to cantidad - 1
    item = CreateObject("roSGNode", "GHContent")

    infoCard = ghGetChild(data, "#" + i.toStr())
    infoCard.rowType = "episodesPlayer"
    ghUtils_ForceSetFields(item, {
      data: infoCard
    })
    obCinta.appendChild(item)

    if ghGetChild(m.top.info, "season", "0") = infoCard.season_number.toStr() then
      if ghGetChild(infoCard, "episode_number", "0") = ghGetChild(m.top.info, "episodenumber", "0") then
        jumpToItem = i
      end if
    end if

  end for

  result.appendChild(obCinta)

  chargeCarousel("episodesPlayer", result, jumpToItem)
end sub

sub chargeCarousel(nameCarousel, cintas, jumpToItem = 0)
  if m.top.debug then print ghLogHead();"chargeCarousel *** ---------------------"
  carousel = m.top.findNode(nameCarousel)
  cantItems = cintas.getChild(0).getChildCount() ' para el cambio de foco por pocos items
  if cantItems > 0 then
    ' talentos sin accion por ahora
    carousel.UnobserveField("value")
    if nameCarousel = "episodesPlayer" then
      carousel.ObserveField("value", "OnEpisodeSelect")
    end if

    carousel.itemComponentName = "ItemPolymorphic" ' "ItemComponent"
    carousel.numRows = 1
    carousel.itemSize = [1170, 210]
    carousel.itemSpacing = [0, 0]
    carousel.rowLabelOffset = [0, 15]
    carousel.showRowLabel = [true]
    carousel.focusXOffset = [1]
    carousel.rowFocusAnimationStyle = "fixedFocusWrap"
    carousel.rowCounterRightOffset = 0
    carousel.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")

    rowHeights = []
    rowSpacings = []
    rowItemSize = []
    rowItemSpacing = []
    focusXOffset = [5, 0]
    focusBitmapUris = []
    rowLabelOffset = []
    showRowLabel = false
    showRowCounter = []

    rowConfig = ghGetchild(m.rowConfig, nameCarousel)
    if rowConfig <> invalid then
      rowHeights.push(ghGetChild(rowConfig, "rowHeights"))
      rowSpacings.push(ghGetChild(rowConfig, "rowSpacings"))
      rowItemSize.push(ghGetChild(rowConfig, "rowItemSize"))
      rowItemSpacing.push(ghGetChild(rowConfig, "rowItemSpacing"))
      focusBitmapUris.push(ghGetChild(rowConfig, "focusBitmapUri", ghGetImageByMode("4px_Focus.9.png")))
      focusXOffset.push(ghGetChild(rowConfig, "focusXOffset"))
      rowLabelOffset.push(ghGetChild(rowConfig, "rowLabelOffset"))
      ' showRowLabel.push(ghGetChild(rowConfig, "showRowLabel"))
      showRowCounter.push(ghGetChild(rowConfig, "showRowCounter"))
      rowTitleComponent = ghGetChild(rowConfig, "rowTitleComponentName")
      focusBitmapUri = ghGetChild(rowConfig, "focusBitmapUri")
    else
      rowConfig = m.rowConfig["Carrouselhorizontal"]
    end if

    carousel.setFields({
      rowHeights: rowHeights,
      rowSpacings: rowSpacings,
      rowItemSize: rowItemSize,
      rowItemSpacing: rowItemSpacing,
      focusXOffset: focusXOffset,
      focusBitmapUris: focusBitmapUris,
      rowLabelOffset: rowLabelOffset,
      showRowLabel: showRowLabel,
      rowTitleComponentName: rowTitleComponent,
    })
    if focusBitmapUri <> invalid then
      carousel.focusBitmapUri = focusBitmapUri
    end if

    carousel.content = cintas ' content
    carousel.visible = true

    if jumpToItem <> invalid then
      carousel.jumpToRowItem = [0, jumpToItem]
    end if
  end if
end sub

' manejo de foco
' ---------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then

      foco = guessFocusTo(key)
      turnFocusTo(foco)

      ' para que al bajar de la botonera, se vea el tobon seleccionado
      if key = "down" and foco <> "seasons" then
        m.seasons.jumpToRowItem = [0, m.selectedSeasonsIndex]
      end if

      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";ghGetChild(m.top, "focusedChild.id", "");"]"
    else
      m.top.visible = false
      handled = true
    end if
    m.top.keypressed = key
  end if
  return handled
end function
function guessFocusTo(direction, current = invalid) as string
  if current = invalid then current = getCurrentFocus()
  if m.top.debug then print ghLogHead();"guessFocusTo *** >>> ";current;" -- ";direction
  ' a donde voy?
  if m.map[current] <> invalid and m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
    ' OH! hay cintas en esta pantalla que pueden estar escondidas !!!
    if m.top.findNode(focusTo).visible = false then
      newFocusTo = guessFocusTo(direction, focusTo) ' reentrante para seguir de largo
      if newFocusTo = focusTo then ' si me quedo en el mismo lugar
        focusTo = current ' ese lugar es el actual
      else
        focusTo = newFocusTo ' sino avanzo
      end if
    end if
  else
    focusTo = current
  end if
  if m.top.debug then print ghLogHead();"guessFocusTo *** <<< ";focusTo
  return focusTo
end function
sub turnFocusTo(id)
  if m.top.debug then print ghLogHead();"turnFocusTo *** ---------------------"
  current = getCurrentFocus()
  ' if current <> id then
  if current <> invalid then
    m.top.findNode(current).focus = false ' apago el actual
  end if
  if m.top.findNode(id).focus <> invalid then
    if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id

    item = m.top.findNode(id)
    item.focus = true
    item.setFocus(true)
  end if
end sub
function getCurrentFocus()
  if m.top.debug then print ghLogHead();"getCurrentFocus *** ---------------------"
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function

