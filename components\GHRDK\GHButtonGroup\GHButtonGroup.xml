<?xml version="1.0" encoding="utf-8" ?>
<component name="GHButtonGroup" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHButtonGroup.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />

  <interface>
    <!-- defaults -->
    <field id="exitUp" type="boolean" value="false" />
    <field id="exitDown" type="boolean" value="false" />
    <field id="exitLeft" type="boolean" value="false" />
    <field id="exitRight" type="boolean" value="false" />
    <!-- layouts -->
    <field id="handleKey" type="boolean" value="true" />
    <field id="layout" type="string" value="map" />
    <field id="map" type="assocarray" value="{}" />
    <field id="backSelected" type="boolean" value="false" />
    <field id="backSelEnable" type="boolean" value="true" />

    <field id="orientation" type="string" value="vertical" />
    <field id="value" type="string" value="" alwaysNotify="true" onChange="updateValue" />
    <field id="foco" type="string" value="" alwaysNotify="true" onChange="updateFoco" />
    <field id="valueFocused" type="string" value="" alwaysNotify="true" />

    <!-- para mantener versiones viejas -->
    <!-- nueva version con solo escuchar value es suficiente -->
    <field id="selected" type="boolean" alwaysNotify="true" value="true" />

    <field id="enabled" type="boolean" value="true" alwaysNotify="true" />
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children />

</component>
