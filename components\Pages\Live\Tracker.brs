' ******************************************************************************
' ** Tracking functions
' ******************************************************************************

' CONFIGURACION
' ------------------------
sub trackInit()
  print ghLogHead();"[T]trackInit"
  ' api
  m.apiTrack = CreateObject("roSGNode", "Track")
  ' timer
  m.tmrTrack = CreateObject("roSGNode", "Timer")
  m.tmrTrack.repeat = true
  m.tmrTrack.ObserveField("fire", "trackTriggerTick")
  ' variables internas
  m.trk = {
    urls: {}
    retries: 3, ' TODO -- cuantas veces reintento el mensaje?
    interval: 15, ' cada cuanto reporto el tick?
    has_view: false, ' ya mande el view?
    lastState: "", ' para compara con el estado anterior
    lastTick: 0, ' ultimo tick que mande
    inTick: false, ' ya estoy mandando uno?
    pausePos: 0, ' posision al momento de pausa, para detectar seek
    hasEndCard: true, ' seamos optimistas
    inRolling: false, ' estoy en los rolling credits?
    Exiting: false ' estoy en proceso de salida sincronica?
    ybTitle2: "" ' titulo de programa de Live para Youbora
  }
  trackSetInterval()
  ' ** YOUBORA * trackInit ********************
  ' print ghLogHead("YB");">> trackInit -- "
  ' m.global.YB_Plugin = m.global.YB_Plugin
  ' ** YOUBORA * trackInit ********************

end sub
sub trackInfoChange(trackInfo)
  print ghLogHead();"[T]onTrackInfoChange."
  if trackInfo <> invalid then
    m.trk.urls = ghGetChild(trackInfo, "urls")
    m.trk.retries = ghGetChild(trackInfo, "policies.retries", 3)
    m.trk.interval = ghGetChild(trackInfo, "policies.tick_interval", 60)
    if ghGetChild(trackInfo, "hasEndCard", invalid) = false then
      m.trk.hasEndCard = false
    end if
    trackSetInterval()
  end if
end sub

' PARAMETRIZAR VIDEO
' ------------------------
sub trackSetInterval()
  print ghLogHead();"[T]trackSetInterval -- ", m.trk.interval
  if m.video <> invalid then
    m.video.notificationInterval = 1 ' m.trk.interval
  end if
  m.tmrTrack.duration = m.trk.interval
end sub

' MENSAJERIA
' ------------------------
sub trackTriggerTick() ' TICK
  print ghLogHead();"[T] >> trackTriggerTick -- onGenericTrackOk onGenericTrackError"
  trackSendToApi("tick", "onGenericTrackOk", "onGenericTrackError")
end sub
sub trackSendTick(callBackOk = "", callBackError = "")
  ' print ghLogHead();"[T] >> trackSendTick -- ";m.video.position;" ";m.trk.lastTick;" ";m.trk.inTick
  if Abs(m.video.position - m.trk.lastTick) >= m.trk.interval then
    if not m.trk.inTick then ' no es reentrante
      print ghLogHead();"[T] >> trackSendTick -- ";callBackOk;" ";callBackError
      m.trk.inTick = true
      trackSendToApi("tick", callBackOk, callBackError)
    end if
  else
    ' print ghLogHead();"[T] >> trackSendTick -- NO ";Abs(m.video.position - m.trk.lastTick);" < ";m.trk.interval
  end if
end sub
sub trackSendView(callBackOk = "", callBackError = "", channel = {}) ' START
  print ghLogHead();"[T] >> trackSendView -- ";callBackOk;" ";callBackError
  trackSendToApi("view", callBackOk, callBackError, "async", channel)
  m.trk.has_view = true
  youboraStart()
end sub
sub trackSendPause(callBackOk = "", callBackError = "") ' PAUSE
  print ghLogHead();"[T] >> trackSendPause -- ";callBackOk;" ";callBackError
  trackSendToApi("pause", callBackOk, callBackError)
  m.trk.pausePos = m.video.position
  m.tmrTrack.control = "start"
end sub
sub trackSendResume(callBackOk = "", callBackError = "") ' RESUME
  print ghLogHead();"[T] >> trackSendResume -- ";callBackOk;" ";callBackError
  m.tmrTrack.control = "stop"
  trackSendToApi("resume", callBackOk, callBackError)
  if m.video.position <> m.trk.pausePos then
    print ghLogHead();"[T] >> trackSendResume -- Seek detected ";m.trk.pausePos;" > ";m.video.position
    trackSendSeek("onGenericTrackOk", "onGenericTrackError")
  end if
end sub
sub trackSendStop(callBackOk = "", callBackError = "") ' STOP
  print ghLogHead();"[T] >> trackSendStop -- ";callBackOk;" ";callBackError
  trackSendToApi("stop", callBackOk, callBackError, "sync") ' soy sincronico
  m.trk.has_view = false
  youboraStop()
end sub
sub trackSendCompletion(callBackOk = "", callBackError = "") ' COMPLETION
  print ghLogHead();"[T] >> trackSendCompletion -- ";callBackOk;" ";callBackError
  trackSendToApi("completion", callBackOk, callBackError)
end sub
sub trackQualityChange(callBackOk = "", callBackError = "") ' QUALITYCHANGE
  print ghLogHead();"[T] >> trackQualityChange -- ";callBackOk;" ";callBackError
  trackSendToApi("qualitychange", callBackOk, callBackError)
end sub
sub trackSendSeek(callBackOk = "", callBackError = "") ' SEEK
  print ghLogHead();"[T] >> trackSendSeek -- ";callBackOk;" ";callBackError
  trackSendToApi("seek", callBackOk, callBackError)
end sub
sub trackSendError(callBackOk = "", callBackError = "") ' ERROR
  print ghLogHead();"[T] >> trackSendSeek -- ";callBackOk;" ";callBackError
  trackSendToApi("error", callBackOk, callBackError)
end sub
sub trackSendCredits(callBackOk = "", callBackError = "") ' CREDITS
  print ghLogHead();"[T] >> trackSendCredits -- ";callBackOk;" ";callBackError
  trackSendToApi("credits", callBackOk, callBackError)
end sub
' EPISODECHANGE
' DUBSUBCHANGE

' API
' ------------------------
sub trackSendToApi(message, callBackOk = "", callBackError = "", mode = "async", channel = {})
  if m.top.debug then print ghLogHead();"[T] >> trackSendToApi -- ";message;" ";callBackOk;" ";callBackError
  api = CreateObject("roSGNode", "Track")
  api.mode = mode
  api.message = message
  api.purchase_id = ghGetChild(channel, "purchaseid", "")
  api.offer_id = ghGetChild(channel, "offerid", "")
  url = m.trk.urls[message]
  if url <> invalid then
    api.url = url
    api.timecode = getTimeCode(message)
    api.ObserveField("content", callBackOk)
    api.ObserveField("error", callBackError)
    api.control = "run"
  else
    print ghLogHead();"[T] >> trackSendToApi -- ERROR ";message
  end if
end sub
function getTimeCode(message)
  timecode = ghGetChild(m.video, "position", 0)

  ' siempre el completion
  if message = "completion" then
    if m.top.debug then print ghLogHead();"getTimeCode -- ";message;" - uso duration - ";m.video.duration
    timecode = m.video.duration
  end if
  ' stop mas alla de la endcard
  if message = "stop" then
    actual = m.video.position
    final = m.video.duration
    rolling = ghGetChild(m.video, "content.rollingcreditstime", "").ToInt() 'viene en negativo -- viene en string
    threshold = final + rolling
    if final > 0 and actual >= threshold then
      timecode = final
      if m.top.debug then print ghLogHead();"getTimeCode -- ";message;" - uso duration - ";m.video.duration
    else
      if actual = 0 then ' no tengo el dato, esta buffereando
        actual = ghGetChild(m.top.content, "BOOKMARKPOSITION", 0)
        if m.top.debug then print ghLogHead();"getTimeCode -- actual en 0, tomo el del top -- me quedo ";actual
      end if
      timecode = actual
      if m.top.debug then print ghLogHead();"getTimeCode -- ";message;" - uso position - ";timecode
    end if
  end if
  return timecode
end function

' CALLBACKS
' ------------------------
sub onGenericTrackOk(event)
  data = event.getData()
  print ghLogHead();"[T] << onGenericTrackOk { " + data.trkMessage + " } [";ghGetChild(m.video, "duration", "--");"] ";
  if data.trkMessage = "tick" then
    m.trk.inTick = false
    printBookmark(data)
  else if data.trkMessage = "view" then
    print "sucess=";ghGetChild(data, "response.success")
  else if data.trkMessage = "pause" then
    print "pos=";ghGetChild(data, "response.bookmark.tc_last");
    print " max=";ghGetChild(data, "response.bookmark.tc_max")
  else if data.trkMessage = "resume" then
    printBookmark(data)
  else if data.trkMessage = "stop" then
    printBookmark(data)
  else if data.trkMessage = "qualitychange" then
    printBookmark(data)
  else if data.trkMessage = "completion" then
    printBookmark(data)
  else if data.trkMessage = "seek" then
    printBookmark(data)
  else if data.trkMessage = "credits" then
    printBookmark(data)
  else
    print ghGetChild(data, "response")
  end if
  ' last tick
  tc_last = ghGetChild(data, "response.bookmark.tc_last") ' el ultimo anotado
  if tc_last <> invalid then m.trk.lastTick = tc_last
end sub
sub onGenericTrackError(event)
  data = event.getData()

  if data.trkMessage = "tick" then
    m.trk.inTick = false
  end if

  ' trato de avisar, pero un error de un error???
  if data.trkMessage <> "error" then
    trackSendError("onGenericTrackOk", "onGenericTrackError")
  end if
  ' despues sigo, error de red?
  if data.error_msg <> invalid then
    if data.error_msg = "NETWORK ERROR"
      ' problema de red... ver que hacemos
      ' entiendo que inicialmente para el tracking no hacemos nada
    end if
  else
    ' mostrar algo de info
    print ghLogHead();"[T] << onGenericTrackError data =", data
    if data.trkMessage <> invalid then
      print ghLogHead();"[T] << onGenericTrackError { " + data.trkMessage + " } data ", data
      print ghLogHead();"[T] << onGenericTrackError { " + data.trkMessage + " } status ", ghGetChild(data, "status")
      print ghLogHead();"[T] << onGenericTrackError { " + data.trkMessage + " } entry ", ghGetChild(data, "entry")
      print ghLogHead();"[T] << onGenericTrackError { " + data.trkMessage + " } response ", ghGetChild(data, "response")
    end if
  end if
end sub

' ROLLING CREDITS
' ------------------------
sub checkRollingCredits()
  if m.trk.hasEndCard then
    actual = m.video.position
    final = m.video.duration
    rolling = ghGetChild(m.video, "content.rollingcreditstime", "").ToInt() 'viene en negativo -- viene en string
    threshold = final + rolling
    faltan = threshold - actual

    if faltan < 30 then
      if not m.endcard.prepare then
        m.endcard.prepare = true
      end if
      if faltan > 0 then
        ' if faltan > 0 then
        print ghLogHead();"[T]{RC} >> checkRollingCredits. INCOMMING in ";threshold - actual;" secs. [ ";m.video.content.rollingcreditstime;" >> ";rolling;"]"
      end if
    end if

    ' print ghLogHead();"[T]{RC} >> checkRollingCredits. ";m.trk.inRolling
    if actual >= threshold then
      if not m.trk.inRolling then
        print ghLogHead();"[T]{RC} >> checkRollingCredits. AHORA! ";actual;"-";threshold;" (";rolling;")"
        m.trk.inRolling = true
        ' showEndCard() -- NO LA ENCUENTRA / NO SE USA
        trackSendCredits("onGenericTrackOk", "onGenericTrackError")
      else
        print ghLogHead();"[T]{RC} >> checkRollingCredits. SHOWING. ";actual;"-";final
      end if
    else ' todavia nada
      if m.trk.inRolling then
        ' hideEndCard()  -- NO LA ENCUENTRA / NO SE USA
        m.trk.inRolling = false ' lo pongo en falso por las dudas que haya un seek para atras despues de los credits
      end if
    end if
    ' else
    '   print ghLogHead();"checkRollingCredits -- no rolling."
  end if
end sub
sub onEndCardGoFlight(event)
  data = event.getData()
  print ghLogHead();"onEndCardGoFlight -- Tengo proximo? ";data
  m.trk.hasEndCard = data
end sub

' UTILITIES
' ------------------------
sub printBookmark(data)
  print "pos=";ghGetChild(data, "response.bookmark.tc_last");
  print " max=";ghGetChild(data, "response.bookmark.tc_max");
  print " concurrentStreaming= ";ghGetChild(data, "response.concurrentStreaming.enabled")
end sub
' Busqueda de programas
' --------------------------
function getChannelCurrentEvent(chan)
  current = 0
  curTime = CreateObject("roDateTime").AsSeconds()
  eCant = chan.GetChildCount()
  for eNro = 0 to eCant - 1
    e = chan.getChild(eNro)
    if e <> invalid then
      tIni = e.playstart
      tEnd = e.playstart + e.playduration
      ' print "?? ";curTime;" ";tIni;" ";tEnd;
      if curTime >= tIni and curTime <= tEnd then
        ' print "?? ";chan.getChild(eNro)
        current = e 'e
        return current
      end if
    end if
  end for
  return current
end function

' YOUBORA
' ------------------------
sub youboraStart()
  debug = true
  if m.global.YB_Plugin <> invalid then
    try
      if debug then print ghLogHead("YB");"youboraStart -- "
      youboraSend(_getBasicPayload())
    catch err
      print ghLogHead("YB");"youboraStart -- ERROR > ";err
    end try
  else
    if debug then print ghLogHead("YB");"youboraStart -- DISBLED."
  end if
end sub
sub youboraStop()
  debug = true
  if m.global.YB_Plugin <> invalid then
    try
      if debug then print ghLogHead("YB");"youboraStop -- "
      m.global.YB_Plugin.event = { handler: "stop" }
      m.global.YB_Plugin.taskState = "stop"
    catch err
      print ghLogHead("YB");"youboraStop -- ERROR > ";err
    end try
  else
    if debug then print ghLogHead("YB");"youboraStop -- DISBLED."
  end if
end sub
sub youboraTrack()
  debug = false
  if m.global.YB_Plugin <> invalid then
    try
      if Abs(m.video.position - m.trk.lastTick) >= m.trk.interval then
        if not m.trk.inTick then ' no es reentrante
          m.trk.inTick = true ' no es reentrante
          if m.video.events <> invalid then
            if m.video.channelPosition <> invalid then
              programa = getChannelCurrentEvent(m.video.events.result.getChild(m.video.channelPosition))
              if programa <> invalid then
                title2 = ghGetChild(programa, "item.id", "") + "-" + ghGetChild(programa, "title", "")
                if title2 <> m.trk.ybTitle2 then
                  if debug then print ghLogHead("YB");"youboraTrack -- DISTINTO [";m.video.position;"] [";title2;"] <> [";m.trk.ybTitle2;"]"
                  ' MANDO A YOUBORA !!! ----------
                  youboraStop() ' primero lo paro
                  pLoad = _getBasicPayload()
                  pLoad["content.metadata"]["title2"] = title2
                  youboraSend(pLoad) ' ahora lo vuelvo a arrancar
                  ' ------------------------------
                  m.trk.ybTitle2 = title2 ' para que no repita
                else
                  if debug then print ghLogHead("YB");"youboraTrack -- IGUAL [";m.video.position;"] [";title2;"] == [";m.trk.ybTitle2;"]"
                end if
              end if
            end if
          else
            print ghLogHead("YB");"youboraTrack -- ERROR - No tengo la grilla de canales."
          end if
          ' acomodo para la proxima
          m.trk.lastTick = m.video.position ' de aca en adelante
          m.trk.inTick = false ' habilitada de nuevo.
        end if
      else
        if debug then print ghLogHead("YB");"youboraTrack -- NO ";Abs(m.video.position - m.trk.lastTick);" < ";m.trk.interval
      end if
    catch e
      print ghLogHead("YB");"youboraTrack -- ERROR - paso algo: ";e
    end try
  else
    if debug then print ghLogHead("YB");"youboraTrack -- DISBLED."
  end if
end sub
sub youboraSend(payload, msg = "init")
  debug = true
  if m.global.YB_Plugin <> invalid then
    try
      print ghLogHead("YB");"youboraSend -- "
      m.global.YB_Plugin.videoplayer = m.video
      m.global.YB_Plugin.options = payload
      m.global.YB_Plugin.event = { handler: msg }
      if debug then
        print "**********************************************"
        print ghLogHead("YB");"youboraSend -- YB.options=";m.global.YB_Plugin.options
        print ghLogHead("YB");"youboraSend -- YB.options[content.metadata]=";m.global.YB_Plugin.options["content.metadata"]
        print "**********************************************"
      end if
    catch e
      print ghLogHead("YB");"youboraSend -- ERROR - paso algo: ";e
    end try
  else
    if debug then print ghLogHead("YB");"youboraSend -- DISBLED."
  end if
end sub
function _getBasicPayload()
  debug = false
  try
    groupId = ghGetChild(m.video, "content.groupid", "")
    infoChannel = {}
    if groupId <> "" then
      keys = ghGetChild(m.global, "channels.info", {})
      infoChannel = keys[groupId]
    end if
    userRegion = ghGetRegistry("region")
    accountCode = ghGetChild(m.global, "youbora." + userRegion + ".tracking.accountcode")
    payload = {
      "accountCode": accountCode, 'Change for you accountcode
      ' "user": "Gustavo_Ripoll_DevAccount"
      "username": ghGetRegistry("user_id", "user"), ' FALTA Id Usuario
      "expectAds": false,
      ' inicialmente mx no lo manda.
      ' "content.transactionCode": "transaction_id",
      "content.isLive": true,
      "content.title": ghGetChild(m.video.content, "ybTitle", "***") '  -- viene de la getMedia
      "content.program": ghGetChild(m.video.content, "ybTitleEpisode", "***") '  -- viene de la getMedia
      "content.metadata": {
        "year": ghGetChild(m.video.content, "ybPublishyear", ""), ' TODO: response.group.common.extendedcommon.media.publishyear
        "genre": ghGetChild(m.video.content, "ybGenres", ""),
        "isLive": true,
        "rendition": ghGetChild(m.video.content, "ybRendition", "")
        ' Ver si viene en la getmedia .profile.hd.detail
        ' this.rendition = null;
        ' "price": "???" ' TODO: LO TENEMOS???? tvod vod? -- no se estaria usando
      },
      ' (perfiles-no implementado) PQT --
      ' se obtiene a través de la user/isloggedin -- no viene
      ' response.paywayProfile.paymentMethods[0]
      ' user_category: "HC_VIP"
      "extraparam.1": ghGetRegistry("paymentMethods_user_category", "user"), ' NO LLEGA (!) -- chequear si viene.
      ' Tipo Suscriptor Claro (todas las suscripciones del usuario)
      ' Si es VOD -- tomar de la getRegistry() -- user/isloggedin - pawayProfile.subscriptions.key
      ' Si es Canal -- payway/linealchannels.key
      "extraparam.2": _getPaywayKeys(),
      ' Tipo de abono del contenido de la visualización
      ' Si es Canal -- payway/linealchannels.key
      ' Si es VOD -- payway/purchasebuttoninfo response.playButton.key
      "extraparam.3": ghGetChild(infoChannel, "key", ""),
      ' Tipo de Contenido visualizado
      "extraparam.4": ghGetChild(m.video.content, "tipo_contenido_visualizado", ""),
      "extraparam.5": ghGetRegistry("email", "user"), ' Email del usuario
      ' Si es VOD -- player/getmedia -- response.group.common.extendedcommon.format.name
      ' Si es Canal -- player/getmedia -- response.group.common.extendedcommon.format.name
      "extraparam.6": ghGetChild(infoChannel, "paymentmethod.gateway", ""),
      "extraparam.7": "", ' OK! Id_usuario_paga -- vacio
      "extraparam.8": "", ' OK! Id_usuario_hijo -- vacio
      "extraparam.9": ghGetRegistry("region"), ' Región
      "extraparam.10": "0", ' is_trailer
      "extraparam.11": ghGetDeviceId(), ' OK -- Device ID Se debe enviar el device id que se utiliza para los reportes de BI.
      "extraparam.12": ghGetFirmwareVersion(), ' FIRMWARE -- FW Se debe enviar la versión de FW utilizada en el device. Solo se envía en los devices que tienen FW
      "extraparam.13": ghGetAppVersion(), ' APK Se debe enviar la versión de APK utilizada en el device. Solo se envía en los devices que manejan APK
      "extraparam.14": ghGetAppVersion(), ' APP VERSION Se debe enviar la versión de APP_VERSION utilizada en el device. Solo se envía en los devices que manejan APP_VERSION
      "extraparam.15": ghGetChild(m.global, "epg.version", ""), '  NO LLEGA (!) -- chequear si viene. -- EPG VERSION Se debe enviar la versión de EPG_VERSION utilizada en el device. Solo se envía en los devices que manejan EPG_VERSION epg/version: epg_version
      "extraparam.16": ghGetRegistry("subregion", "user"), ' R17/R18 mandar! SUB-REGION Se debe enviar la subregion que tiene asignado el usuario. Solo se envía en los devices que manejan subregion user/isloggedin: subregion
      "extraparam.17": "", ' -- no se usa
      "extraparam.18": "", ' -- no se usa
      "extraparam.19": "", ' -- no se usa
      "extraparam.20": "" ' -- no se usa
      ' "content.title": "Batman",   ' "content.rendition": "4.2Mbps",
      ' "content.cdn": "AKAMAI",    ' "network.ip": "***********",
      ' "network.isp": "Verizon"    ' "device.code" : "DEVICE_ID"
      ' "content.resource": "http://example.com/batman_movie.m3u8",   ' "content.duration": 4000,
    }
    if debug then
      print ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
      print ghLogHead("YB");">> _getBasicPayload -- ybOptions=";payload
      print ghLogHead("YB");">> _getBasicPayload -- metadata=";payload["content.metadata"]
      print ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
    end if
    return payload
  catch e
    print ghLogHead("YB");"_getBasicPayload -- ERROR - paso algo: ";e
    return {}
  end try
end function
function _getPaywayKeys()
  debug = false
  try
    if debug then print ghLogHead();"_getPaywayKeys --"
    tSubcriptions = ""
    tmpSubscriptions = ghGetChild(m.global, "paywayProfile.subscriptions", [])
    if tmpSubscriptions.Count() > 0 then
      for tmpS = 0 to tmpSubscriptions.Count() - 1
        tSubcriptions += tmpSubscriptions[tmpS].key + "-"
      end for
      if tSubcriptions <> "" then
        tSubcriptions = left(tSubcriptions, Len(tSubcriptions) - 1)
      end if
    end if
    return tSubcriptions
  catch e
    print ghLogHead("YB");"_getBasicPayload -- ERROR - paso algo: ";e
    return ""
  end try
end function
