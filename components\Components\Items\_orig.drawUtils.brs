' ----------------------
' CHAPITAS
' ----------------------
' sub drawChapitas(totX, totY, offsetX = 0, padX = 5, padY = 5, itemXpad = 3, itemYpad = 3)
sub drawChapitas(totX, totY, offsetX = 0, padX = 5, padY = 5, itemYpad = 3)
  m.top.debug = true
  m.thePanels = m.top.findNode("thePanels")
  arrChapitas = []

  ' --------------------------------------------
  ' live o vod

  ' TODO revisar porque explota al estar iniciando la home


  ' print " "
  ' if m.top.debug then print ghLogHead()"drawChapitas -- INIT"
  live_enabled = ghGetChild(m.top.itemContent, "data.live_enabled")
  live_type = ghGetChild(m.top.itemContent, "data.live_type")
  if live_enabled = "1" and live_type = "1" then
    liveOVod = "live"
  else
    liveOVod = "vod"
  end if
  ' proveedor
  proveedor = ghGetChild(m.top.itemContent, "data.proveedor_code", invalid) ' proveedor_name -- doc dice code
  if proveedor <> invalid then
    m.thePanels.setFields({
      width: totX
      height: totY
      padX: padX
      padY: padY
      padItem: itemYpad
      offsetX: offsetX
    })
    if proveedor = "amco" then ' -amco-----------------------------------
      chAmco = getChapitasAmco(liveOVod)
      if chAmco.Count() > 0 then arrChapitas.Append(chAmco)
    else ' -proveedor------------------------------
      chProv = getChapitasProveedor(proveedor, liveOVod)
      if chProv.Count() > 0 then arrChapitas.Append(chProv)
    end if
  end if
  ' if m.top.debug then print ghLogHead()"drawChapitas -- ARMADA"

  ' --------------------------------------------
  ' drawArrChapitas(arrChapitas, totX, totY, padX, padY, itemXpad, itemYpad)
  drawArrChapitas(arrChapitas)
  ' if m.top.debug then print ghLogHead()"drawChapitas -- DIBUJADA"
  m.top.debug = false
end sub




' PROVEEDOR
function getChapitasProveedor(proveedor, liveOVod)
  ' if m.top.debug then print ghLogHead();"getChapitasProveedor -- TIPO: ???"
  ' tipo = getTypeByModeAndExperience()
  tipo = m.global.nav.typeChapitas

  if tipo = invalid then
    return []
  end if

  ' if m.top.debug then print ghLogHead();"getChapitasProveedor -- TIPO:";tipo
  chProvs = []
  contador = 0

  ' -- MODELO ----------------------
  if m.global.model = "lite" then
    ' if m.top.debug then print ghLogHead();"*** MODO LITE"
    proveedores = m.global.providersAds
  else
    ' if m.top.debug then print ghLogHead();"*** MODO CLASSIC"
    proveedores = m.global.providers
  end if
  ' -- MODELO ----------------------

  if proveedor <> invalid then ' me pasaron algo
    ' IVAN SUSC
    path = tipo + "." + proveedor + "." + tipo + "." + liveOVod
    chapitas = ghGetChild(m.global.providers, path, invalid)
    if chapitas <> invalid then ' encontre la key
      ' if m.top.debug then print ghLogHead();"getChapitasProveedor chapitas=";path;" >> ";chapitas.count()
      for each chapita in chapitas
        if chapita <> invalid then
          contador++ ' solo para ids
          ch = {
            id: "chProveedor" + contador.Tostr()
            type: chapita.type
            gravity: chapita.gravity
            data: chapita
            component: invalid
          }
          ch.component = getChapita(ch)
          chProvs.push(ch) ' agregado
        end if
      end for
      ' else
      '   if m.top.debug then print ghLogHead();"getChapitasProveedor -- no hay chapitas";tipo;" path=";path
    end if
  end if
  return chProvs
end function


function getChapitasAmco(liveOVod)
  ' if m.top.debug then print ghLogHead();"getChapitasAmco -- TIPO: ???"
  ' mode = getTypeByModeAndExperience()
  mode = m.global.nav?.typeChapitas
  if mode = invalid then
    return []
  end if
  ' if m.top.debug then print ghLogHead();"getChapitasAmco -- TIPO:";mode

  ' LogClear(5)
  chAmco = []
  contador = 0
  tipo = ghGetChild(m.top.itemContent, "data.format_types") ' est/ppe
  if tipo <> invalid then
    arrTipos = ghSplit(tipo, ",")
    ' -- MODELO ----------------------
    if m.global.model = "lite" then
      ' if m.top.debug then print ghLogHead();"getChapitasAmco -- *** MODO LITE"
      proveedores = m.global.providersAds
    else
      ' if m.top.debug then print ghLogHead();"getChapitasAmco -- *** MODO CLASSIC"
      proveedores = m.global.providers
    end if
    ' -- MODELO ----------------------

    for each tipo in arrTipos
      path = mode + ".default." + tipo + "." + liveOVod
      ' if m.top.debug then print ghLogHead();"getChapitasAmco -- *** ";tipo;" path=";path
      chapitas = ghGetChild(proveedores, path) ' voy a buscar algo a APA >>
      if chapitas <> invalid then
        ' if m.top.debug then print ghLogHead();"getChapitasAmco chapitas < ";chapitas.count()
        for each chapita in chapitas
          if chapita <> invalid then
            contador++
            ch = {
              id: "chAmco" + contador.Tostr()
              type: chapita.type
              gravity: chapita.gravity
              data: chapita
              component: invalid
            }
            ch.component = getChapita(ch)
            chAmco.push(ch) ' agregado
          end if
        end for
        ' else
        '   if m.top.debug then print ghLogHead();"getChapitasAmco -- no hay chapitas";tipo;" path=";path
      end if
    end for
  end if
  return chAmco
end function
' COMPONENTES
function getChapita(props)
  chap = invalid
  if props.type = "image" then 'chapita con imagen
    chap = getImgChapita(props)
  else 'chapita con texto
    chap = getTxtChapita(props)
  end if
  return chap
end function
function getImgChapita(props) ' genera una chapita con imagen
  ch = CreateObject("roSGNode", "Poster")
  ch.setFields({
    id: props.id
    uri: ghGetAsset(ghGetChild(props, "data.url", ""), "")
  })
  return ch
end function
function getTxtChapita(props) ' genera una chapita con texto
  ch = CreateObject("roSGNode", "GHTag")
  ch.id = props.id
  lblCode = ghGetChild(props, "data.text", "")
  ch.setFields({
    font: ghGetFont(15) ' font: ghGetFont(props.textSize) -- no sirve lo que viene
    text: ghTranslate(lblCode, lblCode) ' si no esta, el codigo
    vertAlign: "top" ' ojo! punto de anclaje fijo, la ubico desde arriba
    horizAlign: "left" ' ojo! punto de anclaje fijo, la ubico desde la izquierda
    backMap: "pkg://images/back_chapita.9.png"
    color: ghGetChild(props, "data.textColor", "0xFFFFFF")
    backColor: ghGetChild(props, "data.backgroundColor", "0xFF0000FF")
  })
  return ch
end function
' DIBUJAR
' sub drawArrChapitas(arrChapitas, totX, totY, padX, padY, itemXpad, itemYpad)
sub drawArrChapitas(arrChapitas)
  ' -dibujo-------------------------------------
  if arrChapitas.Count() > 0
    panL = m.thePanels.findNode("leftPanel")
    panR = m.thePanels.findNode("rightPanel")
    ' LIMPIO
    if panL.getChildCount() > 0 then panL.removeChildrenIndex(panL.getChildCount(), 0)
    if panR.getChildCount() > 0 then panR.removeChildrenIndex(panR.getChildCount(), 0)
    ' DIBUJO
    for i = 0 to arrChapitas.Count() - 1 ' por cada chapita
      ch = arrChapitas[i]
      if ch.gravity = "left" then
        panL.appendChild(ch.component)
      else
        panR.appendChild(ch.component)
      end if
    end for
    ' --------------------------------------------
  end if
end sub
' ----------------------
' TITLE
' ----------------------
sub drawTitle(totX, totY, padX = 5, padY = 5)
  proveedor = ghGetChild(m.top.itemContent, "data.proveedor_code", invalid)
  if proveedor <> invalid then ' si tengo proveedor
    provsTitle = m.global.ItemTitle
    if provsTitle <> invalid then ' si tengo la key de apa
      provsReg = provsTitle.LookupCI(ghGetRegistry("region")) ' x region
      if provsReg = invalid then ' si no tengo la region
        provsReg = provsTitle.LookupCI("default") ' region default
      end if
      if provsReg <> invalid then ' si tengo algo
        if Instr(1, provsReg, proveedor) > 0 then ' si el proveedor esta en la lista.
          prog = m.top.findNode("progress") ' Tengo en cuenta la barra de progreso si existe
          if prog <> invalid then ' tengo progress
            if prog.visible then ' esta prendido
              progBR = prog.boundingRect()
              totY = progBR.y - padY ' tengo menos alto
              padX = progBR.x ' me alineo con el progress
            end if
          end if
          m.title.setFields({
            width: totX - (padX * 2)
            height: totY - (padY * 2)
            text: m.top.itemContent.data.title
            font: ghGetFont(16, "bold")
            translation: [padX, padY]
            color: "0xFFFFFF"
            wrap: "true"
            lineSpacing: "0"
            vertAlign: "bottom"
            visible: true
          })
        end if
      end if
    end if
  end if
end sub
' ----------------------
' PROGESS
' ----------------------
sub drawProgress()
  vistime = ghGetChild(m.top.itemContent, "data.vistime", invalid)
  if vistime <> invalid then
    max = ghGetChild(vistime, "duration.seconds", -1)
    curr = ghGetChild(vistime, "last.seconds")
    porc = curr / max * 100
    if m.top.debug then print ghLogHead();"drawProgress -- ";m.top.itemContent.title;" ";curr;" ";max;" ";porc;"%"
    m.progress.width = m.itemPoster.width - 30
    m.progress.translation = [15, m.itemPoster.height - 22]
    m.progress.padding = 4
    m.progress.height = 12
    m.progress.value = porc
    m.progress.visible = true
  else
    m.progress.visible = false
  end if
end sub

' NO SE USA
' sub drawRating()
'   ' rating
'   rating = ghGetChild(m.top.itemContent, "data.rating_code")
'   if rating <> invalid then
'     m.rating.setFields({
'       font: ghGetFont(10)
'       text: rating
'       visible: true
'       vertAlign: "top"
'       horizAlign: "right"
'       color: "0xFFFFFF"
'       backColor: "0xFF0000FF"
'       translation: [m.itemPoster.width - 5, 5]
'     })
'   else
'     m.rating.visible = false
'   end if
' end sub

' function getTypeByModeAndExperience()
'   ' freemium = anonymous
'   ' freemium_registrado = no_susc
'   ' preemium = susc
'   tipo = ghGetChild(m.global, "nav.type")
'   if tipo <> invalid then ' estoy en lite
'     if tipo = "nav_premium" then
'       ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > susc"
'       return "susc"
'     else if tipo = "nav_freemium_logged" then
'       ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > no_susc"
'       return "no_susc"
'     else ' nav_freemium_not_logged
'       ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (lite) ";m.global.nav.type;" > anonymous"
'       return "anonymous"
'     end if
'   else ' estoy en classic
'     ' if m.top.debug then print ghLogHead();"getTypeByModeAndExperience (classic) > susc"
'     return "susc"
'   end if
' end function


