'
' RouteManager
'
sub init()
  ' m.top.debug = true
  m.sid_object = {}
  m.ui_object = {}
  m.vo_object = {}

  'View stack array
  m.ssA = []
  m.ssUI = m.top.FindNode("ViewStack")

  'View stack component
  m.addView = addView
  m.closeAllViews = closeAllViews
  m.closeView = closeView
  ' m.closeToView = closeToView
  m.replaceCurrentView = replaceCurrentView
  m.syncOutProperties = syncOutProperties
  m.saveState = saveState

  m.top.observeField("change", "procedureObjectChange")
  if m.top.debug then print ghLogHead("ROUTER");"Init"
  dumpManager()
end sub

sub syncOutProperties()
  if m.top.debug then print ghLogHead("ROUTER");"syncOutProperties --"
  if m.ssUI <> invalid
    m.top.currentView = m.ssUI.getchild(0)
  else
    m.top.currentView = invalid
  end if
  m.top.ViewCount = m.ssA.Count()
end sub

' >>>   procedureObject  part -----
sub procedureObjectChange(event as object)
  if m.top.debug then print ghLogHead("ROUTER");"procedureObjectChange --"
  'execute all stacked events
  field = event.getField()
  if field = "change"
    maxEventsCount = 15
    while m.top.getChildCount() > 0 and maxEventsCount > 0
      procedureNode = m.top.getChild(0)
      if procedureNode <> invalid
        m.top.removeChildIndex(0)
        procedureObject = procedureNode.procedureObject
        if type(procedureObject) = "roAssociativeArray" and type(m[procedureObject.fn]) = "roFunction" then
          ?"==============================================================================="
          ?"SGDEX: Run Procedure from ViewManager child -> functionName = "procedureObject.fn
          ?"==============================================================================="
          runProcedure(procedureObject)
        end if
      else
        exit while
      end if
      maxEventsCount = maxEventsCount - 1
    end while
  else if field = "procedureObject" and m.top.procedureObject <> invalid
    procedureObject = m.top.procedureObject
    ?"==============================================================================="
    ?"SGDEX: Run Procedure from procedureObject field -> functionName = "procedureObject.fn
    ?"==============================================================================="
    if type(procedureObject) = "roAssociativeArray" and type(m[procedureObject.fn]) = "roFunction" then
      runProcedure(procedureObject)
    end if
  else
    ?"==============================================================================="
    ?"SGDEX: Run Procedure from "field" field -> functionName = "field
    ?"==============================================================================="
    data = event.getData()
    if type(m[field]) = "roFunction" and type(data) = "roAssociativeArray" and data.fp <> invalid then
      runProcedure({
        fn: field
        fp: data.fp
      })
    end if
  end if
end sub
sub runProcedure(procedureObject)
  if m.top.debug then print ghLogHead("ROUTER");"runProcedure --"
  ' run procedure from 0 to 5 argunments
  procedureParams = procedureObject.fp
  if m.top.debug then print ghLogHead("ROUTER");"runProcedure -- SGDEX: View Manager runProcedure "; procedureObject.fn
  if type(procedureParams) = "roArray" then
    if procedureParams.count() = 0 then
      m[procedureObject.fn]()
    else if procedureParams.count() = 1 then
      m[procedureObject.fn](procedureParams[0])
    else if procedureParams.count() = 2 then
      m[procedureObject.fn](procedureParams[0], procedureParams[1])
    else if procedureParams.count() = 3 then
      m[procedureObject.fn](procedureParams[0], procedureParams[1], procedureParams[2])
    else if procedureParams.count() = 4 then
      m[procedureObject.fn](procedureParams[0], procedureParams[1], procedureParams[2], procedureParams[3])
    else if procedureParams.count() = 5 then
      m[procedureObject.fn](procedureParams[0], procedureParams[1], procedureParams[2], procedureParams[3], procedureParams[4])
    end if
  else
    m[procedureObject.fn]()
  end if
end sub
' <<<   procedureObject  part -----

function createViewVO(NodeOrName, ViewInitData)
  if m.top.debug then print ghLogHead("ROUTER");"createViewVO --"

  if lcase(type(NodeOrName)) = "rosgnode" then
    name = NodeOrName.id
  else
    name = NodeOrName
  end if
  previousViewSid = m.ssA.Peek()
  if previousViewSid <> invalid then
    previousViewsid = previousViewsid.sid
  else
    previousViewSid = ""
  end if
  ViewVO = {
    name: name
    init_data: ViewInitData
    current_state: {
      init_data: ViewInitData
      stop_data: invalid
      closed_View_data: invalid

      previousViewSid: previousViewSid
    }
    'View id
    sid: getViewId(NodeOrName)
  }

  m.vo_object[ViewVO.sid] = ViewVO

  return ViewVO
end function
function getViewId(NodeOrName)
  if m.top.debug then print ghLogHead("ROUTER");"getViewId --"

  if lcase(type(NodeOrName)) = "rosgnode" then
    key = NodeOrName.id
  else
    key = NodeOrName
  end if

  value = m.sid_object[key]
  if value <> invalid then
    value = value + 1
  else
    value = 1
  end if

  m.sid_object[key] = value

  return key + "_" + itostr(value)
end function

sub addView(ViewComponentName, ViewInitData)
  if m.top.debug then print ghLogHead("ROUTER");"addView --";ViewComponentName
  nowViewUI = invalid
  'let previous View save it's state before opening new View
  'good for saving focused child
  if m.ssA.Count() > 0 then
    nowViewVO = m.ssA.Peek()
    nowViewUI = m.ui_object[nowViewVO.sid]
    if nowViewUI <> invalid and nowViewUI.hasField("saveState") then
      nowViewUI.saveState = true
    end if
  end if
  ViewVO = createViewVO(ViewComponentName, ViewInitData)

  if lcase(type(ViewComponentName)) = "rosgnode" then
    UIObject = ViewComponentName
  else
    UIObject = createObject("roSGNode", ViewVO.name)
  end if

  m.ui_object[ViewVO.sid] = UIObject
  newView = m.ui_object[ViewVO.sid]
  m.ssUI.appendChild(newView)

  if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- focuschain=";newView.isinFocusChain()
  if not newView.isinFocusChain() and newView.focusedChild = invalid then ' and ViewInitData.setFocus
    if newView.initialFocusedNode <> invalid
      if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- newView.initialFocusedNode.id=";newView.initialFocusedNode.id
      if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- SGDEX: set focus to :"newView.initialFocusedNode.subtype()
      newView.initialFocusedNode.setfocus(true)
    else
      if not newView.isInFocusChain()
        if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- not newView.isInFocusChain()"
        newView.setFocus(true)
        if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- SGDEX: newView.setFocus(true) => ";newView.id
      end if
    end if
  else
    if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- focusedChild";newView.focusedChild
  end if
  if m.top.debug then print ghLogHead("ROUTER");"addView -- FOCUS -- end."

  SetPageInteface(newView)

  if nowViewUI <> invalid
    nowViewUI.visible = false
    m.ssUI.removeChild(nowViewUI)
  end if

  m.ssA.push(ViewVO)
  syncOutProperties()

  if newView.hasField("wasShown") then
    newView.wasShown = true
  end if

  dumpManager("addView")
end sub

sub closeAllViews(sid = invalid, closeData = invalid)
  print "closeAllView routeManager"
  while m.ssA.Count() > 0
    closedViewVO = m.ssA.Pop()

    closedViewUI = m.ui_object[closedViewVO.sid]
    closedViewUI.visible = false

    m.ssUI.removeChild(closedViewUI)
    m.vo_object.delete(closedViewVO.sid)
    m.ui_object.delete(closedViewVO.sid)
    m.sid_object = {}
  end while
end sub

' cierro sin poner focus en true, ni wasShown en true
' revisar
' vi diferencia con closeView cuando se cierra el player
sub closeAll(sid = invalid, closeData = invalid)
  if m.top.debug then print ghLogHead("ROUTER");"closeView --";sid

  if m.ssA.Count() > 0 then
    closedViewVO = m.ssA.Pop()
    if m.ssA.Count() = 0
      if m.top.allowCloseChannelWhenNoViews = true
        scene = m.top.getScene()
        if scene <> invalid and scene.hasField("exitChannel") then
          scene.exitChannel = true
          return
        end if
      end if
    end if
    closedViewUI = m.ui_object[closedViewVO.sid]
    'tell the View that it was closed
    if closedViewUI.hasField("wasClosed") then
      if m.top.debug then print ghLogHead("ROUTER");"Fire wasClosed for this View."
      closedViewUI.wasClosed = true
    end if

    'Re-add previous View
    if m.ssA.Count() > 0 then
      nowViewVO = m.ssA.Peek()
      nowViewVO.current_state.closed_View_data = closeData
      nowViewUI = m.ui_object[nowViewVO.sid]
      if not IsNodeContainsChild(m.ssUI, nowViewUI) ' check if new View doesn't opened in close callback
        if m.top.debug then print ghLogHead("ROUTER");"Showing previous View."
        nowViewUI.visible = true
        m.ssUI.appendChild(nowViewUI)

        ' if nowViewVO.focusedNode <> invalid then
        '   nowViewVO.focusedNode.setFocus(true)
        ' else
        '   if not nowViewUI.isInFocusChain()
        '     nowViewUI.setFocus(true)
        '   end if
        ' end if
        ' if nowViewUI.hasField("wasShown") then
        '   if m.top.debug then print ghLogHead("ROUTER");"Fire wasShown for this View."
        '   nowViewUI.wasShown = true
        ' end if
      end if
    else
      if m.top.debug then print ghLogHead("ROUTER");"Last View was closed."
    end if

    'Delete and clean closed View
    closedViewUI = m.ui_object[closedViewVO.sid]
    closedViewUI.visible = false
    m.ssUI.removeChild(closedViewUI)
    m.ui_object.delete(closedViewVO.sid)
    m.vo_object.delete(closedViewVO.sid)
  end if

  syncOutProperties()
  dumpManager("closeAll")
end sub

sub closeView(sid = invalid, closeData = invalid)
  if m.top.debug then print ghLogHead("ROUTER");"closeView -- sid=";sid
  if m.top.debug then print ghLogHead("ROUTER");"closeView -- allowCloseChannelWhenNoViews=";m.top.allowCloseChannelWhenNoViews

  if m.ssA.Count() > 0 then
    closedViewVO = m.ssA.Pop()
    if m.ssA.Count() = 0
      if m.top.allowCloseChannelWhenNoViews = true
        scene = m.top.getScene()
        if scene <> invalid and scene.hasField("exitChannel") then
          if m.top.debug then print ghLogHead("ROUTER");"closeView -- hay un exitChannel."
          scene.exitChannel = true
          return
        end if
      end if
    end if
    closedViewUI = m.ui_object[closedViewVO.sid]
    'tell the View that it was closed
    if closedViewUI.hasField("wasClosed") then
      if m.top.debug then print ghLogHead("ROUTER");"Fire wasClosed for this View."
      closedViewUI.wasClosed = true
    end if

    'Re-add previous View
    if m.ssA.Count() > 0 then
      nowViewVO = m.ssA.Peek()
      nowViewVO.current_state.closed_View_data = closeData
      nowViewUI = m.ui_object[nowViewVO.sid]
      if not IsNodeContainsChild(m.ssUI, nowViewUI) ' check if new View doesn't opened in close callback
        if m.top.debug then print ghLogHead("ROUTER");"Showing previous View."
        nowViewUI.visible = true
        m.ssUI.appendChild(nowViewUI)

        if nowViewVO.focusedNode <> invalid then
          nowViewVO.focusedNode.setFocus(true)
        else
          if not nowViewUI.isInFocusChain()
            nowViewUI.setFocus(true)
          end if
        end if
        if nowViewUI.hasField("wasShown") then
          if m.top.debug then print ghLogHead("ROUTER");"Fire wasShown for this View."
          nowViewUI.wasShown = true
        end if
      end if
    else
      if m.top.debug then print ghLogHead("ROUTER");"Last View was closed."
    end if

    'Delete and clean closed View
    closedViewUI = m.ui_object[closedViewVO.sid]
    closedViewUI.visible = false
    m.ssUI.removeChild(closedViewUI)
    m.ui_object.delete(closedViewVO.sid)
    m.vo_object.delete(closedViewVO.sid)
  end if

  syncOutProperties()
  dumpManager("closeView")
end sub
sub replaceCurrentView(ViewComponentName, ViewInitData)
  if m.top.debug then print ghLogHead("ROUTER");"replaceCurrentView --";ViewComponentName

  if m.ssA.Count() > 0 then
    closedViewVO = m.ssA.Pop()

    'Add new View
    ViewVO = createViewVO(ViewComponentName, ViewInitData)

    if lcase(type(ViewComponentName)) = "rosgnode" then
      UIObject = ViewComponentName
    else
      UIObject = createObject("roSGNode", ViewVO.name)
    end if

    m.ui_object[ViewVO.sid] = UIObject
    newView = m.ui_object[ViewVO.sid]
    m.ssUI.appendChild(newView)
    SetPageInteface(newView)
    newView.setFocus(true)

    if newView.hasField("wasShown") then
      newView.wasShown = true
    end if

    m.ssA.push(ViewVO)

    'Delete and clean closed View
    closedViewUI = m.ui_object[closedViewVO.sid]
    closedViewUI.visible = false
    m.ssUI.removeChild(closedViewUI)
    m.ui_object.delete(closedViewVO.sid)
    m.vo_object.delete(closedViewVO.sid) ' GOOSE! +
  end if

  syncOutProperties()
  dumpManager("replaceCurrentView")
end sub
sub RemoveThisViewFromStack(View as object)
  if View <> invalid and m.ssA.Count() > 0 then
    closedViewVO = m.ssA.peek()
    closedViewUI = m.ui_object[closedViewVO.sid]

    if closedViewUI.isSameNode(View) then
      closeView("", {})

    end if
  end if
end sub

sub saveState(sid, saveAA)
  if m.top.debug then print ghLogHead("ROUTER");"saveState --";sid
  ViewVO = m.vo_object[sid]
  if ViewVO <> invalid then
    ViewVO.current_state.stop_data = saveAA
  end if
end sub
function IsNodeContainsChild(node, child) as boolean
  if m.top.debug then print ghLogHead("ROUTER");"IsNodeContainsChild --"
  if node <> invalid and child <> invalid
    for i = 0 to node.getchildcount() - 1
      n_child = node.getChild(i)
      if n_child <> invalid and n_child.isSameNode(child) then return true
    end for
  end if
  return false
end function

sub FocusManagement(newView = invalid)
  if m.top.debug then print ghLogHead("ROUTER");"FocusManagement --"
  ' sino viene, la actual
  if newView = invalid then
    if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- No viene, usando la currentView -> ";m.top.currentView.id
    newView = m.top.currentView
  end if
  ' proceso loco para el focus
  if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS A- focuschain=";newView.isinFocusChain()
  if not newView.isinFocusChain() and newView.focusedChild = invalid then ' and ViewInitData.setFocus
    if newView.initialFocusedNode <> invalid
      if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS B- newView.initialFocusedNode.id=";newView.initialFocusedNode.id
      if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS C- SGDEX: set focus to :"newView.initialFocusedNode.subtype()
      newView.initialFocusedNode.setfocus(true)
    else
      if not newView.isInFocusChain()
        if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS D- not newView.isInFocusChain()"
        newView.setFocus(true)
        if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS E- SGDEX: newView.setFocus(true) => ";newView.id
      end if
    end if
  else
    if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS F- focusedChild=";newView.focusedChild.id
    if newView.hasField("focus") then
      newView.focus = true
      if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS G- focusedChild=";newView.focus
    end if
  end if
  if m.top.debug then print ghLogHead("ROUTER");"FocusManagement -- FOCUS H- end."
end sub

' -------------------------------------------------------------------
' PAGES FUNCTIONS
' -------------------------------------------------------------------
sub SetPageInteface(page)

  if not page.hasField("routerInterface") then
    ' if m.top.debug then
    if m.top.debug then print ghLogHead("ROUTER");"SetPageInteface --";page
    ' compatibilidad
    if not page.hasField("close") then page.addField("close", "string", true) ' close -- back compatibility
    page.observeFieldScoped("close", "routerClose_method")
    ' ---------------------
    ' ROUTER METHODS
    ' ---------------------
    ' routerClose method
    if not page.hasField("routerClose") page.addField("routerClose", "string", true)
    page.observeFieldScoped("routerClose", "routerClose_method")
    ' routerChild method
    if not page.hasField("routerChild") page.addField("routerChild", "assocarray", true)
    page.observeFieldScoped("routerChild", "routerChild_method")
    ' routerJump method
    if not page.hasField("routerJump") page.addField("routerJump", "assocarray", true)
    page.observeFieldScoped("routerJump", "routerJump_method")
    ' routerReset method
    if not page.hasField("routerReset") page.addField("routerReset", "assocarray", true)
    page.observeFieldScoped("routerReset", "routerReset_method")
    ' routerReturn method
    if not page.hasField("routerReturn") page.addField("routerReturn", "assocarray", true)
    page.observeFieldScoped("routerReturn", "routerReturn_method")
    ' routerStatus_method
    if not page.hasField("routerStatus") page.addField("routerStatus", "boolean", true)
    page.observeFieldScoped("routerStatus", "routerStatus_method")
    ' routerSetFocus_method
    if not page.hasField("routerSetFocus") page.addField("routerSetFocus", "assocarray", true)
    page.observeFieldScoped("routerSetFocus", "routerSetFocus_method")
    ' routerAdNavigationTo_method
    if not page.hasField("routerAddRouterTo") page.addField("routerAddRouterTo", "node", true)
    page.observeFieldScoped("routerAddRouterTo", "routerAddRouterTo_method")

    if not page.hasField("closeAll") page.addField("closeAll", "boolean", false)
    page.observeFieldScoped("closeAll", "closeAllViews")

    ' ---------------------
    ' LO SIENTO
    ' ---------------------
    if not page.hasField("routerInterface") page.addField("routerInterface", "boolean", true)
    page.routerInterface = true
  end if
end sub
' -------------------------------------------------------------------
sub routerChild_method(event)
  if m.top.debug then print
  if m.top.debug then print "((((((((((((((((((((((((((("
  if m.top.debug then print "routerChild_method"
  if m.top.debug then print ")))))))))))))))))))))))))))"
  node = event.getRoSGNode()
  child = event.getData()
  if child <> invalid then
    pageName = ghGetChild(child, "page")
    pageId = ghGetChild(child, "page.id")
    props = ghGetChild(child, "fields", {})

    ' para que no abra la misma pagina dos veces,
    ' por ejemplo lanzar dos errores al mismo tiempo en live
    if m.top.currentView.id <> pageId then
      if m.top.debug then print ghLogHead();"[";node.id;"] routerChild_method [";pageName;"] with props=";props

      pagina = router__buildPage(pageName, props)

      if pagina <> invalid then
        addView(pagina, invalid)
        if pagina.hasField("focus") then pagina.focus = true
      else
        if m.top.debug then print ghLogHead();"Page_show [";pageName;"] NOT FOUND! "
      end if
    end if
  else
    if m.top.debug then print ghLogHead();"[";node.id;"] routerChild_method -- doChild is invalid"
  end if
end sub
sub routerJump_method(event)
  if m.top.debug then print
  if m.top.debug then print "((((((((((((((((((((((((((("
  if m.top.debug then print "routerJump_method"
  if m.top.debug then print ")))))))))))))))))))))))))))"
  node = event.getRoSGNode()
  child = event.getData()
  if child <> invalid then
    pageName = ghGetChild(child, "page")
    props = ghGetChild(child, "fields", {})
    if m.top.debug then print ghLogHead();"[";node.id;"] routerChild_method [";pageName;"] with props=";props
    pagina = router__buildPage(pageName, props)
    if pagina <> invalid then
      replaceCurrentView(pagina, invalid)
      if pagina.hasField("focus") then pagina.focus = true
    else
      if m.top.debug then print ghLogHead();"Page_show [";pageName;"] NOT FOUND! "
    end if
  else
    if m.top.debug then print ghLogHead();"[";node.id;"] routerChild_method -- doChild is invalid"
  end if
end sub
sub routerReset_method(event)
  if m.top.debug then print
  if m.top.debug then print "((((((((((((((((((((((((((("
  if m.top.debug then print "routerReset_method"
  if m.top.debug then print ")))))))))))))))))))))))))))"
  ' clearStack()
  for v = 1 to m.ssA.Count() - 1
    if m.top.debug then print ">>>>>>>>>>> BORRANDO ";v, m.ssA[v].sid
    ' para no poner focus o wasShown en true
    ' revisar
    closeAll("", {})
  end for
  node = event.getRoSGNode()
  child = event.getData()
  if child <> invalid then
    pageName = ghGetChild(child, "page", invalid)
    props = ghGetChild(child, "fields", {})
    if m.top.debug then print ghLogHead();"[";node.id;"] routerReset_method - creating [";pageName;"] with props=";props
    if pageName <> invalid then ' si pido una pagina
      pagina = router__buildPage(pageName, props)
      if pagina <> invalid then
        replaceCurrentView(pagina, invalid)
        if pagina.hasField("focus") then pagina.focus = true
      else
        if m.top.debug then print ghLogHead();"Page_show [";pageName;"] NOT FOUND! "
      end if
    end if
  else
    if m.top.debug then print ghLogHead();"[";node.id;"] routerChild_method -- doChild is invalid"
  end if
end sub
sub routerClose_method(event)
  if m.top.debug then print
  if m.top.debug then print "((((((((((((((((((((((((((("
  if m.top.debug then print "routerClose_method"
  if m.top.debug then print ")))))))))))))))))))))))))))"
  RemoveThisViewFromStack(event.getROSGNode())
end sub
sub routerReturn_method(event)
  ' RemoveThisViewFromStack()
  if m.top.debug then print
  if m.top.debug then print "((((((((((((((((((((((((((("
  if m.top.debug then print "routerReturn_method"
  if m.top.debug then print "***** current=";m.top.currentView.id
  RemoveThisViewFromStack(event.getROSGNode())
  if m.top.debug then print "***** current=";m.top.currentView.id
  actual = router__getCurrentPage()
  if actual <> invalid then
    if m.top.debug then print "***** actual=";actual.id
    print "((((((((((((((((((((((((((("
    print event.getData()
    print "((((((((((((((((((((((((((("
    actual.setFields(event.getData())
  end if
  if m.top.debug then print ")))))))))))))))))))))))))))"
end sub
sub routerStatus_method() ' event
  print
  print "((((((((((((((((((((((((((("
  antes = m.top.debug
  m.top.debug = true
  dumpManager("STATUS -- routerStatus_method", true)
  m.top.debug = antes
  print "((((((((((((((((((((((((((("
  print
end sub
sub routerSetFocus_method(event)
  child = event.getData()
  pageName = "" ' el default
  if child <> invalid then ' me mandan el nombre
    pageName = ghGetChild(child, "page", "")
    if lcase(type(pageName)) = "rosgnode" then ' si me manda la pagina
      pageName = pageName.id
    end if
  end if
  if pageName = "" then ' no me pasaron nombre
    page = router__getCurrentPage()
    pageName = page.id
  else
    aBuscar = pageName + "_" + itostr(m.sid_object[pageName])
    page = m.ui_object[aBuscar]
  end if
  if page <> invalid then ' si hay pagina
    if m.top.debug then print ghLogHead("ROUTER");"routerFocusRevive_method -- id=";pageName
    if page.hasField("focus") then page.focus = true
    page.setFocus(true)
  else
    if m.top.debug then print ghLogHead("ROUTER");"routerFocusRevive_method -- NO PAGE ";pageName;"!"
  end if
end sub
sub routerAddRouterTo_method(event)
  navigator = event.getData()
  SetComponentInterface(navigator)
end sub
' -------------------------------------------------------------------
function router__buildPage(page, props)

  if lcase(type(page)) <> "rosgnode" then
    pagina = CreateObject("roSGNode", page)
    pagina.id = page
  else
    pagina = page
  end if

  if pagina = invalid then
    if m.top.debug then print ghLogHead();"router__buildPage [";page;"] NOT FOUND! "
    return invalid
  else
    pagina.setFields(props)

    SetPageInteface(pagina)

    pagina.ObserveField("wasShown", "Page_wasShown")
    pagina.ObserveField("wasClosed", "Page_wasClosed")
    ' if m.top.debug then print ghLogHead();"router__buildPage [";pageName;"] OK! "
    return pagina
  end if
end function
function router__getCurrentPage()
  if m.top.currentView <> invalid then
    if m.top.debug then print "router__getCurrentPage current=";m.top.currentView.id
    current = m.top.currentView
  else
    if m.top.debug then print "router__getCurrentPage current=NO TENGO"
    current = invalid
  end if
  return current
end function
' -------------------------------------------------------------------

' -------------------------------------------------------------------
' COMPONENT FUNCTIONS
' -------------------------------------------------------------------
sub SetComponentInterface(component)
  if not component.hasField("routerInterface") then
    ' ---------------------
    ' ROUTER METHODS
    ' ---------------------
    ' routerClose method
    if not component.hasField("routerClose") component.addField("routerClose", "string", true)
    component.observeFieldScoped("routerClose", "routerClose_method")
    ' routerChild method
    if not component.hasField("routerChild") component.addField("routerChild", "assocarray", true)
    component.observeFieldScoped("routerChild", "routerChild_method")
    ' routerJump method
    if not component.hasField("routerJump") component.addField("routerJump", "assocarray", true)
    component.observeFieldScoped("routerJump", "routerJump_method")
    ' routerReset method
    if not component.hasField("routerReset") component.addField("routerReset", "assocarray", true)
    component.observeFieldScoped("routerReset", "routerReset_method")
    ' routerReturn method
    if not component.hasField("routerReturn") component.addField("routerReturn", "assocarray", true)
    component.observeFieldScoped("routerReturn", "routerReturn_method")
    ' routerStatus_method
    if not component.hasField("routerStatus") component.addField("routerStatus", "boolean", true)
    component.observeFieldScoped("routerStatus", "routerStatus_method")
    ' routerSetFocus_method
    if not component.hasField("routerSetFocus") component.addField("routerSetFocus", "assocarray", true)
    component.observeFieldScoped("routerSetFocus", "routerSetFocus_method")
    ' routerAdNavigationTo_method
    if not component.hasField("routerAdNavigationTo") component.addField("routerAdNavigationTo", "node", true)
    component.observeFieldScoped("routerAdNavigationTo", "routerAdNavigationTo_method")
    ' LO SIENTO
    if not component.hasField("routerInterface") component.addField("routerInterface", "boolean", true)
    component.routerInterface = true
  end if
end sub

'========================
' Helper functions
'========================
'itostr
function itostr(i as integer) as string
  str = Stri(i)
  return strTrim(str)
end function
'Trim a string
function strTrim(str as string) as string
  st = CreateObject("roString")
  st.SetString(str)
  return st.Trim()
end function

' ---------------------------------------------
' HELP
' ---------------------------------------------
sub dumpManager(corrida = "", full = false)
  if m.top.debug then
    print "+========================================="
    print "| STACK >> ";corrida
    print "+-----------------------------------------"
    print "| niveles=";m.top.ViewCount
    if m.top.ViewCount > 0 then
      print "| current=";m.top.currentView.id
    end if
    print "+-----------------------------------------"
    if m.sid_object.Count() > 0 then
      print "| sid_object"
      print "| *********"
      for each item in m.sid_object
        print "| ";item;" [";m.sid_object[item];"]"
      end for
      print "+-----------------------------------------"
    end if
    if m.ui_object.Count() > 0 then
      print "| ui_object"
      print "| *********"
      for each item in m.ui_object
        print "| ";item;": ";type(m.ui_object[item]);": ";m.ui_object[item].id
      end for
      print "+-----------------------------------------"
    end if
    if full then
      if m.vo_object.Count() > 0 then
        print "| vo_object"
        print "| *********"
        for each item in m.vo_object
          print "| ";item;": ";m.vo_object[item]
        end for
        print "+-----------------------------------------"
      end if
      print "+========================================="
      if m.ssA.Count() > 0 then
        print "| m.ssA"
        print "| *****"
        for each item in m.ssA
          print "| ";item
        end for
        print "+-----------------------------------------"
      end if
      if m.ssUI.GetChildCount() > 0 then
        print "| m.ssUI"
        print "| ******"
        for item = 0 to m.ssUI.GetChildCount() - 1
          print "| ";item;": ";m.ssUI.getChild(item)
        end for
        print "+-----------------------------------------"
      end if
    end if
    print "+========================================="
    router__getCurrentPage()
  end if
end sub
' ---------------------------------------------
