sub Init()
  ' m.top.debug = true

  ' navegacion general de pantalla
  m.map = {
    "theGrid": { "up": invalid, "right": invalid, "down": "listo", "left": invalid }
    "listo": { "up": "theGrid", "right": invalid, "down": invalid, "left": invalid }
  }

  m.opcion = invalid

  m.spinner = m.top.findNode("spinner")
  m.logo = m.top.findNode("logo")
  m.title = m.top.findNode("title")
  m.listo = m.top.findNode("listo")
  m.theGrid = m.top.findNode("theGrid")

  componentInit()
  dataInit()
end sub

sub loading(visible as boolean)
  m.spinner.visible = visible
end sub

sub componentInit()
  m.logo.uri = ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")

  m.title.font = ghGetFont(32, "bold")
  m.title.text = ghTranslate("listProfiles_access_option_button_manageProfiles2", "Administrar Perfiles")

  m.listo.text = ghTranslate("manageProfiles_access_option_button_ok", "LISTO")
  m.listo.font = ghGetFont(20, "bold")
  m.listo.ObserveField("selected", "closeScreen")

  m.theGrid.itemComponentName = "ItemPolymorphic"
  m.theGrid.itemSize = [1280, 210]
  m.theGrid.itemSpacing = [0, 0]
  m.theGrid.rowLabelOffset = [50, 15]
  m.theGrid.showRowLabel = [true]
  m.theGrid.rowFocusAnimationStyle = "floatingFocus"
  m.theGrid.rowCounterRightOffset = 0
  m.theGrid.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  m.theGrid.ObserveField("value", "OnCardSelect")
end sub

sub dataInit()
  loading(true)
  m.logger.debug("dataInit.")
  ghCallApi("ProfileReadLite", "profileReadOK", "profileReadFail")
end sub

sub profileReadOK(event)
  data = event.getData()
  m.logger.debug("profileReadOK.", { data: data })
  m.top.data = data.data
end sub

sub profileReadFail(event)
  data = event.getData()
  m.logger.debug("profileReadFail.", { data: data })
  'onCancel()
end sub

sub refreshData() ' event
  m.logger.debug("refreshData.")
  data = m.top.data

  members = ghGetChild(data, "members", [])

  ' TEST
  ' for v = 1 to 6
  '   members.push({
  '     username: "Username"
  '     user_hash: "userHash=999"
  '     user_image: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar04.png?1574450476"
  '   })
  ' end for

  if members.Count() < 5
    new = {
      id: "new"
      username: ghTranslate("listProfiles_access_addProfile_label", "Agregar Perfil")
      user_image: ghGetImageByMode("addProfile.png")
      user_hash: ""
    }
    members.push(new)
  end if

  perfiles = GridContent(members)

  ' agrego cintas
  result = CreateObject("roSGNode", "GHContent")
  result.appendChild(perfiles.cinta)

  refreshTypesGrid(result, m.theGrid)

  m.theGrid.numRows = 1
  m.theGrid.content = result
  m.theGrid.jumpToRowItem = [0, perfiles.selected]

  itemCount = perfiles.cinta.getChildCount()
  m.theGrid.translation = [calculateLeft(itemCount), 200]

  loading(false)

  turnFocusTo("theGrid") ' cambiar el foco
end sub

function calculateLeft(itemCount as integer) as integer
  screenWidth = 1280
  itemWidth = 210
  visibleItems = Int(screenWidth / itemWidth)

  if itemCount >= visibleItems
    return 0
  else
    totalItemWidth = itemCount * itemWidth
    return Int((screenWidth - totalItemWidth) / 2) - 30 ' 30 para centrar mejor ?
  end if
end function

sub onWasShown()
  if m.top.debug then print ghLogHead();"onWasShown."
  turnFocusTo("theGrid")
end sub

sub updateFieldFocus()
  m.logger.debug("updateFieldFocus", { focus: m.top.focus, cant: m.top.cantidad })
  turnFocusTo("theGrid")
end sub

function onKeyEvent(key, press) as boolean
  m.logger.debug("onKeyEvent", { key: key, press: press })

  handled = true ' siempre manejo

  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
    else
      closeScreen()
    end if
  end if

  return handled
end function

sub closeScreen(value = invalid)
  if value <> invalid then m.top.value = value
  m.top.focus = false
  m.top.wasClosed = true
  m.top.close = true
end sub

sub OnCardSelect(event)
  m.opcion = ghGetChild(event.getData(), "data.data", {})
  m.logger.debug("OnCardSelect data=", { option: m.opcion })
  if m.opcion.id = "new" then
    m.logger.debug("TO BE DEVELOPED || OnCardSelect -- es el new", { option: m.opcion })
    addProfile()
  else
    m.logger.debug("TO BE DEVELOPED || OnCardSelect -- es el edit", { option: m.opcion })
    editProfile(m.opcion)
  end if
end sub

sub editProfile(opcion)
  m.logger.debug("editProfile >>", { option: opcion })
  m.ProfManage = CreateObject("RoSGNode", "ProfileEditPageLite")
  m.ProfManage.setFields({
    id: "ProfileEditPageLite"
    profile: opcion
  })
  m.ProfManage.ObserveField("wasClosed", "EditProfileBack")
  m.top.routerChild = { page: m.ProfManage } ' modo router
end sub

sub addProfile()
  m.logger.debug("addProfile >> NEW")
  m.ProfManage = CreateObject("RoSGNode", "ProfileEditPageLite")
  m.ProfManage.id = "ProfileEditPageLite"
  m.ProfManage.ObserveField("wasClosed", "EditProfileBack")
  m.top.routerChild = { page: m.ProfManage } ' modo router
end sub

sub EditProfileBack(event)
  data = event.getData()
  m.logger.debug("EditProfileBack >>", { data: data })
  dataInit()
end sub

function GridContent(members)
  m.logger.debug("GridContent, cantdidad de members=", { membersCount: members.Count() })

  ' detecto en cual me tengo que parar.
  current = ghGetChild(m.global, "profiles_current")

  m.logger.debug("GridContent current=", { current: current })

  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.type = "ProfileManagementLite"
  obCinta.visible = true

  selected = 0
  for v = 0 to members.Count() - 1
    op = members[v]
    op.rowType = "ProfileManagementLite"
    op.cantTotal = members.Count()

    m.logger.debug("item Perfil =", { username: op.username })

    if current <> invalid then
      if op.gamification_id = current.gamification_id then
        selected = v
      end if
    end if

    it = CreateObject("roSGNode", "GHContent")
    it.id = v
    it.data = op
    obCinta.appendChild(it)
  end for

  return { cinta: obCinta, selected: selected }
end function