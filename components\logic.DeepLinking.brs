sub Show(args as object)
  arguments = FormatJson(args)

  m.logger.debug("DeepLink Show", { arguments: arguments })

  ghSetRegistry("arguments", arguments, "DL")
end sub

sub Input(args as object)
  arguments = getDLArguments(m.top.input_args)

  m.logger.debug("DeepLink Input", { arguments: arguments })

  if arguments <> invalid then
    ' TODO probar con live abierto
    ' si hay un video abierto, lo cierro para que no quede reproduciondo en background
    oldVideo = m.top.findNode("ScreenPlayer")
    if oldVideo <> invalid then
      oldVideo.cerrarPlayer = true
    end if

    if ghGetRegistry("isLoggedIn") <> "true" and m.versionSuffix = "" then
      ' solo pasa en version classic
      ghSetRegistry("arguments", FormatJson(args), "DL")
      pagina = CreateObject("roSGNode", "LoginPage")
      pagina.id = "LoginPage"
      m.top.ComponentController.callFunc("show", { view: pagina })
    else
      if isSupportedMediaType(ghGetChild(args, "mediaType", "")) then
        ghDeleteRegistry("arguments", "DL")
        handleDLInput(args)
      end if
    end if

  end if
end sub

sub handleDLInput(arguments)
  m.logger.debug("DeepLink handleDLInput", { arguments: arguments })

  item = getArgumentAsNode(arguments)

  currentView = m.top.ComponentController.ViewManager.currentView
  currentView.routerReset = {
    page: "HomePage" + m.versionSuffix,
    fields: {
      "deeplink": item,
      nodo: ""
    }
  }
end sub

function getDLArguments(args) as object ' checks for specific DL arguments
  if args <> invalid then
    if args.DoesExist("mediaType") and args.DoesExist("contentId")
      if not args.mediaType = "" and not args.contentId = ""
        ' para retornar todos los parametros
        result = args
        result.contentId = args.contentID
        result.mediaType = args.mediaType
        return result
      end if
    end if
  end if
  return invalid
end function

function isSupportedMediaType(mediaType) as boolean ' returns AA with supported media types
  m.logger.debug("isSupportedMediaType", { mediaType: mediaType })

  supported = {
    "series": "series",
    "season": "season",
    "episode": "episode",
    "movie": "movie",
    "live": "live",
    "tvSpecial": "tvSpecial",
    "shortFormVideo": "shortFormVideo"
  }
  return supported[mediaType] <> invalid
end function

function isPlayableMediaType(mediaType) as boolean ' returns AA with supported media types
  supported = {
    "series": "series",
    "season": invalid,
    "episode": "episode",
    "movie": "movie",
    "live": "live",
    "tvSpecial": "tvSpecial",
    "shortFormVideo": "shortFormVideo"
  }
  return supported[mediaType] <> invalid
end function

function getArgumentAsNode(arguments) ' returns de arguments as a GHContent
  m.logger.debug("getArgumentAsNode", { arguments: arguments })
  'contenttype debe ser un numero
  'Content Type
  ' - audio	5
  ' - episode	4
  ' - movie	1
  ' - season	3
  ' - series	2

  contentType = 1
  if arguments.mediaType = "series" then
    contentType = 2
  else if arguments.mediaType = "season" then
    contentType = 3
  else if arguments.mediaType = "episode" then
    contentType = 4
  else if arguments.mediaType = "audio" then
    contentType = 5
  else if arguments.mediaType = "live" or arguments.mediaType = "tvSpecial" then
    contentType = 6
  else if arguments.mediaType = "shortFormVideo" then
    contentType = 7
  end if

  ' retorno todos los argumentos
  item = arguments

  item.contentType = contentType
  item.id = arguments.contentId
  item.mediaType = arguments.mediaType
  item.idPlayable = isPlayableMediaType(arguments.mediaType)
  item.deepLinking = true

  return item
end function