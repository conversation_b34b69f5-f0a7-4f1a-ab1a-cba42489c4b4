<?xml version="1.0" encoding="utf-8" ?>
<component name="GHMenu" extends="LayoutGroup">

  <script type="text/brightscript" uri="GHMenu.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- defaults -->
    <field id="backSelected" type="boolean" value="false" />

    <field id="exitUp" type="boolean" value="false" />
    <field id="exitDown" type="boolean" value="false" />
    <field id="exitLeft" type="boolean" value="false" />
    <field id="exitRight" type="boolean" value="false" />
    <!-- layouts -->
    <field id="orientation" type="string" value="horizontal" />

    <!-- solo parametro de salida -->
    <field id="value" type="string" alwaysNotify="true" value="" />

    <!-- solo parametro de entrada -->
    <field id="selected" type="string" value="" alwaysNotify="true" onChange="updateSelected" />

    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children />

</component>
