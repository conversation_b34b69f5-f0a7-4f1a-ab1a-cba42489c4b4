<?xml version="1.0" encoding="UTF-8"?>

<component name="RowLite" extends="GHApiTask">
  <script type="text/brightscript" uri="RowLite.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="url" type="string" value="homeuser" />
    <field id="id" type="string" />
    <field id="rowType" type="string" />
    <field id="longOKType" type="string" />
    
    <field id="currFilter" type="string" />
    <field id="currFilterTitle" type="string" />

    <!-- filtros -->
    <field id="order_id" type="string" />
    <field id="order_way" type="string" value="DESC"/>
  </interface>
</component>