' LANDINGpage
'

sub Init()
  ' m.top.debug = true
  m.top.getScene().updateTheme = m.global.config.theme
  ' objetos
  m.fondo = m.top.findNode("fondo")
  m.logo = m.top.findNode("logo")
  m.columna = m.top.findNode("columna")

  m.toolTip = m.top.findNode("toolTip")
  m.toolTip.visible = false
  m.chkError = m.top.findNode("chkError")
  m.chkError.visible = false

  m.check = m.top.findNode("check")
  m.check.ObserveField("focus", "onCheckFocus")
  m.check.ObserveField("selected", "OnCheckChange")

  m.botonera = m.top.findNode("botonera")
  m.title = m.top.findNode("title")
  m.description = m.top.findNode("description")
  ' eventos
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnExitScreen")

  m.aceptar = m.top.findNode("btnAccept")
  m.aceptar.enabled = false
  m.aceptar.opacity = 0.2

  ' dibujo pantalla
  initialDraw()
end sub

' PRUEBA
sub onCheckFocus(event)
  prendido = event.getData()
  if not prendido and not m.check.checked then
    m.toolTip.visible = true
    m.chkError.visible = true
  else
    m.toolTip.visible = false
    m.chkError.visible = false
  end if
end sub
sub OnCheckChange(event)
  estado = event.getData()
  ' print "AAAA ";estado
  m.aceptar.enabled = estado
end sub

sub initialDraw()
  m.fondo.setFields({
    'uri: ghGetAssetByMode("landing_access_background", "")
    translation: [0, 0]
    width: "1280"
    height: "720"
  })
  m.logo.setFields({
    uri: ghGetAssetByMode("landing_head_logoClarovideo")
    translation: [60, 23]
    width: "164"
    height: "35"
  })

  m.columna.setFields({
    translation: [640, 100]
    layoutDirection: "vert"
    horizAlignment: "center"
    vertAlignment: "top"
    itemSpacings: [10]
  })
  m.botonera.setFields({
    layout: "childs"
    orientation: "vertical"
    itemSpacings: "5"
    layoutDirection: "vert"
    horizAlignment: "center"
    vertAlignment: "center"
    layout: "childs"
    orientation: "vertical"
  })

  m.title.setFields({
    font: ghGetFont(36.45, "bold")
    text: ghTranslate("terms_title", "Aviso de privacidad")
    focusable: "false"
    ' translation: [272, 200]
    width: "696"
    height: "56"
    horizAlign: "center"
  })
  m.description.setFields({
    font: ghGetFont(24, "regular")
    text: ghReplaceStr(ghTranslate("terms_body", " Para continuar, acepte los nuevos términos y condiciones del servicio"), "{br}", chr(10))
    focusable: "false"
    ' translation: [356, 264]
    width: "480"
    height: "75"
    horizAlign: "center"
  })

  ' tooltip
  m.toolTip = m.top.findNode("toolTip")
  m.toolTip.setFields({
    time: -1 ' no termina
    translation: [648, 635] '[1280/2, alto...]
    alignement: "center"
    separatorWidth: 0
    titleColor: "#C1272D"
    font: ghGetFont(19, "regular")
    backgroundImage: "pkg://images/fondoToolTip.png"
    backgroundColor: "#FFFFFF"
    title: ghTranslate("password_tooltip_checkTermsConditions_label_validation", "Debés aceptar los términos y condiciones.")
  })
  m.chkError.setFields({
    uri: ghGetImageByMode("focus01.9.png")
    blendColor: "#981C15"
    visible: true
    translation: [450, 281]
    width: 62
    height: 62
  })

  m.check.setFields({
    selected: false
    'text: "Acepto los nuevos términos y condiciones" + chr(10) + "y politicas de privacidad del servicio"
    text: ghReplaceStr(ghTranslate("terms_checkbox_body", "Acepto los nuevos términos y condiciones y políticas de privacidad del servicio. "), "{br}", chr(10))
    font: ghGetFont(16, "regular")
    textlineSpacing: 0,
    width: "365"
    height: "57"
    backColor: "#212224"
  })

  m.top.findNode("btnTerminos").setFields({
    text: ghTranslate("terms_button", "CONOCER TÉRMINOS Y CONDICIONES")
    font: ghGetFont(21.33, "bold")
    value: "toTerminos"
    width: "480"
    height: "70"
    backColor: "#2E303D"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    selBackColor: "#2E303D"
    focusColor: "#FFFFFF"
  })
  m.top.findNode("btnPrivacidad").setFields({
    text: ghTranslate("terms_policy_button", "CONOCER POLÍTICAS DE PRIVACIDAD")
    font: ghGetFont(21.33, "bold")
    value: "toPrivacidad"
    width: "480"
    height: "70"
    backColor: "#2E303D"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    selBackColor: "#2E303D"
    focusColor: "#FFFFFF"
  })
  m.top.findNode("btnAccept").setFields({
    text: ghTranslate("terms_continue_button", "CONTINUAR")
    font: ghGetFont(21.33, "bold")
    value: "toContinuar"
    width: "480"
    height: "70"
    backcolor: "#981C15"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    selBackColor: "#981C15"
    focusColor: "#FFFFFF"
  })
end sub

' EVENTS
' -------------------
sub OnButtonSelected(event)
  child = event.getRoSGNode()
  if m.top.debug then print ghLogHead();"OnButtonSelected -- "
  if child.selected then
    child.selected = false
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
    if child.value = "mybutton" then ' check
      m.aceptar.enabled = not m.aceptar.enabled
      if m.aceptar.enabled then
        m.aceptar.opacity = 1
      else
        m.aceptar.opacity = 0.2
      end if
    else if child.value = "toTerminos" then
      if m.top.debug then print ghLogHead();"OnButtonSelected -- TO TERMINOS."
      m.top.routerChild = {
        page: "TerminosPage",
        fields: {
          "titleField": "tyc_overhang_text"
          "textField": "tyc_full_text_plano"
        }
      }
    else if child.value = "toPrivacidad" then
      if m.top.debug then print ghLogHead();"OnButtonSelected -- TO PRIVACIDAD."
      m.top.routerChild = {
        page: "TerminosPage",
        fields: {
          "titleField": "pp_overhang_text"
          "textField": "pp_full_text_plano"
        }
      }
    else if child.value = "toContinuar" then
      if m.top.debug then print ghLogHead();"OnButtonSelected -- TO CONTINUAR."
      if m.check.checked then
        m.apiAccept = ghCallApi("SetTermsAndConditions", "SetTermsAndConditionsOK", "SetTermsAndConditionsERROR")
      else
        jumpToLanding()
      end if
      ' else if child.value = "toRechazar" then
      '   if m.top.debug then print ghLogHead();"OnButtonSelected -- TO RECHAZAR."
      '   jumpToLanding()
      ' else
      '   print "GOOSE PANIC !!!! -- ";child.value
    end if
  end if
end sub
sub onWasShown() ' event
  m.botonera.focus = true
  m.botonera.setFocus(true)
end sub
sub updateFieldFocus(event) ' donde me paro cuando arranca la pantalla
  data = event.getData()
  if m.top.debug then print ghLogHead();"changeFieldFocus -- init > ";data
  if data then
    m.botonera.focus = true
    m.botonera.setFocus(true)
  end if
end sub

' API
sub SetTermsAndConditionsOK(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"SetTermsAndConditionsOK -- content=";data
  jumpToHome()
end sub
sub SetTermsAndConditionsERROR(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"SetTermsAndConditionsOK -- error=";data
  jumpToHome()
end sub

' ACCIONES
sub jumpToLanding()
  ' ----------------------------
  ' ATENCION!
  ' tengo que estar deslogueado
  ' ----------------------------
  ' ghSetRegistry("isLoggedIn", "false")
  ' ghDeleteSectionRegistry("user")
  ' print ghListSectionData()
  ' print ghListSectionData("user")
  ' ----------------------------
  ' ya se hace en la landing
  ' ----------------------------
  ghCallApi("Logout")
  ghSetRegistry("isLoggedIn", "false")
  ghDeleteSectionRegistry("user")
  m.top.routerReset = {
    page: "LandingPage"
  }
end sub
sub jumpToHome()
  arguments = ghGetRegistry("arguments", "DL")
  fields = { nodo: "" }
  if arguments <> invalid and arguments <> "" then
    print ghLogHead("DL");"JumpToHome -- arguments= ";arguments
    args = ParseJson(arguments)
    if args <> invalid then
      if args.DoesExist("mediaType") then
        contentType = 1
        if args.mediaType = "series" then
          contentType = 2
        else if args.mediaType = "season" then
          contentType = 3
        else if args.mediaType = "episode" then
          contentType = 4
        else if args.mediaType = "audio" then
          contentType = 5
        end if
        item = {
          contentType: contentType
          id: args.contentId
          mediaType: args.mediaType
          idPlayable: isPlayableMediaType(args.mediaType),
          deepLinking: true
        }
        fields = { "deeplink": item, nodo: "" }
      end if
    end if
  else
    print ghLogHead("DL");"JumpToHome -- NO arguments= "
  end if
  m.top.routerReset = {
    page: "HomePage",
    fields: fields
  }
  Notify_Roku("Roku_Authenticated")
  ' ghTurnLoading(false, m.loading, m.botonera)
  m.top.signalBeacon("AppDialogComplete")
end sub
function isPlayableMediaType(mediaType) as boolean ' returns AA with supported media types
  supported = {
    "series": "series",
    "season": invalid,
    "episode": "episode",
    "movie": "movie",
    "shortFormVideo": invalid
  }
  return supported[mediaType] <> invalid
end function

' ' KEY EVENTS
' ' -----------------------------
' function onKeyEvent(key, press)
'   if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
'   handled = false
'   if press then
'     if key <> "back" then
'       turnFocusTo(guessFocusTo(key))
'       handled = true
'       if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
'     end if
'   end if
'   return handled
' end function
' function guessFocusTo(direction) as string
'   current = getCurrentFocus()
'   ' a donde voy?
'   if m.map[current][direction] <> invalid then
'     focusTo = m.map[current][direction]
'   else
'     focusTo = current
'   end if
'   return focusTo
' end function
' sub turnFocusTo(id)
'   current = getCurrentFocus()
'   if current <> id then
'     if current <> invalid then
'       m.top.findNode(current).focus = false ' apago el actual
'     end if
'     if m.top.findNode(id).focus <> invalid then
'       if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
'       m.top.findNode(id).focus = true
'     end if
'   else
'     if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
'   end if
' end sub
' function getCurrentFocus()
'   current = invalid
'   if m.top.focusedChild <> invalid then
'     if m.top.focusedChild.id <> "" then
'       if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
'         current = m.top.focusedChild.id
'       end if
'     end if
'   end if
'   if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
'   return current
' end function
