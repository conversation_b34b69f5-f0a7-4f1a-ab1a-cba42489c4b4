sub Init()
    m.top.debug = true
    if m.top.debug then print ghLogHead();"CallCenterPage Init..."
    m.top.getScene().updateTheme = m.global.config.theme

    screenResolution = ghGetGlobalWH()
    screenWidth = screenResolution.w
    screenHeight = screenResolution.h

    m.logo = m.top.findNode("logo")
    m.logo.uri = ghGetAsset("", "pkg:/images/logo.png")

    m.callinfo = m.top.findNode("callinfo")
    m.callinfo.translation = [screenWidth / 2, screenHeight / 2]

    m.callCenterTitle = m.top.findNode("callCenterTitle")
    m.callCenterTitle.font = ghGetFont(27, "regular") ' Font size scaled down from 40


    m.callCenterinfo = m.top.findNode("callCenterinfo")
    m.callCenterinfo.font = ghGetFont(21, "regular") ' Font size scaled down from 32

    m.callDescription = m.top.findNode("callDescription")
    m.callDescription.font = ghGetFont(19, "regular") ' Font size scaled down from 32

    m.numberDescription = m.top.findNode("numberDescription")
    m.numberDescription.font = ghGetFont(19, "regular") ' Font size scaled down from 32

    m.phoneNumber = m.top.findNode("phoneNumber")
    m.phoneNumber.font = ghGetFont(24, "regular") ' Font size scaled down from 36

    m.aceptar = m.top.findNode("aceptar")

    m.botonera = m.top.findNode("botonera")
    m.botonera.ObserveField("selected", "OnButtonSelected")
    m.botonera.ObserveField("backSelected", "BackTo")
end sub

sub handleData(event)
    data = event.getData()
    print ghLogHead();"callhandleData -- ";data
end sub

sub getProviderCode(event)
    providerCode = event.getData()
    if m.top.debug then print ghLogHead(); "getProviderCode -- ";providerCode
    m.callCenterTitle.text = ghTranslate(providerCode + "_subscription_modal_title_OfferCallCenter_label", UCase(providerCode) + "_subscription_modal_title_OfferCallCenter_label")
    m.callCenterinfo.text = ghTranslate(providerCode + "_subscription_modal_description_OfferCallCenter_label", UCase(providerCode) + "_subscription_modal_description_OfferCallCenter_label")
    m.callDescription.text = ghTranslate(providerCode + "_subscription_modal_titleNumber_OfferCallCenter_label", UCase(providerCode) + "_subscription_modal_titleNumber_OfferCallCenter_label")
    m.phoneNumber.text = ghTranslate(providerCode + "_subscription_modal_number_OfferCallCenter_label", UCase(providerCode) + "_subscription_modal_number_OfferCallCenter_label")
    m.numberDescription.text = ghTranslate(providerCode + "_subscription_modal_note_OfferCallCenter_label", UCase(providerCode) + "_subscription_modal_note_OfferCallCenter_label")
    m.aceptar.text = ghTranslate(providerCode + "_subscription_modal_option_button_close", UCase(providerCode) + "_subscription_modal_option_button_close")
end sub

sub onWasShown(event)
    data = event.getData()
    m.top.signalBeacon("AppDialogInitiate")
    GA4Event("screen_view", {
        screen_name: "callCenterPage",
        screen_class: "/callCenterPage"
    })
    if m.top.debug then print ghLogHead();"onWasShown -- init - ";m.top.focus
    if data then
        turnFocusTo("botonera")
    end if
end sub

sub BackTo()
    if m.top.debug then print ghLogHead();"BackTo."
    m.top.signalBeacon("AppDialogComplete")
    m.top.routerClose = true
end sub

sub OnButtonSelected(event)
    child = event.getRoSGNode()
    if m.top.debug then print ghLogHead();"OnButtonSelected -- "
    if child.selected then
        child.selected = false
        if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
        m.top.signalBeacon("AppDialogComplete")
        BackTo()
    end if
end sub

sub turnFocusTo(id)
    current = getCurrentFocus()
    if current <> id then
        if current <> invalid then
            m.top.findNode(current).focus = false ' apago el actual
        end if
        if m.top.findNode(id).focus <> invalid then
            if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
            m.top.findNode(id).focus = true
        end if
    else
        if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
    end if
end sub

function getCurrentFocus()
    current = invalid
    if m.top.focusedChild <> invalid then
        if m.top.focusedChild.id <> "" then
            if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
                current = m.top.focusedChild.id
            end if
        end if
    end if
    if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
    return current
end function

