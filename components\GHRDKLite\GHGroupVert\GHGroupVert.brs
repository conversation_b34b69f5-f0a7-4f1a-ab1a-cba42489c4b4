' GHGroupVert
' -----------------------------
sub Init()
  ' m.top.debug = true
  m.logger = CreateLogger()
  m.logger.debug("Init.")

  m.top.ObserveField("change", "OnChange")
end sub
' EVENTS
' -----------------
sub OnChange(event)
  m.logger.debug("OnChange -- ", { data: event.getData() })
  recalcPositions()
end sub
sub OnRefresh()
  m.logger.debug("OnRefresh -- REFRESH!")
  recalcPositions()
end sub
sub OnCentered(event)
  m.logger.debug("OnCentered -- ", { data: event.getData() })
  recalcPositions()
end sub
' CALCULOS
' -----------------
sub recalcPositions()
  ScrWidth = ghGetGlobalWH().w ' ancho de la pantalla
  cant = m.top.getChildCount() ' cant de childs
  accY = 0 ' incremental de Y
  for c = 0 to cant - 1
    child = m.top.getChild(c)
    if m.top.autoRefreshField <> "" then
      child.unobserveFieldScoped(m.top.autoRefreshField)
      child.observeFieldScoped(m.top.autoRefreshField, "onFieldChange")
    end if
    bound = child.boundingRect()
    chX = child.translation[0]
    chY = child.translation[1]
    m.logger.debug("OnRefresh", { id: child.id, chx: chX, chy: chY, bound: bound, accY: accY })

    if m.top.centered.Lookup(child.id) = true then chx = (ScrWidth - bound.width) / 2

    child.translation = [chX, accY] ' actualizo el translate
    accY += (bound.height + m.top.itemSpacing) ' contador global + separador
    m.logger.debug("OnRefresh", { id: child.id, bound: child.boundingRect(), accY: accY })
  end for
end sub
' DUMPS
' -----------------
sub dumpChildren()
  cant = m.top.getChildCount()
  print "-----"
  for c = 0 to cant - 1
    print "** ";c
    print m.top.getChild(c)
    print "-----"
  end for
end sub


sub onFieldChange(event)
  data = event.getData()
  m.logger.debug("onFieldChange", { field: m.top.autoRefreshField, value: data })
  recalcPositions()
end sub