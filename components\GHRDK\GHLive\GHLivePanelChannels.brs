sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init -- "

  m.channelPositionInt = invalid

  m.selected = invalid
  m.kkey = ""
  m.kpress = false

  m.top.ObserveField("visible", "onPanelVisibleChange")

  m.panR = m.top.findNode("panelRight")
  m.panR.setFields({
    translation: [0, 0]
    width: 1920
    height: 720
    color: "#000000"
  })

  m.title = m.top.findNode("title")
  m.title.setFields({
    font: ghGetFont(48, "regular")
    horizAlign: "center"
    text: ghTranslate("Canales", "Canales")
    color: "#FFFFFF"
  })

  m.listChannels = m.top.findNode("listChannels")
  m.listChannels.ObserveField("itemSelected", "onItemSelected")
  m.listChannels.ObserveField("itemFocused", "onItemFocused")

  buildKeyTimer()
end sub

sub onFocusChange(event)
  data = event.getData()
  m.listChannels.setFocus(data)
end sub

sub handleJumpTo(event)
  data = event.getData()

  if m.top.debug then print ghLogHead("jumpTo");"handleJumpTo -- ";data

  m.listChannels.jumpToItem = data
end sub

sub onItemSelected(event)
  data = event.getData()

  if data <> invalid then
    selected = ghGetChild(m.listChannels.content.getChild(data), "data")
    m.selected = selected

    ' hombre muerto
    m.top.keypressed = "ok"
  end if
end sub

sub onPanelVisibleChange(event)
  data = event.getData()

  if m.top.debug then print ghLogHead();"onPanelVisibleChange -- ";data

  if data then ' entro----
    m.selected = invalid
    m.listChannels.setFocus(true)
  else ' salgo ------------------
    m.listChannels.setFocus(false)
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub

sub onDataUpdate(event)
  ls = event.getData()

  position = 0
  channels = createObject("RoSGNode", "GHContent")
  for each l in ls
    channel = createObject("RoSGNode", "GHContent")
    l.rowType = "Gridchannel"
    l.image_small = l.image
    l.channelPosition = position
    channel.data = l
    channels.appendChild(channel)
    position = position + 1
  end for

  m.listChannels.content = channels

  ' si redibujo por los candados, mantengo posicion
  if m.channelPositionInt <> invalid
    m.listChannels.jumpToItem = m.channelPositionInt
  end if
end sub

' ====================
'      key event
' ====================

sub buildKeyTimer()
  print ghLogHead();"buildKeyTimer -ini- "

  m.kkey = invalid
  m.kpress = false
  m.kTimer = CreateObject("roSGNode", "Timer")
  m.kTimer.duration = 1
  m.kTimer.repeat = false
  m.kTimer.ObserveField("fire", "triggerKeyTimer")

  print ghLogHead();"buildKeyTimer -end- "
end sub

sub triggerKeyTimer()
  print ghLogHead();"triggerKeyTimer -ini- "

  m.kkey = "long_" + m.kkey

  triggerKey()

  print ghLogHead();"triggerKeyTimer -end- "
end sub

function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press

  handled = true

  if press then
    m.ktimer.control = "start"
    m.kkey = key
    m.kpress = true
  else
    if m.kpress then
      if m.ktimer.control <> "stop" then
        m.ktimer.control = "stop"
        triggerKey()
      end if
    end if
  end if

  return handled
end function

' =======================

sub triggerKey()
  print ghLogHead();"triggerKey -ini- "

  if m.kkey = "long_OK" then
    ' m.channelPositionInt = m.selected.channelPosition

    m.top.visible = false
    m.top.cmd = {
      cmd: "showOptions",
      channel: m.selected
      program: invalid
    }
  else if m.kkey = "OK" then ' salida
    m.top.visible = false
    m.top.cmd = { cmd: "changeChannel", channel: m.selected }
  else if m.kkey = "back" or m.kkey = "long_back" then
    m.top.visible = false
  end if

  ' hombre muerto
  m.top.keypressed = m.kkey

  print ghLogHead();"triggerKey -end- "
end sub

sub onItemFocused()
  if m.top.debug then print ghLogHead();"onItemFocused ** !!!!!!!"
  m.top.keypressed = "x"
end sub