function init()
    m.multiSubscriptionList = m.top.findNode("multiSubscriptionList")
    m.titleLabel = m.top.findNode("titleLabel")
    m.titleLabel.font = "font:MediumBoldSystemFont"
    m.titleLabel.fontSize = 42
    m.titleLabel.color = "0xFFFFFF"

    m.botonera1 = m.top.findNode("botonera1")

    m.multiSubscriptionList.translation = [265, 200]
    m.multiSubscriptionList.itemSize = [750, 400]
    m.multiSubscriptionList.rowItemSize = [[165, 400]]
    m.multiSubscriptionList.numRows = 1
    m.multiSubscriptionList.numColumns = 4
    m.multiSubscriptionList.rowItemSpacing = [30, 24]
    m.multiSubscriptionList.itemComponentName = "ItemSubscription"
    m.multiSubscriptionList.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
    m.multiSubscriptionList.ObserveField("rowItemSelected", "ButtonSelected")
    m.multiSubscriptionList.jumpToItem = 0

    m.map = {
        "multiSubscriptionList": { "up": invalid, "right": invalid, "down": "botonera1", "left": invalid, "default": true },
        "botonera1": { "up": "multiSubscriptionList", "right": invalid, "down": invalid, "left": invalid },
    }
    m.titleLabel.ObserveField("text", "adjustPositionContent")
    m.selectedData = invalid
end function


sub onWasShown(event)
    data = event.getData()
    m.top.signalBeacon("AppDialogInitiate")
    GA4Event("screen_view", {
        screen_name: "multiPackPage",
        screen_class: "/multiPackPage"
    })
    if m.top.debug then print ghLogHead();"onWasShown -- init - ";m.top.focus
    if data then
        m.multiSubscriptionList.setFocus(true)
        ' turnFocusTo("multiSubscriptionList")
    end if
    m.titleLabel.text = ghTranslate("subscription_modal_title_label", "")
end sub



function handleData(event)
    data = event.getData()
    rowNode = createContentNode(event.getData())
    m.multiSubscriptionList.content = rowNode
    adjustPositionRowList(m.multiSubscriptionList)
end function

function createContentNode(arrayList) as object
    rootChildren = []
    row = {}
    row.children = []
    for each data in arrayList
        print "createContentNode -- " data
        subNode = subContentNode(data)
        row.children.push(subNode)
    end for
    rootChildren.Push(row)
    contentNode = CreateObject("roSGNode", "ContentNode")
    contentNode.Update({ children: rootChildren }, true)
    return contentNode
end function

function adjustPositionContent(event)
    if m.top.debug then print ghLogHead();"adjustPositionContent -- ";event.getData()
    node = event.getRoSGNode()
    boundingRect = node.boundingRect()
    uiResolution = ghGetGlobalWH()
    if boundingRect <> invalid and uiResolution <> invalid
        centerX = (uiResolution.w - boundingRect.width) / 2
        centerY = ghYtoAbstract(node.translation[1])
        node.translation = [centerX, centerY]
    end if
end function


function subContentNode(data) as object
    item = {}
    for each key in data
        item[key] = data[key]
    end for
    return item
end function


function ButtonSelected(event)
    if m.top.debug then print ghLogHead();"Button Selected"
    data = event.getData()
    selectedRow = data[0]
    selectedRowItem = data[1]
    m.selectedData = m.multiSubscriptionList.content.getChild(selectedRow).getChild(selectedRowItem)
    m.selectedData = NodeToAssociativeArray(m.selectedData)
    if m.top.debug then print ghLogHead();"Button Selected data" m.selectedData
    if m.selectedData <> invalid
        m.top.selected = m.selectedData
        BackTo()
    end if
end function

function NodeToAssociativeArray(node as object) as object
    associativeArray = {}
    ' Check if the input is a valid node
    if node <> invalid
        ' Iterate over all the fields in the node
        fields = node.getFields()
        for each field in fields
            associativeArray[field] = node[field]
        end for
    end if

    return associativeArray
end function

sub BackTo()
    if m.top.debug then print ghLogHead();"BackTo."
    m.top.signalBeacon("AppDialogComplete")
    m.top.routerClose = true
end sub

sub adjustPositionRowList(RowList)
    if RowList <> invalid
        uiResolution = ghGetGlobalWH()
        rowCount = RowList.content.getChild(0).getChildCount()
        itemWidth = 165
        spacing = 30
        if rowCount < 4
            totalWidth = (itemWidth * rowCount) + (spacing * rowCount)
            centerX = (uiResolution.w - totalWidth) / 2
            positionY = ghYtoAbstract(RowList.translation[1])
            RowList.translation = [centerX, positionY]
        else
            centerX = ghXtoAbstract(RowList.translation[0])
            positionY = ghYtoAbstract(RowList.translation[1])
            RowList.translation = [centerX, positionY]
        end if
        ' print "RowList.translation: "; RowList.translation
    end if
end sub


' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
    if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
    handled = false
    if press then
        if key <> "back" then
            turnFocusTo(guessFocusTo(key))
            handled = true
            if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
        end if
    end if
    return handled
end function

function guessFocusTo(direction) as string
    current = getCurrentFocus()
    ' a donde voy?
    if m.map[current][direction] <> invalid then
        focusTo = m.map[current][direction]
    else
        focusTo = current
    end if
    return focusTo
end function

sub turnFocusTo(id)
    current = getCurrentFocus()
    if current <> id then
        if current <> invalid then
            m.top.findNode(current).focus = false ' apago el actual
        end if
        if m.top.findNode(id).focus <> invalid then
            if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
            m.top.findNode(id).focus = true
        end if
    else
        if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
        m.top.findNode(id).focus = true
    end if
end sub

function getCurrentFocus()
    current = invalid
    if m.top.focusedChild <> invalid then
        if m.top.focusedChild.id <> "" then
            if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
                current = m.top.focusedChild.id
            end if
        end if
    end if
    if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
    return current
end function

