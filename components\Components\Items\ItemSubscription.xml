<?xml version="1.0" encoding="utf-8"?>
<component name="ItemSubscription" extends="Group">
	<script type="text/brightscript" uri="ItemSubscription.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
	<script type="text/brightscript" uri="drawUtils.brs" />
	<interface>
		<!-- interfaz de entrada -->
		<field id="rowIndex" type="integer" />
		<field id="width" type="float" alias="itemPoster.width" />
		<field id="height" type="float" alias="itemPoster.height" />
		<field id="itemContent" type="node" onChange="itemContentChanged" />
		<!-- <field id="focusPercent" type="float" onChange="showfocus" /> -->
		<field id="itemHasFocus" type="boolean" onChange="showfocus" />
		<!--
		<field id="rowListHasFocus" type="boolean" onChange="showfocus" />
		<field id="rowHasFocus" type="boolean" onChange="showfocus" />
		-->
		<!-- interfaz interna -->
		<field id="debug" type="boolean" value="false" />
	</interface>
	<children>
		<Poster id="itemPoster" />
		<LayoutGroup id="infoPlan" translation="[80,250]" layoutDirection="vert" vertAlignment="center" horizAlignment="center" itemSpacings="[20]" visible="true">
			<Poster id="pstrLogoAddon" translation="[0,0]" visible="true" />
			<LayoutGroup id="infoPlan2" translation="[0,0]" layoutDirection="vert" vertAlignment="center" horizAlignment="center" itemSpacings="[3]" visible="true">
				<LayoutGroup id="pricecurrency" translation="[0,0]" layoutDirection="horiz" itemSpacings="[5]" visible="true" horizAlignment="center" vertAlignment="center">
					<Label id="lblPrice" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center" />
					<Label id="lblCurrency" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center" />
				</LayoutGroup>
				<Label id="lblIva" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center" />
			</LayoutGroup>
		</LayoutGroup>
		<GHButtonGroup id="botonera2" layout="map" orientation="horizontal" handleKey="false">
			<GHButton value="cntrBtn" id="cntrBtn" color="0xFFFFFF" translation="[-8,340]" width="180" height="55" backcolor="#981C15" focusColor="0xFFFFFF" />
		</GHButtonGroup>
	</children>
</component>
