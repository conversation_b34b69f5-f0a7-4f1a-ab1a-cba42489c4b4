# GHRDK - Componentes



- [<u>GHButton</u>](GHButton.md) : botón con funcionalidad completa y foco.

- [<u>GHButtonGroup</u>](GHButtonGroup.md): agrupador de componentes (`GHButton, GHInput, GHCheckbox, etc.`) que manejan foco.

- [<u>GHCheckBox</u>](GHCheckBox.md): checkbox similar al de HTML con manejo de tilde y foco.

- [<u>GHError</u>](GHError.md): pantalla tipo popup genérica para mostrar un error con un solo botón OK.

- [<u>GHInput</u>](GHInput.md): componente similar al input de HTML con manejo de keyboard por popup, password y foco.

- [<u>GHLabel</u>](GHLabel.md): componente tipo párrafo de HTML con manejo de foco.

- [<u>GHLoading</u>](GHLoading.md): pantalla tipo popup genérica para mostrar un cargando, sin funcionalidad propia y que fija el foco.

- [<u>GHMenu</u>](GHMenu.md): agrupador de `GHOption` para un manejo de foco tipo menú.

- [<u>GHOption</u>](GHOptions.md): opción de menú para utilizarse con `GHMenu`.

- [<u>GHTextScroll</u>](GHTextScroll.md): componente para mostrar textos tipo txt largos, con scroll e instrucciones de uso, también manejo de foco.
