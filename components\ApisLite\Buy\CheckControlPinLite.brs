' Offers
' ---------------------------

' object_type=[A]
' offer_id=[offer id]
' authpn=[authpn]
' authpt=[authpt]
' HKS=[HKS]
' device_category=[device category]
' device_manufacturer=[device  manufacturer]
' device_model=[device model]
' device_type=[device type]
' region=[country name]
' api_version=[v5.89]

sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/user/checkcontrolpin"
  m.api.query.Append({
    "controlPIN": m.top.controlPIN
    "userId": ghGetRegistry("user_id", "user")
    ' "user_id": ghGetRegistry("user_id", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then print "*********************************"
  if m.top.debug then print "*********************************"
  if m.top.debug then print ghLogHead();"body = ";res
  if m.top.debug then print ghLogHead();"body.response = ";ghGetChild(res, "response")

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = res

  if m.top.debug then print "*********************************"
  if m.top.debug then print "*********************************"
end sub