' ApiSuperHighlight
' https://app.swaggerhub.com/apis/ClaroVideo/Superhighlight/1.0.0#/default
' -----------------------

sub DataInit()
  ' m.top.debug = true

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/v1/superhighlight"

  m.api.query.delete("format")
  m.api.query.delete("api_version")
  m.api.query.delete("HKS")
  m.api.query.delete("region")

  m.api.query.Append({
    "user_token": ghGetRegistry("user_token", "user")
  })
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print res
    print left(raw, 20)
  end if
  if res = invalid or ghGetChild(res, "errors") <> invalid then
    m.top.error = res.errors
    return
  end if

  m.global.superhighlight = ghGetChild(res, "data.superhighlight", []) ' SUPERHIGHLIGHT

  m.top.content = ghGetChild(res, "data")
end sub
