' ProfileLite
' https://app.swaggerhub.com/apis/ClaroVideo/v1_profile/1.0.0
' -----------------------

sub DataInit()
  ' m.top.debug = true
  m.logger.debug("DataInit **")
  ' if m.top.debug then print ghLogHead();"DataInit **"

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/payway/paymentservice/v1/profile"
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.query.delete("format") ' sin user id
  m.api.query.delete("region") ' sin user id
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  ' m.api.query.Append({
  '   region: ghGetRegistry("region")
  '   module_version: "v2"
  ' })

  ' if m.top.debug then
  '   print ghLogHead();"DataInit -- api="m.api
  '   print ghLogHead();"DataInit -- params="m.api.params
  ' end if
  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })

end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  response = res?.response
  if response <> invalid then
    ' Payment Methods especial para youbora
    if response.paymentMethods <> invalid then
      try
        payMethods = response.paymentmethods
        txtPayMethods = ""
        for i = 0 to payMethods.Count() - 1
          cat = payMethods[i].user_category
          if cat <> invalid then
            txtPayMethods += payMethods[i].user_category + "-"
          end if
        end for
        if Len(txtPayMethods) > 0 then
          txtPayMethods = Left(txtPayMethods, Len(txtPayMethods) - 1)
        end if
      catch err
        print "ERROR >> ";err
        txtPayMethods = ""
      end try
    else
      txtPayMethods = ""
    end if
    ghSetRegistry("paymentMethods_user_category", txtPayMethods, "user")

    ' se usaba asi antes
    m.global.paywayProfile = response ' para Youbora

    ' payment methods a global
    m.global.paymentMethods = ghGetChild(response, "paymentMethods", [])
    ' subcriptions a global
    m.global.subscriptions = ghGetChild(response, "subscriptions", [])
    ' salida

    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print m.global.paymentMethods
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print m.global.subscriptions
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"
    ' print "^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$^$"

    m.top.content = { status: "OK." }
  else
    ' salida
    m.top.content = { status: "FAILED." }
  end if

  ' err = res.errors
  ' response = res.experience
  ' if err <> invalid then ' dio un error
  '   print ghLogHead();"ProcessData -- ERROR = ";err
  '   m.top.error = err
  ' else
  '   m.global.user_experience = response
  '   m.top.content = response
  ' end if
end sub
