# GHButtonGroup

## Propiedades

| Nombre       | Tipo       | Default  | Descripción                                                  |
| ------------ | ---------- | -------- | ------------------------------------------------------------ |
| defaultChild | string     |          | componente seleccionado por default                          |
| exitUp       | boolean    | false    | puede salirse del grupo por arriba?                          |
| exitDown     | boolean    | false    | puede salirse del grupo por abano?                           |
| exitLeft     | boolean    | false    | puede salirse del grupo por la izquierda?                    |
| exitRight    | boolean    | false    | puede salirse del grupo por la derecha?                      |
| layout       | string     | map      | `map|childs` tipo de layout que se va a utilizar             |
| map          | assocarray | {}       | array asociativo con el mapa de distribución de foco de componentes |
| orientation  | string     | vertical | `horizontal|vertical` tipo de orientación para layout tipo `childs` |
| backSelected | boolean    | false    | fue apretado el botón back mientras se recorre el grupo?     |
| selected     | boolean    | false    | fue seleccionado algún componente mientras se recorre el grupo? |
| enabled      | boolean    | true     | esta habilitado el grupo para tener foco?                    |
| value        | string     | mybutton | valor del componente que fue seleccionado                    |
| focus        | boolean    | false    | tiene foco el grupo de componentes?                          |
| debug        | boolean    | false    | prender logs para debug                                      |

## Ejemplo

Declaración del grupo en el xml.

```xml
<GHButtonGroup id="botonera" layout="map" defaultChild="user">
  <GHInput id="user" .... />
  <GHInput id="pass" ... />
  <GHCheckBox id="check" ... />
  <GHButton id="terminos" ... />
  <GHButton id="register" ... />
  <GHButton id="cancel" ... />
  <GHButton id="toLogin" ... />
</GHButtonGroup>
```

Manejo del grupo con `layout` tipo `map`.

```basic
  ' botonera
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.map = {
    "user": { "up": "toLogin", "right": invalid, "down": "pass", "left": invalid },
    "pass": { "up": "user", "right": invalid, "down": "check", "left": invalid },
    "check": { "up": "pass", "right": "terminos", "down": "register", "left": invalid },
    "terminos": { "up": "pass", "right": invalid, "down": "register", "left": "check" }
    "register": { "up": "check", "right": invalid, "down": "cancel", "left": invalid }
    "cancel": { "up": "register", "right": invalid, "down": "toLogin", "left": invalid }
    "toLogin": { "up": "cancel", "right": invalid, "down": "user", "left": invalid }
  }
```






