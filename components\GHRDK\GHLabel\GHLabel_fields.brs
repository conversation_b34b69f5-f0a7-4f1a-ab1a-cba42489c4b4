' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

'
' FIELDS
' -----------------------------

sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then
    m.label.width = m.top.width
  end if
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then
    m.label.height = m.top.height
  end if
end sub

sub updateFieldColor()
  recalcColors()
end sub

sub updateFieldSelColor()
  recalcColors()
end sub

sub recalcColors()
  if m.top.focus then
    m.label.color = m.top.selColor
  else
    m.label.color = m.top.color
  end if
end sub

'
'
' END FILE ------------------