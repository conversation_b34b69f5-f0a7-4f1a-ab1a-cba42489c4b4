' HUBGATE

sub PurchaseHubGate()
  m.logger.debug("Purchase HubGate init")
  m.region = ghGetRegistry("region")
  HubGateRun()
end sub

sub HubGateRun(newState = invalid, info = {})
  m.logger.debug("HubGateRun", { state: newState, info: info })
  setLoading(false)

  if newState = invalid then
    HubgateMovilInfo()
    HubGateCheck()

  else if newState = "go" then
    setLoading(true)
    HubGateGoExtraParam(info)
    'HubGateGo()
  else if newState = "back" then
    JumpTo("checkout")
  else if newState = "finalCheckout" then
    ' FinalCheckReview()
  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")
  end if
end sub

sub HubGateCheck()
  data = ghGetChild(m.buy.states, "purchase.paymentMethod")

  m.logger.debug("Purchase HubGate Check", { data: data })

  if data <> invalid then
    m.buy.states["purchase"].method = {}
    m.buy.states["purchase"].method.parameters = {
      link: ghGetChild(data, "data.buylink", "")
      buyToken: ghGetChild(data, "data.buyToken", "")
      object_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(m.buy, "data.access_code", "")
    }
    print "buyToken :" ghGetChild(data, "data.buyToken", "")
    'HubGateRun("go")
  else
    HubGateRun("missingparameters")
  end if
end sub

sub HubGateGoExtraParam(extra_params)
  data = ghGetChild(m.buy.states, "purchase.method.parameters")
  if data.extra_params = invalid
    data.extra_params = extra_params
  else
    data.extra_params.Append(extra_params)
  end if
  print "HubGateGoExtraParam :" data, data.extra_params

  m.logger.debug("Purchase HubGate Go", { data: data })
  if data <> invalid then
    if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
      apiConfirm = ghCallApi("BuyConfirmLite_hubgate", "BuyConfirm_HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      apiConfirm.setFields(data)
      apiConfirm.control = "run"

    else if inStr(1, ghGetChild(data, "link", ""), "/confirm") > 0 then
      apiConfirm = ghCallApi("PaywayConfirmLite_hubgate", "Payway_HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      apiConfirm.setFields({
        buylink: ghGetChild(data, "link", "")
        data: data.extra_params
      })
      apiConfirm.control = "run"
    else
      HubGateRun("missingparameters")
    end if
  end if
end sub

sub HubGateGo()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")
  m.logger.debug("Purchase HubGate Go", { data: data })

  if data <> invalid then
    if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
      apiConfirm = ghCallApi("BuyConfirmLite", "HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      apiConfirm.setFields(data)
      apiConfirm.control = "run"

    else if inStr(1, ghGetChild(data, "link", ""), "/confirm") > 0 then
      apiConfirm = ghCallApi("PaywayConfirmLite", "HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      print "api info:" ghGetChild(data, "extra_params", "")
      apiConfirm.setFields({
        buylink: ghGetChild(data, "link", "")
        data: ghGetChild(data, "extra_params", "")
      })
      apiConfirm.control = "run"
    else
      HubGateRun("missingparameters")
    end if
  end if
end sub

sub BuyConfirm_HubGateGo_ReturnOk(event)
  data = event.getData()
  node = event.getRoSGNode()
  print "BuyConfirm_HubGateGo_ReturnOk :" node.extra_params, node.extra_params.DoesExist("pin")

  m.logger.debug("Purchase HubGate Go Ok", { data: data })
  setLoading(false)

  if m.region = "colombia"
    'HubGateRun("error")
    '  HubGateRun("back")
    TicketScreenLanding(data)
    ' HubGateRun("ok")  'success logic
  else
    if node.extra_params <> invalid
      if node.extra_params.DoesExist("pin")
        ' HubGateRun("ok")  'success logic
        ' HubGateRun("back")  ' checkout screen
        TicketScreenLanding(data) 'Ticket Screen
        ' HubGateRun("error")   'Error screen
      else
        hubgatePinEnter()
      end if
    end if
  end if

end sub

sub Payway_HubGateGo_ReturnOk(event)
  data = event.getData()
  node = event.getRoSGNode()
  print "Payway_HubGateGo_ReturnOk :" node.data, node.data.DoesExist("pin")

  m.logger.debug("Purchase HubGate Go Ok", { data: data })
  setLoading(false)

  if m.region = "colombia"
    'ADD YOUR COLOMBIA LOGIC
    ' HubGateRun("back")
    TicketScreenLanding(data)
    'HubGateRun("ok")
  else
    if node.data <> invalid
      if node.data.DoesExist("pin")
        ' HubGateRun("ok")
        'HubGateRun("back") 'back
        TicketScreenLanding(data)
        'HubGateRun("error")
      else
        hubgatePinEnter()
      end if
    end if
  end if

end sub



sub HubGateGo_ReturnFails(event)
  data = event.getData()
  m.logger.error("Purchase HubGate Go Fails", { data: data })
  HubGateRun("error", { data: data })
end sub

'create screen information

sub HubgateMovilInfo()
  m.hubgateMovilStart = CreateObject("roSGNode", "CheckOutFieldsEntry")
  'm.hubgateMovilStart.ObserveField("value", "Hubgate_Entry_Return")
  m.hubgateMovilStart.ObserveField("wasClosed", "Hubgate_Entry_Return")
  m.hubgateMovilStart.id = "hubgateMovilStart"

  ' Get buyData from the current buy flow
  buyData = {
    buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
    buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
    buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
    buyProductType: ghGetChild(m.buy, "data.button.producttype")
    buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
    buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
    buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
    buyBanner: ghGetChild(m.buy, "data.button.banner", "")
    oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

    contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
    contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
    contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
    contentId: ghGetChild(m.buy, "data.contentId", "")
    content_name: ghGetChild(m.buy, "data.content_name", "")
    content_type: ghGetChild(m.buy, "data.content_type", "")
    content_category: ghGetChild(m.buy, "data.content_category", "")

    paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
    screen_name: "hubgate",
    screen_class: "/hubgate",

  }
  GA4Event("purchase_hubgate", buyData)

  m.top.routerChild = {
    page: m.hubgateMovilStart,
    fields: {
      accessCode: ""
      data: ""
      screenType: 1
      checkoutFieldType: "hubgate"
      buyData: buyData
    }
  }
end sub

sub Hubgate_Entry_Return(event)
  scr = event.getRoSGNode()

  m.logger.debug("Hubgate_Entry_Return", { opcion: scr.value.opcion, data: scr.value.data })

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    print "inside BACK LOGIC CALLED"
    HubGateRun("back")
  else if scr.value.opcion = "SELECT" then
    print "data :" scr.value.data
    dataValue = ghGetChild(m.buy.states, "purchase.method.parameters")
    print "dataValue :" dataValue
    if m.region = "mexico" or m.region = "argentina" or m.region = "peru"
      m.numberField = scr.value.data ' single input values
    else
      m.numberField = scr.value.data
    end if
    if m.region = "colombia"
      if m.buy.states["purchase"] <> invalid
        if m.buy.states["purchase"].method <> invalid and m.buy.states["purchase"].method.parameters <> invalid then
          if m.buy.states["purchase"].method.parameters.extra_params <> invalid
            m.buy.states["purchase"].method.parameters.extra_params.append(scr.value.data)
          else
            m.buy.states["purchase"].method.parameters.extra_params = scr.value.data
          end if
        end if
      end if
      FinalcheckoutShowInfo() 'ENABLE FOR API
    else
      HubGateRun("go", scr.value.data) 'ENABLE FOR API
    end if
  end if
end sub


'PIN ENTER SCREEN CALL

sub hubgatePinEnter()
  m.hubgatePinEnter = CreateObject("roSGNode", "CheckOutFieldsEntry")
  ' m.hubgatePinEnter.ObserveField("value", "Hubgate_Pin_Return")
  m.hubgatePinEnter.ObserveField("wasClosed", "Hubgate_Pin_Return")
  m.hubgatePinEnter.id = "hubgatePinEnter"
  m.top.routerChild = {
    page: m.hubgatePinEnter,
    fields: {
      accessCode: ""
      data: ""
      screenType: 2
      checkoutFieldType: "hubgate"
    }
  }
end sub


sub Hubgate_Pin_Return(event)
  scr = event.getRoSGNode()
  print "Hubgate_Pin_Return :" scr
  m.logger.debug("Hubgate_Pin_Return", { opcion: scr.value.opcion, data: scr.value.data })
  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    print "src.value.BACK :" scr.value.opcion
    m.top.close = true
    HubgateMovilInfo()
  else if scr.value.opcion = "SELECT" then
    print "pin src.value.SELECT checkout:" scr.value.opcion
    ' HubGateRun("go", scr.value.data) api data
    if m.buy.states["purchase"] <> invalid
      if m.buy.states["purchase"].method <> invalid and m.buy.states["purchase"].method.parameters <> invalid then
        if m.buy.states["purchase"].method.parameters.extra_params <> invalid
          m.buy.states["purchase"].method.parameters.extra_params.append(scr.value.data)
        else
          m.buy.states["purchase"].method.parameters.extra_params = scr.value.data
        end if
      end if
    end if

    FinalcheckoutShowInfo()
    'HubGateRun("back")
  else if scr.value.opcion = "PIN" then
    print "pin src.value.PIN :" m.numberField
    HubGateRun("go", m.numberField)
  end if
end sub



sub FinalcheckoutShowInfo()
  FinalScrCheckoutLite = CreateObject("roSGNode", "CheckoutPaymentReview")
  FinalScrCheckoutLite.id = "FinalCheckoutPaymentReview"
  FinalScrCheckoutLite.ObserveField("wasClosed", "FinalcheckoutShowInfoReturn")

  m.logger.debug("FinalcheckoutShowInfo", { checkout: m.buy.states["checkout"] })
  print "FinalcheckoutShowInfo :" m.buy.states["checkout"]

  'TEMP LOGIC START
  accountDetails = {}
  m.extra_params = {}
  if m.buy.states["purchase"] <> invalid
    if m.buy.states["purchase"].method <> invalid and m.buy.states["purchase"].method.parameters <> invalid then
      if m.buy.states["purchase"].method.parameters.extra_params <> invalid
        accountDetails = m.buy.states["purchase"].method.parameters.extra_params
        m.extra_params = m.buy.states["purchase"].method.parameters.extra_params
      end if
    end if
  end if

  'TEMP LOGIC

  FinalScrCheckoutLite.SetFields({
    buyData: {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")

      ' ya viene seteado del paso inicial de checkout
      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
      'TEMPARARY LOGIC NEED TO REMOVE
      account: ghGetChild(accountDetails, "account", "")
      extra_params: m.extra_params
    }
  })

  m.top.routerChild = { page: FinalScrCheckoutLite }
end sub


sub FinalcheckoutShowInfoReturn(event)
  scr = event.getRoSGNode()
  m.logger.debug("FinalcheckoutShowInfoReturn", { option: scr.value.option, value: scr.value })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    hubgatePinEnter()
  else if scr.value.option = "CERRAR" then
    ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
    m.logger.debug("TicketScreen cancel button clicked, exiting buy flow")
    JumpTo("out", "ok")
  else if scr.value.option = "SELECT" then
    JumpTo("checkout", "selectPaymentMethod")
  else if scr.value.option = "ACCEPT" then
    'JumpTo("checkout", "makePurchase")
    print "Extra Params:" m.extra_params
    HubGateRun("go", m.extra_params)
  else
    m.logger.error("no se reconoce la seleccion", { option: scr.value.option })
    JumpTo("checkout", "error")
  end if
end sub


sub TicketScreenLanding(data)
  print "TicketScreenLanding :" data

  ' ' Check if hidden_confirm_trans_config is set, similar to stateTicket.brs logic
  ' hiddenConfirmTransConfig = ghGetChild(m.buy.data, "hidden_confirm_trans_config", false)

  ' if hiddenConfirmTransConfig then
  '   ' Don't show ticket screen, proceed to success flow instead
  '   HubGateRun("ok")
  '   return
  ' end if
  ticketInfo = data
  if ticketInfo = invalid then ticketInfo = invalid
  TicketScreenFinal = CreateObject("roSGNode", "TicketScreen")
  TicketScreenFinal.id = "TicketScreenFinal"
  TicketScreenFinal.ObserveField("wasClosed", "FinalcheckoutShowInfoReturn")

  m.logger.debug("TicketScreenFinal", { checkout: m.buy.states["checkout"] })
  print "TicketScreenFinal :" m.buy.states["checkout"]

  TicketScreenFinal.SetFields({
    buyData: {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")
      ' ya viene seteado del paso inicial de checkout
      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
      data: data
      'TEMPARARY LOGIC NEED TO REMOVE

    }
  })

  m.top.routerChild = { page: TicketScreenFinal }
end sub