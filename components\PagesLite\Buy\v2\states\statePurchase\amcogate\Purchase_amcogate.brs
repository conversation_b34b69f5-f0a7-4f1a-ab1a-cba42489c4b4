' AMCOGATE

sub PurchaseAmcoGate()
  m.logger.debug("Purchase AmcoGate Init")

  AmcoGateRun()
end sub

sub AmcoGateRun(newState = invalid, info = {})
  m.logger.debug("AmcoGateRun", { state: newState, info: info })

  setLoading(false)

  if newState = invalid then
    AmcoGateCheck()

  else if newState = "go" then
    setLoading(true)
    AmcoGateGo()

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")

  end if
end sub

sub AmcoGateCheck()
  data = ghGetChild(m.buy, "states.purchase.paymentMethod")

  m.logger.debug("Purchase AmcoGate Check", { data: data })

  if data <> invalid then
    m.buy.states["purchase"].method = {
      parameters: {
      link: ghGetChild(data, "data.buyLink", "")
      buyToken: ghGetChild(data, "data.buyToken", "")
      object_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(data, "data.access_code", "")
    }
  }
    AmcoGateRun("go")
  else
    AmcoGateRun("missingparameters")
  end if
end sub

sub AmcoGateGo()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")

  if data <> invalid then
    apiConfirm = ghCallApi("BuyConfirmLite", "AmcoGateGo_ReturnOk", "AmcoGateGo_ReturnFails", false)
    apiConfirm.setFields(data)
    apiConfirm.control = "run"
  end if
end sub

sub AmcoGateGo_ReturnOk(event)
  data = event.getData()

  m.logger.debug("Purchase AmcoGate Go Ok", { data: data })

  AmcoGateRun("ok")
end sub

sub AmcoGateGo_ReturnFails(event)
  data = event.getData()

  m.logger.error("Purchase AmcoGate Go Fails", { data: data })

  AmcoGateRun("error", { data: data })
end sub