<?xml version="1.0" encoding="utf-8" ?>

<component name="GHCustomKeyboard" extends="Group">

  <script type="text/brightscript" uri="GHCustomKeyboard.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- INTERFAZ DE ENTRADA -->
		<field id="keyPress" type="string" alwaysNotify = "true"/>
    <function name="updateFieldFocus"/>
  </interface>

  <children>
    
  </children>

</component>
