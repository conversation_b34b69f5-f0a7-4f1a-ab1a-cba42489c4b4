<?xml version="1.0" encoding="UTF-8"?>

<component name="GHApiTask" extends="Task">

  <script type="text/brightscript" uri="GHApiTask.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Files.brs" />

  <interface>
    <!-- interfaz de salida -->
    <field id="content" type="assocarray" />
    <field id="error" type="assocarray" />
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false" />

    <!-- NO USAR -->
    <field id="flag" type="int" value="0" />

  </interface>


</component>
