sub Init()
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  m.itemPoster.width = 373
  m.itemPoster.height = 209
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  m.title.setFields({
    width: m.itemPoster.width - 20
    text: ghTranslate("vcard_access_metadata_season", "Temporada ") + " " + ghGetChild(data, "number")
    font: ghGetFont(20, "medium")
    translation: [10, 235]
    color: "0xFFFFFF50"
    horizAlign: "center"
    vertAlign: "top"
  })

  ' drawChapitas(m.itemPoster.width, m.itemPoster.height) ' , 5, 5, 5, 5)
  initTimer() ' para chapitas
end sub

sub showfocus()
  if m.top.focusPercent = 1
    ' m.itemPoster.opacity = "1"
    m.title.setFields({
      color: "0xFFFFFF"
    })

  else
    m.title.setFields({
      color: "0xFFFFFF50"
    })
  end if
end sub