sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/device/attach"

  m.api.query.Append({
    "group_id": m.top.group_id
    "purchase_id": m.top.purchase_id
    "device_id": "f7785395-3dc0-5ca4-b2bd-b4e6346221e3"
    "device_name": "roku"
    "device_so": "roku"
  })

  m.api.query.Delete("format")
end sub

sub ProcessData(res, raw)
  ' response = ghGetChild(res, "response")

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = {}
end sub