sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/user/favorited"

  m.api.query.Append({
    "user_hash": ghGetRegistry("session_userhash", "user")
    "filterlist": getFilter(ghGetRegistry("region")),
    ' "lasttouch": ghGetRegistry("lasttouch_favorited", "user"),
    "lasttouch": ghGetChild(m.global, "lastTouch.favorited"),
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

function getFilter(region = "mexico")
  filters = m.global.filter_list

  return ghGetChild(filters, region + ".filterlist")
end function

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub