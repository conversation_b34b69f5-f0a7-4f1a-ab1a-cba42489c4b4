<?xml version="1.0" encoding="UTF-8"?>

<!--
    @Public
    TimeGridView represents SGDEX view that is responsible for:
        - Rendering TimeGrid node
        - content loading with different content models
        - loading channels content when user is navigating
        - lazy loading of channels in IDLE
    @Sample
    timeGrid = CreateObject("roSGNode", "TimeGridView")
    content = CreateObject("roSGNode", "ContentNode")
    content.addfields({
        HandlerConfigTimeGrid: {
            name: "HCTimeGrid"
        }
    })
    timeGrid.content = content
    timeGrid.observeField("rowItemSelected", "OnTimeGridRowItemSelected")
    m.top.ComponentController.callFunc("show", { view: timeGrid })
-->

<component name="TimeGridView" extends="Group">
    <!-- <component name="TimeGridView" extends="SGDEXComponent" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->

    <interface>
        <field id="focus" type="boolean" value="false" onChange="onFocusChange" alwaysNotify="true" />
        <field id="viewContentGroup" type="node" />
        <field id="data" type="node" onChange="handleData" alwaysNotify="true" />
        <!-- @Private MAX_SIMULTANEOUS_LOADINGS is used to limit number of task for loading rows -->
        <field id="MAX_SIMULTANEOUS_LOADINGS" type="integer" value="3" />
        <!-- @Public Alias to the TimeGrid contentStartTime field The earliest time that the TimeGrid can be moved to -->

        <field id="contentStartTime" type="integer" alias="contentTimeGrid.contentStartTime" />
        <!-- @Public Alias to the TimeGrid maxDays field Specifies the total width of the TimeGrid in days -->
        <field id="maxDays" type="integer" alias="contentTimeGrid.maxDays" />
        <!-- @Public Updated when user selects a program from the TimeGrid Value is an array of indexes represents [channelIndex, programIndex] updated simultaneously with channelSelected and programSelected -->

        <field id="itemSelected" type="assocarray" alwaysNotify="true" />
        <field id="rowItemSelected" type="array" alwaysNotify="true" />

        <!-- @Public @WriteOnl Set grid focus to specified row Value is an integer index of the row that should be focused This field must be set after setting the content field. -->
        <field id="jumpTo" type="integer" onChange="handleJumpTo" />
        <field id="jumpToRow" type="integer" alwaysNotify="true" />
        <!-- @Public @WriteOnly Set grid focus to specified item in a row Value is an array containing the index of the row and item that should be focused This field must be set after setting the content field. -->
        <field id="jumpToRowItem" type="vector2d" alwaysNotify="true" />

        <field id="cmd" type="assocarray" alwaysNotify="true" />
        <field id="refresh" type="boolean" onChange="refresh" alwaysNotify="true" />
        <!-- interfaz interna -->
        <field id="debug" type="boolean" value="false" />
        <field id="keypressed" type="string" alwaysNotify="true"/>

    </interface>

    <script type="text/brightscript" uri="TimeGridView.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

    <children>
        <Group id="viewContentGroup" />
        <CustomTimeGrid id="contentTimeGrid" translation="[0.0, 360.0]" programTitleFocusedColor="#FFFFFF" FocusBitmapUri="pkg:/images/HD/selector.9.png" channelInfoBackgroundBitmapUri="pkg:/images/HD/fondoCanales.9.png" programBackgroundBitmapUri="pkg:/images/HD/fondoProgramas.9.png" visible="false" overlayBitmapUri=""/>
        <!-- pastTimeScreenBlendColor="#AC0000" Cambia el color que indica el tiempo actual de reproducción del contenido, con un blend -->
        <!-- programBackgroundBitmapUri="pkg:/images/HD/PIllTopNavBarEn-bg.9.png" -->
        <!-- Now Bar permite cambiar la barra que indica el momento de reproducción del contenido-->
        <!-- channelInfoBackgroundBitmapUri="pkg:/images/HD/pruebita20.9.png" -->
        <!-- programBackgroundBitmapUri="pkg:/images/HD/pruebita4.9.png" -->
        <BusySpinner id="spinner" />
    </children>
</component>
