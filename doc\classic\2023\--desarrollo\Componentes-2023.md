# Componentes 2023



## GHToast

*Goose*



*Popup de mensaje con autocierre y con foco opciional.*

### Parámetros

- [optativo] Time: numeric (0) Tiempo de autocerrado, si está en cero desactivado
- [optativo] Close: boolean (true) Cierre con botón, con foco (activo si se le hace foco)



- [optativo] Títle: string (“***”) Si es el default no se muestra.
- [optativo] Body: string (“***”) Si es el default no se muestra.
- [optativo] Ok; string (“ACEPTAR”) Texto del boton de cierre.



- [optativo] BackgroundImage: url (imagen.9 para usar de fondo) default a un gris usado



### Diseño

- Diseño básico standar (Título / mensaje / ok)
- Heredable para diseño.

### Para uso en 

- TimeShift





## GHSubMenu

*Jose*
