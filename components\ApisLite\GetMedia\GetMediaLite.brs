' GetMediaLite
' https://app.swaggerhub.com/apis/ClaroVideo/getmedia/1.00#/default/getmedia

sub DataInit()
  m.top.debug = false

  m.api.url = m.config.mfwk.host + "/services/player/v1/getmedia"

  m.api.query.delete("api_version")
  m.api.query.delete("HKS")
  m.api.query.Append({
    "device_id": "f7785395-3dc0-5ca4-b2bd-b4e6346221e3"
    "stream_type": m.top.streamType
    "quality": "HD"
    "appversion": ghGetAppVersion()
    "group_id": m.top.groupId
    "content_id": m.top.contentId
    "preview": m.top.preview
    "user_id": ghGetRegistry("user_id", "user"),
    "user_token": ghGetRegistry("user_token", "user"),
    "payway_token": m.top.paywayToken,
  })
end sub

sub ProcessData(video, raw)
  'Hubo un contenido que salia status 200 pero traia un mensaje de error por eso esta condicion
  errors = ghGetChild(video, "errors")
  if errors <> invalid then
    video.raw = raw
    m.top.error = video
    return
  end if

  if video = invalid then
    m.top.error = "error html"
    return
  end if

  'Armado customData para la licencia (VOD y LIVE)
  ' Si otro proveedor da error en la licencía, consultar con Player!
  if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "HBO"
    if ghGetChild(video, "response.group.common.extendedcommon.media.islive") = "1" 'esto viene en la getmedia
      ' HBO LIVE se arma así como las peliculas que no son HBO
      customData = DataCustom(video.response.media.challenge, video.entry.device_id)
    else
      ' HBO EN PELICULA/SERIE, el custom data se tiene que pasar así
      customData = ghGetChild(video, "response.media.challenge")
    end if
  else if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "PICARDIA2"
    ' Para picardía el customData va vacio
    customData = ""
  else
    'Resto de peliculas/series se arma así
    customData = DataCustom(video.response.media.challenge, video.entry.device_id)
  end if

  item = {}
  item.groupId = m.top.groupId
  item.description = ghGetChild(video, "response.group.common.description")
  item.status = "Duración: " + ghGetChild(video, "response.group.common.duration", "")
  item.hdPosterURL = ghGetChild(video, "response.group.common.image_large")
  'item.title = ghGetChild(video, "response.group.common.title")
  item.id = video.id
  item.url = ghGetChild(video, "response.media.video_url")
  item.StreamFormat = "smooth"
  item.EncodingType = "PlayReadyLicenseAcquisitionAndChallenge"
  if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "PICARDIA2"
    item.EncodingKey = ghGetChild(video, "response.media.server_url", "")
  else
    ' pruebas
    ' if m.top.groupId = "1039284" then 'tyc sport con error de certificado
    '   item.EncodingKey = ghGetChild(video, "response.media.server_url") + "%%%" + customData + "asdf"
    ' else
    item.EncodingKey = ghGetChild(video, "response.media.server_url", "") + "%%%" + customData
    ' end if
  end if

  ' ----------------------------
  ' tracking
  item.trackInfo = ghGetChild(video, "response.tracking")
  item.rollingcreditstime = ghGetChild(video, "response.group.common.extendedcommon.media.rollingcreditstime", 0)
  ' ----------------------------
  ' languajes
  item.languages = ghGetChild(video, "response.language.options", [])

  ' ----------------------------
  ' OmitirIntro
  item.trackInfo.omitirIntro = {
    languages: getOmitirInfoData(ghGetChild(video, "response.group.common.extendedcommon.media.language.options.option", []))
  }

  ' obtener content_id
  item.contentId = ""
  cant = item.languages.count()
  for x = 0 to cant - 1
    lang = ghGetChild(item.languages, "#" + x.toStr())
    if lang.is_current = true then
      contentId = ghGetChild(lang, "content_id")
      if contentId <> invalid then
        item.contentId = contentId.toStr()
      end if
    end if
  end for

  ' datos de la serie
  datSerie = ghGetChild(video, "response.group.common.extendedcommon.media.serie")

  ' ----------------------------
  if m.top.preview = "0" then
    item.title = ghGetChild(video, "response.group.common.title")
    item.BookmarkPosition = ghGetChild(video, "response.media.initial_playback_in_seconds", 0) ' -- GOOSE
    item.trackInfo.hasEndCard = invalid
  else
    ' Titulos serie trailer - serie episodio - pelicula
    if datSerie <> invalid then
      item.title = ghGetChild(video, "response.group.common.extendedcommon.media.serie.title")
    else
      item.title = ghGetChild(video, "response.group.common.title")
    end if
    item.BookmarkPosition = 0
    item.trackInfo.hasEndCard = false
  end if

  ' next
  item.next_group = ghGetChild(video, "response.next_group")
  item.stream_uuid = ghGetChild(video, "response.stream_uuid")

  ' ----------------------------
  ' Se reemplaza el dominio para poder levantar los .bif en test
  trickplay = ghGetChild(video, "response.group.common.image_trickplay")
  ' newUrl = ghReplaceStr(trickplay, "https://clarovideocdn4.clarovideo.net", "http://img-amazon-roku.clarovideo.com")
  item.hdbifurl = trickplay

  ' // para tracker
  item.is_trailer = m.top.preview

  datSerie = ghGetChild(video, "response.group.common.extendedcommon.media.serie", invalid)

  ' ----------------------
  ' // YOUBORA
  ' ----------------------
  item.ybPublishyear = ghGetChild(video, "response.group.common.extendedcommon.media.publishyear", "****")
  item.ybGenres = _getGenres(ghGetChild(video, "response.group.common.extendedcommon.genres.genre", []))
  ybProfile = ghGetChild(video, "response.group.common.extendedcommon.media.profile.hd.enabled", "")
  if ybProfile = "true" then
    item.ybRendition = "HD"
  else
    item.ybRendition = "SD"
  end if
  ' Tipo de Contenido visualizado
  ' Si es VOD -- player/getmedia response.group.common.extendedcommon.format.name
  ' Si es Canal -- player/getmedia response.group.common.extendedc.ommon.format.name
  item.tipo_contenido_visualizado = ghGetChild(video, "response.group.common.extendedcommon.format.name")

  ' TITULO -----
  if ghGetChild(video, "response.group.common.extendedcommon.media.islive") = "1" ' en vivo
    ' Cuando es Live
    '   title: group_id del canal-nombre del canal
    '   title2: id del programa-nombre del programa (se seteará de manera asíncrona). El impacto es que no se podrá filtrar.
    '     Para implementarse, se setearía el dato después de llamar la EPG: En el 1er envío, llegaría el title2 vacío -  Se mostrará como Undefined
    '     En el 2do envío, se llama la línea del plugin.setoptions a gregando nuevamente el parámetro:
    '       title2: events.name
    '     NOTA: En el caso de que puedan enviar plugin.
    '       setoptions cuando acabe un programa y empiece otro durante la misma reproducción, en la vista de detalle de la reproducción, negocio podrá ver los diferentes programas que vió el usuario durante esa reproducción.
    liveId = ghGetChild(video, "response.group.common.id")
    liveTitle = ghGetChild(video, "response.group.common.title")
    item.ybTitle = liveId + "-" + liveTitle' YB
    item.ybTitleEpisode = ""
  else
    ' VOD
    if datSerie <> invalid then ' es serie
      ' Cuando es Serie
      '   title: id de la serie-nombre de la serie
      '   title2: id de la temporada-número de temporada-group_id del episodio-nombre de episodio
      item.ybSerie = true
      serieId = ghGetChild(video, "response.group.common.extendedcommon.media.serie.id", "")
      serieTitle = ghGetChild(video, "response.group.common.extendedcommon.media.serie.title", "")
      seasonId = ghGetChild(video, "response.group.common.extendedcommon.media.serieseason.id", "")
      seasonNumber = ghGetChild(video, "response.group.common.extendedcommon.media.serieseason.number", "")
      episodeId = ghGetChild(video, "response.group.common.id", "")
      episodeTitle = ghGetChild(video, "response.group.common.title", "") ' NO ESTA!!!
      item.ybTitle = serieId + "-" + serieTitle ' YB
      item.ybTitleEpisode = seasonId + "-" + seasonNumber + "-" + episodeId + "-" + episodeTitle ' YB
      '// PREVIEW ES IGUAL
    else
      ' Cuando es Película
      '   title: group_id de la pelicula-nombre de la película
      '   title2: vacío aparecerá cómo Undefined.
      item.ybSerie = false
      peliId = ghGetChild(video, "response.group.common.id", "[falta commonid]")
      peliTitle = ghGetChild(video, "response.group.common.title", "[falta common.title]")
      item.ybTitle = peliId + "-" + peliTitle ' YB
      item.ybTitleEpisode = ""
      '// PREVIEW ES IGUAL
    end if
  end if
  item.ybData = ghGetChild(video, "response.group.common.extendedcommon", {})
  ' ----------------------

  'lasttouch
  lasttouch = ghGetChild(video, "response.user.lasttouch")
  if lasttouch <> invalid
    ' ghSetRegistry("lasttouch_seen", lasttouch, "user")
    updateGlobalArray("lasttouch", { favorited: lasttouch })
  end if

  if m.top.debug then
    print ghLogHead();"GetItemData -- item:";item
    print ghLogHead();"**********************************"
  end if
  ' ----------------------------
  m.top.content = item
end sub


' ----------------------------
' OmitirIntro
function getOmitirInfoData(langs)
  data = {}
  for l = 0 to langs.Count() - 1
    iId = ghGetChild(langs[l], "option_id")
    iStart = ghGetChild(langs[l], "intro_start_time")
    iFinish = ghGetChild(langs[l], "intro_finish_time")
    ' me conviene que esten todos...
    ' if iId <> invalid and iStart <> invalid and iFinish <> invalid then
    data[iId] = { intro_start_time: iStart, intro_finish_time: iFinish }
    ' end if
  end for
  return data
end function
' ----------------------------


' Se podría pasar a UTILS
function DataCustom(challenge, deviceid)
  cDataString = "{" + chr(34) + "customdata" + chr(34) + ":" + challenge + "," + chr(34) + "device_id" + chr(34) + ":" + chr(34) + deviceid + chr(34) + "}"
  ba = CreateObject("roByteArray")
  ba.FromAsciiString(cDataString)
  customData = ba.ToBase64String()

  return customData
end function
function _getGenres(genres)
  genreString = ""
  ' genres = ghGetChild(video, "response.group.common.extendedcommon.genres.genre", [])
  if genres.Count() > 0 then
    for tmpG = 0 to genres.Count() - 1
      genreString += genres[tmpG].name + ", "
    next
    if genreString <> "" then
      genreString = Left(genreString, Len(genreString) - 2)
    end if
  end if
  return genreString
end function
