<?xml version="1.0" encoding="utf-8" ?>

<component name="ScrPinInputV2" extends="Page">

  <script type="text/brightscript" uri="ScrPinInputV2.brs" />

  <interface>
    <field id="textTitle" type="string" value="" onChange="componentsInit" />
    <field id="textSubTitle" type="string" value="" onChange="componentsInit" />
    <field id="textButton" type="string" value="" onChange="componentsInit"/>
    <field id="hideSendpin" type="boolean" value="false" onChange="componentsInit"/>
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <!-- textos -->
    <Label id="title" focusable="false" translation="[0,64]" width="1280" height="48" text="*" />
    <Label id="descrip" focusable="false" translation="[0,132]" wrap= "true" width="1280" height="80" text="*" />
    <!-- Botonera -->
    <GHButtonGroup id="botonera" layout="map" orientation="vertical">
      <!-- <GHInputMKH id="cvv" placeholder="" translation="[400,240]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="true" width="504" height="72" titleColor="0xFFFFFF" messageColor="0xFFFFFF"/> -->
      <GHInput id="cvv" placeholder="" translation="[400,240]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="true" width="504" height="72" title="Inicia sesión" titleColor="0xFFFFFF" messageColor="0xFFFFFF"/>
      <GHButton id="accept" text="ACCEPT" value="ACCEPT" translation="[400,424]" color="0xFFFFFF" selColor="#FFFFFF" width="504" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
      <GHButton id="sendpin" text="RECUPERA TU PIN" value="SENDPIN" translation="[400,487.33]" color="0xFFFFFF" selColor="#FFFFFF" width="504" height="72" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" />
    </GHButtonGroup>
    <!-- PopUps -->
    <!-- <GHError id="error" /> -->
  </children>

</component>
