<?xml version="1.0" encoding="UTF-8"?>

<component name="GHApiCall" extends="Task">

  <script type="text/brightscript" uri="GHApiCall.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Files.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />

  <interface>
    <!-- interfaz de salida -->
    <field id="content" type="assocarray" />
    <field id="error" type="assocarray" />
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false" />
    <field id="showCurl" type="boolean" value="true" />
  </interface>


</component>
