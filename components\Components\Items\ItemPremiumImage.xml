<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemPremiumImage" extends="Group">
  <script type="text/brightscript" uri="ItemPremiumImage.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>

    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" onChange="showfocus"/>
    <field id="rowHasFocus" type="boolean" onChange="showfocus"/>

    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemPoster"/>
    <Label id="title" visible="true" translation="[0,10]" width="185" color="0xCCCCCC" wrap="true" height="30" lineSpacing="0" vertAlign="bottom" />
          <LayoutGroup id="focoCarruselInfo" translation="[800,472]"  horizAlignment= "center" vertAlignment= "center" layoutDirection="vert" itemSpacings="[-84.5]" visible="false">
            <Rectangle id="fondoInfo2" visible="true" color="#FFFFFF" width= "508" height="90"/>
            <Rectangle id="fondoInfo1" visible="true" color="#121212" width= "500" height="80.5"/>
        </LayoutGroup>
    <LayoutGroup id="infoCarrusel" translation="[800,472]"  horizAlignment= "center" vertAlignment= "center" layoutDirection="vert" itemSpacings="[-63]" visible="false">
       <Rectangle id="fondoInfo" visible="true" color="#981C15" width= "493" height="72"/>
       <Label id="info" visible="true" text="SUSCRIBIRME MENSUAL" translation="[-110,24]" width="461" color="0xCCCCCC" height="60" lineSpacing="0" horizAlign= "center" vertAlign="center" />
    </LayoutGroup>
    <GHProgressBar id="progress" visible="false" translation="[0,50]" backColor="#2C2C2C" barColor="#DE1717"/>
    <GHPanel id="thePanels"/>
  </children>

</component>