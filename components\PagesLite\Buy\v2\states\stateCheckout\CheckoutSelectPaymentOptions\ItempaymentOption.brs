sub Init()
  m.top.debug = true
  m.container = m.top.findNode("container")
  m.contentGroup = m.top.findNode("contentGroup")
  m.label = m.top.findNode("label")
  m.image = m.top.findNode("image")
  m.image.observeField("loadStatus", "checkImageSize")
  m.label.observeField("text", "alignContent")
end sub

sub showContent()
  ' print "inside your component" m.top.width, m.top.height
  content = m.top.itemContent
  ' print "itemContentChanged :"content
  m.container.width = handlingSizeForHD(276)
  m.container.height = handlingSizeForHD(276)
  m.label.width = handlingSizeForHD(276)


  if m.top.debug then print ghLogHead();"itemContentChanged - gateway=";gateway
  gateway = ghGetChild(m.top.itemContent, "data.gateway")
  region = ghGetRegistry("region")
  'print "gateway :" gateway

  'Label Node
  m.itemText = ghTranslate("MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + region + "_" + gateway, "")
  if m.itemText = "MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + region + "_" + gateway then
    m.itemText = ghTranslate("MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + gateway, "")
  end if

  'print "itemText :" m.itemText

  'Logo Node
  m.itemLogo = ghGetAsset("MDP_AgregarMediodepago_Tarjeta_Logo_" + region + "_" + gateway, "invalid")
  if m.itemLogo = "invalid" then
    m.itemLogo = ghGetAsset("MDP_AgregarMediodepago_Tarjeta_Logo_" + gateway, "invalid")
    if m.itemLogo = "invalid"
      'm.itemLogo = ghGetAsset("select_payment_method_default_placeholder", "pkg:/images/Placeholder_SelectorMDP-FHD.png")
    end if
  end if

  m.image.uri = m.itemLogo
  m.label.text = m.itemText

  m.label.setFields({
    font: ghGetFont(handlingSizeForHD(24), "bold")
    text: m.itemText
  })
end sub

sub checkImageSize(event as object)
  if m.image.loadStatus = "ready"
    m.image.width = handlingSizeForHD(m.image.bitmapWidth)
    m.image.height = handlingSizeForHD(m.image.bitmapHeight)
  end if
  alignContent()
end sub

sub alignContent()
  containerBounds = m.container.boundingRect()
  spacing = 10
  labelHeight = 0
  imageHeight = 0
  contentHeight = 0

  ' Determine label height
  if m.label.visible
    if m.label.text <> ""
      labelBounds = m.label.boundingRect()
      labelHeight = labelBounds.height
    end if
  end if

  ' Determine image height
  if m.image.visible
    if m.image.loadStatus = "ready"
      imageBounds = m.image.boundingRect()
      imageHeight = imageBounds.height
    end if
  end if

  ' Calculate total content height
  if labelHeight > 0 and imageHeight > 0
    contentHeight = labelHeight + spacing + imageHeight
  else
    contentHeight = labelHeight + imageHeight
  end if

  startY = containerBounds.height / 2 - contentHeight / 2

  ' Align label
  if labelHeight > 0
    labelX = containerBounds.width / 2 - m.label.boundingRect().width / 2
    m.label.translation = [labelX, startY]
    startY = startY + labelHeight + spacing
  end if

  ' Align image
  if imageHeight > 0
    imageX = containerBounds.width / 2 - m.image.boundingRect().width / 2
    m.image.translation = [imageX, startY]
  end if
end sub




