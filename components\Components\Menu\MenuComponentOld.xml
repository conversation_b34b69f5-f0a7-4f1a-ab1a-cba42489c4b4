<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="MenuComponent" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="MenuComponentOld" extends="Group">

  <script type="text/brightscript" uri="MenuComponentOld.brs" />
  <script type="text/brightscript" uri="MenuComponentFunctions.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Screen.brs" />

  <interface>
    <field id="menuSelected" type="string" value="" alwaysNotify="true" onChange="menuSelected" />
    <field id="action" type="assocarray" />
    <field id="backSelected" type="boolean" value="false" />
    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- las x se manejan desde codigo -->
    <Poster id="menuBack" translation="[0,10.5]" width="100" height="48" />
    <Poster id="logo" translation="[0,20]" width="123" height="24" loadDisplayMode="scaleToFit"/>
    <GHMenu id="menu" translation="[0,17]" exitDown="true" layoutDirection="horiz" horizAlignment="left" itemSpacings="7" />
  </children>

</component>
