<?xml version="1.0" encoding="utf-8" ?>

<component name="LongPressEliminar" extends="Group">

  <script type="text/brightscript" uri="LongPressEliminar.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="translation" type="array" value="[0,0]"/>
    <field id="alignment" type="string" value="center"/>
    <!-- interfaz de entrada -->
    <field id="BackGround" type="string" value="pkg://images/TopNavBarBG.png" />
    <field id="TitleKey" type="string" value="generic_alert_pressAndHold_label" />
    <field id="DescriptionKey" type="string" value="deleteContent_alert_pressAndHold_label" />
    <field id="IconKey" type="string" value="generic_alert_pressAndHold_img" />
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Poster id="background"/>
    <LayoutGroup translation="[276,170]" layoutDirection="horiz" itemSpacings="[55]">
    </LayoutGroup>
  </children>

</component>
