<component name="SearchPage" extends="Page" initialFocus="keyboard">

    <script type="text/brightscript" uri="SearchPage.brs" />

    <interface>
        <field id="noResultsLabelText" type="string" value="No hay resultados!" />
        <field id="query" type="string" alwaysNotify="true" />
        <field id="contenido" type="assocarray" />
    </interface>

    <children>
        <MenuComponent id="menu" translation="[40,20]" />
        <GHInputMKH id="keyboard" translation="[280, 100]" />
        <GHRowList id="theGrid" visible="true" translation="[-100, 330]" />
        <Label id="message" />
        <GHLoading id="loading" visible="false" backColor="#00000088" />
    </children>

</component>
