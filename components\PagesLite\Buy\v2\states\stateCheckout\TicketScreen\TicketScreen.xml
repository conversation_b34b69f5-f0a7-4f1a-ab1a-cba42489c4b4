<?xml version="1.0" encoding="utf-8"?>
<component name="TicketScreen" extends="Page">
	<script type="text/brightscript" uri="TicketScreen.brs" />
	<interface>
		<!-- interfaz de entrada -->
		<field id="buyData" type="assocarray" onChange="updateBuyData" />
		<!-- interfaz de salida -->
		<field id="value" type="assocarray" />
	</interface>
	<children>
		<!-- Posters -->
		<Poster id="logoAddon" focusable="false" visible="true" translation="[595,165]" />
		<Poster id="movieImage" width="160" height="298" focusable="false" translation="[350,187]" />
		<!-- Labels -->
        <Label id="title" focusable="false" translation="[0,94]" width="1280" height="32" text="" />
        <!-- <LayoutGroup id="verticalOrientacion" translation="[500,310]" layoutDirection="horiz" vertAlignment= "left" horizAlignment= "left" itemSpacings="[3]" visible="true"> -->
		<Label id="total" visible="true" translation="[510,340]" width = "100" color="0xCCCCCC" wrap="false" lineSpacing="0" />
		<Label id="buyPrice" focusable="false" translation="[620,340]" />
		<Label id="periodo" visible="true" translation="[650,340]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="left" vertAlign="center" width="50" />
		<!-- </LayoutGroup> -->
        <Label id="oferta" visible="true" translation="[510,230]" color="#00A9FF" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
        <Label id="textoInformativo" visible="true" translation="[510,263]" color="#0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="left" vertAlign="left"/>

		<Label id="IvaInclude" visible="true" translation="[690,340]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="left" vertAlign="center" width="200" />
		<Label id="mediodepago" text="Medio de pago: " focusable="false" translation="[510,367]" width="1280" height="24" />
		<Label id="payMethodLabel" text="Vigencia: " focusable="false" translation="[510,400]"  height="24" />
		<Label id="Vigencia" focusable="false" translation="[510,430]" width="100" height="24" />
		<Label id="VigenciaHour" focusable="false" translation="[620,430]" width="1280" height="24" />
		<Label id="VigenciaReproducer" focusable="false" translation="[510,465]" width="1280" height="24" />
		<Label id="expirelabel" focusable="false" text=": " translation="[620,430]" width="1280" height="24" />

		<!-- Botonera -->
		<GHButtonGroup id="botonera" layout="childs" orientation="vertical">
			<GHButton id="accept" text="ACEPTAR" value="ACCEPT" translation="[460,500]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
			<GHButton id="cancelbtn" text="CERRAR" value="CERRAR" translation="[460,575]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#2E303D " selBackColor="#2E303D " focusColor="0xFFFFFF" />
		</GHButtonGroup>
	</children>
</component>
