<?xml version="1.0" encoding="utf-8" ?>

<component name="LevelComponent" extends="Page">
  <script type="text/brightscript" uri="LevelComponent.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="title" type="string" value="" alwaysNotify="true" />
    <field id="node" type="string" value="invalid" onChange="updateNode" alwaysNotify="true" />
    <field id="reloadLevel" type="boolean" value="false" alwaysNotify="true" />
    <field id="reloadLevelUser" type="boolean" value="false" alwaysNotify="true" />
    <!-- manejo interno -->
    <field id="vista" type="string" value="*" onChange="TurnVista" />
    <!-- interfaz de salida -->
    <field id="contenido" type="assocarray" alwaysNotify="true" />
    <field id="itemFocused" type="assocarray" alwaysNotify="true" />
  </interface>

  <children>
    <LevelGridComponent id="grilla" visible="false"/>
    <LevelListComponent id="lista" visible="false"/>
  </children>
</component>
