sub Init()
    m.top.debug = true

    m.map = { "buttons": { "up": invalid, "right": invalid, "down": invalid, "left": invalid, "default": true } }

    m.buttons = m.top.findNode("buttons")
    m.buttons.ObserveField("selected", "ButtonSelected")
    m.buttons.ObserveField("backSelected", "BackSelected")

    m.loading = m.top.findNode("loading")
    m.headerTitle = m.top.findNode("headerTitle")
    m.title = m.top.findNode("title")
    m.whatIncludesButton = m.top.findNode("whatIncludesButton")
    m.subscribeButton = m.top.findNode("subscribeButton")
    m.cancelButton = m.top.findNode("cancelButton")
    m.priceAmount = m.top.findNode("priceAmount")
    m.pricebar = m.top.findNode("pricebar")
    m.trailPeriod = m.top.findNode("trailPeriod")
    m.description = m.top.findNode("description")
    m.infoadd = m.top.findNode("infoadd")
    m.priceRow = m.top.findNode("priceRow")

    m.description.setFields({ font: ghGetFont(18, "regular") })
    m.trailPeriod.setFields({ font: ghGetFont(18, "regular") })
    m.priceAmount.setFields({ font: ghGetFont(24, "bold") })
    m.pricebar.setFields({ font: ghGetFont(24, "bold") })

    m.trailPeriod.ObserveField("text", "adjustPositionContent")
    m.description.ObserveField("text", "adjustPositionContent")
    m.pricebar.ObserveField("text", "alignLayGroup")
    m.title.ObserveField("loadStatus", "alignContent")
    m.headerTitle.ObserveField("text","alignContent")
end sub

sub updateFieldFocus()
    turnFocusTo("buttons")
end sub

function onKeyEvent(key, press) as boolean
    handled = false

    if press then
        m.logger.debug(["keyEvent: ", key])
        if key <> "back" then
            changeFocusBasedOnKey(key)

            handled = true
        end if
    end if

    return handled
end function

sub ButtonSelected(event)
    child = event.getRoSGNode()

    m.logger.debug("ButtonSelected -- ", { value: child.value })

    if child.selected then
        m.top.value = child.value

        if child.value = "OK" then
            m.logger.debug("ButtonSelected -- OK")
            m.top.selected = m.top.data
        end if

        m.top.close = true
    end if
end sub

sub BackSelected(event)
    child = event.getRoSGNode()

    if child.backSelected then
        child.backSelected = false
        m.top.value = ""

        m.top.close = true
    end if
end sub

sub handleData(event)
    data = event.getData()

    m.headerTitle.text = ghTranslate("subscription_modal_title_label", "Para poder acceder a este canal, contrata: ")

    bonus = ghGetChild(data, "bonus", "")
    family = ghGetChild(data, "family", "")

    'print "value :", bonus

    'm.title.uri = ghGetAsset(family + "_subscription_logo", "")
    m.title.uri = ghGetAsset("transactional_" + family + "_logo")
    m.priceAmount.text = ghGetChild(data, "currency", "") + ghGetChild(data, "price", "") + " " + ghTranslate("subscription_costTaxIcluded_complement_label_1", "")
    m.pricebar.text = ghTranslate("subscription_periodicity_month_label_1", "") + getTraslate(ghGetChild(data, "periodicity", ""))
    if ghTranslate(bonus, "") <> invalid and ghTranslate(bonus, "") <> "" then
        m.trailPeriod.text = ghTranslate("transactional_" + bonus + "_subscription_tryInvite_description", "") + ghTranslate(bonus, "")
    end if
    m.description.text = ghTranslate(family + "_scope_description_label", "")

    m.subscribeButton.text = ghTranslate("subscription_modal_option_button_checkout", "CONTRATAR")
    m.cancelButton.text = ghTranslate("subscription_modal_option_button_cancel ", "CANCELAR")

end sub

function getTraslate(text as string)
    return ghReplaceStr(ghReplaceStr(text, "month", "mes"), "year", "año")
end function

function adjustPositionContent(event)
    if m.top.debug then print ghLogHead();"adjustPositionContent -- ";event.getData()
    node = event.getRoSGNode()
    boundingRect = node.boundingRect()
    uiResolution = ghGetGlobalWH()
    if boundingRect <> invalid and uiResolution <> invalid
        centerX = (uiResolution.w - boundingRect.width) / 2
        centerY = ghYtoAbstract(node.translation[1])
        node.translation = [centerX, centerY]
    end if
end function

function alignLayGroup()
    adjustPositionContentNode(m.priceRow)
end function

function alignContent(event)
    data = event.getData()
    node = event.getRoSGNode()
    adjustPositionContentNode(node)
end function

function adjustPositionContentNode(Node)
    if m.top.debug then print ghLogHead();"adjustPositionContent -- ";Node
    node = Node
    boundingRect = node.boundingRect()
    uiResolution = ghGetGlobalWH()
    if boundingRect <> invalid and uiResolution <> invalid
        centerX = (uiResolution.w - boundingRect.width) / 2
        centerY = ghYtoAbstract(node.translation[1])
        node.translation = [centerX, centerY]
    end if
end function