' -----------------------------
' GHButtonGroup
' -----------------------------

function Init()
  m.top.debug = true
  if m.top.debug then print ghLogHead("YB");"Init"

  m.top.logging = true ' debug !!!
end function

' EVENTS
sub updateDebugGlobal(event)
  data = event.getData()
  if m.global.YouboraLogActive = invalid then
    if m.top.debug then print ghLogHead("YB");"updateDebugGlobal >> creating global."
    m.global.addField("YouboraLogActive", "boolean", false)
  end if
  if m.top.debug then print ghLogHead("YB");"updateDebugGlobal >> " ;data
  m.global.YouboraLogActive = data
end sub
