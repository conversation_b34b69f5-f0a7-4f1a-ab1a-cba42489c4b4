' PbiLite
' https://app.swaggerhub.com/apis-docs/ClaroVideo/v1_purchasebuttoninfo/2.0.0

sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/payway/v2/purchasebuttoninfo"

  m.api.query.delete("api_version")
  m.api.query.delete("HKS")
  m.api.headers.Append({ "user-token": ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    "object_type": m.top.object_type,
    "user_id": ghGetRegistry("user_id", "user"),
    ' "group_id": m.top.group_id, ' solo para tipo G
  })
  if m.top.object_type = "G" then
    m.api.query.Append({ "group_id": m.top.group_id })
  end if

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw= ", { raw: raw })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })
  if res = invalid then
    m.top.content = {}
    return
  end if

  response = ghGetChild(res, "response")

  m.logger.debug("entry:", { entry: ghGetChild(res, "entry") })
  m.logger.debug("body:", { res: res })

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub