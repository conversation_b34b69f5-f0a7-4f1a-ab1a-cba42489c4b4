' Copyright (c) 2018 Roku, Inc. All rights reserved.

sub Init()
  ' m.top.debug = true
  m.stacks = []

  ' viewManager = CreateObject("roSGNode", "ViewManager")
  viewManager = CreateObject("roSGNode", "RouterManager")
  m.top.InsertChild(viewManager, 0)

  m.top.observeField("addStack", "OnAddStackChanged")
  m.top.observeField("removeStack", "OnRemoveStackChanged")
  m.top.observeField("selectStack", "OnSelectStackChanged")
  m.top.addStack = "default"

  m.top.observeField("allowCloseChannelOnLastView", "OnAllowCloseChannel")
  m.top.allowCloseChannelOnLastView = true
  if m.top.debug then print ghLogHead("ROUTER");"Init"
end sub

function Show(config as object)
  if m.top.debug then print ghLogHead("ROUTER");"Show"
  if m.top.debug then print ghLogHead("ROUTER");"Show -- config:";config
  if GetInterface(config, "ifAssociativeArray") = invalid or config.view = invalid then
    if m.top.debug then print ghLogHead("ROUTER");"Show -- Error: Component controller, received wrong config"
    return invalid
  end if

  View = config.view
  contentManager = config.contentManager

  data = {}

  if config.setFocus = invalid
    data.setFocus = true
  else if type(config.setFocus) = "roBoolean"
    data.setFocus = config.setFocus
  else
    if m.top.debug then print ghLogHead("ROUTER");"Show -- Error: Component controller, received wrong config. Field setFocus must be Boolean."
  end if

  subTypesSupported = {
    GridView: "ContentManager",
    SearchView: "ContentManager",
    'TimeGridView: "ContentManagerTimeGrid"
  }
  cmTypesSupported = {
    grid: {
      nodeType: "ContentManager"
      configName: "HandlerConfigGrid"
    }
    timegrid: {
      nodeType: "ContentManagerTimeGrid"
      configName: "HandlerConfigTimeGrid"
    }
  }

  subtype = View.subtype()
  parentType = View.parentSubtype(View.subtype())
  if m.top.debug then print ghLogHead("ROUTER");"parentType=";parentType
  cmType = View.contentManagerType
  if m.top.debug then print ghLogHead("ROUTER");"Show -- View.contentManagerType=";View.contentManagerType

  ' Create content manager for SGDEX views
  if subTypesSupported[subtype] <> invalid
    contentManager = CreateObject("roSgNode", subTypesSupported[subtype])
    if subtype = "SearchView"
      contentManager.configFieldName = "HandlerConfigSearch"
    end if
    ' else create content manager for custom view if it' supported
  else if cmType <> invalid and GetInterface(cmType, "ifString") <> invalid and cmTypesSupported[cmType] <> invalid
    contentManager = CreateObject("roSgNode", cmTypesSupported[cmType]["nodeType"])
    contentManager.configFieldName = cmTypesSupported[cmType]["configName"]
  else if cmType <> invalid and cmTypesSupported[cmType] = invalid
    if m.top.debug then print ghLogHead("ROUTER");"Show -- [SGDEX] Content Manager was not created. Please specify correct value for contentManagerType view interface."
  else
    if m.top.debug then print ghLogHead("ROUTER");"Show -- [SGDEX] Content Manager was not created"
  end if

  if contentManager <> invalid
    contentManager.Parent = m.top.getparent()
    contentManager.callFunc("setView", View)
    data.contentManager = contentManager
  end if

  m.top.ViewManager.callFunc("runProcedure", {
    fn: "addView"
    fp: [View, data]
  })

  ' buttonBar = m.top.GetScene().buttonBar
  ' if data.setFocus = false and buttonBar.visible
  '   if not buttonBar.IsInFocusChain()
  '     buttonBar.SetFocus(true)
  '   else if subtype = "MediaView" and view.mode = "audio"
  '     timer = m.top.CreateChild("Timer")
  '     timer.duration = 0.001
  '     timer.repeat = false
  '     timer.control = "start"
  '     timer.ObserveField("fire", "OnMediaTimerFired")
  '   end if
  ' end if
  if contentManager <> invalid then
    contentManager.control = "start"
  end if
  'do other stuff for proper registering and unregistering events
end function

function Replace(config as object)
  m.top.ViewManager.callFunc("runProcedure", {
    fn: "replaceCurrentView"
    fp: [config.view, config.data]
  })
end function

sub OnMediaTimerFired()
  if m.top.debug then print ghLogHead("ROUTER");"OnMediaTimerFired"
  ' buttonBar = m.top.GetScene().buttonBar
  ' buttonBar.SetFocus(true)
end sub

sub OnCurrentViewChange()
  stack_id = m.top.activeStack
  stack = m.stacks.Peek()
  if m.top.debug then print ghLogHead("ROUTER");"OnCurrentViewChange | activeStack=";stack_id;" | stack=";ghGetChild(stack, "id")
  if stack <> invalid
    if stack_id = stack.id
      m.top.currentView = stack.currentView
    end if
  end if
end sub

sub OnAllowCloseChannel(event as object)
  allowCloseChannel = event.getData()
  if m.top.debug then print ghLogHead("ROUTER");"OnAllowCloseChannel =";allowCloseChannel
  ' need to pass this flag to View stack; it will set scene.exitChannel to true if no Views left
  m.top.ViewManager.allowCloseChannelWhenNoViews = allowCloseChannel
end sub

function onkeyEvent(key as string, press as boolean) as boolean
  if m.top.debug then print ghLogHead("ROUTER");"onkeyEvent"
  handled = false
  if press
    ' buttonBar = m.top.findNode("buttonBar")
    ' if buttonBar.visible
    ' handled = handleButtonBarKeyEvents(buttonBar, key)
    if key = "back"
      handled = closeView()
    end if
  end if

  return handled
end function

' function handleButtonBarKeyEvents(buttonBar as object, key as string) as boolean
'   if m.top.debug then print ghLogHead("ROUTER");"handleButtonBarKeyEvents"
'   ' handle switch focus between the ButtonBar and a showed view
'   currentView = m.top.currentView
'   if currentView <> invalid
'     if (key = "back" or (key = "up" and buttonBar.alignment = "top")) and currentView.Subtype() = "MediaView" and currentView.mode = "audio" then
'       return handleMediaViewBBKeyEvents(currentView, buttonBar, key)
'     else if key = "back"
'       if buttonBar.isInFocusChain()
'         return closeView()
'       else if currentView.isInFocusChain()
'         buttonBar.SetFocus(true)
'         return true
'       end if
'     else if buttonBar.alignment = "top"
'       if key = "up" and currentView.isInFocusChain()
'         buttonBar.SetFocus(true)
'         return true
'       else if key = "down" and buttonBar.isInFocusChain()
'         currentView.SetFocus(true)
'         return true
'       end if
'     else if buttonBar.alignment = "left"
'       if key = "left" and currentView.isInFocusChain()
'         if currentView.Subtype() <> "MediaView" and currentView.isContentList <> true
'           buttonBar.SetFocus(true)
'           return true
'         end if
'       else if key = "right" and buttonBar.isInFocusChain()
'         currentView.SetFocus(true)
'         return true
'       end if
'     end if
'   end if
'   return false
' end function

' function handleMediaViewBBKeyEvents(mediaView as object, buttonBar as object, key as string) as boolean
'   if m.top.debug then print ghLogHead("ROUTER");"handleMediaViewBBKeyEvents"
'   ' isAbleToFocusBB = mediaView.state = "paused" or mediaView.state = "buffering" or buttonBar.renderOverContent
'   ' if buttonBar.visible and mediaView.isInFocusChain() and isAbleToFocusBB then
'   ' buttonBar.SetFocus(true)
'   ' return true
'   if key <> "up"
'     return closeView()
'   end if
' end function

' handles closing View in View stack
' if no View left, closes scene and exits channel
function closeView() as boolean
  if m.top.debug then print ghLogHead("ROUTER");"closeView"
  ' developer should receive back button when all Views are closed

  ' save flags locally because developer can change it in wasClosed callback
  allowCloseLastViewOnBack = m.top.allowCloseLastViewOnBack
  allowCloseChannelOnLastView = m.top.allowCloseChannelOnLastView

  result = m.top.ViewManager.ViewCount > 1

  ' allowCloseLastViewOnBack is checked here because if it is set to true we need to close the View even it is last View in stack
  if result or allowCloseLastViewOnBack
    m.top.ViewManager.callFunc("runProcedure", {
      fn: "closeView"
      fp: ["", {}]
    })

    ' result is bool if count of Views is 2 or more, so View in stach is closed and back button successfully handled
    if result then return true

    ' if last View is closed check if developer opens a new one in wasClosed callback, if so, back is handled
    if allowCloseLastViewOnBack and not allowCloseChannelOnLastView
      if m.top.ViewManager.ViewCount > 0 then return true
    end if

    if allowCloseLastViewOnBack then
      return false
    end if
  end if
  return not allowCloseChannelOnLastView
end function

' -------------------------------
' EVENTS
' -------------------------------
sub OnAddStackChanged(event as object)
  stack_id = event.getData()
  if m.top.debug then print ghLogHead("ROUTER");"OnAddStackChanged -- ";stack_id
  if stack_id <> ""
    index = FindElementIndexInArray(m.stacks, stack_id)
    if index > -1
      ?"SGDEX: STACK """stack_id""" ALREADY EXISTS"
      MoveElementToTail(m.stacks, index)
      ReplaceCurrentViewManager(m.stacks.Peek())
    else
      ' ViewManager = CreateObject("roSGNode", "ViewManager")
      ViewManager = CreateObject("roSGNode", "RouterManager")
      ViewManager.ObserveField("currentView", "OnCurrentViewChange")
      ViewManager.allowCloseChannelWhenNoViews = m.top.allowCloseChannelOnLastView
      ViewManager.id = stack_id
      m.stacks.Push(ViewManager)
      ReplaceCurrentViewManager(m.stacks.Peek())
    end if
  end if
end sub
sub OnRemoveStackChanged(event as object)
  stack_id = event.getData()
  if m.top.debug then print ghLogHead("ROUTER");"OnRemoveStackChanged -- ";stack_id
  if stack_id <> ""
    index = FindElementIndexInArray(m.stacks, stack_id)
    if index > -1
      removeStack = m.stacks[index]
      result = false
      if removeStack.id <> "default"
        result = m.stacks.Delete(index)
      end if
      if result
        stack = m.stacks.Peek()
        if stack <> invalid
          ReplaceCurrentViewManager(stack)
        end if
      end if
    else
      ?"SGDEX: STACK """stack_id""" NOT FOUND"
    end if
  end if
end sub
sub OnSelectStackChanged(event as object)
  stack_id = event.getData()
  if m.top.debug then print ghLogHead("ROUTER");"OnSelectStackChanged -- ";stack_id
  if stack_id <> ""
    index = FindElementIndexInArray(m.stacks, stack_id)
    if index > -1
      MoveElementToTail(m.stacks, index)
      ReplaceCurrentViewManager(m.stacks.Peek())
      m.top.currentView = m.top.ViewManager.currentView
    else
      ?"SGDEX: STACK """stack_id""" NOT FOUND "
    end if
  end if
end sub

sub ReplaceCurrentViewManager(viewManager as object)
  if m.top.debug then print ghLogHead("ROUTER");"ReplaceCurrentViewManager"
  if viewManager <> invalid
    m.top.activeStack = viewManager.id
    m.top.ViewManager = viewManager
    if viewManager.id <> m.top.GetChild(0).id ' to avoid replacing the node by itself
      m.top.ReplaceChild(viewManager, 0)
    end if
    if m.top.debug then print ghLogHead("ROUTER");"ReplaceCurrentViewManager - ";viewManager.id

    ' buttonBar = m.top.findNode("buttonBar")
    ' isButtonBarFocused = buttonBar.visible and buttonBar.isInFocusChain()
    ' if buttonBar is focused and active stack is changed
    ' keep focus on BB
    ' if not isButtonBarFocused
    '   if m.top.ViewManager.currentView <> invalid
    '     m.top.ViewManager.currentView.setFocus(true)
    '   else
    '     m.top.ViewManager.setFocus(true)
    '   end if
    ' end if
  end if
end sub

sub MoveElementToTail(array as object, index as integer)
  if m.top.debug then print ghLogHead("ROUTER");"MoveElementToTail"
  item = array[index]
  if item <> invalid
    array.Delete(index)
    array.Push(item)
  end if
end sub

function FindElementIndexInArray(array as object, value as object) as integer
  if m.top.debug then print ghLogHead("ROUTER");"FindElementIndexInArray"
  for i = 0 to (array.Count() - 1)
    compareValue = array[i]
    if compareValue <> invalid
      if lcase(compareValue.id) = lcase(value)
        return i
      end if
    end if
  end for
  return -1
end function

function CloseAllViews(config as object)
  m.top.ViewManager.callFunc("runProcedure", {
    fn: "closeAllViews"
    fp: []
  })
end function