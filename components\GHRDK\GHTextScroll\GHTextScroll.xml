<?xml version="1.0" encoding="utf-8" ?>
<component name="GHTextScroll" extends="Group" initialFocus="fondo">

  <script type="text/brightscript" uri="GHTextScroll.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- text -->
    <field id="text" type="string" value="MyButton" onChange="updateFieldText"/>
    <field id="showhelp" type="boolean" value="true" onChange="recalcLabelSizeAndPadding" />
    <!-- position -->
    <field id="translation" type="string" value="[100,100]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="900" onChange="updateFieldWidth" />
    <field id="height" type="string" value="500" onChange="updateFieldHeight" />
    <!-- align -->
    <!-- <field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign" /> -->
    <!-- <field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign" /> -->
    <!-- colors -->
    <!-- <field id="borderColor" type="string" value="0x00FF00" onChange="updateFieldBorderColor" /> -->
    <field id="color" type="string" value="0x555555" onChange="recalcColors" />
    <field id="selColor" type="string" value="0xFFFFFF" onChange="recalcColors" />
    <field id="backColor" type="string" value="0x101010" onChange="recalcColors" />
    <field id="selBackColor" type="string" value="0x101010" onChange="recalcColors" />
    <!-- <field id="selBorderColor" type="string" value="0xFF0000" onChange="updateFieldSelBorderColor" /> -->
    <field id="barColor" type="string" value="0xFFFFFF" onChange="recalcColors" />
    <field id="barBackColor" type="string" value="0xFF0000" onChange="recalcColors" />
    <!-- padding and margin -->
    <field id="padding" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true" />
    <!-- <field id="focusPadding" type="string" value="12" onChange="updateFieldFocusPadding" alwaysNotify="true" /> -->
    <!-- <field id="focusMap" type="string" value="pkg:/images/focus01.9.png" alias="border.uri" /> -->
    <!-- <field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor" /> -->
    <!-- focus and select -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="reading" type="boolean" value="false" onChange="updateFieldReading" />
    <field id="selected" type="boolean" value="false" />
    <field id="leaving" type="boolean" value="false" />
    <field id="value" type="string" value="mybutton"/>
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="fondo" width="900" height="200" color="#121212">
      <ScrollableText id="scrolltext" translation="[20,20]" text="Roku now offers..." horizAlign="left" vertAlign="top" />
      <Rectangle id="instructionbar">
        <LayoutGroup id="instructiongroup" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" itemSpacings="20">
          <Poster id="imgExit" uri="pkg:/images/keys_back_HD.png" />
          <Label id="txtExit" text="Salir" />
          <Poster id="imgUpDn" uri="pkg:/images/keys_updn_HD.png" />
          <Label id="txtUpDn" text="Up/Down" />
          <Poster id="imgPgUpDn" uri="pkg:/images/keys_ffrw_HD.png" />
          <Label id="txtPgUpDn" text="PgUp/PgDw" />
        </LayoutGroup>
      </Rectangle>
    </Rectangle>
  </children>

</component>
