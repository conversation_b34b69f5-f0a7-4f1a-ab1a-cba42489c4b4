' Copyright (c) 2018-2019 Roku, Inc. All rights reserved.

sub Init()
  ' Developer should store proper splash in this location as we can't read manifest here
  ' This is to avoid View blinking
  ' TODO write proper handling
  ' m.top.backgroundURI = "pkg:/images/splash_HD.png"
  m.top.ComponentController = m.top.findNode("ComponentController")
  ' m.top.buttonBar = m.top.ComponentController.findNode("buttonBar")
  ' m.top.buttonBar.visible = false
  m.top.ObserveField("theme", "SceneSetTheme")
  print ghLogHead("ROUTER");"Init."
end sub

sub LaunchArgumentsReceived()
  ' launch_args interface callback
  ' This is safe to start channel
  Show(m.top.launch_args)
end sub
sub InputArgumentsReceived()
  ' input_args interface callback
  Input(m.top.input_args)
end sub

' ----------------------------------
' METHODS
' ----------------------------------
sub SceneSetTheme(event as object)
  m.top.actualThemeParameters = event.getData()
end sub
function onKeyEvent(key as string, press as boolean) as boolean
  ' if back button is passed here View stack is done with Views operation
  ' developer can override onKeyEvent to prevent closing channel and show exit dialog for example
  if press and key = "back"
    if m.top.ComponentController.allowCloseChannelOnLastView
      m.top.GetScene().exitChannel = true
    end if
    return true
  end if
  return false
end function
function createObjectOnDemand(value) as object
  'This is workaround for accessing scope of channel from framework library
  'callback for creating objects needed in library
  return CreateObject("roSGNode", value)
end function

' ----------------------------------
' @ABSTRACT
' ----------------------------------
sub Show(args as object)
  print ghLogHead("ROUTER");"Show -- please implement sub show(args) in your code in order to show any View"; args
end sub
sub closeAllViews(args as object)
  print ghLogHead("ROUTER");"Show -- please implement sub closeAllViews(args) in your code in order to show any View"; args
end sub
sub Input(args as object)
  print ghLogHead("ROUTER");"Input -- SGDEX: Please implement 'sub Input(args)' in your scene to handle roInputEvent deep linking"; args
end sub
' ----------------------------------
