<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemEpisodesPlayer" extends="Group">
  <script type="text/brightscript" uri="ItemEpisodesPlayer.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>

    <field id="focusPercent" type="float" onChange="showfocus" />
    <field id="itemHasFocus" type="boolean" onChange="showfocus" />
    <field id="rowListHasFocus" type="boolean" onChange="showfocus"/>
    <field id="rowHasFocus" type="boolean" onChange="showfocus"/>

    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemPoster"/>
    <Label id="title" visible="true" translation="[0,10]" width="185" color="0xCCCCCC" wrap="true" height="30" lineSpacing="0" vertAlign="bottom" />
    <LayoutGroup id="infoCarrusel" translation="[0,170]" layoutDirection="horiz" itemSpacings="[70]" visible="true">
      <Label id="info" visible="true" translation="[50,10]" width="270"  color="0xCCCCCC" wrap="false" height="130" lineSpacing="0" vertAlign="bottom" />
    </LayoutGroup>
    <GHProgressBar id="progress" visible="false" translation="[0,50]" backColor="#2C2C2C" barColor="#DE1717"/>
    <GHPanel id="thePanels"/>
  </children>

</component>