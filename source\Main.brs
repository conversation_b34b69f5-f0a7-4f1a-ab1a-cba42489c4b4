'*************************************************************
'** Hello World example
'** Copyright (c) 2015 Roku, Inc.  All rights reserved.
'** Use of the Roku Platform is subject to the Roku SDK License Agreement:
'** https://docs.roku.com/doc/developersdk/en-us
'*************************************************************

sub Main()
  ' print "::::::::::::::::::::::::::::::::::::::::::::::::::::::"
  ' print "::::::::::::::::::::::::::::::::::::::::::::::::::::::"
  ' print "MAIN :::::::::::::::::::::::::::::::::::::::::::::::::"
  ' print "::::::::::::::::::::::::::::::::::::::::::::::::::::::"
  ' print "::::::::::::::::::::::::::::::::::::::::::::::::::::::"
  'Indicate this is a Roku SceneGraph application'
  ' screen = CreateObject("roSGScreen")
  ' m.port = CreateObject("roMessagePort")
  ' screen.setMessagePort(m.port)
  ' 'Create a scene and load /components/helloworld.xml'
  ' scene = screen.CreateScene("HelloWorld")
  ' screen.show()
  ' while(true)
  '   msg = wait(0, m.port)
  '   msgType = type(msg)
  '   print "source\Main.brs -- Main -- while ";
  '   print msgType ; " | " ; msg
  '   if msgType = "roSGScreenEvent"
  '     if msg.isScreenClosed() then return
  '   end if
  ' end while
end sub

''''''''
' GetSceneName:
' Crea automaticamente la escena principal
' @returns Stringreturn"MainScene"endfunction
''''''''
function GetSceneName() as string
  ' return "ExGHButtonsVC"
  ' return "ExProgress"
  ' return "ExGHInputMKH"
  ' return "ExGrid"
  ' return "ExGHTextScroll"
  ' return "ExGHCheckBox"
  ' return "ExGHLoading"
  ' return "ExApi"
  ' return "ExGHError"
  ' return "ExGHDialog"
  ' return "ExGHInput"
  ' return "ExGHMenu"
  ' return "ExGHButtons"
  ' return "HomePoc" ' para llamar al HomePOC
  ' return "ExVcard" ' acceso a la VCARD y al PLAYER personalizado con TrickPlay
  ' ---------------------------
  return "MainScene"
  ' ---------------------------
end function