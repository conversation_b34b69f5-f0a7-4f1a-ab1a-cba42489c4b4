' -----------------------------
' GHButtonGroup
' -----------------------------

function Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"init -- getChildCount ";m.top.getChildCount()
end function

' se cargan las opciones
sub onOptions(event)
  data = event.getData()

  if m.top.debug then print ghLogHead();"onOptions -- getChildCount1 ";m.top.getChildCount()

  ' se estaban agrando mas y mas cada vez que se entraba en un child
  cant = m.top.getChildCount()
  m.top.removeChildrenIndex(cant, 0)

  yPos = 60
  xPos = 0
  for each item in data
    op = item

    if m.top.debug then print ">> ";item

    ' ------------------------
    but = m.top.createChild("GHButton")
    but.font = ghGetFont(18, "bold")
    but.id = op.code
    but.value = op.code
    but.text = op.label
    but.width = 220
    but.height = 60
    but.translation = [xPos, yPos]
    ' ---
    xPos += 220
  end for

  if m.top.debug then print ghLogHead();"onOptions -- getChildCount2 ";m.top.getChildCount()
end sub

' cambia el boton activo
sub onActiveChange(event)
  data = event.getData()

  if m.top.debug then print ghLogHead();"onActiveChange -- activeCode ";data

  cant = m.top.getChildCount()
  for c = 0 to cant - 1
    op = m.top.getChild(c)
    if op.id = data then
      op.selected = true
      op.backColor = "#9B0F0F"
      op.selBackColor = "#DE1717"
      op.color = "#FFFFFF"
      op.selColor = "#FFFFFF"
      m.valueFocused = op.id

      if m.top.debug then print ghLogHead();"onActiveChange -- selected ";op.id
    else
      op.backColor = "#1D1E26"
      op.selBackColor = "#DE1717"
      op.selColor = "#FFFFFF"
      op.color = "#656767"
      op.selected = false

      if m.top.debug then print ghLogHead();"onActiveChange -- not selected ";op.id
    end if
  end for
end sub






