<?xml version="1.0" encoding="utf-8"?>
<component name="hubfacturafijagate" extends="Page">
	<!-- <script type="text/brightscript" uri="purchase_hubfacturafijagate.brs" /> -->
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Files.brs" />
	<!-- <script type="text/brightscript" uri="pkg:/components/DeepLinking.brs" /> -->
	<interface>
	</interface>
	<children>
		<!-- Fondo y Textos -->
		<Poster id="fondo" translation="[0,0]" width="1280" height="720" />
		<Label id="title" focusable="false" translation="[700,64]" width="504" height="48" horizAlign="center" />
		<Label id="descrip" focusable="false" translation="[800,155]" width="280" height="200" text="Código de sucursal + 2 últimos dígitos de la cédula de identidad. Obtén tu código de sucursal en tu estado de cuenta" wrap="true" horizAlign="center" />
		<!-- Botonera -->
		<GHButtonGroup id="botonera" layout="map" orientation="vertical" handleKey="false">
			<GHInput id="user" placeholder="Usuario" translation="[700,300]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="false" width="504" height="65" titleColor="0xFFFFFF" message="Código de sucursal + 2 últimos dígitos de la cédula de identidad. Obtén tu código de sucursal en tu estado de cuenta" messageColor="0xFFFFFF" />
			<Label id="title2" focusable="false" translation="[700,380]" text="Servicio al que se hara el cargo" width="504" height="48" horizAlign="center" />
			<GHButton id="user2" text="Servicios Contarados" value="user2" translation="[700,400]" color="0xFFFFFF" selColor="#FFFFFF" width="504" height="72" backcolor="#454751ff" selBackColor="#454751ff" focusColor="0xFFFFFF" />
			<CheckoutFieldsEntry id="checkoutFields" translation="[700,500]" />
		</GHButtonGroup>
		<GHError id="error" />
		<GHLoading id="loading" visible="false" />
	</children>
</component>
