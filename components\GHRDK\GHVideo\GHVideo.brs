' GHVideo
' --------------------------
sub Init()
  m.top.debug = true
  m.logger = CreateLogger()

  ' top
  m.top.ObserveField("content", "onContentChange")
  m.top.ObserveField("state", "onStateChange")
  m.top.ObserveField("duration", "onDurationChange")
  m.top.ObserveField("availableAudioTracks", "onAvailableAudioTracks")
  m.top.ObserveField("availableSubtitleTracks", "onAvailableSubtitleTracks")
  m.top.ObserveField("position", "onPositionChange")
  m.top.ObserveField("showOmitirIntro", "onShowOmitirIntro")

  m.introCard = m.top.findNode("theIntroCard")
  m.introCard.ObserveField("value", "onIntroCardValue")

  ' Player Panel
  m.playerPanel = m.top.findNode("playerPanel")
  m.playerPanel.ObserveField("visible", "onPlayerPanelVisibleChange")
  m.playerPanel.ObserveField("position", "onPositionChange")
  m.playerPanel.ObserveField("duration", "onDurationChange")
  m.playerPanel.ObserveField("cmd", "onPlayerCmd")
  m.playerPanel.ObserveField("keypressed", "onKeyPressed")

  ' Audio Panel
  m.audioPanel = m.top.findNode("audioPanel")
  m.audioPanel.ObserveField("visible", "onAudioPanelVisibleChange")
  m.audioPanel.ObserveField("selected", "onAudioPanelSelected")
  m.audioPanel.ObserveField("keypressed", "onKeyPressed")
  ' Season Panel
  m.seasonPanel = m.top.findNode("seasonPanel")
  m.seasonPanel.ObserveField("visible", "onSeasonPanelVisibleChange")
  m.seasonPanel.ObserveField("selected", "onSeasonPanelSelected")
  m.seasonPanel.ObserveField("keypressed", "onKeyPressed")

  m.timerOmitirIntroValue = 7
  m.tmrCount = m.timerOmitirIntroValue
  initTimerOmitirIntro()

  ' replay settings
  m.IsReplayCaption = false
  m.ReplayCaptionTo = 0
end sub

' timer, para al dar ok en omitir intro,
' no se muestre de nuevo ( por si vuelve a una posicion anterior )
sub initTimerOmitirIntro()
  m.tmrSeguir = CreateObject("roSGNode", "Timer")
  m.tmrSeguir.ObserveField("fire", "tmrTriggerSeguir")
  m.tmrSeguir.repeat = true ' chequea varias veces...
  m.tmrSeguir.duration = 1
  m.tmrCount = m.timerOmitirIntroValue
end sub

sub tmrTriggerSeguir()
  if m.tmrCount > 0 then
    m.tmrCount -= 1 ' contamos
  else
    m.tmrSeguir.control = "stop"
    m.tmrCount = m.timerOmitirIntroValue

    ' cuando termina el timer, muestro el boton omitir intro si corresponde
    showButtomOmitirIntro(1)
  end if
end sub

sub onShowOmitirIntro(event)
  data = event.getData()

  ' data = 0 = false
  ' data = 1 = solo mostrar en panel
  ' data = 2 = mostrar boton separado y en panel

  ' mostrar boton separado, solo una ves o si la posicion es anterior al start
  if data = 0
    m.logger.debug("ocultando boton omitir intro")
    m.playerPanel.showOmitirIntro = false

    m.introCard.visible = false
    m.introCard.setFocus(false)
    onExit()

  else
    showButtomOmitirIntro(data)
  end if
end sub

sub showButtomOmitirIntro(data)
  ' verifico que no haya cambiado m.top.showOmitirIntro
  if m.top.showOmitirIntro > 0 then
    if m.tmrCount = m.timerOmitirIntroValue then
      m.logger.debug("mostrando boton omitir intro en panel")
      m.playerPanel.showOmitirIntro = true

      ' si no estoy mostrando ningun panel, mustro boton
      ' si estoy mostrando omitir intro desde el inicio, mustro boton suelto
      if data = 2 and m.playerPanel.visible = false and m.seasonPanel.visible = false and m.audioPanel.visible = false then
        m.logger.debug("mostrando boton omitir intro suelto")
        m.introCard.visible = true
        m.introCard.SetFocus(true)
      end if
    end if
  end if
end sub

sub onIntroCardValue(event)
  data = event.getData()

  if data <> invalid and data <> "" then
    if data = "CANCEL" then
      m.logger.debug("Se canceló omitir intro")
      m.introCard.visible = false
      m.introCard.setFocus(false)
      onExit()
    else if data = "JUMP"
      enviarOmitirIntro()
      onExit()
    end if
  end if
end sub

sub enviarOmitirIntro()
  m.logger.debug("Se seleccionó omitir intro")

  ' aviso al panel que hay que ocultar boton omitir intro
  m.playerPanel.showOmitirIntro = false

  ' por si vuelve el showIntro = true ( porque volvio atras al dar ok )
  ' no muestre el boton de omitir intro, hasta que termine el timer
  m.top.onOmitirIntro = true

  m.tmrCount -= 1
  m.tmrSeguir.control = "start"

end sub

sub onPositionChange()
  m.playerPanel.positionPlayer = m.top.position
  if m.IsReplayCaption then
    if m.top.position > m.ReplayCaptionTo then
      print "ReplayCaption Lo apago!!!"
      m.top.globalCaptionMode = "Instant replay"
      m.IsReplayCaption = false
      m.ReplayCaptionTo = 0
    else
      print "ReplayCaption prendido ";m.top.position;"<=";m.ReplayCaptionTo
    end if
    ' else
    '   print "NO IsReplayCaption"
  end if
end sub

' KEY HANDLING
' ---------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key = "OK" then ' pantalla basica
      if m.top.debug then print ghLogHead();"onKeyEvent -- PLAYER PANEL - cause ";key
      refreshPlayerPanel()
      m.playerPanel.inTo = {
        "focusTo": "playbuttons",
        "fieldsTo": { "foco": "btnPlay" }
      }
      handled = true
    else if key = "play" then ' pido pausa
      if m.top.debug then print ghLogHead();"onKeyEvent -- PLAYER PANEL - to PAUSE cause ";key
      refreshPlayerPanel()
      m.playerPanel.inTo = {
        "focusTo": "playbuttons",
        "action": "pause"
      }
      ' handled = false
      handled = true
    else if key = "left" or key = "right" or key = "fastforward" or key = "rewind" then
      m.top.onkey = key

      if m.top.debug then print ghLogHead();"onKeyEvent -- PLAYER PANEL - to SEEKBAR - cause ";key
      refreshPlayerPanel()
      m.playerPanel.inTo = {
        "focusTo": "barSeek"
        "fieldsTo": { "keyBuffer": [key] }
      }
      handled = true
    else if key = "replay" then
      curPos = m.top.position
      newPos = curPos - 25
      if newPos < 0 then newPos = 0
      m.top.seek = newPos

      print "#########################################################"
      print "#########################################################"
      print ghLogHead();"onKeyEvent REPLAY position=";m.top.position
      print "#########################################################"
      print ghLogHead();"onKeyEvent REPLAY globalCaptionMode=";m.top.globalCaptionMode
      print ghLogHead();"onKeyEvent REPLAY currentSubtitleTrack=";m.top.currentSubtitleTrack
      print ghLogHead();"onKeyEvent REPLAY availableSubtitleTracks=";m.top.availableSubtitleTracks

      if m.IsReplayCaption then
        print "No hago nada, ya esta hecho, queda hasta el ";m.ReplayCaptionTo
      else
        if m.top.globalCaptionMode = "Instant replay" then ' Off
          print ghLogHead();"onKeyEvent * captions apagados"
          if m.top.availableSubtitleTracks.Count() > 0 then
            print ghLogHead();"onKeyEvent * tengo disponibles."
            m.top.globalCaptionMode = "On"
            m.IsReplayCaption = true
            m.ReplayCaptionTo = curPos
          else
            print ghLogHead();"onKeyEvent * NO tengo disponibles."
          end if
        else
          print ghLogHead();"onKeyEvent * captions prendidos"
        end if
      end if

      print "#########################################################"
      print ghLogHead();"onKeyEvent REPLAY globalCaptionMode=";m.top.globalCaptionMode
      print "#########################################################"
      print "#########################################################"


      handled = true
    else if key = "up" or key = "down" then ' cambio capitulo
      refreshPlayerPanel()
      m.playerPanel.inTo = {
        "focusTo": "playbuttons",
        ' "timer": "resume"
        ' "fieldsTo": { "foco": "btnPlay" }
      }
      handled = true


      ' else if key = "up" then ' cambio capitulo
      '   if m.top.debug then print ghLogHead();"onKeyEvent -- PLAYER PANEL - to SEASON - cause ";key
      '   if m.top.info.season <> invalid and m.top.info.season <> "" then
      '     refreshPlayerPanel()
      '     m.playerPanel.inTo = {
      '       "timer": "pause"
      '       "focusTo": "playbuttons",
      '       "fieldsTo": { "foco": "btnSeason", "value": "season" }
      '     }
      '     handled = true
      '   end if
      ' else if key = "down" then ' cambio de idioma
      '   if m.top.debug then print ghLogHead();"onKeyEvent -- PLAYER PANEL - to LANG - cause ";key
      '   refreshPlayerPanel()
      '   m.playerPanel.inTo = {
      '     "timer": "pause"
      '     "focusTo": "playbuttons",
      '     "fieldsTo": { "foco": "btnLang", "value": "lang" }
      '   }
      '   handled = true
      '   ' else if key = "down" or key = "up" then ' cambio de idioma
      '   '   print ghLogHead();"onKeyEvent -- AUDIOPANEL ON";key
      '   '   if not m.audioPanel.visible then
      '   '     m.audioPanel.visible = true
      '   '     handled = true
      '   '   end if
    else if key = "*" then ' no funciona...
      if m.top.debug then print "NO FUNCIONA"
      handled = true
    end if
  end if
  return handled
end function
sub onKeyPush(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onKeyPush -- key=";data
  onKeyEvent(data, true)
end sub
sub refreshPlayerPanel()
  m.playerPanel.setFields({
    "position": m.top.position
    "state": m.top.state
    "content": m.top.content
    "trickBif": m.top.bifDisplay
    "availableAudioTracks": m.top.availableAudioTracks
    "availableSubtitleTracks": m.top.availableSubtitleTracks
  })
end sub

' EVENTOS
' ---------------------
sub onStateChange(event)
  state = event.getData()
  if m.top.debug then print ghLogHead();"onStateChange -- state=";state

  m.playerPanel.setField("state", state) ' paso el dato al panel
end sub

sub onDurationChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onDurationChange -- duration=";data
  m.playerPanel.duration = data
end sub
sub onInfoChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onInfoChange -- duration=";data
  m.playerPanel.info = data
  m.audioPanel.info = data
  m.seasonPanel.info = data
end sub
sub onSeasonsChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onSeasonsChange -- duration=";data
  m.seasonPanel.seasons = data
end sub
sub onContentChange()
  ' data = event.getData()
end sub

' PLAYER PANEL
' ---------------------
sub onPlayerPanelVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onPlayerPanelVisibleChange -- data=";data
  if data then
    ' m.top.control = "pause"
  else
    ' m.top.control = "resume"
    ' m.playerPanel.setFocus(false)
    ' m.top.setFocus(true)
  end if
end sub
sub onPlayerCmd(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onPlayerCmd -- data=";data
  if data <> invalid then
    if data.command = "position" then
      if m.top.debug then print ghLogHead();"onPlayerCmd -- POSITION! (DESABILITADO)"
      ' print ghLogHead("CMD");"onPlayerCmd -- cambio de pos a";data.position
      ' m.top.seek = data.position
    else if data.command = "ok" then
      if m.top.debug then print ghLogHead();"onPlayerCmd -- OK!"
      m.top.onkey = "seekPosition"
      m.top.seek = m.playerPanel.position ' resultado
      m.playerPanel.visible = false
      m.top.onkey = ""
      onExit()
    else if data.command = "exit" then
      if m.top.debug then print ghLogHead();"onPlayerCmd -- EXIT!"
      m.playerPanel.visible = false
      onExit()
    else if data.command = "pause" then
      if m.top.control = "pause" then
        m.top.control = "resume"
        m.playerPanel.visible = false
        onExit()
      else
        m.top.control = "pause"
      end if
    else if data.command = "showAudioPanel" then
      if m.top.debug then print ghLogHead();"onPlayerCmd -- PANEL IDIOMAS/SUBTITULOS!"
      m.audioPanel.visible = true
      m.playerPanel.visible = false
    else if data.command = "showSeasonPanel" then
      if m.top.debug then print ghLogHead();"onPlayerCmd -- PANEL TEMPORADAS!"
      m.seasonPanel.visible = true
      m.playerPanel.visible = false
    else if data.command = "lista" then
      if m.top.favorited then
        m.top.favorited = false
      else
        m.top.favorited = true
      end if
    else if data.command = "omitirIntro" then
      enviarOmitirIntro()
    end if
    m.playerPanel.cmd = invalid ' limpio
  end if
end sub
sub onAvailableAudioTracks() ' event
  if m.top.debug then print ghLogHead();"onAvailableAudioTracks -- PASE!"
  LogicaMacabra()
end sub
sub onAvailableSubtitleTracks(event)
  data = event.getData()

  if m.top.debug then print ghLogHead();"onAvailableSubtitleTracks -- PASE!"

  if data.Count() > 0 then
    LogicaMacabra()
  end if
end sub
sub onAudioPanelVisibleChange(event) ' se prende o apaga el panel de audio
  data = event.getData()

  if m.top.debug then print ghLogHead();"onPanelFocusChange -- data=";data

  if data = true then
  else
    m.audioPanel.visible = false
    m.audioPanel.setFocus(false)

    onExit()
    ' m.playerPanel.focus = true
    ' m.playerPanel.SetFocus(true)
  end if
end sub

sub onAudioPanelSelected(event)
  data = event.getData()

  m.logger.debug("audio selected", { data: data })

  if data <> invalid then

    if data.itemActivacion = true then
      m.top.globalCaptionMode = data.activate
      ' cambio el titulo al item activar/desactivar subtitulo, de la lista de idiomas
      ls = m.audioPanel.langData
      for each l in ls
        if l.itemActivacion = true then
          if m.top.globalCaptionMode = "On" then
            l.activate = "Off"
            l.label_large = "Desactivar subtitulos"
          else
            l.activate = "On"
            l.label_large = "Activar subtitulos"
          end if
        end if
      end for
      m.audioPanel.langData = ls

    else

      ' guardo la preferencia de audio y subtitulo
      ghSetRegistry("preference_vod_audio", LCase(ghGetChild(data, "audio", "")), "user")
      ghSetRegistry("preference_vod_subtitle", LCase(ghGetChild(data, "subtitle", "")), "user")

      if data.audio_track = invalid and data.subtitle_track = invalid then
        ' single audio, continuo para llamar a getMedia
        m.top.selected = data
      else

        ' tengo audio o subtitulo
        ' cambio los track sin llamar a la getMedia
        if data.audio_track <> invalid then
          m.top.audioTrack = data.audio_track
        end if

        if data.subtitle_track <> invalid then
          m.top.subtitleTrack = data.subtitle_track

          m.top.globalCaptionMode = "On"
          setGlobalCaption("On")
        else
          m.top.globalCaptionMode = "Off"
          setGlobalCaption("Off")
        end if
      end if
    end if
  end if

  m.audioPanel.visible = false
  m.playerPanel.visible = false

  onExit()
end sub

' SEASON PANEL
' ---------------------
sub onSeasonPanelVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onSeasonPanelVisibleChange ** ";data

  if data = true then
  else
    m.seasonPanel.visible = false
    m.seasonPanel.setFocus(false)

    onExit()
    ' m.playerPanel.SetFocus(true)
    ' m.playerPanel.focus = true
  end if
end sub
sub onSeasonPanelSelected(event)
  data = event.getData()

  if m.top.debug then print ghLogHead();"onSeasonPanelSelected ** ";data

  m.seasonPanel.visible = false
  m.seasonPanel.setFocus(false)

  onExit()
  ' m.playerPanel.visible = false
  ' m.playerPanel.setFocus(false)
  ' m.top.SetFocus(true)

  if data.content_id = m.top.content.contentid.toStr() then
    m.top.control = "resume"
  else
    m.top.selected = data
  end if
end sub

sub handleLanguages(event)
  data = event.getData()
  m.languages = data
end sub

sub LogicaMacabra()

  if m.languages = invalid then
    m.logger.debug("aun no tengo la info de los idiomas")
  end if

  if m.top.availableAudioTracks = invalid then
    m.logger.debug("aun no tengo la info de los audios")
  end if

  if m.top.availableSubtitleTracks = invalid then
    m.logger.debug("aun no tengo la info de los subtitulos")
  end if

  m.logger.debug("languages", { options: ghGetChild(m.languages, "options", []), audio: ghGetChild(m.languages, "audios"), subtitles: ghGetChild(m.languages, "subtitles") })
  m.logger.debug("available audios/Subtitle", { subtitles: m.top.availableSubtitleTracks, audios: m.top.availableAudioTracks })

  subtitleOptions = ghGetChild(m.languages, "subtitles.options", {})
  audioOptions = ghGetChild(m.languages, "audios.options", {})

  m.logger.debug("subtitleOptions", subtitleOptions)
  m.logger.debug("audioOptions", audioOptions)

  finalLanguages = []

  if m.top.globalCaptionMode = "On" then
    itemActivate = { is_current: false, itemActivacion: true, activate: "Off", label_large: "Desactivar subtitulos" }
  else
    itemActivate = { is_current: false, itemActivacion: true, activate: "On", label_large: "Activar subtitulos" }
  end if
  finalLanguages.push(itemActivate)


  ' agrego a la lista de idiomas los tacks de audio y subtitulos
  ' asi al elegir de la lista, si tiene track no llamo a la getMedia
  for each item in ghGetChild(m.languages, "options", [])
    m.logger.debug("current item", item)

    if item.audio <> invalid then
      m.logger.debug("audio seleccionado", { item_audio: item.audio, option_seleccionado: audioOptions[item.audio] })

      if audioOptions[item.audio] <> invalid then
        for each audio in m.top.availableAudioTracks
          if audio.Language = audioOptions[item.audio] then
            item.audio_track = audio.Track
            exit for
          end if
        end for
      end if
    end if

    if item.subtitle <> invalid then
      m.logger.debug("subtitle seleccionado", { item_subtitle: item.subtitle, option_seleccionado: subtitleOptions[item.subtitle] })

      ' en las opciones de subtitulos de la get Media viene clave/valor que tiene el codigo ej: "es"
      ' busco en las opciones de subtitulos del player que la propiedad Language sea igual al codigo clave/valor que viene en la getMedia
      if subtitleOptions[item.subtitle] <> invalid then
        for each subtitle in m.top.availableSubtitleTracks
          if subtitle.Language = subtitleOptions[item.subtitle].internal then
            item.subtitle_track = subtitle.TrackName
            exit for
          end if
        end for
      end if
    end if

    if item.is_current then
      m.top.audioTrack = item.audio_track
      m.top.subtitleTrack = item.subtitle_track
    end if

    finalLanguages.push(item)
  end for

  m.logger.debug("final languages", finalLanguages)

  m.audioPanel.langData = finalLanguages
end sub

' UTILITIES
' ---------------------
sub LogicaMacabraOld(arrLanguages, contentLanguages)
  if m.top.debug then print ghLogHead();"LogicaMacabra --"

  if arrLanguages = invalid and contentLanguages <> invalid then
    arrLanguages = getLenguajesCompleto(contentLanguages, m.top.availableAudioTracks, m.top.availableSubtitleTracks) ' tengo un roAssociativeArray
    langs = []

    ' esto es cuando entra a la peli/serie
    ' agrego el item de activacion de subtitulos
    if m.top.globalCaptionMode = "On" then
      itemActivate = { is_current: false, itemActivacion: true, activate: "Off", label_large: "Desactivar subtitulos" }
    else
      itemActivate = { is_current: false, itemActivacion: true, activate: "On", label_large: "Activar subtitulos" }
    end if
    langs.push(itemActivate)

    for each l in arrLanguages ' lo tengo que convertir a roArray
      item = arrLanguages[l]
      langs.push(item)
      if item.is_current then

        ' subtitulos -- apagar y prender
        ' if item.trackType = "audio" then
        '   if m.top.globalCaptionMode <> "Instant replay" then
        '     m.top.globalCaptionMode = "Off"
        '     setGlobalCaption("Off")
        '   end if
        ' else
        '   m.top.globalCaptionMode = "On"
        '   setGlobalCaption("On")
        ' end if

        ' si es multiple audio seteo el audio y el subtitulo
        if item.modo = "MA" then
          if item.trackType = "audio" then
            m.top.audioTrack = item.track.Track
          else
            m.top.subtitleTrack = item.track.TrackName
            if item.audioOriginal <> invalid then
              m.top.audioTrack = item.audioOriginal.Track
            end if
          end if
        end if
      end if
    end for

    m.audioPanel.langData = langs
  end if
end sub
function getLenguajesCompleto(langs, audios, subs)
  print ghLogHead();"getLenguajesCompleto --"

  arrSalida = {}
  ' ---------------------
  ' languages
  for each l in langs
    if m.top.debug then print ">>>>> ";l
    l.modo = "SA"
    ' marco si es subtitulo o audio
    identification = Left(ghGetChild(l, "option_id", " "), 1)
    if identification = "O" then ' si es el original
      l.trackType = "audio"
    else if identification = "D"
      l.trackType = "audio"
    else if identification = "S"
      l.trackType = "subtitle"
    else
      l.trackType = "audio"
    end if

    arrSalida[l.option_id] = l
  end for
  ' ---------------------
  ' audios
  audioOriginal = invalid
  for each a in audios
    lang = getLanguage("A", a)
    if lang <> invalid and lang <> "D-" then
      if lang = "O-" then
        lang = getOriginal(arrSalida) ' busco el original
        audioOriginal = a
      end if
      ' revisar
      if lang <> invalid and arrSalida.count() > 0 and arrSalida[lang] <> invalid then
        arrSalida[lang].modo = "MA"
        arrSalida[lang].trackType = "audio"
        arrSalida[lang].track = a
      end if
    end if
  end for
  ' ---------------------
  ' subtitle
  for each a in subs
    lang = getLanguage("S", a)
    if lang <> invalid then
      if lang <> invalid and arrSalida.count() > 0 and arrSalida[lang] <> invalid then
        arrSalida[lang].modo = "MA"
        arrSalida[lang].trackType = "subtitle"
        arrSalida[lang].track = a
        arrSalida[lang].audioOriginal = audioOriginal
      end if
    end if
  end for
  if m.top.debug then
    print "#####################################"
    for each item in arrSalida
      print ghLogHead();item;" --> ";arrSalida[item]
      print ghLogHead();item;" T --> ";arrSalida[item].track
    end for
    print "#####################################"
  end if
  return arrSalida
end function
function getLanguage(mode, ori) ' obtiene el lenguaje apropiado de acuerdo al formato
  lang = ori.Language
  ' para contenido hbo viene language = audioORI
  if ori.name <> invalid then
    langR = Right(UCase(ori.name), 3)
    if langR = "ORI" then
      lang = "ORI"
    end if
  end if
  result = ""
  if mode = "A" then
    result = getLanguage3to2("D-", lang)
  else if mode = "S" then
    result = getLanguage3to2("S-", lang)
  else if mode = "L" then
    result = ori.option_id
  end if
  return result
end function
function getLanguage3to2(prefix, l3) ' convierte de un formato de lenguaje al otro
  lang = Left(UCase(l3), 2)
  if Left(lang, 1) = "O" then ' si es el original
    lang = "O-"
  else if lang = "PO" ' porque no se ponen de acuerdo
    lang = prefix + "PT"
  else if lang = "SP" ' porque no se ponen de acuerdo
    lang = prefix + "ES"
  else
    lang = prefix + lang ' agrego el prefijo
  end if
  return lang
end function
function getOriginal(langs) ' encuentra el idioma original en languages
  for each l in langs
    if Left(l, 1) = "O" then
      return l ' es el que busco
    end if
  end for
  return invalid
end function

sub onExit()
  m.playerPanel.setFocus(false)
  m.top.setFocus(true)
end sub

sub onKeyPressed(event)
  data = event.getData()
  print ghLogHead();"KeyPressed !!! [";data;"]"
  m.top.keypressed = data
end sub