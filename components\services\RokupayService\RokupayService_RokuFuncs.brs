' # Features
' ## 1
' -------------------------------------------------------------
' Feature 01: Recuperación de compras durante Startup
' 	https://dlatvarg.atlassian.net/browse/AFE-1010

' "Recuperar las suscripciones realizadas en el canal Claro video realizadas con el medio de pago Roku Pay para validar el estatus en el que se encuentran y determinar el comportamiento que tendrá la aplicación"

' ESTO ES DURANTE EL STARTUP, NOS ASEGURARON QUE SÓLO ÍBAMOS A HACER CAMBIOS EN VCARD, PREGUNTÉ EN LA REUNIÓN.

' - Durante el startup : esto va a linkear con el usuario del dispositivo, aunque sean varios los usuarios de CV, y no tiene conexión para saber cuál es cual.
' - Qué se hace con esta información?
' - Posibilidad: global especificando si tiene o no compras con rokupay: DUDOSO, porque esto hay que cambiarlo si hace una compra.
' - Posibilidad: global con la lista de suscripçiones recibidas, para usar después en [Feature02] y en el momento de la compra ?????.

' >>> PEDIDO
' * objeto "store"
' * evento "onGetPurchases"
' * comando de Roku "getAllPurchases" : store.command = "getAllPurchases"
' <<< RESPUESTA
' * "store.purchases.status" tiene un valor igual a "1" -- response OK
' * "store.purchases" no tiene elementos hijos
' 	La cuenta de Roku no tiene suscripciones en el canal realizadas con Roku Pay.
' * "store.purchases" tiene al menos un elemento hijo
' 	La cuenta de Roku tiene al menos una suscripción en el canal realizadas con Roku Pay.
' 	Se debe validar el estatus de la suscripción
' * "store.purchases.child[#].status" tiene un valor igual a <status>
' * "store.purchases.child[#].inDunning" tiene un valor igual a <inDuning>

'     | estatus_suscripcion  | inDuning | status |
'     | Vigente              | true     | false  |
'     | En días de gracia    | true     | false  |
'     | En suspención        | false    | false  |
'     | Cancelada            | false    | true   |
sub RokuGetPurchases(callBack)
  if m.top.debug then print ghLogHead("RPay");"RokuGetPurchases -- init."
  m.store = CreateObject("roSGNode", "ChannelStore")
  m.store.ObserveField("purchases", callBack)
  m.store.command = "getAllPurchases"
end sub
' sub vuelta(event)
'   data = event.getData()
'   print "****************** ";data
'   a = 10
' end sub

' ## 2
' -------------------------------------------------------------
' Feature 02: Validación de compras durante Startup
' 	https://dlatvarg.atlassian.net/browse/AFE-1011

' ESTO ES DURANTE EL STARTUP, NOS ASEGURARON QUE SÓLO ÍBAMOS A HACER CAMBIOS EN VCARD, PREGUNTÉ EN LA REUNIÓN.

' - transaction_ids obtenidos por medio del comando getAllPurchases [Feature01], para verificar el estado de las suscripciones realizadas por medio de Roku Pay.
' - De dónde sale el {partnerAPIKey} ??
' - Si hay transacciones en período de gracia o on-hold hay que proceder al manejo de ROKUPAY ??

' * Para cada uno de los transaction_ids:
' 	* GET al servicio "validate-transaction" por cada uno de los elementos de la lista
' 	  GET https://apipub.roku.com/listen/transaction-service.svc/validate-transaction/{partnerAPIKey}/{transaction_id}
' 	  header "accept" con un valor igual a "application/json"

'     | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
'     | Vigente              | true      | false     | Fecha posterior a la actual |
'     | En días de gracia    | true      | false     | Fecha actual o anterior     |
'     | On-Hold              | false     | false     | Fecha actual o anterior     |
'     | Cancelada            | false     | true      | Fecha anterior a la actual  |
sub RokuValidatePurchases()
  if m.top.debug then print ghLogHead("RPay");"RokuValidatePurchases -- init."
end sub

' ## 5
' -------------------------------------------------------------
' Feature 05: Reproducción de contenido - Usuario sin suscripciones Roku Pay
' 	https://dlatvarg.atlassian.net/browse/AFE-1014

' - INCONSISTENCIA: Suscripción vigente: Given no se encontraron suscripciones realizadas por medio de Roku Pay al ejecutar el comando "getAllPurchases"
'   >>> Si el usuario CV compró por ROKUPAY desde una cuenta de ROKU diferente, entonces no va a aparecer la compra AUNQUE LA TENGA.

' * Si no tiene suscripciones de ROKUPAY [Feature01][Feature02], se ejecuta el contenido.

' ## 6
' -------------------------------------------------------------
' Feature 06: Reproducción de contenido - Usuario con suscripción Roku Pay vigente
' 	https://dlatvarg.atlassian.net/browse/AFE-1015

' "Reproducción de forma normal a usuarios con una suscripción vigente realizada por medio de Roku Pay".

' - INCONSISTENCIA: Suscripción vigente: Given no se encontraron suscripciones realizadas por medio de Roku Pay al ejecutar el comando "getAllPurchases"
'   >>> Si el usuario CV compró por ROKUPAY desde una cuenta de ROKU diferente, entonces no va a aparecer la compra AUNQUE LA TENGA.

' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está VIGENTE, entonces PLAY.
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???

function RokuHasTransaction(transactionId)
  if m.top.debug then print ghLogHead("RPay");"RokuHasTransaction -- init."
  return false
end function

' ## 7
' -------------------------------------------------------------
' Feature 07: Reproducción de contenido - Usuario con suscripción Roku Pay en días de gracia
' 	https://dlatvarg.atlassian.net/browse/AFE-1016

' DUDA : PARA QUÉ hacemos una pantalla propia que tiene las mismas opciones que la de ROKU, si igualmente tiene que ir a la pantalla de ROKU >> tenemos dos pantallas iguales.

' "Comportamiento esperado en la reproducción de contenido para cuando existe una suscripción en días de gracia".
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???
' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está EN DIAS DE GRACIA.

' * En vCard de un contenido
' * >>> Se ejecuta el API "payway/v2/purchasebuttoninfo"
' 	<<<  "response.playButton.visible" de la respuesta tiene un valor igual a "1"
' 	Se muestra el botón de "Play".
' * Selecciona el botón "Play"
' 	"response.playButton.paymentmethod.gateway" <> "rokugate"
' 	Se muestra el contenido.
' * Se encuentra la transacción y la misma esta EN DIAS DE GRACIA
' 	PANTALLA SUSCRIPCION EN DIAS DE GRACIA!!!
' * Selecciona la opción "Más tarde"
' 	>>> Continúa con la reproducción.
' 	EXIT del flujo
' * Selecciona la opción "Regularizar pago"
' 	* request = {}
' 	* request.params.recoveryContext = "playback"
' 	* request.command = "DoRecovery"
' 	* store.observeField("requestStatus", "onRequestStatus")
' 	* store.request = request
' 	* Se muestra la PANTALLA NATIVA de Roku "Update payment information"
' 	>>> Contiene las opciones: "Update payment information" / "Continuar viendo"
' * Usuario selecciona opción continuar viendo en pantalla de Roku
' 	Se reproduce el contenido
' * Usuario selecciona la opción "Update payment information" y registra un medio de pago válido
'     	"store.requestStatus.status" es igual a "1"
' 	"store.result.recoveryStatus" es igual a "1"
' 	Hay que actualizar la información de [Feature01] y [Feature02]
' * Continúa con la reproducción.
function RokuDoRecovery(data)
  if m.top.debug then print ghLogHead("RPay");"RokuDoRecovery -- init.";
  request = {}
  request.command = "DoRecovery"
  m.store = CreateObject("roSGNode", "ChannelStore")
  m.store.observeField("requestStatus", "RokuDoRecoveryReturn")
  m.store.request = request
end function
sub RokuDoRecoveryReturn(event)
  data = event.getData()
  if m.top.debug then
    print ghLogHead("RPay");"RokuDoRecoveryReturn -- init. data=";data
    print ghLogHead("RPay");"RokuDoRecoveryReturn -- init. data.context=";data.context
    print ghLogHead("RPay");"RokuDoRecoveryReturn -- init. data.result=";data.result
    print ghLogHead("RPay");"RokuDoRecoveryReturn -- init. data.result.recoveryProducts=";data.result.recoveryProducts
  end if
  m.top.cmdResult = {
    "cmd": "doRecovery"
    "response": data
  }
end sub

' ## 8
' -------------------------------------------------------------
' Feature 08: Reproducción de contenido - Usuario con suscripción Roku Pay suspendida
' 	https://dlatvarg.atlassian.net/browse/AFE-1017

' "Establecer el comportamiento esperado en la reproducción de contenido para cuando existe una suscripción en suspensión"
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???
' - PROBLEMA: (???) Según documentación ( https://developer.roku.com/es-ar/docs/developer-program/roku-pay/subscription-recovery/subscription-on-hold.md#dorecovery-api ), el "store.result.recoveryStatus" puede venir también en 2, lo que significaría que tiene varias suscripciones en recuperación ( a lo mejor, de otras aplicaciones? ), y devuelve un listado "recoveryProducts" con la lista de las suscripciones que todavía están en recuperación, por lo tanto, si la que mandamos nosotros no está entre esas, se puede entender que a pesar de tener otras en recuperación, recuperó la que nos importaba.
' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está SUSPENDIDA.

' * En vCard de un contenido
' * >>> Se ejecuta el API "payway/v2/purchasebuttoninfo"
' 	<<<  "response.playButton.visible" de la respuesta tiene un valor igual a "1"
' 	Se muestra el botón de "Play".
' * Selecciona el botón "Play"
' 	"response.playButton.paymentmethod.gateway" <> "rokugate"
' 	Se muestra el contenido.
' * Se encuentra la transacción y la misma esta SUSPENDIDA
' 	PANTALLA SUSCRIPCION EN DIAS DE GRACIA!!!
' * Selecciona la opción "Más tarde"
' 	<<< VUELVE A LA VCARD
' 	EXIT del flujo
' * Selecciona la opción "Regularizar pago"
' 	* request = {}
' 	* request.params.recoveryContext = "playback"
' 	* request.command = "DoRecovery"
' 	* store.observeField("requestStatus", "onRequestStatus")
' 	* store.request = request
' 	* Se muestra la PANTALLA NATIVA de Roku "Update payment information"
' 	>>> Contiene las opciones: "Update payment information" / "Cerrar"
' * Usuario selecciona la opción "Update payment information" y registra un medio de pago válido
'     	"store.requestStatus.status" = "1"
' 	"store.result.recoveryStatus" = "1"
' 	Hay que actualizar la información de [Feature01] y [Feature02]
' 	>>> Continúa con la reproducción.
' 	EXIT del flujo
' * Usuario selecciona la opción  "Cerrar"
'     	"store.requestStatus.status" <> "1" (VERIFICAR)
' 	"store.result.recoveryStatus" <> "1" (VERIFICAR)
' 	Hay que actualizar la información de [Feature01] y [Feature02] (VERIFICAR)
' 	<<< VUELVE A LA VCARD
' 	EXIT del flujo

' -------------------------------------------------------------
