sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/apa/asset"
  ' TODO preguntar que si se cambia o se clona esta api ya que la HU tiene otra url
  m.api.query.Append({
    "sessionKey": m.config.mfwk.appKey + "-" + ghGetRegistry("region")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- api="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  final = {}

  ' saco la region a las key _select_
  for each asset in res
    if asset.Instr(ghGetRegistry("region") + "_select_") = 0 then
      final[asset.Replace(ghGetRegistry("region") + "_select_", "select_")] = res[asset]
    else
      final[asset] = res[asset]
    end if
  end for

  m.global.setFields({
    assets: final,
  })

  m.top.content = res
end sub