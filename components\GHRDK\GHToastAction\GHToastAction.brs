' GHToast
' byGoose(20203 <EMAIL>

function init()
  m.top.debug = true
  m.top.focusable = true
  ' objects
  m.background = m.top.findNode("background")
  m.icon = m.top.findNode("icon")
  ' m.separator = m.top.findNode("separator")
  m.card = m.top.findNode("card")
  m.title = m.top.findNode("titleText")
  ' m.divisor = m.top.findNode("divisorText")
  m.body = m.top.findNode("bodyText")
  m.botonera = m.top.findNode("botonera")

  ' events
  m.top.ObserveField("visible", "onVisibleChange")
  m.top.ObserveField("focusedChild", "onFocusChange")
  m.botonera.ObserveField("selected", "onButtonSelected")
  m.botonera.ObserveField("backSelected", "onButtonCanceled")

  ' controls
  InitialDraw()
  buildVTimer()

  if m.top.debug then print ghLogHead("WIDGET");"Init "
end function
sub InitialDraw()
  if m.top.debug then print ghLogHead("WIDGET");"InitialDraw "
  m.title.setFields({
    visible: false
    font: ghGetComponentFont("GHErrorTitle")
  })
  m.body.setFields({
    visible: false
    font: ghGetComponentFont("GHErrorMessage")
  })
  m.icon.setFields({
    visible: false
    loadSync: true
    width: 100
    loadDisplayMode: "scaleToFit"
  })
  if m.top.debug then print ghLogHead("WIDGET");"InitialDraw rect.. ";m.card.boundingRect()
end sub
sub buildVTimer()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer --"
  m.VTimer = CreateObject("roSGNode", "Timer")
  m.VTimer.duration = m.top.time
  m.VTimer.repeat = false
  m.VTimer.ObserveField("fire", "VTimerTrigger")
  m.VTimer.control = "start"
  if m.top.debug then print ghLogHead("WIDGET");"buildKeyTimer -end- "
end sub
sub VTimerTrigger()
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer -- VISIBLE!"
  m.top.visible = false
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer -- FOCUS!"
  m.top.setFocus(false)
  m.top.wasClosed = true ' importante para limpiar memoria
  if m.top.debug then print ghLogHead("WIDGET");"buildVTimer -- CLOSE!"






















  ' PROBAR
  ' EN ROUTER, HAY UNA FUNCION QUE PONE EL FOCO EN LA PAGINA ACTUAL
  ' VER SI ESO ANDA...
  ' Y VER SI PUEDO DISPARARLA DESDE ACA, PROBABLEMENTE
  ' HAGA FALTA INYECTAR LAS FUNCIONES EN EL WIDGET, POR EJEMPLO



























end sub
' EVENTS
' -----------------------------
sub onFocusChange(event)
  ' ghDumpEvent(event)
  data = event.getData()

  print ghLogHead("WIDGET");"onFocusChange -- -*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/"
  print ghLogHead("WIDGET");"onFocusChange -- hasFocus       = ";m.botonera.hasFocus()
  print ghLogHead("WIDGET");"onFocusChange -- isInFocusChain = ";m.botonera.isInFocusChain()


  ' if m.top.debug then
  if data = invalid then
    print ghLogHead("WIDGET");"onFocusChange -- ME FUI !! "
    if m.top.visible then
      m.top.setFocus(true)
      m.top.focus = true
      ' m.card.setFocus(true)
      ' m.botonera.focus = true
      print ghLogHead("WIDGET");"onFocusChange -- TRATO DE VOLVER !! "
    else
      print ghLogHead("WIDGET");"onFocusChange -- YA ME CERRE !! "
    end if

    ' s = m.top.GetScene()
    ' print "pepe"

  else
    print ghLogHead("WIDGET");"onFocusChange -- ESTOY ACA! (";data.id;") "
  end if
  ' end if

  print ghLogHead("WIDGET");"onFocusChange -- -*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/-*/"
end sub
sub updateFieldFocus(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- ";data
  if data then
    m.top.setFocus(true)
    m.botonera.focus = true
    m.botonera.setFocus(true)
  end if
end sub
sub onVisibleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onVisibleChange -- ";data
  if data then m.VTimer.control = "start"
end sub
sub onTimeChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onTimeChange -- ";data
  m.VTimer.control = "stop"
  m.VTimer.duration = data
  m.VTimer.control = "start"
end sub
sub onTitleChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onTitleChange -- ";data
  m.title.setFields({
    text: data
    visible: (data <> "***")
  })
  Redraw()
end sub
sub onBodyChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onBodyChange -- ";data
  m.body.setFields({
    text: data
    visible: (data <> "***")
  })
  Redraw()
end sub
sub onIconChange(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onIconChange -- ";Right(data, 15)
  if data <> invalid then
    m.icon.uri = data
    m.icon.visible = true
    ' m.separator.setFields({
    '   width: 20
    '   visible: true
    ' })
  else
    m.icon.visible = false
    ' m.separator.setFields({
    '   width: 0
    '   visible: false
    ' })
  end if
  Redraw()
end sub

' function onKeyEvent(key, press) as boolean
'   if m.top.debug then print ghLogHead("WIDGET");"onKeyEvent -- key=";key;" press=";press
'   handled = true
'   if press then
'     if key = "back" then
'       ' m.top.visible = false
'       print "**********"
'       print "BACK"
'       print "**********"
'       handled = true
'     else if key = "OK" then
'       ' m.top.visible = false
'       print "**********"
'       print "OK"
'       print "**********"
'       handled = true
'     end if
'   end if
'   return handled
' end function


sub onOkChange(event)
  data = event.getData()
  if data <> invalid and data <> "" then
    if m.top.debug then print ghLogHead("WIDGET");"onOkChange -- ";data
    m.botonera.observeFieldScoped("selected", data)
  end if
end sub
sub onCancelChange(event)
  data = event.getData()
  if data <> invalid and data <> "" then
    if m.top.debug then print ghLogHead("WIDGET");"onCancelChange -- ";data
    m.botonera.observeFieldScoped("backSelected", data)
  end if
end sub

sub onButtonSelected(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-"
  if m.top.debug then print ghLogHead("WIDGET");"onButtonSelected -- ";data
  if m.top.debug then print ghLogHead("WIDGET");"=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-"
  VTimerTrigger()
end sub
sub onButtonCanceled(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-"
  if m.top.debug then print ghLogHead("WIDGET");"onButtonCanceled -- ";data
  if m.top.debug then print ghLogHead("WIDGET");"=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-"
  VTimerTrigger()
end sub

' DRAW
' -----------------------------
sub Redraw()
  if m.top.debug then print ghLogHead("WIDGET");"Redraw -- GO! alignement="m.top.alignement

  ' m.divisor.visible = (m.title.visible and m.body.visible)

  m.card.translation = [0, 0]
  m.card.horizAlignment = m.top.alignement
  rec = m.card.boundingRect()

  bW = rec.width + (m.top.contentOffset[0] * 2)
  bH = rec.height + (m.top.contentOffset[1] * 2)
  bX = rec.x - m.top.contentOffset[0]
  bY = rec.y - m.top.contentOffset[1]
  m.background.setFields({ width: bW, height: bH, translation: [bX, bY] })

  if m.top.debug then
    print ghLogHead("WIDGET");"Redraw BoundRec=";" ";rec.x;" ";rec.y;" ";rec.width;" ";rec.height
    print ghLogHead("WIDGET");"Redraw Background=";bX;" ";bY;" ";bW;" ";bH
  end if
end sub
' END FILE



