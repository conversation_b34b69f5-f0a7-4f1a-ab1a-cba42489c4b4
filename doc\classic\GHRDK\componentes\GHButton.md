# GHButton

## Propiedades

| Nombre       | Tipo    | Default                          | Descripción                                                                            |
| ------------ | ------- | -------------------------------- | -------------------------------------------------------------------------------------- |
| text         | string  | MyButton                         | texto de la etiqueta del botón.                                                        |
| wrap         | boolean | false                            | si la etiqueta del botón debe continuar en multilínea o bien mostrar el elipsado [...] |
| font         | node    | `ghGetComponentFont("GHButton")` | objeto de tipo font para la etiqueta del botón                                         |
| translation  | string  | [0,0]                            | posición del botón, incluido el borde de selección                                     |
| width        | string  | 350                              | ancho del botón, incluido el borde de selección                                        |
| height       | string  | 80                               | alto del botón, incluido el borde de selección                                         |
| horizAlign   | string  | center                           | alineamiento horizontal de la etiqueta dentro del botón.                               |
| vertAlign    | string  | center                           | alineamiento vertical de la etiqueta dentro del botón.                                 |
| backColor    | string  | 0xFFFFFF                         | color de fondo del botón cuando no está seleccionado.                                  |
| color        | string  | 0x000000                         | color de texto del botón cuando no está seleccionado.                                  |
| selBackColor | string  | 0xFFFFFF                         | color de fondo del botón cuando está seleccionado.                                     |
| selColor     | string  | 0x000000                         | color de texto del botón cuando está seleccionado.                                     |
| padding      | string  | 8                                | espacio entre el fondo del botón y el texto.                                           |
| focusPadding | string  | 12                               | espacio entre el borde de selección y el botón.                                        |
| focusMap     | string  | pkg:/images/focus01.9.png        | imagen tipo 9 del borde de selección                                                   |
| focusColor   | string  | 0xFFFFFF                         | color del borde de selección.                                                          |
| focus        | boolean | false                            | si el componente tiene foco.                                                           |
| selected     | boolean | false                            | si el componente fue seleccionado.                                                     |
| value        | string  | mybutton                         | valor a devolver en el boton, para uso con `GHButtonGroup`.                            |
| debug        | boolean | false                            | enciende el logeo por consola.                                                         |

## Ejemplo

Definición en el xml

```xml
<GHButton id="btnLogin"
          width= "368"
          height= "72"
          text="INICIA SESIÓN"
          value="page_login"
          translation="[464,416]"
          backcolor="#6C57A0"
          color="#FFFFFF"
          selColor="#FFFFFF"
          selBackColor="#6C57A0"
          focusColor="0xFFFFFFFF"
/>
```

Cambio del valor de las propiedades

```basic
m.btnLogin= m.top.findNode("btnLogin")
m.btnLogin.text= gh Translate("landing_menu_option_button_login", "INICIA SESIÓN")
```

Acción sobre el botón

```basic
m.btnLogin.ObserveField("selected", "OnButtonSelected")
```
