' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

'
' FIELDS
' -----------------------------
sub updateFieldText()
  if m.top.text <> invalid then m.label.text = m.top.text
end sub
sub updateFieldWrap()
  Refresh()
end sub

sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then
    m.background.width = m.top.width
    recalcLabelSizeAndPadding()
  end if
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then
    m.background.height = m.top.height
    recalcLabelSizeAndPadding()
  end if
end sub

sub updateFieldHorizAlign()
  if m.top.horizAlign <> invalid then m.label.horizAlign = m.top.horizAlign
end sub
sub updateFieldVertAlign()
  if m.top.vertAlign <> invalid then m.label.vertAlign = m.top.vertAlign
end sub

sub updateFieldBackColor()
  if m.top.backColor <> invalid then m.background.color = m.top.backColor
end sub
sub updateFieldColor()
  if m.top.color <> invalid then m.label.color = m.top.color
end sub

sub updateFieldFocusMap()
  if m.top.focusMap <> invalid then m.border.uri = ghGetImageByMode(m.top.focusMap)
end sub
sub updateFieldPadding()
  if m.top.padding <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldBorder()
  if m.top.border <> invalid then recalcLabelSizeAndPadding()
end sub

' utils ------------
sub Refresh()
  recalcLabelSizeAndPadding()
  recalcColors()
end sub
sub recalcLabelSizeAndPadding()
  width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))
  paddingFocus = ghXtoAbstract(val(m.top.focusPadding))
  padding = ghXtoAbstract(val(m.top.padding))
  paddingFocusSize = paddingFocus * 2
  paddingSize = padding * 2

  m.border.translation = "[0,0]" ' el unico que va en la misma posicion
  m.border.height = height
  m.border.width = width
  m.background.translation = ghVal2Trans(paddingFocus, paddingFocus)
  m.background.height = height - paddingFocusSize
  m.background.width = width - paddingFocusSize
  m.label.translation = ghVal2Trans(padding, padding)
  m.label.width = width - paddingFocusSize - paddingSize
  m.label.height = height - paddingFocusSize - paddingSize
  m.disableButtonGradient.height = m.background.height
  m.disableButtonGradient.width = m.background.width
  m.disableButtonGradient.translation = ghVal2Trans(paddingFocus, paddingFocus)


  if m.top.wrap then
    m.label.wrap = m.top.wrap
    m.label.vertAlign = "top"
    m.label.height = invalid
    m.label.lineSpacing = 0
  end if

  if m.top.rightLabelContent <> "" then
    ' se usa con etiqueta a la izquierda
    m.label.horizAlign = "left"
    ' calculo de posicion
    rlx = m.label.translation[0] + m.label.width - paddingSize
    rly = m.label.translation[1]
    if m.top.debug then print ghLogHead();"[";m.top.rightLabelContent;"] rlx=";rlx;" rly=";rly
    ' seteo
    m.rightLabel.SetFields({
      translation: [rlx, rly]
      text: m.top.rightLabelContent
      height: m.label.height
      visible: true
    })
    ' font
    if m.top.rightLabelFont <> invalid then
      m.rightLabel.font = m.top.rightLabelFont
    else
      m.rightLabel.font = m.top.font
    end if
  else
    ' no lo muestro
    m.rightLabel.visible = false
  end if

  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding -- "
  if m.top.debug then print m.background.translation, m.background.width, m.background.height
  if m.top.debug then print m.label.translation, m.label.width, m.label.height
end sub

sub recalcColors()
  if m.top.focus then
    m.border.visible = true
    m.background.color = m.top.selBackColor
    m.label.color = m.top.selColor
    m.rightLabel.color = m.top.selColor
  else
    m.border.visible = false
    m.background.color = m.top.backColor
    m.rightLabel.color = m.top.color
  end if
end sub

'
'
' END FILE ------------------