sub Init()
  m.title = m.top.findNode("title")
  m.msg = m.top.findNode("msg")

  m.spinner = m.top.findNode("spinner")
  m.markupgrid = m.top.findNode("Grid")

  m.top.observeField("data", "CargarDatos")
end sub

sub onWasShown() ' event
  m.markupgrid.setFocus(true)
end sub

sub CargarDatos() ' data = []
  apiVertical = ghCallApi("Vertical", "verticalHandleOk", "verticalHandleError", false)
  apiVertical.value = ghGetChild(m.top.data, "id")
  if ghGetChild(m.top.data, "name") <> invalid then
    apiVertical.provider = "1"
  else
    apiVertical.provider = "3"
  end if
  apiVertical.control = "run"

  fullName = ghGetChild(m.top.data, "first_name")
  if fullName <> invalid then
    fullName = fullName + " " + ghGetChild(m.top.data, "last_name", "")
  else
    fullName = ghGetChild(m.top.data, "name")
    if fullName <> invalid then
      fullName = fullName + " " + ghGetChild(m.top.data, "surname", "")
    else
      fullName = ghGetChild(m.top.data, "fullname", "")
      if fullName = invalid then
        fullName = "UnFullname"
      end if
    end if
  end if

  m.fullname = fullName

  m.title.setFields({
    font: ghGetFont(32, "medium")
    horizAlign: "center"
    text: fullName
  })
end sub

sub verticalHandleOk(event)
  data = event.getData()

  items = ghGetChild(data, "groups")

  content = CreateObject("roSGNode", "GHContent")

  cantidad = 0
  if items <> invalid then
    cantidad = items.count()
  end if

  if cantidad = 0 then
    m.msg.setFields({
      visible: true
      font: ghGetFont(32, "medium")
    })
  end if

  for i = 0 to cantidad - 1
    infoCard = ghGetChild(items, "#" + i.toStr())
    isSerie = ghGetChild(infoCard, "is_series", false)
    ContentType = 1
    if isSerie = true then
      ContentType = 2
    end if

    infoCard.ContentType = ContentType

    itemcontent = content.createChild("GHContent")
    itemcontent.setFields({
      data: infoCard
    })
  end for

  m.markupgrid.content = content
  m.markupgrid.UnobserveField("itemSelected")
  m.markupgrid.ObserveField("itemSelected", "OnCardSelect")

  m.spinner.visible = false
  m.markupgrid.setFocus(true)
end sub

sub showError(salir = true, message = "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (TAL-01)", title = "Hubo un error inesperado", accept = "OK")
  ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
  ScrGenericMessage.SetFields({
    title: title,
    message: message,
    accept: accept,
  })
  if salir = true then
    ScrGenericMessage.ObserveField("wasClosed", "OnExit")
  end if

  m.top.routerChild = {
    page: ScrGenericMessage,
    fields: {}
  }
end sub

sub OnExit(event)
  data = event.getData()
  if data then
    m.top.routerClose = true
  end if
end sub

sub verticalHandleError() ' event
  showError(true)
end sub

' si las nuevas vcard abiertas cambia el reloadhome, aviso en cascada hasta llegar a la primer vcard
sub handleReload() ' event
  m.top.reloadHome = true
end sub

sub OnCardSelect(event)
  data = event.getData()

  if data <> invalid
    m.markupgrid.setFocus(false)

    item = m.markupgrid.content.getChild(data)
    group_id = ghGetChild(item, "data.group_id", invalid)
    if group_id = invalid then
      group_id = ghGetChild(item, "data.id", invalid)
    end if

    vcard = CreateObject("roSGNode", "Vcard")
    vcard.id = "Vcard-" + group_id.Tostr()
    vcard.ObserveField("reloadHome", "handleReload")

    ' contenido = ghGetChild(data, "data")
    contentType = 1
    if ghGetChild(item, "data.is_series", false) then
      contentType = 2
    end if
    contenido = {
      contentType: contentType
    }

    contenido.groupId = group_id

    ' enviando a google analytics
    data = ghGetChild(item, "data", invalid)
    contentType = "movie"
    if ghGetChild(data, "is_series", false) = true or ghGetChild(data, "episodenumber", "") <> "" or ghGetChild(data, "season", "") <> "" then
      contentType = "series"
    end if

    order = ghGetChild(m,"markupgrid.itemSelected")
    ' enviando a google analytics
    GA4Event("select_content", {
      content_id: group_id,
      content_name: ghGetChild(data, "title", "")
      content_type: contentType
      content_category: "not apply"
      content_availability: "by subscription"
      content_position: order

      content_list: m.fullname
      content_section: "talent subcategories"

      user_type: getUserTypeGA4(true),
      country: ghGetRegistry("country_code", "user"),
      user_id: ghGetRegistry("user_id", "user"),
      screen_name: "content selection"
      screen_class: "/content-selection"
    })

    m.top.routerChild = {
      page: vcard,
      fields: {
        data: contenido
      }
    }
  end if
end sub