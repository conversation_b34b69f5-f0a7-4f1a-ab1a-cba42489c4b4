<?xml version="1.0" encoding="utf-8" ?>

<component name="GHVideoPanel" extends="Group">
  <script type="text/brightscript" uri="GHVideoPanel.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- estados -->
    <field id="positionPlayer" type="int" value="0" onChange="refreshPositionPlayer" />
    <field id="state" type="string" value="" alwaysNotify="true" onChange="onStateChange" />
    <field id="position" type="int" value="0" onChange="refreshPosition" />
    <field id="duration" type="int" value="0" onChange="refreshPosition" />
    <field id="availableAudioTracks" type="array" />
    <field id="availableSubtitleTracks" type="array" />
    <!-- revisar -->
    <field id="selected" type="assocarray" alwaysNotify="true" />
    <!-- interfaz de entrada -->
    <field id="inTo" type="assocarray" value="" alwaysNotify="true" onChange="onInTo" />
    <field id="trickBif" type="node" />
    <field id="content" type="node" onChange="onContentChange" />
    <field id="langData" type="array" />
    <field id="info" type="assocarray" onChange="onInfoUpdate" />

    <field id="showOmitirIntro" type="boolean" value="false" alwaysNotify="true" onChange="checkOIntro" />

    <!-- interfaz de salida -->
    <field id="cmd" type="assocarray" value="" alwaysNotify="true" />
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
  </interface>

  <children>
    <Poster id="fondo" uri="pkg:/images/gradientMiniEpg.png" translation = "[0,0]" width="1280" height="720" />
    <LayoutGroup id="infoCard" layoutDirection="horiz" itemSpacings="[10]" horizAlignment="left" vertAlignment="center" translation="[130,464]" />
    <!-- <Label id="xInfoTitle" text="." color="0xFFFFFF" translation="[0,0]" width="600" /> -->
    <!-- <Label id="xInfoYear" text="." color="0xFFFFFF" translation="[0,0]" width="200" /> -->
    <!-- <Label id="xInfoDesc" text="." color="0xFFFFFF" translation="[0,0]" width="800" /> -->
    <!-- </LayoutGroup> -->
    <!-- <LayoutGroup id="trickpics" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" translation="[640,400]" itemSpacings="10" visible="false"> -->
    <!--<Poster id="tPic-1" width="216" height="162" /> -->
    <!--<Poster id="tPic-2" width="240" height="180" /> -->
    <!-- <GHFramePoster id="tPic-3" visible= "false" width="184" height="104" translation="[640,400]" /> -->
    <!--<Poster id="tPic-4" width="240" height="180" /> -->
    <!--<Poster id="tPic-5" width="216" height="162" /> -->
    <!-- </LayoutGroup> -->
    <Poster id="tpickU" width="184" height="104" visible= "false" />

    <LayoutGroup id="avance" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" translation="[640,512]" itemSpacings="10">
      <Label id="barSeekCurrent" text="current" color="0xFFFFFF" />
      <GHSeekBar id="barSeek" width="1020" focusable="true" />
      <Label id="barSeekTotal" text="total" color="0xFFFFFF" />
    </LayoutGroup>
    <GHButton id="ointro" visible="false" translation="[40,550]" focusMap="" width= "250" height= "70" selColor="#FFFFFF" selBackColor="#981C15" backcolor="#981C15" color="0xFFFFFF" text="OMITIR INTRO" value="btnNext" />
    <GHButtonLayout handleKey="false" id="playbuttons" itemSpacings="5" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" layout="childs" orientation="horizontal" translation="[640,630]" exitUp="true" exitLeft="true" />

    <Label id="info" text="" color="0xFFFFFF" translation="[0,0]" horizAlign="right" width="1280" />
  </children>

</component>
