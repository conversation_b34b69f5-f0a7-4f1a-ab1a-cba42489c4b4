' Login
' -----------------------------

sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/user/settermsandconditions"
  m.api.query.Append({
    api_version: ghGetChild(m.global.config, "api.version.ProfileRead", m.global.config.api.versions.default)
    terminos: m.top.terminos
    user_hash: ghGetRegistry("session_userhash", "user")
  })
  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- response= ";res.response
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)

    print ghLogHead();"ProcessData -- raw= ";m.lastResponse.headers
    print ghLogHead();"ProcessData -- raw= ";m.lastResponse.code
  end if

  if res = invalid then
    m.top.error = {
      code: "No response"
      error: ["No response from api"]
      html_code: m.lastResponse.code
      html_headers: m.lastResponse.headers
    }
    return
  end if

  errorMsg = ghGetChild(res, "errors.code")
  if errorMsg <> invalid then
    m.top.error = {
      errors: ghGetChild(res, "errors")
      html_code: m.lastResponse.code
      html_headers: m.lastResponse.headers
    }
  else
    m.top.content = {
      msg: "OK"
      html_code: m.lastResponse.code
      html_headers: m.lastResponse.headers
    }
  end if
  ' ----------------------------------------------------
  if m.top.debug then
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
    print ghLogHead();"ProcessData -- content:";m.top.content
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
    print ghLogHead();"ProcessData -- error:";m.top.error
    print ghLogHead();"++++++++++++++++++++++++++++++++++++++"
  end if
  ' ----------------------------------------------------
end sub


' "response": null,
' "status": "1",
' "msg": "ERROR",
' "errors": {
'   "error": [
'     "dataprovider_already_accepted"
'   ],
'   "code": "already_accepted"
' }