' BuyConfirm
' ---------------------------

sub DataInit()
  ' ******************************************
  ' PAYWAYCONFIRM
  ' ******************************************

  m.api.url = m.config.mfwk.host + m.top.buylink

  m.api.query.delete("api_version")

  m.api.headers.Append({ "user-token": ghGetRegistry("user_token", "user") })

  data = m.top.data

  print "final data :" FormatJson(data)

  m.api.query.Append(data)
  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user")
  })
  m.api.timeout = 30000

  print "m.api :" FormatJson(m.api)

end sub

sub ProcessData(res, raw)
  if m.top.debug then print "*********************************"
  if m.top.debug then print ghLogHead();"body = ";res
  if m.top.debug then print ghLogHead();"body.entry = ";ghGetChild(res, "entry", "! no existe")

  ' para probar errores
  ' res.errors = {
  '   code: "TEXT_PGS_PRUEBA_2"
  ' }

  'STATIC VALUE ADD START
  account = ghGetChild(m.top.data, "account")
  pin = ghGetChild(m.top.data, "pin", invalid)

  print "payway your account number :"account, pin
  if pin = invalid and account <> invalid
    if account.Len() <= 9
      print "payway PhoneNumber error logic"
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_error.json"))
    else
      print "payway PhoneNumber success logic"
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_success.json"))
    end if
  else
    if pin <> invalid and pin.Len() = 5
      print "payway pin success logic"
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_pin_success.json"))
    else
      print "payway pin error logic"
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_pin_error.json"))
    end if
  end if

  print "CHECK YOUR RESPONSE :" res
  'STATIC VALUE ADD END

  if account.Len() < 10 or (pin <> invalid and pin.Len() <> 5)
    errors = ghGetChild(res, "errors")
    if errors <> invalid then
      res.raw = raw
      m.top.error = res
      return
    end if
  end if

  content = ghGetChild(res, "response", {})
  content.msgTitle = "¡Transacción exitosa!"
  content.msgContent = ghDecodeHTML(ghGetChild(content, "msg", "Su transacción ha sido exitosa."))

  updateGlobalArray("lasttouch", {
    purchased: ghGetChild(res, "response.lastTouch")
  })

  m.top.content = content

  if m.top.debug then print "*********************************"
end sub

