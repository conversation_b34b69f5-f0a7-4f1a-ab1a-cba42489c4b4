sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/apa/metadata"
  ' m.api.url = "https://mfwkstbroku-api.clarovideo.net/services/apa/metadata"
  m.api.query = {
    "sessionKey": "d48c48c956cda082e2e03b717c81c220-" + ghGetRegistry("region"),
  }
  ' m.api.headers.AddReplace("partition", "claroglobaluat")

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
  end if
end sub

sub ProcessData(res, raw)
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  trans = ParseJson(ghGetChild(res, "translations"))
  print trans
  if m.top.debug then print ghLogHead();"trans = ";type(trans)
  if trans <> invalid then
    print "ENCONTRE TRANS!!!"
    translations = ghGetChild(trans, "language." + ghGetRegistry("region"))
    print translations
    if translations = invalid then
      translations = {}
    end if
  else
    print "NO LO ENCONTRE!!!!"
    translations = {}
  end if

  m.top.content = { textos: translations }

end sub