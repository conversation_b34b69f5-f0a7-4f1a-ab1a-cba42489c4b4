sub Init()
  if m.top.debug then print ghLogHead();"Init"
  ' componentes
  m.itemBack = m.top.findNode("itemBack")
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  ' item = m.top.itemContent
  data = ghGetChild(m.top.itemContent, "data")
  rowType = ghGetChild(data, "rowtype", "std")
  order = ghGetChild(data, "order", 1)
  if m.top.debug then
    print ghLogHead();"Refresh -------------------- [";rowType;"]"
    print ghLogHead();m.top.itemContent
    print ghLogHead();m.top.itemContent.data
    print ghLogHead();"---------------------------- [";rowType;"]"
  end if

  m.itemPoster.translation = [0, 0]

  m.itemBack.translation = [8, 22]
  m.itemBack.width = 75
  m.itemBack.height = 104
  if order < 11 then
    m.itemBack.uri = ghGetAsset("ribbons_top_" + ghTrim(str(order)) + "_icon", "")
  else
    m.itemBack.uri = ghGetImageByMode("defaultLoMasTop.png")
  end if
  m.itemBack.visible = true

  m.itemPoster.translation = [74, 0]
  m.itemPoster.width = 274
  m.itemPoster.height = 153
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  ' drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height, 79)
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height, 79) ' , 5, 5, 5, 5)
  initTimer()
end sub
