sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/user/favoriteadd"

  if m.top.add = false then
    m.api.url = m.config.mfwk.host + "/services/user/favoritedel"
  end if

  m.api.query.Append({
    "object_type": "1",
    "object_id": m.top.group_id,
    "user_hash": ghGetRegistry("session_userhash", "user")
    "user_id": ghGetRegistry("user_id", "user"),
    "region": ghGetRegistry("region"),
    "api_version": ghGetChild(m.global.config, "api.version.favoriteadd", m.global.config.api.versions.default),
    ' "api_version": "v5.93"
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = res.response

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' ghSetRegistry("lasttouch_favorited", res.lasttouch.favorited, "user")
  updateGlobalArray("lasttouch", { favorited: res.lasttouch.favorited })

  ' si es canal modificamos el valor global de favorites
  if m.top.isChannel = true then
    channels = ghGetChild(m.global, "favorites.channels", {})

    if channels = invalid then channels = {}

    if channels = invalid then channels = {}

    if m.top.add = true then
      channels[m.top.group_id] = m.top.group_id
    else
      channels[m.top.group_id] = ""
    end if

    ' print "YYYYYYYYYYYYYYYYYYYYY"
    ' print channels
    ' print m.global
    ' print m.global.favorites
    m.global.setFields({
      favorites: { channels: channels }
    })
  end if

  m.top.content = {}
end sub