sub DataInit()
  m.top.debug = false

  m.api.url = m.config.mfwk.host + "/services/track/dubsubchange"

  m.api.query.Append({
    "user_token": ghGetRegistry("user_token", "user")
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"),
    "group_id": m.top.groupId
    "content_id": m.top.contentId
    "preferred_audio": m.top.preferred_audio
    "preferred_subtitle": m.top.preferred_subtitle
    "timecode": m.top.timecode
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
    print ghLogHead();"DataInit -- params="m.api.body
    print ghLogHead();"DataInit -- params="m.api.headers
  end if
end sub

sub ProcessData(res, raw)
  response = res.response

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = ghGetChild(res, "data")
end sub