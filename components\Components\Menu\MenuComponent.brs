' MenuComponent
' -----------------------------
sub Init()
  m.logger = CreateLogger()
  m.top.debug = true

  m.versionSuffix = ""
  if ghGetChild(m.global, "model", "") = "lite" then
    m.versionSuffix = "Lite"
  end if

  m.haveChannel = false

  m.menu = m.top.findNode("menu")
  m.menu.ObserveField("value", "OnMenuSelected")
  m.menu.ObserveField("backSelected", "onBackMenu")

  changeNav()
end sub

sub changeNav()
  ' para cambiar el comportamiento segun si esta logueado
  if m.versionSuffix = "" then
    m.menu.ObserveField("openProfile", "handleExit")
  else
    if ghGetRegistry("isLoggedIn") = "true" then
      m.menu.UnobserveField("openProfile")
      m.menu.ObserveField("openProfile", "handleProfile")
    else
      m.menu.UnobserveField("openProfile")
      m.menu.ObserveField("openProfile", "MnuLogin")
    end if
  end if


  m.options = []

  nav = m.global.nav
  for each item in ghGetChild(nav, "items", [])
    appBehaviourString = ghGetChild(item, "app_behaviour", "")

    appBehaviour = {}
    if appBehaviourString <> invalid or appBehaviourString <> "" then
      ' borro palabra app_beahviour: por si viene mal, para que no falle el parseo
      appBehaviourString = appBehaviourString.Replace("app_beahviour:", "")
      appBehaviour = ParseJson(appBehaviourString)
    end if
    showNode = ghGetChild(appBehaviour, "node_config.show_node", true)
    layout = ghGetChild(appBehaviour, "layout", "")

    if showNode = true then
      if layout = "guidechannels" then
        m.haveChannel = true

        m.options.Push({
          optId: item.code,
          optLabel: {
            component: "GHOption",
            props: { text: item.text }
          }
          optAction: {
            actionType: "function",
            actionName: "MnuLive"
          },
          noSelection: true ' para que no quede seleccionado el menu
        })
      else
        ' comentado ya que deberia venir el width ?
        ' if item.image <> invalid and item.image <> "" then
        '   width = 120 ' TODO deberia venir en la config ?
        '   m.options.Push({
        '     optId: item.code,
        '     optLabel: {
        '       component: "GHOptionImg",
        '       props: {
        '         height: 36
        '         width: width
        '         imageURI: item.image
        '         imageHeight: 24
        '         imageWidth: width
        '         imageVertAlign: "center"
        '         imageHorizAlign: "center"
        '         color: "0xFFFFFF80"
        '         selColor: "0xFFFFFFFF"
        '       }
        '     }
        '     optAction: {
        '       actionType: "level",
        '       actionName: item.code
        '     }
        '   })
        ' else
        m.options.Push({
          optId: item.code,
          optLabel: {
            component: "GHOption",
            props: { text: item.text }
          }
          optAction: {
            actionType: "level",
            actionName: item.code
          }
        })
        ' end if
      end if
    end if
  end for

  ' m.options.Push({
  '   optId: "subscripciones",
  '   optLabel: {
  '     component: "GHOption",
  '     props: { text: "subscripciones" }
  '   }
  '   optAction: {
  '     actionType: "function",
  '     actionName: "MnuProfile"
  '   }
  ' })


  ' if m.haveChannel = false then
  '   m.options.unshift({ ' Live -----
  '     optId: "live",
  '     optLabel: {
  '       component: "GHOption",
  '       props: { text: "Live" }
  '     }
  '     optAction: {
  '       actionType: "function",
  '       actionName: "MnuLive"
  '     },
  '   })
  ' end if

  m.options.unshift({ ' Search -----
    optId: "search",
    optLabel: {
      component: "GHOptionImg",
      props: {
        height: 36
        width: 45
        imageURI: ghGetAssetByMode("search_icon", "pkg:/images/lupa.png")
        imageHeight: 24
        imageWidth: 24
        imageVertAlign: "center"
        imageHorizAlign: "center"
        color: "0xFFFFFF80"
        selColor: "0xFFFFFFFF"
      }
    }
    optAction: {
      actionType: "function",
      actionName: "MnuSearch"
    }
  })

  ' ------------------------------------------------
  ' devuelve el item seleccionado
  node = getMenuSelected()
  m.logger.debug("getMenuSelected_changeNav", { node: node })

  ' agrego true al item seleccionado
  optionsFinal = []
  for opNum = 0 to m.options.Count() - 1
    opcion = m.options[opNum]
    if opcion.optId = node then
      opcion.selected = true
      m.menu.valueSelected = opNum
    else
      opcion.selected = false
    end if
    optionsFinal.push(opcion)
  end for
  ' m.logger.debug("optionsFinal", { options: optionsFinal })

  m.menu.data = optionsFinal
  'imagen por defecto
  'imagen en global seleccionada
  if m.versionSuffix = "" then
    m.menu.profile_image = ghGetImageByMode("ic_avatar.png")
  else
    m.menu.profile_image = ghGetChild(m.global, "profile_img", ghGetImageByMode("ic_avatar.png"))
  end if
end sub

sub changeStateFocus(event)
  reloadfocus = event.getData()
  navSelect = m.top

  if reloadfocus = true then
    print reloadFocus; " reloadFocus true"
  else
    print reloadFocus; " reloadFocus false"
  end if

  ' devuelve el item seleccionado
  node = getMenuSelected()
  print node; " node selected reloadFocus"
  navSelect = m.global.navSelect
  print navSelect ; " anavSelect"
  'm.top.menuSelected = node
  'changeNav()


end sub

function getMenuSelected(data = invalid)
  m.logger.debug("getMenuSelected", { data: data })

  ' seteo menu por defecto / nodo por defecto viene en apa
  node = ghGetChild(m.global.nodes, ghGetRegistry("region") + ".susc.default_node")

  ' si encuentro el nodo por defecto de apa, selecciono este
  ' si no se encuentra, selecciono el primero que no sea el search
  firstItem = invalid
  nodoApa = false

  ' nodo APA
  cant = m.options.count()
  for i = 0 to cant - 1
    nodo = m.options[i]

    if nodo.optId = node then
      nodoApa = true
    end if

    if firstItem = invalid and nodo.optId <> "search" then
      firstItem = nodo
    end if
  end for

  if nodoApa = false then
    node = ghGetChild(firstItem, "optId", "")
  end if

  ' si no tengo nada guardado, lo almaceno en global
  if m.global.navSelect <> invalid and m.global.navSelect <> "" then
    node = m.global.navSelect
  end if

  m.top.menuSelected = node

  m.logger.debug("getMenuSelected - node:", { node: node })

  return node
end function

sub updateFieldFocus(event)
  data = event.getData()
  print "focusa " ;data
  m.menu.focus = data
end sub

sub onBackMenu()
  m.top.backSelected = true
end sub

sub OnMenuSelected(event)
  data = event.getData()
  opcion = data

  ' no selecciono el menu, si tiene el parametro noSelection = true
  ' si es false, le digo al menu que seleccionar
  if ghGetChild(opcion, "noSelection", false) <> true then
    m.global.setFields({
      navSelect: ghGetChild(opcion, "optId")
    })
  end if

  if opcion <> invalid then
    accion = ghGetChild(opcion, "optAction")

    if accion.actionType = "level" then ' niveles -----
      if m.top.hasField("routerReset") then
        m.top.routerReset = {
          page: "HomePage" + m.versionSuffix,
          fields: {
            nodo: ghGetChild(opcion, "optId")
          }
        }
      end if
    else if ghGetChild(accion, "actionType") = "function" then ' funciones especiales -----
      if ghGetChild(accion, "actionName") = "MnuSearch" then
        MnuSearch()
      else if ghGetChild(accion, "actionName") = "MnuLive" then
        MnuLive()
        ' else if ghGetChild(accion, "actionName") = "MnuPrueba" then ' no se usa
        '   MnuPrueba()
      else if ghGetChild(accion, "actionName") = "MnuLogout" then
        MnuLogout()
      else if ghGetChild(accion, "actionName") = "MnuProfile" then
        MnuSubscriptions()
      end if
    else
      m.top.action = accion
    end if
  end if
end sub

sub handleExit()
  dialog = createObject("roSGNode", "Dialog")
  dialog.ObserveField("buttonSelected", "On_msgDialog_buttonSelected")
  dialog.title = "Perfil"
  ' dialog.message = "Press * To Dismiss"
  dialog.iconUri = ghGetImageByMode("")
  dialog.dividerUri = ghGetImageByMode("Divider-separator-line.png")
  dialog.titleColor = "#FFFFFF"
  dialog.optionsDialog = true
  ' dialog.buttons = ["Perfil", "Cerrar sesión"]
  dialog.buttons = ["Cerrar sesión"]
  dialog.buttonGroup.focusBitmapUri = ghGetImageByMode("Keyboard-focus01.9.png")
  dialog.buttonGroup.focusedIconUri = ghGetImageByMode("")
  dialog.buttonGroup.iconUri = ghGetImageByMode("")
  dialog.buttonGroup.focusedTextColor = "0xFEFEFEFF"
  dialog.buttonGroup.textColor = "0xFEFEFEFF"
  dialog.backgroundUri = ghGetImageByMode("PopUp-Notification.9.png")
  m.top.GetScene().dialog = dialog
end sub

sub On_msgDialog_buttonSelected()
  dialog = m.top.GetScene().dialog

  if dialog <> invalid then
    dialog.close = true
  end if

  if dialog.buttonSelected = 0 then
    MnuLogout()
  end if
end sub

sub handleProfile()
  m.top.routerChild = { page: "AvatarPageLite" }
end sub
