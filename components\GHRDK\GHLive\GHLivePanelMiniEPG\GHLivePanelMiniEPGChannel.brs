' GHLivePanelMiniEPGItem
' ------------------------------

sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init ** "
  ' componentes
  m.backpanel = m.top.findNode("backpanel")
  m.card = m.top.findNode("card")

  m.candado = m.top.findNode("candado")
  m.corazon = m.top.findNode("corazon")

  m.candado.translation = [160, 10]
  m.corazon.translation = [25, 70]
  m.candado.uri = ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))
  m.corazon.uri = ghGetAsset("playingLive_alert_channelFavorite_icon")

  m.image = m.top.findNode("image")
  m.title = m.top.findNode("title")
  ' eventos generales
  m.top.ObserveField("width", "Draw")
  m.top.ObserveField("height", "Draw")
  ' acciones
  InitialDraw()
end sub
' FORMATO
' ------------------------------
sub InitialDraw()
  if m.top.debug then print ghLogHead();"InitialDraw ** "
  m.image.setFields({
    uri: ghGetAsset("mini_epg_channel_default", "pkg:/images/Placeholder_tv envivo.png") 'acá va la key
    translation: [0, 0]
    loadDisplayMode: "scaleToFit"
  })
  m.title.setFields({
    width: 50
    height: 50
    horizAlign: "center"
    vertAlign: "center"
    text: "" ' esto es de la izquierda
    font: ghGetFont(24, "regular")
    color: "#FFFFFF"
  })
end sub
sub Draw()
  if m.top.debug then print ghLogHead();"Draw ** "
  m.card.setFields({
    layoutDirection: "horiz"
    horizAlignment: "left"
    vertAlignment: "top"
    itemSpacings: 0
    translation: [m.top.cardPaddingX, m.top.cardPaddingY]
  }) ' card
  m.backpanel.setFields({
    color: m.top.back_color
    width: m.top.width
    height: m.top.height
    translation: [0, 0]
  }) ' back
  m.title.setFields({
    width: (m.top.width - (2 * m.top.cardPaddingX)) / 3 ' un tercio
    height: m.top.height - (2 * m.top.cardPaddingY)
  }) ' title
  m.image.setFields({
    width: (m.top.width - (2 * m.top.cardPaddingX)) / 3 * 2 ' dos tercios
    height: m.top.height - (2 * m.top.cardPaddingY)
  }) ' title

end sub
' EVENTS
' ------------------------------
sub onContentChange(event)
  data = event.getData()

  m.candado.visible = false
  m.corazon.visible = false

  m.title.text = data.number
  m.image.uri = data.image
  m.image.opacity = "1"
  m.title.opacity = "1"

  groupId = ghGetChild(data, "group_id", "")

  channels = ghGetChild(m.global, "parental.channels", {})
  if channels[groupId] <> invalid and channels[groupId] <> "" then
      m.candado.visible = true
      m.image.opacity = "0.5"
      m.title.opacity = "0.5"
  end if

  favorites = ghGetChild(m.global, "favorites.channels", {})
  if favorites[groupId] <> invalid and favorites[groupId] <> "" then
      m.corazon.visible = true
  end if
end sub
