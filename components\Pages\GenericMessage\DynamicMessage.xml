<?xml version="1.0" encoding="utf-8" ?>

<component name="DynamicMessage" extends="Page">

  <script type="text/brightscript" uri="DynamicMessage.brs" />

  <interface>
    <!-- INTERFAZ DE ENTRADA -->
    <field id="images" type="array" onChange="buildImagenes" alwaysNotify="true"/>
    <field id="icons" type="array" onChange="buildIconos" alwaysNotify="true"/>
    <field id="titles" type="array" onChange="buildTitulos" alwaysNotify="true"/>
    <field id="messages" type="array" onChange="buildMensajes" alwaysNotify="true"/>
    <field id="buttons" type="array" onChange="buildBotonera" alwaysNotify="true"/>
    <!-- INTERFAZ DE SALIDA -->
    <!-- a que variable del parent le devuelvo el resultado -->
    <field id="resultProperty" type="string" value="DynamicMessageResult" />
    <!-- que resultado devuelvo si apreto back -->
    <field id="resultOnBack" type="string" value="back" />
  </interface>

  <children>
    <Rectangle id="pantalla" translation="[0,0]" width="1920" height="1080" color="#0000FFEE" visible="true">
      <Group id="imagenes"/>
      <LayoutGroup id="columna">
        <LayoutGroup id="iconos"/>
        <LayoutGroup id="titulos"/>
        <LayoutGroup id="mensajes"/>
        <GHButtonLayout id="botonera"/>
      </LayoutGroup>
    </Rectangle>
  </children>

</component>
