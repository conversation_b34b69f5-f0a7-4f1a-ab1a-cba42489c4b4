' no filtramos porque viene mal la informacion
' function filterLangs(langs)
'   ' filtrar por encodes soportados

'   langsFiltered = []

'   encodesAppSupported = getEncodesSupported()

'   for i = 0 to langs.Count() - 1
'     item = langs[i]
'     encodes = findInArray(ghGetChild(item, "encodes"), encodesAppSupported)
'     if encodes.count() > 0 then
'       item.encodesSupported = encodes
'       langsFiltered.push(item)
'     end if
'   end for

'   return langsFiltered
' end function

sub onGetPreferences(event)
  data = event.getData()
  userLang = {
    audio: ghGetRegistry("preference_vod_audio", "user"),
    subtitle: ghGetRegistry("preference_vod_subtitle", "user"),
  }

  m.logger.debug("Idioma de usuario: ", { lang: userLang })

  if data <> invalid and ghGetChild(data, "audio", "") <> "" then
    userLang = data
  end if

  m.logger.debug("Idioma de pelicula: ", { data: data })

  ' no hace falta filtrar, siempre probamos todos los encodes, porque la informacion viene mal
  ' langsFiltered = filterLangs(ghGetChild(m.dataContent, "group.common.extendedcommon.media.language.options.option", []))

  selectLanguage(userLang)
end sub

sub selectLanguage(userLang)
  langsFiltered = ghGetChild(m.dataContent, "group.common.extendedcommon.media.language.options.option", [])
  m.langs = langsFiltered

  m.logger.debug("todos los idiomas: ", { data: langsFiltered })

  langSelect = invalid
  for i = 0 to langsFiltered.Count() - 1
    l = langsFiltered[i]

    ' selecciona primer lang, por si no hay match con el lang del profile
    if langSelect = invalid then
      langSelect = l
    end if

    if LCase(ghGetChild(userLang, "audio", "")) = LCase(ghGetChild(l, "audio", ".")) then
      m.logger.debug("coincide el audio: ", { audio: l.audio })

      if userLang.subtitle = invalid or userLang.subtitle = "" then
        m.logger.debug("sin subtitulo, se encontro lenguaje con opcion de usuario: ", { lang: l })

        langSelect = l
        exit for
      else if LCase(ghGetChild(userLang, "subtitle", "")) = LCase(ghGetChild(l, "subtitle", ".")) then
        m.logger.debug("con subtitulo, se encontro lenguaje con opcion de usuario: ", { lang: l })

        langSelect = l
        exit for
      end if
    end if
  end for

  if langSelect = invalid then
    showMessage({ message: "Contenido no displonible para esta plataforma" })
  end if

  m.logger.debug("Lang seleccionado: ", { lang: langSelect })

  ' content tiene info para mostrar en player '
  ' reemplazo group_id y content con el lang seleccionado por el profile
  content = m.content
  content.id = langSelect.group_id
  content.content_id = langSelect.content_id

  m.preferred_audio = langSelect.audio
  m.preferred_subtitle = langSelect.subtitle

  ' content.tipo_abono_contenido = m.tipo_abono_contenido
  ' content.favorited = m.top.favorite

  m.contentVideo = m.content

  if m.video = invalid then
    m.logger.debug("Creando m.video en y llamando a OpenVideoPlayer")

    m.video = OpenVideoPlayer(m.content, m.payway, m.content.id, m.trailer, m.content.content_id, m.seasons, m.inicio, m.enableAds)
    m.video.UnobserveField("wasClosed")
    m.video.ObserveField("wasClosed", "handleCloseVideo")
    m.video.ObserveField("favorited", "switchFavorite")
    m.video.ObserveField("info", "handleVideoChange")

    m.top.ObserveField("favorite", "changeInfoVideo")
  else
    m.logger.debug("ya existe m.video, llama a OpenVideoPlayer")
    OpenVideoPlayer(m.content, m.payway, m.content.id, m.trailer, m.content.content_id, m.seasons, m.inicio, m.enableAds)
  end if
end sub

sub iniciarReproduccion(content, payway, trailer, inicio, seasons, enableAds, player = invalid, tipo_abono_contenido = invalid, favorite = false)
  ' primero consultar a api getPreferences
  ' si no hay idioma en preferences, ver registry

  m.logger.debug("iniciando reproduccion: ", { content: content, payway: payway, trailer: trailer, inicio: inicio, seasons: seasons, enableAds: enableAds })

  m.player = player
  m.dataContent = content

  m.content = parseDataContent(ghGetChild(content, "group.common"))
  m.content.tipo_abono_contenido = tipo_abono_contenido
  m.content.favorited = favorite

  m.payway = payway

  m.trailer = false
  if trailer = true then
    m.trailer = true
  end if

  m.inicio = inicio
  m.seasons = seasons
  m.enableAds = enableAds

  ' con preferencias de usuario
  getPreferences = ghCallApi("GetPreferences", "onGetPreferences", "onGetPreferences", false)
  getPreferences.group_id = m.content.id
  getPreferences.control = "run"

  ' ' sin preferencias de usuario, busco en registry
  ' selectLanguage({
  '   audio: ghGetRegistry("preference_vod_audio", "user"),
  '   subtitle: ghGetRegistry("preference_vod_subtitle", "user"),
  ' })
end sub

function OpenVideoPlayer(content as object, payway as string, groupId as string, trailer = false as boolean, contentId = "" as string, seasons = invalid, inicio = false, enableAds = false) as object

  m.currentStreamType = 0

  m.groupId = groupId
  m.payway = payway
  m.streamTypes = getEncodesSupported()
  m.preview = trailer
  m.contentId = contentId
  m.content = content
  m.seasons = seasons
  m.inicio = inicio
  m.enableAds = enableAds
  m.preroll = ghGetChild(content, "preroll", false)
  m.postroll = ghGetChild(content, "postroll", false)

  m.iniciado = true
  ' m.player = player
  if m.player = invalid then
    m.logger.debug("inicio player desde cero")

    m.iniciado = false
    m.player = CreateObject("roSGNode", "PlayerADLite")
    m.player.id = "ScreenPlayer" ' para borrar el player al hacer deepLink
    m.player.UnobserveField("endcardItemSelected")
    m.player.ObserveField("endcardItemSelected", "OnEndcardItemSelected")
  end if

  if ghGetChild(m.global, "model", "") = "lite" and m.enableAds then
    m.logger.debug("OpenVideoPlayer -- Buscando campaña")

    campaign = ghCallApi("CampaignLite", "campaignResponse", "campaignResponse", false)
    campaign.group_id = groupId
    campaign.payway_token = payway
    campaign.control = "run"
  else
    getMedia(m.groupId, m.payway, m.preview, m.streamTypes[m.currentStreamType], m.contentId)
  end if

  return m.player
end function

sub campaignResponse(event)
  data = event.getData()

  m.campaign = ghGetChild(data, "campaign.url", "")

  getMedia(m.groupId, m.payway, m.preview, m.streamTypes[m.currentStreamType], m.contentId)
end sub

function getMedia(groupId as string, payway as string, trailer = false as boolean, streamType = "smooth_streaming_ma" as string, contentId = "" as string) as object
  apiGetMedia = ghCallApi("GetMedia2", "OnMainContentLoaded", "OnError", false)

  m.logger.debug("getMedia", { groupId: groupId, contentId: contentId, streamType: streamType, payway_token: payway })

  apiGetMedia.groupId = groupId
  apiGetMedia.contentId = contentId
  apiGetMedia.streamType = streamType
  apiGetMedia.paywayToken = payway

  apiGetMedia.preferred_audio = m.preferred_audio
  apiGetMedia.preferred_subtitle = m.preferred_subtitle

  if trailer then
    apiGetMedia.preview = "1"
  end if

  apiGetMedia.control = "run"
end function

sub OnMainContentLoaded(event)
  datos = event.getData()

  m.logger.debug("OnMainContentLoaded -- ", { data: datos })

  if m.inicio = true or m.iniciado = true then
    datos.BookmarkPosition = 0
  end if

  datos.iniciado = m.iniciado
  datos.preroll = m.preroll
  datos.postroll = m.postroll

  if m.player <> invalid then
    m.player.ObserveField("selected", "OnChangeContent")
  end if

  content = CreateObject("roSGNode", "VideoContent")
  content.Update(datos, true)

  useAd = false
  if m.enableAds and m.campaign <> invalid and m.campaign <> "" then
    content.ad_url = m.campaign
    ' content.ad_url = "https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/vmap_ad_samples&sz=640x480&cust_params=sample_ar%3Dpremidpost&ciu_szs=300x250&gdfp_req=1&ad_rule=1&output=vmap&unviewed_position_start=1&env=vp&impl=s&cmsid=496&vid=short_onecue&correlator="
    useAd = true
  end if

  ' prueba google https://developers.google.com/interactive-media-ads/docs/sdks/tvos/client-side/tags
  ' content.ad_url = "https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/vmap_ad_samples&sz=640x480&cust_params=sample_ar%3Dpremidpost&ciu_szs=300x250&gdfp_req=1&ad_rule=1&output=vmap&unviewed_position_start=1&env=vp&impl=s&cmsid=496&vid=short_onecue&correlator="

  ' prueba de claro
  ' content.ad_url = "https://pubads.g.doubleclick.net/gampad/ads?iu=/21628222637/ClaroVideo_Video&description_url=https%3A%2F%2Fwww.clarovideo.com&tfcd=1&npa=0&sz=1x1%7C400x300%7C480x361%7C640x480%7C640x481&cust_params=Pod%3D1%2C4%26device%3DTV%2Candroid%2CiPad%2CiPhone%2Cweb%26region%3Dargentina%2Cbrasil%2Cchile%2Ccolombia%2Ccostarica%2Cdominicana%2Cecuador%2Celsalvador%2Cguatemala%2Chonduras%2Cmexico%2Cnicaragua%2Cpanama%2Cparaguay%2Cperu%2Curuguay%26user_type%3Danonymous%2Cno_susc%2Csusc&min_ad_duration=5000&max_ad_duration=59000&gdfp_req=1&output=vast&env=vp&unviewed_position_start=1&impl=s&correlator=&output=xml_vmap1"

  m.player.setFields({
    content: content,
    control: "init",
    useAd: useAd
    seasons: m.seasons,
    info: m.content,
    trackInfo: datos.trackInfo,
    languages: datos.languages
  })

  m.top.routerChild = {
    page: m.player,
    fields: {}
  }
end sub

sub OnChangeContent(event)
  data = event.getData()
  if data <> invalid then
    m.logger.debug("OnChangeContent -- ", { data: data })

    m.player.UnobserveField("selected")
    m.currentStreamType = 0

    m.contentId = ghGetChild(data, "content_id", m.contentId)
    m.groupId = ghGetChild(data, "group_id", "")

    m.streamTypes = getEncodesSupported()

    getMedia(m.groupId, m.payway, m.preview, m.streamTypes[m.currentStreamType], m.contentId)
  end if
end sub

sub OnError(event)
  datos = event.getData()

  code = ghGetChild(datos, "errors.#0.code", "DESCONOCIDO")

  code = code.toStr()
  m.logger.error("OnError", { code: code, message: ghGetChild(datos, "errors.#0.message") })

  if code = "PLY_PLY_00009"
    if m.currentStreamType = m.streamTypes.count() - 1
      m.player.errorMsg = ghTranslate("PLY_PLY_00009", "El contenido que quieres ver no se encuentra disponible por el momento.")
      m.player.wasClosed = true
    else
      m.currentStreamType = m.currentStreamType + 1
      getMedia(m.groupId, m.payway, m.preview, m.streamTypes[m.currentStreamType], m.contentId)
    end if
  else if code = "PLY_PLY_00007"
    showMessage({ title: ghTranslate(code + "_title", "Hubo un error inesperado"), message: ghTranslate(code, ""), onAccept: "CloseScreen" })
  else
    m.logger.warn("Error desconocido: ", { code: code })
    setLoading(false)
    showMessage({ title: ghTranslate(code + "_title", "Hubo un error inesperado"), message: ghTranslate(code, ""), onAccept: "CloseScreen" })
    m.player.wasClosed = true
  end if
end sub

' Logica de fin de movie
' ------------------------------------------
sub OnEndcardItemSelected(event as object)
  video = event.GetRoSGNode()
  video.UnobserveField("endcardItemSelected")
  video.close = true
end sub

function parseDataContent(data)
  m.logger.debug("parseDataContent")
  item = data

  genres = []
  for each aItem in ghGetChild(item, "extendedcommon.genres.genre", [])
    genres.push(aItem.desc)
  end for

  serie_id = invalid
  duration = ""
  rating = ""
  release = ""

  episode = ""
  season = ""
  titleEpisode = ""
  if item.extendedcommon.media <> invalid
    if item.extendedcommon.media.serie <> invalid
      season = item.extendedcommon.media.episode.season
      episode = item.extendedcommon.media.episode.number
      titleEpisode = item.title
    end if
  end if

  seasonObj = {}
  if item.extendedcommon.media <> invalid
    if item.extendedcommon.media.serie <> invalid
      serie_id = item.extendedcommon.media.serie.id
      if m.top.seasons <> invalid
        seasonNumber = season.toStr().toInt()
        seasonObj = m.top.seasons[seasonNumber.toStr()]
      end if
    end if
    ' duration = ghGetChild(item, "extendedcommon.media.duration", "00:00:00")
    duration = ghGetChild(item, "extendedcommon.media.duration")
    if duration <> invalid then
      duration = Left(duration, 5) + " min"
    end if
    rating = item.extendedcommon.media.rating.code
    ratingcode = ghGetChild(item.extendedcommon.media.rating, "id")
    release = item.extendedcommon.media.publishyear
  end if
  title = ghGetChild(item, "title", "")
  ' if contenttype > 1 then
  if serie_id <> invalid then
    title = ghGetChild(item, "extendedcommon.media.serie.title", "")
  end if

  fondo = ghGetChild(item, "image_base_horizontal")
  if fondo = invalid then
    fondo = "pkg:/images/mkh_back.jpg"
  else
    fondo = fondo + "?size=675x380"
    ' fondo = fondo + "?size=1280x720"
  end if

  dataFinal = {
    id: item.id,
    serie_id: serie_id,
    season: season,
    episodenumber: episode,
    categories: genres,
    duration: duration,
    rating: rating,
    ratingcode: ratingcode,
    year: release
    fondo: fondo
    imagenCheckout: ghGetChild(item, "image_medium")
    title: title
    titleEpisode: titleEpisode
    titleSeason: ghGetChild(seasonObj, "title", "")
    description: ghGetChild(item, "large_description", "")
    preroll: ghGetChild(item, "rolls.pre-roll", false)
    postroll: ghGetChild(item, "rolls.post-roll", false)
  }

  ' m.logger.debug("datos de content", dataFinal)

  return dataFinal
end function

sub showMessage(data = { message: "", title: "", accept: "OK", onAccept: "" })
  m.logger.debug("Mostrando mensaje genérico", { title: data.title, mensaje: data.message })

  ScrGenericMessage = CreateObject("roSGNode", "GenericMessage")
  ScrGenericMessage.id = "errorVOD"
  ScrGenericMessage.SetFields({
    title: ghGetChild(data, "title", ghTranslate("vod_error_generic_title", "Hubo un error inesperado"))
    message: ghGetChild(data, "message", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (LPL-01)"),
    accept: ghGetChild(data, "accept", ghTranslate("error_button_accept", "OK"))
  })
  ScrGenericMessage.ObserveField("wasClosed", ghGetChild(data, "onAccept", "CloseScreen"))

  m.top.routerChild = {
    page: ScrGenericMessage,
    fields: {}
  }
end sub

sub CloseScreen()
  m.top.focus = false
  m.top.close = true
  if m.video <> invalid then m.video.control = "stop"
end sub