' Offers
' ---------------------------

sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + m.top.link

  'm.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.query.delete("api_version") ' sin api version
  ' m.api.headers.Append({ "user-token": "Bearer " + ghGetRegistry("user_token", "user") })
  m.api.headers.Append({ "user-token": ghGetRegistry("user_token", "user") })

  ' m.api.query.Append({
  ' user_token: ghGetRegistry("user_token", "user")
  ' "object_type": m.top.object_type,
  ' "offer_id": m.top.offer_id,
  ' "suscription_id": m.top.suscription_id,
  ' "group_id": m.top.group_id,
  ' "HKS": ghGetRegistry("HKS")
  ' "device_category": config.mfwk.device_category
  ' "device_manufacturer": config.mfwk.device_manufacturer
  ' "device_model": config.mfwk.device_model
  ' "device_type": config.mfwk.device_type,
  ' ' registry
  ' "region": ghGetRegistry("region")
  ' "api_version": config.mfwk.api_version
  ' })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print "*********************************"
    print "*********************************"
    print ghLogHead();"body = ";res
    ' if m.top.debug then print ghLogHead();"body = ";ghGetChild(res, "response", "**nada**")
    print ghLogHead();"raw = ";raw
    if res.neterror <> invalid then print ghLogHead();"netError = ";res.neterror
  end if

  if res = invalid or res.neterror <> invalid
    m.top.error = ghErrorNetwork(m.api.name, m.api.url, raw)
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    print res?.errors
    print res?.errors?.error

    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = res

  if m.top.debug then print "*********************************"
  if m.top.debug then print "*********************************"
end sub