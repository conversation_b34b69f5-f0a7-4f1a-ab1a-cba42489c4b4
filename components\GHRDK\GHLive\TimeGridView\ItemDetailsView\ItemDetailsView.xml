<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->

<component name="ItemDetailsView" extends="Group" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd">
    <interface>
        <field type="node" id="content" onChange="onContentSet" />

        <field type="color" id="titleColor" alias="title.color" />
        <field type="color" id="descriptionColor" alias="description.color" />

        <field type="integer" id="descriptionMaxLines" alias="description.maxLines" />
        <field type="integer" id="maxWidth" onChange="onMaxWidthChange" value="0" />
    </interface>

    <script type="text/brightscript" uri="ItemDetailsView.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />



    <children>
        <Rectangle id="blending" opacity="0.8" color="#070707" width="1920" height="1080" translation="[0,0]" />
    </children>
    <children>
        <LayoutGroup itemSpacings="[15]" translation="[-56,-32]">
            <Label id="title" font="font:MediumBoldSystemFont" wrap="false" maxLines="1" />
            <LayoutGroup id="layoutGenre" translation="[10,10]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[22]" visible="true" />
            <Label id="description" lineSpacing="2" maxLines="2" wrap="true" width="994" height="60"/>
            <LayoutGroup id="layoutTalents" translation="[10,10]" itemSpacings="[15]" layoutDirection="horiz" vertAlignment="center" visible="true" />
        </LayoutGroup>
    </children>
</component>
