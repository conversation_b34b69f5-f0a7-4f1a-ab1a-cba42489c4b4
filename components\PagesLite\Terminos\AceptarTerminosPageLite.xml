<?xml version="1.0" encoding="utf-8" ?>

<component name="AceptarTerminosPageLite" extends="Page">

  <script type="text/brightscript" uri="AceptarTerminosPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <interface>
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Rectangle id="fondo" color="#121212" />
    <Poster id="logo" visible="true"/>
    <!-- Botonera -->
    <LayoutGroup id="columna" layoutDirection="vert" horizAlignment="center" vertAlignment="top" itemSpacings="[50]">
      <LayoutGroup id="titleAndDescription" layoutDirection="vert" horizAlignment="center" vertAlignment="top" itemSpacings="[40]">
        <Label id="title" />
        <Label id="description" wrap="true"/>
      </LayoutGroup>
      <GHButtonLayout id="botonera">
        <GHCheckBox id="check"/>
        <GHButton id="btnTerminos" />
        <GHButton id="btnPrivacidad" />
        <GHButton id="btnAccept" />
        <!-- <GHButton id="btnRejectr" /> -->
      </GHButtonLayout>
    </LayoutGroup>
    <!-- Pop Up -->
    <GHToastMessage id= "toolTip" title="Estoy probando" visible="true" translation= "[640, 600]" time= "-1"/>
    <Poster id="chkError" uri="pkg:/images/HD/focus01.9.png" />
  </children>

</component>
