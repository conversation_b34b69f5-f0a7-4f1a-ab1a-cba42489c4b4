# Advertising

## Status

1.1 / 1.10 No estamos usando propaganda.



## Items

**1.1** Channels that include video advertising must integrate the [Roku Advertising Framework (RAF)](https://developer.roku.com/docs/developer-program/advertising/roku-advertising-framework.md) for all ads without modifying, obstructing, or disabling RAF functionality in any way. Replays of live broadcast streams are exempt from this requirement, unless new ads are being inserted in the replay.

**1.2** Partners must disclose integration/use of all non-Roku SDKs, libraries, or other software systems that enable video, audio, or banner ad insertion, and Roku has the right to approve or deny such non-Roku SDKs, libraries, or other software systems.

**1.3** Channel must pass Roku's ID for Advertisers (RIDA) and "limit ad tracking" (LAT) value on ad server requests. If the user has opted out, channels must still pass the temporary ID returned by the [rodeviceInfo.GetRida()](https://developer.roku.com/docs/references/brightscript/interfaces/ifdeviceinfo.md#getrida-as-string) function to support frequency capping (this temporary ID is different than the UUID returned if the user has not opted out; it expires after 30 days). For more information, see: [GetRida()](https://developer.roku.com/docs/references/brightscript/interfaces/ifdeviceinfo.md#getrida-as-string), [IsRIDADisabled()asBoolean](https://developer.roku.com/docs/references/brightscript/interfaces/ifdeviceinfo.md#isridadisabled-as-boolean), and the [URL parameter macros populated by RAF](https://developer.roku.com/docs/developer-program/advertising/integrating-roku-advertising-framework.md#url-parameter-macros).

**1.4** For channels in the U.S. Channel Store only and which are not child-directed: The channel must be enabled for [General Audience Measurement](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#IntegratingtheRokuAdvertisingFramework-GeneralAudienceMeasurement) using enableAdMeasurements and pass the required content metadata within the following methods: [setContentGenre](https://developer.roku.com/docs/developer-program/advertising/raf-api.md), [setContentId](https://developer.roku.com/docs/developer-program/advertising/raf-api.md), and [setContentLength](https://developer.roku.com/docs/developer-program/advertising/raf-api.md).

Optionally, channels may use the [setNielsenGenre API](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#IntegratingtheRokuAdvertisingFramework-set-nielsen-genre) to pass specific Nielsen Genre granularity and the [setNielsenAppId API](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#setnielsenappidid-as-string) for those who specify a custom Nielsen App ID. However, channels typically only implement the listed General Audience Measurements and content metadata APIs. The [enableAdMeasurements](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#enableadmeasurementsenabled) method deprecates the [enableNielsenDAR](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#nielsen-dar) API; therefore, do not use the [enableNielsenDAR](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#nielsen-dar) API.

**1.5** For channels with child-directed content: If ads are served during child-directed content, the ad requests must indicate that the content is child-directed. For more information, see the [kidsContent parameter in the setContentGenre() method](https://developer.roku.com/docs/developer-program/advertising/raf-api.md#setcontentgenregenres-as-string-kidscontent-as-boolean) and the [ROKU_ADS_KIDS_CONTENT URL parameter macro](https://developer.roku.com/docs/developer-program/advertising/integrating-roku-advertising-framework.md#url-parameter-macros).

**1.6** For all channels that have an inventory relationship with Roku: Channels make ad requests to Roku so that the corresponding terms are met. For more details see [Video Advertising](https://developer.roku.com/docs/features/monetization/video-advertisements.md). Additionally, effective after March 31, 2021, channels in the U.S. Channel Store that have both streamed more than an average of 100,000 hours per month and averaged more than 10,000 new installs per month over the last three months must implement the Demand API as part of their integration (this requirement is also applicable to new channels projected to reach the specified thresholds shortly after launch). See [Implementing the Demand API](https://developer.roku.com/docs/developer-program/advertising/demand-api.md).

**1.7** Channels selling ads exclusively and/or with Roku must comply with ad load, ad frequency, and acceptable ad requirements. See the [Roku Advertising Guidelines](http://www.roku.com/adguidelines) for more details.

**1.8** For ads requested client-side, the number of ads are displayed during ad breaks using the standard Roku-branded label applied by RAF.

**1.9** For all channels except those streaming live content or replaying live broadcast streams: When the back button is pressed during an ad break and then playback resumes (with the same or different content), the channel must attempt to initiate an ad break to preserve the previously exited ad experience.

**1.10** For all channels except those streaming live content or replaying live broadcast streams: The channel ignores FF/REW commands received during an ad break (via either key presses or voice commands).