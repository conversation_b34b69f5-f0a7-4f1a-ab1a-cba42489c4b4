sub updateState(state, data = {})
	m.logger.debug("estado del player= ", { state: state })

	if state = "init" then
		postroll = ghGetChild(m.top.content, "postroll", false)
		m.episodeCharged = false
		m.logger.debug("iniciando player", { useAd: m.top.useAd })

		if m.top.content.widevineParams <> invalid then
			m.logger.debug("Drm params: ", { drmparams: m.top.content.DRMPARAMS, widevineparams: m.top.content.widevineParams })

			m.video.setHttpAgent(buildEmptyHttpAgent())
			m.video.drmHttpAgent = buildDrmHttpAgent(m.top.content.widevineParams)
		else
			m.logger.debug("NO ENCONTRE LOS widevineParams")
		end if

		m.logger.debug("iniciando player", { useAd: m.top.useAd, postroll: postroll })
		' si estamos cambiando de episodio, no iniciamos de nuevo el player con ad
		' if ghGetChild(m.top.content, "iniciado", false) = false then
		playContentAD(m.top.useAd, postroll)
		' else
		' 	' m.video.content = m.top.content
		' 	' m.video.control = "play"
		' end if
		m.video.SetFocus(true)

	else if state = "trackInfoChange" then
		urls = ghGetChild(data, "urls")
		retries = ghGetChild(data, "policies.retries", 3)
		interval = ghGetChild(data, "policies.tick_interval", 60)

		trackInit(urls, retries, interval, ghGetChild(data, "hasEndCard", invalid))
		setEndCard()

	else if state = "buffering" then
		m.loading.visible = true

	else if state = "playing" then
		m.video.visible = true
		m.loading.visible = false
		sendGa4Event(state)
		if m.lastState = "paused" or m.lastState = "buffering"
			trackEvent("resume")
		else
			trackEvent("view")
		end if

	else if state = "paused" then
		sendGa4Event(state)
		trackEvent("pause")

	else if state = "done" or state = "stop" or state = "finished" then
		' para que no termine la ejecucion si ya se esta produciendo
		' un cambio de capitulo
		if m.episodeCharged = false then
			m.logger.debug("mando track finish o stop y salgo del player")

			if state = "finished"
				trackEvent("completion")
			else
				trackEvent("stop")
			end if

			if state = "finished" then
				if m.top.useAd = true and ghGetChild(m.top.content, "postroll", false) = true then
					m.logger.debug("Se termino el contenido, se espera fin del player de publicidad")
					return
				end if
			end if

			if m.nextEpisodeShouldChange <> invalid then
				m.logger.debug("Se esta cambiando de episodio, no salgo del player")

				updateState("chargeNextEpisode", m.nextEpisodeShouldChange)

			else
				m.video.visible = false
				m.video.control = "stop"
				m.top.close = true
			end if
		end if

	else if state = "onkey" then

		sendGa4Event(state, data)

	else if state = "positionChange" then
		trackEvent("tick")

		checkCreditsAndOmitirIntro(m.video)

	else if state = "qualitychange" then
		trackEvent("qualitychange")

	else if state = "error" then
		m.video.visible = false
		m.video.control = "stop"

		errorCode = ghGetChild(data, "message", "")

		m.logger.error("Error en el player: ", { errorCode: errorCode })

		if errorCode <> "" then
			error = m.video.errorCode or (m.video.errorMsg <> invalid and m.video.errorMsg <> "")
			errorCode.toStr()
		end if

		print "===================================="
		print "ERROR VOD <VIDEO> <DASHWV>"
		print "[VOD] video.url == ";m.video.url
		print "[VOD] video.errorCode == ";m.video.errorCode
		print "[VOD] video.errorMsg == ";m.video.errorMsg
		print "===================================="
		print "[VOD] video.licenseStatus == ";m.video.licenseStatus
		print "===================================="

		showMessage({
			message: errorCode,
			onAccept: "exitPlayer"
		})

	else if state = "showEndCard" then
		' si no tiene adquirido el siguiente episodio, no muestro el end card
		if m.nextEpisodePlay = true then
			m.endCard.value = invalid
			m.endCard.visible = true
		end if

		trackEvent("credits")

	else if state = "endCardFinish"
		postroll = ghGetChild(m.top.content, "postroll", false)

		m.logger.debug("Endcard finish", { postRoll: postroll, useAd: m.top.useAd })

		' si no tiene publicidad o no tiene postroll, cambio de episodio
		' caso contrario, oculto el end card
		if m.top.useAd = false or postroll = false then
			m.logger.debug("Cambiando de episodio")
			updateState("chargeNextEpisode", m.nextEpisodeShouldChange)
		else
			updateState("hideEndCard")
		end if

	else if state = "hideEndCard" then
		m.logger.debug("Ocultando end card")
		m.endCard.visible = false

	else if state = "prepareNext" then
		getOffers(ghGetChild(m.top, "content.next_group.common.id"), true)

	else if state = "contentDataCharged"
		if m.changeEpisode = true then
			m.changeEpisode = false

			updateState("chargeNextEpisode", {
				content: m.nextContentData,
				offer: m.nextEpisodeOffer
			})
		end if

	else if state = "episodeChange" then
		m.video.visible = false
		m.video.control = "pause"
		m.loading.visible = true

		getOffers(data.group_id)

	else if state = "languageChange" then
		m.logger.debug("Cambiando idioma del contenido", { data: data })

		m.languageSelect = data

		trackEvent("dubsubchange")

		m.top.selected = data

	else if state = "chargeNextEpisode" then
		m.nextEpisodeShouldChange = invalid

		if ghGetChild(data, "isComplete", false) = true
			trackEvent("completion")
		end if

		content = ghGetChild(data, "offer")
		if content = invalid then
			m.logger.warn("Intento de cargar el siguiente episodio sin contenido válido", { offer: ghGetChild(data, "offer"), content: ghGetChild(data, "content") })

			showMessage({
				message: "Error al cargar el contenido",
				onAccept: "exitPlayer"
			})
			return
		else
			m.logger.debug("cambiando episodio", { offers: ghGetChild(data, "offer"), content: ghGetChild(data, "content") })

			' envio info a hombre muerto, para que sume capitulos
			m.hombreMuerto.newEpisode = true

			m.episodeCharged = true

			nextEpisode(ghGetChild(data, "offer"), ghGetChild(data, "content"))
		end if

	else if state = "showIntro" then
		sendGa4Event(state)
		if ghGetChild(data, "desdeInicio", false) = true then
			m.video.showOmitirIntro = 2
		else
			m.video.showOmitirIntro = 1
		end if
		m.trk.oi.shown = true

	else if state = "hideIntro" then
		m.video.showOmitirIntro = 0
		m.trk.oi.shown = false
		m.video.setFocus(true)

		if data <> invalid and ghGetChild(data, "position") <> invalid then
			m.video.seek = ghGetChild(data, "position")
		end if

	end if

	m.lastState = state
end sub

sub exitPlayer()
	updateState("done")
end sub

sub sendGa4Event(state, data = invalid)
	' enviando a google analytics
	' comentario de explo
	event_body = {
		content_id: ghGetChild(m.top.content, "groupid")
		content_name: ghGetChild(m.top.content, "title", "")
		content_type: "movie"
		content_category: ghGetChild(m.top.content, "ybgenres", "")
		content_availability: "by subscription"
		'progress_percentage:"0,25", ' falta calcular porcentaje de la pelicula
		content_episode: "not apply",
		content_list: "not apply",
		user_type: getUserTypeGA4(true),
		country: getCountryCode(ghGetRegistry("country_code", "user")),
		user_id: ghGetRegistry("user_id", "user"),
		screen_name: "play content",
		screen_class: "/play-content",
	}

	episodenumber = ghGetChild(m.top, "info.episodenumber")
	season = ghGetChild(m.top, "info.season")
	titleepisode = ghGetChild(m.top, "info.titleepisode")
	if episodenumber <> invalid and episodenumber <> "" and season <> invalid and season <> "" then
		event_body.content_type = "series"
		event_body.content_episode = titleepisode + " E" + episodenumber
		event_body.content_list = "season " + season
	else
		event_body.content_type = "movie"
		event_body.content_episode = "not apply"
		event_body.content_list = "not apply"
	end if

	event_name = "content_progress"

	if state = "playing" then
		event_name = "content_play"
	else if state = "paused" then
		event_name = "content_pause"
	else if state = "onkey" then
		key = ghGetChild(data, "key")

		if key = "fastforward"
			event_name = "content_forward"
		else if key = "rewind"
			event_name = "content_delay"
		else if key = "seekPosition"
			event_name = "content_progress"
			current = ghGetChild(m, "video.focusedChild.position", 0)
			duration = ghGetChild(m, "video.duration", 1)
			percentage = 100 * current / duration
			' Multiplicar por 100 y redondear a dos decimales
			percentage = Fix(percentage * 100 + 0.5) / 100
			formattedPercentage = percentage.ToStr() + "%"
			event_body.progress_percentage = formattedPercentage
		end if
	else if state = "showIntro" then
		event_name = "interaction_content"
	end if

	GA4Event(event_name, event_body)
end sub