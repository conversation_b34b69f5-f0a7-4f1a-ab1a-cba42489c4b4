<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemNavchilds" extends="Group">
  <script type="text/brightscript" uri="ItemNavchilds.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="rowIndex" type="integer" />
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <field id = "focusPercent" type = "float" onChange = "showfocus" />
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemPoster"/>
    <Rectangle id="background" translation="[0,0]" width="254" height="60">
      <Label id="title" visible="true" translation="[0,0]" width="254" wrap="true" height="60" lineSpacing="0" vertAlign="bottom" />
    </Rectangle>
  </children>

</component>