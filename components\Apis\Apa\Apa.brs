sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/apa/metadata"
  m.api.query.Append({
    ' "sessionKey": "531eed34tvfy7b73a818a234-mexico" ' -- web, para pruebas
    "sessionKey": m.config.mfwk.appKey + "-" + ghGetRegistry("region")
    ' "sessionKey": m.config.mfwk.appKey + "-argentina"
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- api="m.api.query
  end if

  ' assets
  ghCallApi("ApaAssets", "assetOk", "assetError")
end sub
' ASSETS
' ---------------------------
sub assetOk()
  if m.top.debug then print "api assets ok"
end sub
sub assetError()
  print "api assets error"
end sub
' PROCESOS
' ---------------------------
sub ProcessData(res, raw)

  if m.top.debug then print ghLogHead();"ProcessData"

  if res = invalid then
    m.top.error = { response: raw }
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors = invalid then
    errors = ghGetChild(res, "result.error")
  end if
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  try
    ' TIMERS - en el mismo archivo
    getTimersConfig(res)

    translations = ParseJson(res.translations)
    translationsFinal = ghGetChild(translations, "language." + ghGetRegistry("region"), {})

    nodes = ParseJson(ghGetChild(res, "nodes_configuration", "{}"))
    filter = ParseJson(ghGetChild(res, "byr_filterlist_configuration", "{}"))

    ' providers = ParseJson(ghGetChild(res, "providers_label_configuration", "{}"))
    ' providersAds = ParseJson(ghGetChild(res, "providers_label_configuration_ads", "{}"))
    providers = getByRegion(res, "providers_label_configuration")
    if providers = invalid then providers = {}
    providersAds = getByRegion(res, "providers_label_configuration_ads")
    if providersAds = invalid then providersAds = {}

    search = ParseJson(ghGetChild(res, "search_priority", "{}"))
    searchNew = ParseJson(ghGetChild(res, "search", "{}"))
    itemTitle = ParseJson(ghGetChild(res, "ItemTitle", "{}"))
    youbora = ParseJson(ghGetChild(res, "youbora_options", "{}"))
    tvConfig = ParseJson(ghGetChild(res, "tv_epg_menu_nodo", "{}"))
    ga4 = ParseJson(ghGetChild(res, "ga4", "{}"))
    hombreMuerto = getByRegion(res, "continuePlayback_modal_config")

    ' ' ---------------------------------
    ' ' HARDCO CHAPITAS
    ' providers = getByRegionNotText(getProvidersLabelConfiguration(), "providers_label_configuration")
    ' providersAds = getByRegionNotText(getProvidersLabelConfigurationAds(), "providers_label_configuration_ads")
    ' print "APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS "
    ' print "APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS "
    ' print providers
    ' print "APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS "
    ' print providersAds
    ' print "APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS "
    ' print "APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS APA-CHAPITAS "
    ' ' ---------------------------------


    finalSearch = search
    if searchNew.count() > 0 then
      finalSearch = searchNew
    end if

    ' ----------------------------------------------
    ' HARDCO PARA PROPAGANDA
    ' -----
    ' cv_advertising_config: {
    '   "default": {
    '     "enable": false
    '   },
    '   "mexico": {
    '     "enable": true,
    '     "skip_offset": 5,
    '     "experience_hour_refresh": 4
    '   }
    ' }
    ' ----------------------------------------------
    cv_advertising_config = getByRegion(res, "cv_advertising_config")

    ' ----------------------------------------------
    ' HARDCO PARA DEVELOPMEN CAMBIO DE ADVERTISING -- JOSE
    ' -----
    ' si estoy en development, y no tengo la configuracion, la seteo
    ' para que entre en modo lite si estoy en homolog y en mexico o argentina
    ' env = ghGetChild(m.global, "config.env")
    ' if env = "development" then
    '   if cv_advertising_config = invalid then
    '     ' TODO solo test
    '     if ghGetChild(m.headers, "cdn", "") = "MX HOMOLOG" then
    '       res.cv_advertising_config = "{ ""default"": { ""enable"": false }, ""mexico"": { ""enable"": true, ""skip_offset"": 5, ""experience_hour_refresh"": 4 }, ""argentina"": { ""enable"": true, ""skip_offset"": 5, ""experience_hour_refresh"": 4 } }"
    '     else
    '       res.cv_advertising_config = "{ ""default"": { ""enable"": false }, ""mexico"": { ""enable"": false, ""skip_offset"": 5, ""experience_hour_refresh"": 4 }, ""argentina"": { ""enable"": false, ""skip_offset"": 5, ""experience_hour_refresh"": 4 } }"
    '     end if
    '     cv_advertising_config = getByRegion(res, "cv_advertising_config")
    '   end if
    ' end if
    ' HARDCO
    ' -----
    ' TODO sin lite
    cv_advertising_config = {
      "enable": false,
      "skip_offset": 5,
      "experience_hour_refresh": 4
    }
    ' ----------------------------------------------

    ' ----------------------------------------------
    ' PERFILES
    ' -----
    '   "profiles_config": {
    '     "default": {
    '       "enable": false
    '     },
    '     "mexico": {
    '       "enable": true
    '     }
    '   }
    profiles_config = getByRegion(res, "profiles_config")
    ' HARDCO
    ' -----
    ' profiles_config = { "enable": true }
    ' ----------------------------------------------


    ' ----------------------------------------------
    ' AUTOHIDE ALERTAS
    ' -----
    ' "alert_autoHideTime": {
    '     "default": {
    '       "profiles_selectKids": 14
    '     }
    alert_autoHideTime = getByRegion(res, "alert_autoHideTime")
    ' HARDCO
    ' -----
    alert_autoHideTime = { "profiles_selectKids": 14 }
    ' ----------------------------------------------
    ' ----------------------------------------------

    ' rokupay suscripciones
    ' alerta para canceladas
    alert_rokupay_subscription = getByRegion(res, "alerta_suscripciones_rokupay")

    hidden_confirm_trans_config = getByRegion(res, "hidden_confirm_trans_config")

    m.global.setFields({
      cv_advertising_config: cv_advertising_config
      translations: translationsFinal,
      filter_list: filter,
      search: ghGetChild(finalSearch, "default"),
      nodes: nodes,
      ' providers: ghGetChild(providers, "default"),
      ' providersAds: ghGetChild(providersAds, "default"),
      providers: providers,
      providersAds: providersAds,
      youbora: youbora
      ItemTitle: itemTitle
      tvConfig: tvConfig,
      ga4: ga4,
      hombreMuerto: hombreMuerto
      profiles_config: profiles_config
      alert_autoHideTime: alert_autoHideTime
      alert_rokupay_subscription: alert_rokupay_subscription
      hidden_confirm_trans_config: hidden_confirm_trans_config
    })
  catch error
    m.top.error = { response: raw }
    return
  end try

  try
    if m.top.debug then print "YOUBORA ++++++++++++++++++++++++++++++++++"
    InitYoubora(true) ' FORCED !!!!!!!!!!!!!
    if m.top.debug then print "YOUBORA ++++++++++++++++++++++++++++++++++"
  catch error
    print error
  end try

  m.top.content = res
end sub

' TIMERS
sub getTimersConfig(res)

  theTimers = ghGetChild(m.global, "config.timers")
  if theTimers = invalid then
    print ghLogHead();"Using defaults from CODE (!) -- missing configuration."
    theTimers = {
      "panels": {
        "player": 300,
        "miniEPG": 300
      },
      "skipIntro": {
        "enable": true,
        "time": 7
      },
    }
  end if

  ' controlPlayerHide: ParseJson(ghGetChild(res, "control_player_hide", ghGetChild(m.global, "timers.controlPlayerHide", "{}")))
  panels_player = getByRegion(res, "control_player_hide")
  panels_player = ghGetChild(panels_player, "time")
  if panels_player <> invalid then theTimers.panels.player = panels_player
  if m.top.debug then print "Setting panels_player", theTimers.panels.player

  ' mini_epg_auto_hide_seconds miniEPGAutoHide: ParseJson(ghGetChild(res, "mini_epg_auto_hide", ghGetChild(m.global, "timers.miniEPGAutoHide", "{}")))
  panels_miniEPG = getByRegion(res, "mini_epg_auto_hide_seconds")
  panels_miniEPG = ghGetChild(panels_miniEPG, "time")
  if panels_miniEPG <> invalid then theTimers.panels.miniEPG = panels_miniEPG
  if m.top.debug then print "Setting panels_miniEPG", theTimers.panels.miniEPG

  ' skipIntroConfig: ParseJson(ghGetChild(res, "skip_intro_configuration", ghGetChild(m.global, "timers.skipIntroConfig", "{}")))
  ' print "!!!!!!!", ghGetChild(res, "skip_intro_configuration")
  skipIntro = getByRegion(res, "skip_intro_configuration")
  ' skipIntro = {
  '   "time": 7
  '   "enable": true
  ' }
  if skipIntro <> invalid then
    if skipIntro.enable <> invalid then theTimers.skipIntro.enable = skipIntro.enable
    if skipIntro.time <> invalid then theTimers.skipIntro.time = skipIntro.time
    if m.top.debug then print "Setting skipIntro", theTimers.skipIntro.enable;" >> ";theTimers.skipIntro.time
  else
    theTimers.skipIntro.enable = false
    theTimers.skipIntro.time = 1
  end if
  ' SETTING
  if not m.global.hasField("timers") m.global.AddField("timers", "assocarray", false)
  m.global.setFields({ timers: theTimers })

  if m.top.debug then
    print " "
    print "+-------------------------------------"
    print "| Configuracion de timers"
    print "+-------------------------------------"
    for each item in m.global.timers.items(): print item.key;" : ";item.value: end for
    print "+-------------------------------------"
    print " "
  end if
end sub
' getByRegion
' -----
' devuelve una configuracion por pais, o la default
function getByRegion(origin, path)
  if m.top.debug then print ghLogHead();"getByRegion -- ";path, Type(origin)
  ' try
  child = ghGetChild(origin, path)
  if child <> invalid then
    ini = ParseJson(ghGetChild(origin, path))
    if ini <> invalid then ' si tengo la key
      res = ghGetChild(ini, ghGetRegistry("region")) ' por region
      if res = invalid then res = ghGetChild(ini, "default") ' el default
      if res <> invalid then
        if m.top.debug then print ghLogHead();"getByRegion -- ";path, res
        return res
      end if
    end if
  end if
  ' catch error
  '   print ghLogHead();"ERROR -- ";error
  ' end try
  if m.top.debug then print ghLogHead();"INVALID -- ";path
  return invalid
end function

' function getByRegionNotText(origin, path)
'   if m.top.debug then print ghLogHead();"getByRegion -- ";path, Type(origin)
'   ' try
'   child = ghGetChild(origin, path)
'   if child <> invalid then
'     ini = ghGetChild(origin, path)
'     if ini <> invalid then ' si tengo la key
'       res = ghGetChild(ini, ghGetRegistry("region")) ' por region
'       if res = invalid then res = ghGetChild(ini, "default") ' el default
'       if res <> invalid then
'         if m.top.debug then print ghLogHead();"getByRegion -- ";path, res
'         return res
'       end if
'     end if
'   end if
'   ' catch error
'   '   print ghLogHead();"ERROR -- ";error
'   ' end try
'   if m.top.debug then print ghLogHead();"INVALID -- ";path
'   return invalid
' end function

' "vod": [
'   {
'     "type": "text",
'     "text": "labels_free_value",
'     "gravity": "right",
'     "backgroundColor": "#E668B75C",
'     "textColor": "#FFFFFF",
'     "textSize": "14"
'   }
' ]

' "free": {
'   "vod": [
'     {
'       "type": "text",
'       "text": "labels_free_value",
'       "gravity": "left",
'       "backgroundColor": "#6cb863",
'       "textColor": "#FFFFFF",
'       "textSize": "14"
'     }
'   ]
' }


