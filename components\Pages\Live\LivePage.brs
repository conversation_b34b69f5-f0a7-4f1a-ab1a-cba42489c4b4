sub Init()
  m.top.debug = true

  if m.top.debug then print ghLogHead();"Init ** "

  m.message = m.top.findNode("message")
  m.pBackground = m.top.findNode("pBackground")
  m.loading = m.top.findNode("spinner")

  ' para no mostrar mensaje de canal bloquedo, si ya se esta viendo un canal
  ' solo pasa de pantalla en negro si se entra a un canal especifico bloqueado y se ingresa mal el pin
  m.iniciado = false

  m.top.ObserveField("content", "OnContentSet")
  m.top.ObserveField("wasShown", "OnVideoViewWasShown")
  m.top.ObserveField("wasClosed", "OnVideoWasClosed")

  ' Hombre Muerto
  m.hombreMuerto = m.top.findNode("hombreMuerto")
  m.hombreMuerto.ObserveField("cmdSalir", "onSalidaHombreMuerto")
  m.top.ObserveField("close", "onCloseHombreMuerto")
  ' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
  ' m.hombreMuerto.playerType = "live"

  VivoPage()

  CreateVideoNode()
  trackInit()

  EPGReloadTimer()
  BuildEPGReloadTimer()
  ghCallApi("LastTouch")


  m.Timer = CreateObject("roSGNode", "Timer")
  m.Timer.duration = 3
  m.Timer.repeat = false
end sub

sub onLoadingChange(event)
  data = event.getData()

  m.loading.visible = data

  if data = true then
    m.video.showError = invalid
  end if
end sub

sub OnVideoViewWasShown(event as object)
  print ghLogHead();"OnVideoViewWasShown"

  data = event.getData()

  m.video.focus = data
  m.video.SetFocus(data)
end sub

sub OnContentSet(event as object)
  print ghLogHead();"OnContentSet"

  data = event.getData()

  if data.widevineParams <> invalid then
    m.logger.debug("OnContentSet -- WIDEVINE Player - OnContentSet", { drmparams: data.DRMPARAMS, widevineParams: data.widevineParams })

    m.video.setHttpAgent(buildEmptyHttpAgent())
    m.video.drmHttpAgent = buildDrmHttpAgent(data.widevineParams)
  else
    m.logger.debug("OnContentSet -- NO ENCONTRE LOS widevineParams")
  end if

  if data <> invalid then
    ' cuando cambio el contenido ( cambio de canal )
    ' hago stop si ya se estaba reproduciendo algo
    ' y luego mando play, para reproducir nuevo contenido
    if m.top.state <> "stopped" then
      m.video.control = "stop"
    end if
    m.video.content = data.clone(false)

    m.logger.debug("OnContentSet -- languages", m.languages)
    m.video.languages = m.languages
    m.video.control = "play"
  end if
end sub

sub CreateVideoNode() as object
  print ghLogHead();"CreateVideoNode"

  ClearVideoNode()

  m.video = m.top.findNode("theVideo")
  m.video.id = "video"
  m.video.width = "1280"
  m.video.height = "720"
  m.video.translation = "[0,0]"
  m.video.enableUI = true ' muestra el loading y el titulo del canal

  m.video.trickPlayBar.trackBlendColor = "0xFFFFFFFF"
  m.video.trickPlayBar.filledBarImageUri = ghGetImageByMode ("PlayerProgressBar.png")
  m.video.trickPlayBar.currentTimeMarkerBlendColor = "0xFFFFFFFF"
  m.video.bufferingBar.filledBarImageUri = ghGetImageByMode ("PlayerProgressBar.png")
  m.video.retrievingBar.filledBarImageUri = ghGetImageByMode ("PlayerProgressBar.png")

  m.video.UnobserveField("selected")
  m.video.ObserveField("selected", "onPanelSelected")

  ' if m.LastThemeAttributes <> invalid then
  '   SGDEX_SetTheme(m.LastThemeAttributes)
  ' end if

  m.video.UnobserveFieldScoped("state")
  m.video.UnobserveFieldScoped("streamInfo")

  m.video.ObserveFieldScoped("state", "OnVideoStateChanged")
  m.video.ObserveFieldScoped("streamInfo", "OnBitrateChanged")
  m.video.ObserveFieldScoped("position", "OnPositionChanged")

  m.video.ObserveFieldScoped("keypressed", "onKeyPressed")

  ' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
  ' m.hombreMuerto.videoObj = m.video
end sub

sub ClearVideoNode()
  print ghLogHead();"ClearVideoNode"

  if m.video <> invalid
    m.video.UnobserveFieldScoped("state")
    m.video.control = "stop"
    m.video.content = invalid

    m.video = invalid
  end if
end sub

sub OnVideoStateChanged(event as object)
  state = event.GetData()

  ' print ">>>";ghGetRegistry("paymentMethod", "user")

  print ghLogHead();"OnVideoStateChanged -- ";state

  m.top.state = state

  m.loading.visible = false ' oculto el spinner

  if state = "stopped" then
    trackSendStop("onGenericTrackOk", "onGenericTrackError")
  else if state = "playing" then
    ' si tiene multiples audios, selecciono el español
    for each item in m.video.availableAudioTracks
      if m.video.currentAudioTrack <> "audio_spa" then
        if item.language = "SPA" or item.name = "audio_spa" then
          m.video.audioTrack = item.Track
        end if
      end if
    end for
    ' m.video.panelVisible = true ' aparece en cuanto entro
    m.video.showMainPanel = true ' cuando cambia de canal, prende miniepg

    trackSendView("onGenericTrackOk", "onGenericTrackError", m.channel)
  end if

  if state = "error" then
    print "===================================="
    print "ERROR LIVE <VIDEO>"
    print "[LIVE] video.errorCode == ";m.video.errorCode
    print "[LIVE] video.errorMsg == ";m.video.errorMsg
    print "===================================="
    print "[LIVE] video.licenseStatus == ";m.video.licenseStatus
    print "===================================="
  end if

  errorCode = m.video.errorCode or (m.video.errorMsg <> invalid and m.video.errorMsg <> "")
  if errorCode <> 0
    ' mostrar error en pantalla negra
    m.video.showError = { title: ghReplaceStr(ghTranslate("error_channel_title", "El canal {canal} no está disponible por ahora"), "{canal}", ghGetChild(m.channel, "name", "") + " " + ghGetChild(m.channel, "number", "")) }
    showError({
      message: "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (LPL - 02)",
      onAccept: "showGridChannels"
    })
    m.video.control = "stop"
  end if
end sub
sub OnPositionChanged() ' event as object
  youboraTrack()
end sub

sub OnBitrateChanged(event as object)
  data = event.GetData()

  print ghLogHead();"OnBitrateChanged -- ";data.streamBitrate;" ";data.isResume

  trackQualityChange("onGenericTrackOk", "onGenericTrackError")
end sub

sub OnVideoWasClosed() ' event as object
  print ghLogHead();"OnVideoWasClosed -- "
  ClearVideoNode()
end sub

sub OnExitToHome()
  print ghLogHead("VIVO PAGE");"OnExitToHome *** "

  trackSendStop() ' YB
  m.EPGTimer.control = "stop" ' stop al timer que refresca la epg
  m.video.control = "stop" ' stop a la reproduccion

  m.top.close = true
end sub

sub SGDEX_SetTheme() ' theme as object
  print ghLogHead("THEME");"SGDEX_SetTheme"
end sub

' recibe lista de canales y lo pasa al componente
sub handleChannels(event)
  print ghLogHead();"handleChannels ** "
  data = event.GetData()
  if data <> invalid and m.video <> invalid then
    m.video.channels = data
  end if
end sub

' recibe lista de eventos de epg y lo pasa al componente
sub handleEvents(event)
  print ghLogHead();"handleEvents ** "
  data = event.GetData()
  if data <> invalid and m.video <> invalid then
    m.video.events = data
  end if
end sub

' cuando se selecciona un canal en los componentes
sub onPanelSelected(event)
  print ghLogHead();"onPanelSelected ** "
  data = event.getData()
  if data <> invalid then
    m.channelNumber = data.number
    m.channelName = data.name

    actionType = ghGetChild(data, "actionType", "change")
    if actionType = "change" then
      setPlayer(data)
    else if actionType = "block" then
      ghCheckAuthentication("block", "changeAuth", [data.group_id])
    else if actionType = "unblock" then
      ghCheckAuthentication("unblock", "changeAuth", [data.group_id])
    else if actionType = "favorited" then
      ghCheckAuthentication("addFavorite", "changeAuth", [data.group_id])
    else if actionType = "unfavorited" then
      ghCheckAuthentication("deleteFavorite", "changeAuth", [data.group_id])
    end if
  end if
end sub

' cuando hago login y me mantengo en la misma pantalla
' tengo que recargar la info
' y ejecutar la funcion callback que viene despues de hacer login
sub changeAuth(event)
  data = event.getRoSGNode()

  m.logger.debug("changeAuth")

  ' como estoy cambiando todo el menu
  ' seteo el menu seleccionado en vacio, para buscar por apa el menu seleccionado por default
  m.global.setFields({ navSelect: "" })

  m.top.reloadHome = true

  ghCallApi("GetFavoritesLive")
  ' cargo la info de canales bloqueados
  controlPinList()
  ' cargo los canales de nuevo
  ghCallApi("EpgVersion", "handleEpgVersion", "handleErrorEpgVersion")

  m.login_fncCallback = ghGetChild(data, "fncCallback")
  m.login_params = ghGetChild(data, "params")

  m.logger.debug("changeAuth", { function: m.login_fncCallback, params: m.login_params })

  m.Timer.ObserveField("fire", "pauseAfterLogin")
  m.Timer.control = "start"
end sub

sub pauseAfterLogin()
  m.logger.debug("pauseAfterLogin", { function: m.login_fncCallback, params: m.login_params })

  useCallback(m.login_fncCallback, m.login_params)
end sub

sub block(event)
  data = event.getRoSGNode()
  group_id = ghGetChild(data, "params.#0")

  m.logger.debug("block", { groupId: group_id })

  hasPin = ghGetChild(m.global.status_pin, "has_a_pin", false)

  if hasPin = false then ' para probar el flujo de las dos cosas
    checkPinMessage({ group_id: group_id })
  else
    apiCheckControlPin = ghCallApi("ControlPinAdd", "pinAddOk", "pinAddError", false)
    apiCheckControlPin.group_id = group_id
    apiCheckControlPin.control = "run"
  end if
end sub

sub unblock(event)
  data = event.getRoSGNode()
  group_id = ghGetChild(data, "params.#0")

  m.logger.debug("unblock", { groupId: group_id })

  m.pinCallback = { "ok": "deletePin", "error": "handlerErrorPin", parameters: { group_id: group_id } }
  getPin("Desbloqueo de canal", "ingrese el pin para desbloquear el canal")

  ' apiCheckControlPin = ghCallApi("ControlPinDelete", "pinDeleteOk", "pinDeleteError", false)
  ' apiCheckControlPin.group_id = data.group_id
  ' apiCheckControlPin.control = "run"
end sub

sub addFavorite(event)
  data = event.getRoSGNode()
  group_id = ghGetChild(data, "params.#0")

  m.logger.debug("addFavorite", { groupId: group_id })

  apiCheckControlPin = ghCallApi("Favorite", "favoritedAdd", "favoriteAddError", false)
  apiCheckControlPin.isChannel = true
  apiCheckControlPin.add = true
  apiCheckControlPin.group_id = group_id
  apiCheckControlPin.control = "run"

  event_name = "interaction_content"

  ' enviando a google analytics
  GA4Event(event_name, {
    content_section: "vcard",
    interaction_type: "add to my list",
    content_id: ghGetChild(m, "channel.id", ""),
    content_name: ghGetChild(m, "channelname", ""),
    content_type: "tv channel",
    content_category: "no apply"
    content_availability: "by subscription",
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "content detail",
    screen_class: "/content-detail"
  })
end sub

sub deleteFavorite(event)
  data = event.getRoSGNode()
  group_id = ghGetChild(data, "params.#0")

  m.logger.debug("deleteFavorite", { groupId: group_id })

  apiCheckControlPin = ghCallApi("Favorite", "favoriteDelete", "favoriteDeleteError", false)
  apiCheckControlPin.isChannel = true
  apiCheckControlPin.add = false
  apiCheckControlPin.group_id = group_id
  apiCheckControlPin.control = "run"

  event_name = "interaction_content"

  ' enviando a google analytics
  GA4Event(event_name, {
    content_section: "vcard",
    interaction_type: "remove from my list",
    content_id: ghGetChild(m, "channel.id", ""),
    content_name: ghGetChild(m, "channelname", ""),
    content_type: "tv channel",
    content_category: "no apply"
    content_availability: "by subscription",
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "content detail",
    screen_class: "/content-detail"
  })
end sub

sub addPin(event)
  roNode = event.getRoSGNode()
  data = ghGetChild(roNode, "parametersCallback", {})

  apiCheckControlPin = ghCallApi("ControlPinAdd", "pinAddOk", "pinAddError", false)
  apiCheckControlPin.group_id = data.group_id
  apiCheckControlPin.control = "run"
end sub
sub deletePin(event)
  roNode = event.getRoSGNode()
  data = ghGetChild(roNode, "parametersCallback", {})

  apiCheckControlPin = ghCallApi("ControlPinDelete", "pinDeleteOk", "pinDeleteError", false)
  apiCheckControlPin.group_id = data.group_id
  apiCheckControlPin.control = "run"
end sub

sub pinDeleteOk() ' event
  m.top.loading = false
  m.pBackground.visible = true

  title = ghReplaceStr(ghTranslate("unlockChannel_alert_title_label", "El @canal se ha desbloqueado"), "@canal", ghGetChild(m, "channelName", "") + " " + ghGetChild(m, "channelNumber", ""))

  m.mensaje = CreateObject("roSGNode", "GHToastMessage")
  m.mensaje.ObserveField("wasClosed", "ocultarOverlay")
  m.mensaje.setFields({
    id: "fovoriteAdd"
    time: 5 '
    backgroundBlendColor: "#2E303D"
    iconWidth: 20
    iconHeight: 20
    translation: [640, 880]
    alignement: "center"
    separatorWidth: 10
    iconPosition: "left"
    title: title
    iconImage: ghGetAsset("playingLive_alert_unlockChannel_icon", ghGetImageByMode("unblock.png"))
    divisor: ghTranslate("", "")
    body: ghTranslate("", "")
  })

  m.global.WIDGET.run = { cmd: "add", componente: m.mensaje }
end sub
sub pinDeleteError() ' event
  m.top.loading = false
  showMessage("Error al desbloquear canal")
end sub

sub pinAddOk() ' event
  m.top.loading = false

  playFirstChannel()

  m.pBackground.visible = true
  m.mensaje = CreateObject("roSGNode", "GHToastMessage")
  m.mensaje.ObserveField("wasClosed", "ocultarOverlay")
  title = ghReplaceStr(ghTranslate("lockChannel_alert_title_label", "El @canal se ha bloqueado"), "@canal", ghGetChild(m, "channelName", "") + " " + ghGetChild(m, "channelNumber", ""))

  m.mensaje.setFields({
    id: "fovoriteAdd"
    time: 5 '
    backgroundBlendColor: "#2E303D"
    iconWidth: 20
    iconHeight: 20
    translation: [640, 880]
    alignement: "center"
    separatorWidth: 10
    iconPosition: "left"
    title: title
    iconImage: ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))
    divisor: ghTranslate("addFavorites_alert_separatorPipe_format", "|")
    body: ghTranslate("", "Para ver este canal necesitarás tu PIN de seguridad")
  })

  m.global.WIDGET.run = { cmd: "add", componente: m.mensaje }
end sub
sub pinAddError() ' event
  m.top.loading = false
  showMessage("Error al bloquear canal")
end sub

sub favoritedAdd() ' event
  m.top.reloadHome = true
  m.top.loading = false
  m.pBackground.visible = true

  m.mensaje = CreateObject("roSGNode", "GHToastMessage")
  m.mensaje.ObserveField("wasClosed", "ocultarOverlay")
  m.mensaje.setFields({
    id: "fovoriteAdd"
    time: 5 '
    translation: [640, 880]
    alignement: "center"
    backgroundBlendColor: "#2E303D"
    separatorWidth: 10
    iconWidth: 20
    iconHeight: 20
    title: ghTranslate("addFavorites_alert_title_label", "Añadido a canales favoritos")
    iconPosition: "left"
    iconImage: ghGetAsset("playingLive_alert_channelFavorite_icon")
    divisor: ghTranslate("addFavorites_alert_separatorPipe_format", "|")
    body: ghTranslate("addFavorites_alert_description_label", "Consulta tus canales favoritos en la Guía Completa")
  })

  m.global.WIDGET.run = { cmd: "add", componente: m.mensaje }
end sub

sub favoritedAddError() ' event
  m.top.loading = false
  m.pBackground.visible = true

  showMessage(ghTranslate("", "Error a agregar a favoritos"))
end sub

sub favoriteDelete() ' event
  m.top.loading = false
  m.top.reloadHome = true
  m.pBackground.visible = true

  m.mensaje = CreateObject("roSGNode", "GHToastMessage")
  m.mensaje.ObserveField("wasClosed", "ocultarOverlay")
  m.mensaje.setFields({
    id: "fovoriteDelete"
    time: 5 '
    translation: [640, 880]
    alignement: "center"
    backgroundBlendColor: "#2E303D"
    separatorWidth: 10
    title: ghTranslate("deleteFavorite_alert_title_label", "")
    iconPosition: "left"
    iconImage: ghGetAsset("playingLive_alert_channelFavorite_icon")
    iconWidth: 20
    iconHeight: 20
    divisor: ghTranslate("addFavorites_alert_separatorPipe_format", "|")
    body: ghTranslate("deleteFavorite_alert_description_label", "")
  })

  m.global.WIDGET.run = { cmd: "add", componente: m.mensaje }
end sub
sub favoriteDeleteError() ' event
  m.top.loading = false
  showMessage(ghTranslate("", "Error al eliminar de favoritos"))
end sub

' EPG REALOAD FLOW
sub BuildEPGReloadTimer()
  print ghLogHead();"BuildEPGReloadTimer -ini- "

  m.EPGTimer = CreateObject("roSGNode", "Timer")
  ' m.EPGTimer.duration = 60 ' un minuto - 1hora = 60min = 3600s -- 5min = 300
  m.EPGTimer.duration = 2 * 3600 ' seteo 2 horas =  4 x 3600 ( 3600 seg en una hora )
  m.EPGTimer.repeat = true
  m.EPGTimer.ObserveField("fire", "EPGReloadTimer")
  m.EPGTimer.control = "start"

  print ghLogHead();"BuildEPGReloadTimer -end- "
end sub

sub EPGReloadTimer()
  print ghLogHead();"EPGReloadTimer -ini- "

  epg = ghGetChild(m.global, "channelProvider")

  if epg <> invalid
    if epg.result.getChildCount() > 0 then
      m.top.events = epg
    end if
  end if

  print ghLogHead();"EPGReloadTimer -end- "
end sub

sub showMessage(message, color = "0xddddddff", verticalPos = "bottom", horizontalPos = "right", duration = 10)
  m.message.text = message
  m.message.color = color
  m.message.font = ghGetFont(22, "bold")
  m.message.horizAlign = horizontalPos
  m.message.vertAlign = verticalPos
  m.message.visible = true

  ' para darle un margen
  translationHor = 0
  translationVer = 0
  if horizontalPos = "left"
    translationHor = 20
  end if
  if verticalPos = "top" then
    translationVer = 20
  end if
  m.message.translation = [translationHor, translationVer]

  ' visible durante 10 segundos
  m.MessageTimer = CreateObject("roSGNode", "Timer")
  m.MessageTimer.duration = duration
  m.MessageTimer.repeat = false
  m.MessageTimer.ObserveField("fire", "hideMessage")
  m.MessageTimer.control = "start"
end sub

sub hideMessage() ' event
  m.message.visible = false
end sub

sub onSalidaHombreMuerto()
  print ghLogHead();"onSalidaHombreMuerto -- HOMBRE MUERTO! SALIDA"

  ' salgo y vuelvo a la home (reset a inicio de app)
  ClearVideoNode()

  m.global.setFields({
    navSelect: invalid
  })

  m.top.routerReset = {
    page: "HomePage",
    fields: {
      nodo: ""
    }
  }

  ' exitPlayer()
end sub

sub onCloseHombreMuerto()
  m.hombreMuerto.shutDown = true
end sub

sub exitPlayer()
  ClearVideoNode()
  m.top.close = true
end sub

sub onKeyPressed(event)
  data = event.getData()
  print ghLogHead();"KeyPressed !!! [";data;"]"
  m.hombreMuerto.keyReset = true
end sub

sub ocultarOverlay()
  m.pBackground.visible = false
  print "oculté el overlay"
end sub