' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes
  m.border = m.top.findNode("border")
  m.background = m.top.findNode("background")
  m.label = m.top.findNode("label")
  m.label.font = ghGetComponentFont("GHButton")
  m.label.setFocus(true)
  m.rightLabel = m.top.findNode("rightLabel")
  m.rightLabel.font = ghGetComponentFont("GHButton")
  m.border.uri = ghGetImageByMode(m.top.focusMap)
  m.disableButtonGradient = m.top.findNode("disableButtonGradient")
  recalcLabelSizeAndPadding()
end function

'
' EVENTS
' -----------------------------

function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function

sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.setFocus(true)
  end if
  Refresh()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub



' END FILE ------------------