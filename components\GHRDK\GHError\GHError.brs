' GHButton
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true ' si si
  m.top.visible = false ' amanece apagado
  ' texts
  m.titleText = m.top.findNode("titleText")
  m.titleText.font = ghGetComponentFont("GHErrorTitle")
  m.titleText.translation = ghVal2Trans(348, 256)

  m.descripText = m.top.findNode("descripText")
  m.descripText.font = ghGetComponentFont("GHErrorMessage")
  m.descripText.translation = ghVal2Trans(410, 304)

  m.bOk = m.top.findNode("bOk")
  m.bOk.translation = ghVal2Trans(400, 420)

  ' buttons
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "ButtonSelected")
  m.botonera.ObserveField("backSelected", "BackSelected")
  m.botonera.map = {
    "bOk": { "up": invalid, "right": invalid, "down": "bCancel", "left": invalid },
  }
  ghFocusJumpTo("botonera")
end function

'
' EVENTS
' -----------------------------

sub ButtonSelected(event)
  if m.top.debug then print ghLogHead();"ButtonSelected ***"
  child = event.getRoSGNode()
  if child.selected then
    if m.top.debug then print ghLogHead();"ButtonSelected *** SELECTED=";m.botonera.value
    child.selected = false
    m.top.value = m.botonera.value ' que boton apreto? a futuro...
    m.top.wasClosed = true ' cierro la ventana
  end if
end sub
sub BackSelected(event)
  if m.top.debug then print ghLogHead();"Pushed [BACK]"
  child = event.getRoSGNode()
  if child.backSelected then
    child.backSelected = false
    m.top.value = "" ' no apreto nada, a futuro...
    m.top.wasClosed = true ' cierro la ventana
  end if
end sub


'
' FIELDS
' -----------------------------

' FOCUS
sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.visible = true
    m.botonera.focus = true
  else
    m.top.visible = false
  end if
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus;" visible ";m.top.visible
end sub

sub recalcColors()
  m.background.color = m.top.backColor
end sub

'
'
' END FILE ------------------