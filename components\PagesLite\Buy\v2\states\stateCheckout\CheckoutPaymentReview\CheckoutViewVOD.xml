<?xml version="1.0" encoding="utf-8" ?>

<component name="CheckoutViewVOD" extends="Group">

    <script type="text/brightscript" uri="CheckoutViewVOD.brs"/>

    <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

    <interface>
        <field id="data" type="assocarray" onChange="updateData"/>

        <field id="focus" type="boolean" value="false" />
        <field id="debug" type="boolean" value="false" />
    </interface>

    <children>
        <LayoutGroup id="infoSuscripcion2" translation="[0,0]" layoutDirection="vert" vertAlignment= "center" horizAlignment= "center" itemSpacings="[3]" visible="true">
            <LayoutGroup id="priceAndCurrency" translation="[0,0]" layoutDirection="horiz" itemSpacings="[5]" visible="true" horizAlignment= "center" vertAlignment= "center">
                <Label id="price" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
                <Label id="currency" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
            </LayoutGroup>

            <Label id="iva" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>

            <LayoutGroup id="blabla" translation="[80,10]" layoutDirection="vert" vertAlignment= "center" horizAlignment= "center" itemSpacings="[-10]" visible="true">
                <Label id="suscInfo1" visible="true" translation="[4,340]" width= "157" height= "24" color="#FFFFFF" wrap="true" lineSpacing="0" horizAlign="center" vertAlign="center"/>
                <Label id="suscInfo2" visible="true" translation="[4,340]" width= "157" height= "24" color="#FFFFFF" wrap="true" lineSpacing="0" horizAlign="center" vertAlign="center"/>
            </LayoutGroup>
        </LayoutGroup>

        <Poster id="logoAddon" focusable="false" visible= "true" translation="[595,165]" />

        <Label id="title" focusable="false" translation="[0,94]" width="1280" height="32" text="" />
        <Label id="movieTitle" vertAlign="left" horizAlign="left" focusable="false" translation="[527,187]" height="30" width="300" text="" />

        <LayoutGroup id="columnas" translation="[315,155]" layoutDirection="horiz" itemSpacings="[37]" visible="true">
            <LayoutGroup id="verticalOrientacionTest" layoutDirection="vert" itemSpacings="[3]" visible="true">
                <Poster id="movieImage" width= "160" height= "240" focusable="false" />
                <Label id="fantasma" height="100" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
            </LayoutGroup>

            <LayoutGroup id="verticalOrientacion" translation="[200,700]" layoutDirection="vert" vertAlignment= "left" horizAlignment= "left" itemSpacings="[3]" visible="true">
                <Label id="fantasma" height="80" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>

                <LayoutGroup id="priceAndCurrency" translation="[400,600]" layoutDirection="horiz" itemSpacings="[5]" visible="true">
                    <Label id="total" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0"/>
                    <Label id="buyPrice" focusable="false" translation="[0,0]" text="*" />
                    <Label id="periodo" visible="true" translation="[0,0]" color="0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="left" vertAlign="center" width="50"/>
                </LayoutGroup>

                <Label id="oferta" visible="true" translation="[0,0]" color="#00A9FF" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
                <Label id="textoInformativo" visible="true" translation="[100,100]" color="#0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="left" vertAlign="left"/>
                <Label id="leyendaInformativa"  translation="[0,0]" color="#0xCCCCCC" wrap="false" lineSpacing="0" horizAlign="center" vertAlign="center"/>
            </LayoutGroup>
        </LayoutGroup>
    </children>
</component>