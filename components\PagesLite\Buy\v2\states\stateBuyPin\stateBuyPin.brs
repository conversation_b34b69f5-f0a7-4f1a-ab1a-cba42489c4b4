' stateBuyPin
' -----------------------

sub stateBuyPin()
  if m.top.debug then print ghloghead("buypin");"stateBuyPin BUYPIN ";m.buy.states["buypin"]

  substate = m.buy.states["buypin"].state
  if substate = invalid then ' tiene pin el usuario?
    BuyPinGetCheckEnabled()
  else if substate = "ok" then
    JumpTo("checkout")
  else if substate = "fail" then
    JumpTo("out")
  else if substate = "errorMessage" then ' mensaje de error y vuelvo a la pantalla de pin
    buyPinShowErrorMessage()
  else if substate = "error" then ' mensaje de error y vuelvo a la pantalla de pin
    JumpTo("out", "error")
  else if substate = "back" then ' vuelvo a la vCard
    JumpTo("out") 
  else if substate = "getpin"
    buyPinOpenScrPinInput()
  else
    print "ERROR"
    buyPinShowGenericMessage()
  end if
end sub

sub BuyPinGetCheckEnabled()
  if m.top.debug then print ghloghead("buypin");"BuyPinGetCheckEnabled -- ";ghGetChild(m.buy, "data.accessCode.enabled", false)
  m.buy.states["buypin"].enabled = ghGetChild(m.buy, "data.accessCode.enabled", false)
  if m.buy.states["buypin"].enabled then
    Jumpto("buypin", "getpin")' hago algo
  else
    JumpTo("buypin", "ok") ' sigo de largo
  end if
end sub

sub buyPinOpenScrPinInput()
  if m.top.debug then print ghloghead("buypin");"buyPinOpenScrPinInput"
  ScrOptions = CreateObject("roSGNode", "ScrPinInputV2")
  ScrOptions.id = "ScrPinInput"
  ScrOptions.ObserveField("wasClosed", "BuyPinReturn")
  ScrOptions.SetFields({ data: m.buy })
  m.top.routerChild = { page: ScrOptions } ' modo router
end sub

sub BuyPinReturn(event)
  scr = event.getRoSGNode()
  if m.top.debug then print ghloghead("buypin");"BUY:BuyPinReturn -- ";scr.value.opcion;" ";scr.value.data
  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    JumpTo("buypin","fail")
  else if scr.value.opcion = "SELECT" then
    m.buy.states["buypin"].state = "process"
    m.buy.states["buypin"].userId = ghGetRegistry("user_id", "user")
    m.buy.states["buypin"].controlPIN = scr.value.data
    ' llamo al api
    apiCheckControlPin = ghCallApi("CheckControlPinLite", "CheckControlPinReturnOK", "CheckControlPinReturnERROR", false)
    apiCheckControlPin.userId = m.buy.states["buypin"].userId
    apiCheckControlPin.controlPIN = m.buy.states["buypin"].controlPIN
    apiCheckControlPin.control = "run"
  end if  
end sub

sub CheckControlPinReturnOK(event)
  if m.top.debug then print ghloghead("buypin");"BUY:CheckControlPinReturnOK -- ";
  data = event.getData()
  m.buy.states["buypin"].data = data
  JumpTo("buypin","ok")
end sub

sub CheckControlPinReturnERROR(event)
  if m.top.debug then print ghloghead("buypin");"BUY:CheckControlPinReturnERROR -- ";
  data = event.getData()
  print ghloghead("buypin");"BUY:CheckControlPinReturnERROR -- **[ERROR]****************************"
  print ghloghead("buypin");"BUY:CheckControlPinReturnERROR -- ";data
  print ghloghead("buypin");"BUY:CheckControlPinReturnERROR -- *************************************"
  JumpTo("buypin","errorMessage")
end sub

sub buyPinShowErrorMessage()
  m.buy.states["buypin"].state = invalid ' empezamos de nuevo
  title = ghTranslate("buypin_error_title", "Error en la operación")
  msg = ghTranslate("buypin_error_description", "Por favor inténtalo de nuevo")
  button = ghTranslate("buypin_error_button_accept", "ACEPTAR")
  callback = { state: "buypin", substate: "error" }
  ShowGenericErrorMessage(title, msg, button, callback, callback)
end sub

sub buyPinShowGenericMessage()
  m.buy.states["buypin"].state = invalid ' empezamos de nuevo
  title = ghTranslate("error_generic_title", "¡Lo Sentimos!")
  msg = ghTranslate("error_generic_description", "Ha ocurrido un error. Por favor vuelve a intentarlo.")
  button = ghTranslate("error_generic_button_next", "OK")
  callback = { state: "buypin", substate: "fail" }
  ShowGenericErrorMessage(title, msg, button, callback, callback)
end sub
