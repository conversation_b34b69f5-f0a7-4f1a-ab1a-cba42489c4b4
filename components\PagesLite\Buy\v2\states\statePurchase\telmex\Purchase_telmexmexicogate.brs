' TELMEXMEXICOGATE

sub PurchaseTelmexmexicoGate()
  m.logger.debug("Purchase TELMEXMEXICOGATE init")

  TelmexmexicoRun()
end sub

sub TelmexmexicoRun(newState = invalid, info = {})
  m.logger.debug("Purchase TELMEXMEXICOGATE run", { state: newState, info: info })

  setLoading(false)

  if newState = invalid then

    TelmexmexicoGateCheck()

  else if newState = "go" then
    setLoading(true)
    Telmexmexico_CallApi()

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
    ScrCodigo.id = "telmexmexicogate"
    ScrCodigo.ObserveField("wasClosed", "onTelmexReturn")

    ' Get buyData from the current buy flow
    buyData = {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")
      oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")

      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
      screen_name: "telemexmexicogate",
      screen_class: "/telemexmexicogate",

    }
    GA4Event("purchase_telemexmexicogate", buyData)

    m.top.routerChild = { page: ScrCodigo,
      fields: {
        checkoutFieldType: "telmexmexicogate"
        buyData: buyData
      }
    }
    ' showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")
  end if
end sub

sub TelmexmexicoGateCheck()
  data = ghGetChild(m.buy.states, "purchase.paymentMethod")

  m.logger.debug("Purchase TELMEXMEXICOGATE Check", { data: data })

  if data <> invalid then
    m.buy.states["purchase"].paymentMethod.parameters = {
      link: ghGetChild(data, "data.buylink", "")
      buyToken: ghGetChild(data, "data.buytoken", "")
      object_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(m.buy, "data.accesscode.enabled", "")
    }
    TelmexmexicoRun("go")
  else
    TelmexmexicoRun("missingparameters")
  end if
end sub

sub Telmexmexico_CallApi()
  data = ghGetChild(m.buy.states, "purchase.paymentMethod.parameters")

  m.logger.debug("Purchase TELMEXMEXICOGATE CallApi", { data: data })

  if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
    apiConfirm = ghCallApi("BuyConfirmLite", "Telmexmexico_Return", "Telmexmexico_ReturnError", false)
    apiConfirm.setFields({ link: data.link, buyToken: data.buytoken })
    apiConfirm.control = "run"

  else if inStr(1, ghGetChild(data, "link", ""), "/confirm") > 0 then
    apiConfirm = ghCallApi("PaywayConfirmLite", "Telmexmexico_Return", "Telmexmexico_ReturnError", false)
    apiConfirm.setFields({ buylink: data.link })
    apiConfirm.control = "run"

  else
    TelmexmexicoRun("missingparameters")
  end if
end sub

sub Telmexmexico_Return(event)
  data = event.getData()

  m.logger.debug("Purchase TELMEXMEXICOGATE Return", { data: data })

  TelmexmexicoRun("ok", { data: data })
end sub

sub Telmexmexico_ReturnError(event)
  data = event.getData()

  m.logger.error("Purchase TELMEXMEXICOGATE Return Error", { data: data })
  TelmexmexicoRun("error", { data: data })
end sub

sub onTelmexReturn()
  checkoutSelectPaymentMethod()
end sub