<?xml version="1.0" encoding="utf-8" ?>
<component name="GHButtonDesc" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHButtonDesc.brs"/>
  <!-- <script type="text/brightscript" uri="GHButtonDesc_fields.brs"/> -->
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- text -->
    <field id="text" type="string" value="MyButton" onChange="updateFieldText"/>
    <field id="wrap" type="boolean" value="true" onChange="updateFieldWrap"/>
    <field id="font" type="node" alias="label.font" />
    <!-- icon -->
    <field id="mode" type="string" value="icon" onChange="updateFieldText"/>
    <field id="buttonText" type="string" onChange="updateFieldText"/>
    <field id="icon" type="string" value= "pkg:/images/focus01.9.png" alias="ico.uri" />
    <field id="iconColor" type="string" value="0xFFFFFF" alias="ico.blendColor" />
    <field id="icoWidth" type="string" value="18" alias="ico.width" />
    <field id="icoHeight" type="string" value="18" alias="ico.height" />
    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="112" onChange="updateFieldWidth" />
    <field id="height" type="string" value="80" onChange="updateFieldHeight" />
    <!-- align -->
    <field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign" />
    <field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign" />
    <!-- colors -->
    <field id="color" type="string" value="0xFFFFFF" onChange="updateFieldColor" />
    <field id="backColor" type="string" value="0x282828" onChange="updateFieldBackColor" />
    <field id="buttonTextColor" type="string" value="0xFFFFFF" onChange="updateFieldColor"/>
    <field id="selColor" type="string" value="0xFFFFFF" onChange="updateFieldSelColor" />
    <field id="selBackColor" type="string" value="0x981C15" onChange="updateFieldSelBackColor" />
    <field id="selButtonTextColor" type="string" value="0xFFFFFF" onChange="updateFieldColor"/>
    <!-- padding and margin -->
    <field id="padding" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true" />
    <field id="focusPadding" type="string" value="12" onChange="updateFieldFocusPadding" alwaysNotify="true" />
    <field id="focusMap" type="string" value= "pkg:/images/focus01.9.png" alias="border.uri" />
    <field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor" />
    <!-- focus and select -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="value" type="string" value="mybutton"/>
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- <rectangle id="deb" color="0xff0000" width="50" height="50" /> -->
    <Poster id="border" translation="[0,0]" width="360" height="120" uri="" visible="false" blendColor="0x0000ff" />
    <Rectangle id="background" color="0xffffff" translation="[0,0]" width="360" height="80">
      <Poster id="ico" translation="[0,0]" width="60" height="60" uri="" blendColor="0x0000ff" />
      <Label id="bLabel" visible="false" text="*" color="0x000000" translation="[0,0]" width="360" height="80" vertAlign="center" horizAlign="center" />
    </Rectangle>
    <Label id="label" text="Ok" color="0x000000" translation="[0,100]" width="360" height="80" vertAlign="center" horizAlign="center" />
  </children>

</component>
