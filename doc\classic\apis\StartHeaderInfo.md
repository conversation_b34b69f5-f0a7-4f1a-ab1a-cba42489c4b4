# StartHeaderIfno

Api inicial del servicio para obtener datos del cliente.

Al diagrama de secuencia de la documentación se le agrega una condición en la cual si en la aplicación existe una sesión guardada, no se realiza el flujo de solicitud de startheaderinfo. En cambio, si no existe la sesión, se realiza la solicitud sin enviar HKS.
Además, en el momento de realizar el logout, debe realizarse una limpieza del hks guardado en el storage.
