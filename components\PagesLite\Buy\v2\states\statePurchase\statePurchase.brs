' statePurchase

sub statePurchase()
  m.logger.debug("statePurchase", { purchase: m.buy.states["purchase"] })

  substate = m.buy.states["purchase"].state

  if substate = invalid then
    purchaseCheckData()

  else if substate = "missingparameters" then
    JumpTo("out", "fail") ' missing error screen

  else if substate = "gotdata" then
    purchaseRunMethod()

  else if substate = "notimplemented" then
    print "notimplemented called"
    JumpTo("checkout", "paymentmethodnotimplemented")

  else if substate = "fail" then
    JumpTo("out", "fail")

  else if substate = "error" then
    JumpTo("out", "fail")

  else if substate = "ok" then
    JumpTo("ticket")

  else
    a = 10' TODO: error interno
  end if

end sub

sub purchaseCheckData()
  m.buy.states["purchase"].paymentMethod = ghGetChild(m.buy.states, "checkout.paymentMethod.selected")

  m.logger.debug("purchaseCheckData", { paymentMethod: m.buy.states["purchase"].paymentMethod })

  if m.buy.states["purchase"].paymentMethod <> invalid then
    JumpTo("purchase", "gotdata")
  else
    JumpTo("purchase", "missingparameters")
  end if
end sub


sub purchaseRunMethod()
  data = ghGetChild(m.buy, "states.purchase.paymentMethod")
  method = ghGetChild(data, "gateway", "")

  m.logger.debug("purchaseRunMethod", { method: method })

  if method = "amcogate" then
    PurchaseAmcoGate()

  else if method = "promogate" then
    PurchasePromogate()

  else if method = "telmexmexicogate" then
    PurchaseTelmexmexicoGate()

  else if method = "hubgate" then
    PurchaseHubGate()

  else if method = "rokugate" then
    PurchaseRokuGate()

  else if method = "hubfacturafijagate" then
    PurchaseHubFacturaFijaGate()
  else if method = "claropagosgate" then
    PurchaseClaroPagosGate()
  else
    purchasePaymentMethodNotImplemented()
  end if
end sub

sub purchaseRunMethod_Return()
  JumpTo("purchase", "ok")
end sub

sub purchasePaymentMethodNotImplemented()
  m.logger.info("purchasePaymentMethodNotImplemented")

  ' TODO: pasar textos a KEYS !!!
  title = "Purchase"
  msg = "El medio de pago no esta implementado"
  button = "OK"
  callback = { state: "purchase", substate: "notimplemented" }

  ShowGenericErrorMessage(title, msg, button, callback, callback)
end sub

sub showMessageError(data)
  code = findBuyConfirmErrorCode(data)
  title = "Error en la operación"
  button = ghTranslate("buyconfirm_error_msg_button_ok", "**")
  callback = { state: "purchase", substate: "error" }

  m.logger.info("purchaseShowMessageError", { code: code, title: title, button: button, callback: callback })

  if code <> invalid then
    content = ghTranslate(code, "** " + code + " **")
    msgDividido = ghDividirTitCont(content, title, content)
    title = msgDividido.title
    content = msgDividido.content
  else
    if code = invalid then code = "" 'temp part need to remove
    content = "Lo sentimos, ha ocurrido un error (" + code + ")." + chr(10) + "Por favor vuelve a intentarlo."
  end if

  ShowGenericErrorMessage(title, content, button, callback, callback)
end sub

function findBuyConfirmErrorCode(data)
  codigo = invalid

  ' version 1
  codigo = ghGetChild(data, "code")

  ' version 2
  if codigo = invalid then
    codigo = ghGetChild(data, "errors.code")
  end if

  ' version 3
  if codigo = invalid then
    codigo = ghGetChild(data, "errors.#0.code")
  end if

  return codigo
end function