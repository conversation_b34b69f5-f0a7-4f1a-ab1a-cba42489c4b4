sub PurchaseClaroPagosGate()
    m.logger.debug("Purchase PromoGate Init")
    ClaroPagosGateRun(invalid)
end sub

sub ClaroPagosGateRun(newState = invalid, info = {})
    ' m.logger.debug("PromoGateRun", { state: newState, info: info })
    print "ClaroPagosGateRunlogs" ; newState
    setLoading(false)
    if newState = invalid then
        ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
        ' ScrCodigo.checkoutFieldType = "hubfacturafijagate"
        ScrCodigo.id = "claropagosgate"
        'ScrCodigo.ObserveField("wasClosed", "ClaroGagosGate_Return")

        ' Get buyData from the current buy flow
        buyData = {
            buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
            buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
            buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
            buyProductType: ghGetChild(m.buy, "data.button.producttype")
            buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
            buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
            buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
            buyBanner: ghGetChild(m.buy, "data.button.banner", "")
            oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

            contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
            contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
            contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
            contentId: ghGetChild(m.buy, "data.contentId", "")
            content_name: ghGetChild(m.buy, "data.content_name", "")
            content_type: ghGetChild(m.buy, "data.content_type", "")
            content_category: ghGetChild(m.buy, "data.content_category", "")

            paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
            screen_name: "claropagosgate",
            screen_class: "/claropagosgate",
        }

        GA4Event("purchase_claropagosgate", buyData)

        m.top.routerChild = { page: ScrCodigo,
            fields: {
                checkoutFieldType: "claropagosgate"
                buyData: buyData
            }
        }
    else if newState = "back" then
        JumpTo("checkout")

    else if newState = "go" then

    else if newState = "ok" then
        JumpTo("purchase", "ok")

    else if newState = "error" then
        showMessageError(ghGetChild(info, "data", {}))

    else
        JumpTo("purchase", "fail")

    end if
end sub