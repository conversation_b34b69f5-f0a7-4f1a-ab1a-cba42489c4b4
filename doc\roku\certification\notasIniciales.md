# Anotaciones iniciales sobre certificación



En base a la reunión con gente de ROKU.



## Roku Play

- roku adds [NO]
- rokupay obligatorio? [NO]
- si otro método de pago, entonces hay que dar documentación.



## Performance

- Roku Streaming Stick+  |mínimo
- Lanza la aplicación en menos de 15s - homescreen
- beacon para medir tiempos.
- screen to screen 3s
- si varias paginas para la home, se suma todo (?)
- respuesta del botón 250ms
- playback en 8 segundos



## Peso

- Package de 4MB o menos.



## Subida

- No hay que relogear
- Signing key no debe cambiar



## Channel

- Roku Event dispatcher.
- También durante el full screen
- no interfiera con el screen saver
- back siempre a la pantalla previa
- back desde deeplink a la home del canal.
- [ ] trickplay para mayor de 15min (aunque sea algunos, por reprocesado).
- subtítulos en mute y en replay de 15s
- subtítulos enganchados con el sistema en general.
- instant replay entre 10 y 25 seg
- bookmarking para mas de 15min
- [ ] NO APLICA comandos de voz en un futuro
- SI APLICA Simple voice control: arrow link de búsqueda dentro de la aplicación y deeplinking llama a la misma aplicación.



## Deep linking

- Todos los contenidos, incluso serie y episodio.
- comandos de voz tienen que andar sin volver a cargar la aplicación
- No se puede hacer deep link a otra aplicación.



## UI

- No debe tener versión cero
- No contenido para adultos.
- Si hay contenido, no esta en search
- La imagen debe ser representatitva del canal.



## Tiempos

- el peor caso promedio el resultado en 3 días.
- 2 o 3 días para reenviar.
- Al lanzamiento.. mas o menos una semana. dos semanas.



---





