# Dudas

 [volver](README.md) 



## DUDAS ARQ

https://dlatvarg.atlassian.net/browse/EPC-13423



### AFE-165 



#### HN007 - Reglas para visualización de publicidad en contenidos 

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Para el punto 2.a se menciona una experiencia diferenciada para Premium y Freemium , sin embargo no queda claro cuál es la diferencia de esa experiencia ?* 
R. Va a haber un servicio que te va a indicar que tipo de modalidad tienes (Premium o Freemium) y de ahí solo se tiene que consultar la nav/data para saber que árbol se tiene que mostrar y la level para saber que contenidos vas a mostrar, al final se puede tener en freemium con contenidos premium y un premium con contenidos freemium y un mix entre ambas



*¿Que pasa si un contenido tiene publicidad, Esta se le presentara a usuarios Premium y Freemium o solo a los Freemium?* 
R. Para la Fase 1 solo los contenidos Freemium son los que van a tener la publicidad. Pero al ser un atributo del contenido puede aplicarse a cualquier tipo de contenido en etapas posteriores 



#### HN008 - Registro de reproducción de contenido Freemium

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿En que herramienta se estan considerando enviar las metricas para que sea enviada la información? (YB, G analytics)* 
R. La información que BE va a entregar al equipo de DAT se va alimentar de los servicios de tracking que hoy en día ya se ejecutan desde las aplicaciones.  Se va a revisar con el equipo de DAT si se requiere una HT para dicho reporte. De lado de FE no se requiere hacer algo adicional, mas que asegurar que el tracking se continúe enviando. 



*¿Para el punto 1.d a qué tiempo se hace referencia , Es decir al iniciar la reproducción , finalizar o mientras se esta reproduciendo ?* 
R. Hace referencia al tiempo total de reproducción de contenido que llego a hacer el usuario. Ejemplo: Si un contenido dura 2 horas, pero solo visualizo 1 hora con 40 minutos, la visualización de contenido fue de 1 hora con 40 minutos 



#### HN009 - Formatos de publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*A qué se refiere con el perfilado? (también se indica en HN023) ya que la publicidad puede no ser apta para un perfil kids.* 
R. Se refiere a que, dependiendo de su navegación, los contenidos que ha visualizado el usuario, los favoritos, se va haciendo un perfilado de que es lo que le gusta. No entra en esta fase, entrará posterior con el proyecto de inteligencia artificial



#### HN010 - Reglas para visualización del Pre-roll

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165


*En caso de error en la carga de la publicidad, cómo se comporta/maneja este error a nivel experiencia de usuario?* 
R. Pendiente, se va revisar los errores que se pueden tener del ad manager para su validación con negocio/ux con el fin determinar cual es la mejor experiencia para el usuario. 



*En los flujos de VOD->Pin parental, primero el pin luego publicidad?* 
R. Si.  Primero el PIN y luego la publicidad                 |



#### HN011 - Reglas para visualización del Post-roll

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*En el punto 3.a ¿A qué botón del fin player se refiere (VER AHORA o CERRAR)?* 
R. Es el tache o el botón cerrar que se muestra abajo en el caso de las Smart TV y en ningún lado del criterio 3.a. mencionan algo sobre un botón o hacen referencia al criterio 2a?



#### HN012 - Reglas para visualización al Continuar reproducción del contenido

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Qué acción se tomará en caso de que al reanudar la reproducción y se detone la publicidad Pre-roll el bookmark sea casi llegando al final y se empalme con la publicidad Post-roll?* 
R. No se empalmaría. Si tu contenido dura 45 minutos con 59 segundos y le das reanudad por mas al final que te quedes seria 45 minutos con 59 segundos y el post Roll siempre se muestra después de los créditos, es decir cuando tu contenido este finalizado, por lo tanto, no se empalmaría.



#### HN013 - Reglas para Saltar publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Respecto al tiempo indicado en el punto 2.b no es controlable desde el FE, es decir lo que indique el ad server es lo que se respetará.* 
R: Es controlable desde FE, la llave de conf "cv_advertising_config"cuenta con ese parámetro en caso de que el XML no devuelva ese valor. 



#### HN014 - Contenidos incluidos en alcance

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Del lado de OTT no queda claro el punto 1.c (No hay contenidos de terceros)* 
R Hoy no hay contenidos de terceros pero puede haber, se deja la HU por si durante el desarrollo se solicita en algún pedido operativo. 



*Del lado de IPTV no se tiene Disney y tampoco aplica para IPTV por lo que entendemos en la HUN23 - 1.f.i “El alcance no incluye la implementación en los STB“* 
R: Correcto, no hay afectación en IPTV                       



#### HN017 - Acceso con IP Telmex

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Plataforma WEB: ¿Cómo quedará la definición cuando se tenga activa la pantalla para el logueo automático de ip telmex al cerrar sesión ? Nota: Es configurable, en producción se encuentra ahora apagada , pero es importante revisar este escenario.*
R: Esta pantalla se encuentra obsoleta para este flujo, se agregará una HT posterior con lo anterior. 



#### HN029 - Formulario de Inicio de sesión HN030 - Formulario de Registro

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Se menciona rediseño actual , sin embargo no se entiende si habrá registro e inicio de sesión nuevo para la experiencia de claro video con publicidad ?. Cuando se indica el rediseño actual es el productivo o es el esperado de deuda técnica de rediseño (otros BRF’s)?* 
R: Revisar los insumos de diseño que ya fueron entregados, ya que ahí viene la experiencia 



*CA 1c y 1d - Ayuda y chat no aplica para AAF correcto? si es así por favor mencionarlo.*
R: Por eso se puso el punto 2, donde se deben de implementar los cambios con base en los insumos de diseño. En el caso de AAF se validó que no vienen estos elementos. 



#### HN031 - Flujo transaccional en modalidad Freemium

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Criterio 2.d: En VOD no aplica la modal de Formulario de oferta de contratación de canal. para VOD faltaría agregar la landing comercial de vcard/selector de planes/carrusel_selector de planes o esta se eliminará ?.* 
R: En esta fase no irán canales con publicidad pero puede haber canales free y canales premium, esto haría que cuando el usuario lo intente ver le muestra la oferta. El componente de landing conercial no se ve afectado por eso no se incluyo. 



#### HN032 - Pantalla Check out para Claro video con publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿La pantalla de checkout sera una nueva para CV con publicidad? Se indica "Se debe implementar para la iniciativa de Claro video con Publicidad la pantalla actual (rediseño )", si es como esta productiva en cada plataforma ok, pero si es la esperada a nivel BRF de transacciones requeriríamos tener últimos bocetos e HU's esperadas. A nivel boceto entenderiamos solo tiene un componente adicional , es decir el boton “Ingresa para continuar” (cuando el usuario no se a logueado).*
R: Revisar los insumos que se acaban de entregar y construir el componente de acuerdo a ello. Nota: se debe asegurar que la pantalla de checkout productiva no tenga afectación. 



#### HN033 - Inicio de sesión desde un flujo transaccional

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Qué pasa si el usuario tiene un código promocional? Actualmente al seleccionar el botón de código promocional no pasa por el checkout, se va directo al formulario de código promocional.* 
R: Se definió en listado de medios de pagos, si tiene un código promocional debería venir en la payway/workflowtstar y si viene este item dentro de las opciones, se tiene que mostrar





### AFE-166

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166



#### HN005 - Contenidos Premium

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*De acuerdo al servicio de la HT022 ¿Como vamos a identificar si un contenido es Premium o Freemium?*
R: La HT022 es para BE pero se identificará como hoy en día está con el servicio de la PBI y esta indicado en la HT014.

 

*¿Qué parámetro se va a utilizar para saber si el contenido requiere publicidad y donde va a venir la url de la publicidad? De acuerdo a la HT013 viene un atributo publicity: si viene informado es que tiene derecho a publicidad (aunque tenga prerrol/postroll apagado)?? o si viene url es que tiene derecho de publicidad. No queda claro que atributo identificaria que se muestra o no*
R: ¿Cuál sería la duda? Ya que el atributo "publicity" tendra los parámetros "post_roll" y "pre_roll", si estos vienen como 1, entonces nos indica que contará con publicidad, de lo contrario vendra en 0.



#### HN006 - Contenidos Freemium

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*De acuerdo al servicio de la HT022 ¿Como vamos a identificar si un contenido es Premium o Freemium?*
R: La HT022 es para BE pero se identificará como hoy en día está con el servicio de la PBI y esta indicado en la HT014.

 

*¿Qué parámetro se va a utilizar para saber si el contenido requiere publicidad y donde va a venir la url de la publicidad? De acuerdo a la HT013 viene un atributo publicity: si viene informado es que tiene derecho a publicidad (aunque tenga prerrol/postroll apagado)?? o si viene url es que tiene derecho de publicidad. No queda claro que atributo identificaria que se muestra o no*
R: ¿Cuál sería la duda? Ya que el atributo "publicity" tendra los parámetros "post_roll" y "pre_roll", si estos vienen como 1, entonces nos indica que contará con publicidad, de lo contrario vendra en 0.



#### HN010 - Reglas para visualización del Pre-roll

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Tema técnico: Antes de iniciar la publicidad hay unos segundos en los que se podría mostrar la modal de error de getmedia *
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.



#### HN011 - Reglas para visualización del Post-roll

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*ema técnico: En pruebas del SDK en la web no se pude controlar el postroll para mostrar en el fin player, es decir podría haber inconsistencias con los criterios q indican una interacción entre ambas funcionalidades. Se entiende que el Post roll es el final de la reproducción.*
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.



#### HN015 - Configuración de contenidos por operación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*El listado de contenidos dependiendo de la modalidad , se realizaria del lado de BE o FE?*
R: Depende la operación y BE categorizará pero del lado de FE se mostrará lo que devuelva el servicio cms/level.

#### HN019 - Arte de formulario de Inicio de sesión

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

#### HN020 - Arte de formulario de Registro

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Cual va a ser la llave para este nuevo arte? , es decir en las keys q se tienen hasta ahora son solo de traduccion, se desconoce si hay para imagenes , etc )*
R: Hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.



#### HN021 - Árbol de navegación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*ara dispositivos donde se tienen soportado el perfil kids, con la nueva version de nav/data como se manejaria? ejemplo si soy kids y soy premium a cual le daria prioridad? habra que revisar las distintas combinaciones de types q se tendrán.*
R: En la HT008 se menciona que se identificará con el user_token y el type.



#### HN024 - Generar reportes

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*e entiende que esos reportes son del equipo de BE o es en conjunto con las métricas q se solicitaran a FE? Pendiente conocer las métrica que se deben enviar y en que herramienta (YB, G analytics) ?*
R: Basado en la información o navegación que hace el usuario y es para el equipo de Métricas y al inicio del BRF se encuentra el documento Métricas del producto Claro Video  y se encuentra pendiente el doc de Google Analytics al finalizar el día.



#### HN027 - Menú de navegación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*el punto 3. La opción ingresar vendrá como parte de las keys de traducción? para user freemium.*
R: También está contemplada como una llave y hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.



#### HN032 - Pantalla Check out para Claro video con publicidad

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*e requiere de una llave de configuracion para prender y apagar el botón.*
R: No, solo si el texto viene de la llave de traducción se visualiza, de lo contrario no se visualizará.



#### HT002 - Modificaciones al servicio user/login y user/register

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Se van a utilizar los servicios solo cuando este encendido claro video con publicidad ?*
R. Todo lo relacionado con las historias técnicas depende de esa llave, cuando está encendida debe mostrar los flujos de CV con publicidad y cuando está apagada debe mostrar los flujos actuales. No se deben hacer modificaciones a los flujos/componentes actuales se debe generar una nueva lógica.



#### HT003 - Modificaciones al servicio v2/loginbytoken

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



#### HT004 - Preservación del user_token

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



#### HT005 - Renovación del token

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



#### HT006 - Utilización user_token como parámetro de autenticación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



#### HT008 - Modificaciones al servicio nav/data

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?



#### HT009 - Versionado de CMS

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?



#### HT010 - Modificaciones al servicio v1/plan/offers

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?



#### HT0011 - Carrusel Selector de planes

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*No queda claro el uso de los valores carousel_version*
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.

 

*Se va a implementar la fase 2 de la homologación de llaves(BRF-10022)? Qué llaves se van a utilizar para la información del carrusel plan selector o va a seguir siendo el mismo carrusel ?*
R. Dentro del servicio de la cms/level se encontrará el nuevo type con la URL del servicio de offers.

 

*carousel_version solo se va a enviar si esta prendida la funcionalidad de CV con publicidad ?*
R. Si.

 

*Validar si hay mock’s de ejemplo del servicio antes del swagger, esto nos ayudaría en ir maquetando.*
R. Se entrega el jueves al final del día.



#### HT012 - Carruseles con publicidad

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Esta HU fue cancelada por lo que se tendría que eliminar, dado que se indicó el BRF es el MVP no debería estar dentro del BRF.*
R. Está en proceso de elminación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.



#### HT014 - Modificaciones al servicio payway/v1/purchasebuttoninfo

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Si no se tiene el listbutton como vamos a pintar el boton play , la tabla no queda clara.*
R: Se revisará a mayor detalle en la llamada de las 4.



#### HT016 - Modificaciones a los servicios search/predictive y search/linealpredictive

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*¿Este filtrado estaria considerado del lado de FE o BE ?*
R: Considerado del lado de BE.



#### HT018 - Acciones con contexto de usuario en modalidad Freemium

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Esta funcionalidad solo aplica si esta encendida la parte de claro Publicidad, o aplica con usuario anónimo?*
R: Esta funcionalidad aplica solo para claro video con publicidad, el usuario anónimo es parte de la experiencia productiva no parte de esta nueva.



#### HT020 - Implementar comunicación de llaves apa/metadata y apa/asset

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*pendiente conocer el listado final*
R: También está contemplada como una llave y hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.



#### HT021 - No considerar la implementación de Bitmovin

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*en el caso de AAF en la poc se definió si usar, modifican la HU?*
R. Está en proceso de modifiación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.



#### HT023 - Modificaciones al servicio user/logout

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*El nuevo servicio se va a implementar y solo va a aplicar para lo nuevo de claro publicidad o aplica para todas las regiones sin importar si esta activo o no ?*
R: Todos los servicios solo se van a aplicar si esta activo claro video con publicidad.

 

*El criterio 1 debería indicar que tambien es para FE ya que es un cambio de versión, esto se repite en casi todas las HUT , por lo que deberían aplicar para ambos.*
R. Está en proceso de modificación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.






### AFE-167






#### AFE-167/8

https://dlatvarg.atlassian.net/browse/AFE-167
https://dlatvarg.atlassian.net/browse/AFE-168

#### HN016 - Acceso a la plataforma

*¿Ya no se pasa por la pantalla de selección de perfil al ingresar a la aplicación con la nueva experiencia de publicidad? Porque en la HN016 dice que tanto usuario a freemium com premium se le debe dirigir al nodo inicio:*
R: 1. Si la llave de configuración profiles_config se encuentre como "enable": true en la región, si tendría que estar mostrando la pantalla de perfiles al iniciar sesión.



*Se debe considerar que con android no hay asociación de método de pago. Para un usuario anónimo/freemium la pbi regresa la bandera purchasable=false, con lo cual no se muestran los botones de renta/compra/suscripción, se muestra el mensaje que indica que vaya a web para realizar la transacción.*
R: 2. Lo tomamos en cuenta y lo agregaremos como criterio de aceptación en las HT que tienen que ver con ese servicio.



### AFE-169

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169


#### HN019 - Arte en login

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*Las pantallas de Inicio de sesión/Registro aparentemente son iguales a las actuales de producción, sin embargo, se observan diferencias en tipografía, fuente de teclado y botones. Se requiere contar con los specs o indicarnos si se mantendrá lo productivo?*
R. No son las mismas pantallas y por lo que se comento la experiencia productiva debe estar separada de esta nueva por el hecho de que pueden permanecer ambas en una misma partición. Adicional los Specs fueron entregados, favor de revisar.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentran estas pantallas.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 



*El ticket no se mostraría ya que en México tenemos one click, pero al activarse en otras regiones puede caer en el mismo escenario donde no se tengan bocetos/specs correctos de AAF, requerimos los bocetos/specs de pantallas checkout/ticket.*
R. En los insumos de diseño para AAF si se cuentan con las pantallas de check out y ticket.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentra la pantalla de ticket.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 



#### HN020 - Arte en Register

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*Las pantallas de Inicio de sesión/Registro aparentemente son iguales a las actuales de producción, sin embargo, se observan diferencias en tipografía, fuente de teclado y botones. Se requiere contar con los specs o indicarnos si se mantendrá lo productivo?*
R. No son las mismas pantallas y por lo que se comento la experiencia productiva debe estar separada de esta nueva por el hecho de que pueden permanecer ambas en una misma partición. Adicional los Specs fueron entregados, favor de revisar.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentran estas pantallas.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 



*El ticket no se mostraría ya que en México tenemos one click, pero al activarse en otras regiones puede caer en el mismo escenario donde no se tengan bocetos/specs correctos de AAF, requerimos los bocetos/specs de pantallas checkout/ticket.*
R. En los insumos de diseño para AAF si se cuentan con las pantallas de check out y ticket.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentra la pantalla de ticket.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 



### AFE-171

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

#### HN030

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Plataforma AAF: El botón de Telmex solo tiene el texto "Conoce las promociones", cambiará? si es así se requiere conocer máximo caracteres necesarios para el botón.*
R. Se comparten los insumos de rediseño 2023:
https://amcomx.invisionapp.com/freehand/CVSTVOnboarding2023-Llaves-gLMNhK3DH
En estos se indican las siguientes medidas para este botón:



*Criterio 1.d no aplica para AAF ni Roku, favor de aclarar en HU.*
R. Como se ha mencionando en reuniones, actualmente no se están actualizando las HN para dispositivos específicos. Por otro lado, en los insumos de diseño no se están considerando estos dispositivos.



#### HN027

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿Hay algún documento donde vendrán los nodos que se mostrarán en la SD México, dependiendo de si es usuario Freemium o Premium o se deberán mostrar tal y como están en el boceto?*
R. Se deberán mostrar como están en los bocetos.



#### No existe

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Una vez que el usuario se registra (freemium con sesión), en los bocetos ya no se muestra el cintillo azul de los paquetes para contratar para mostrar el plan selector. ¿Esto es correcto o debe seguirse mostrando? No hay una HN que lo especifique.*
R. El comportamiento del cintillo azul se comportará de manera similar al como está actualmente, se mostrará unicamente a los usuarios Freemium con sesión.



#### HN028

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿Qué tipo de contenido HTML se incluirá, solo texto enriquecido? , es decir tanto imagen como texto vienen en el HTML o solo es una parte ? No queda claro respecto a como se visualiza en bocetos.*
R. El HTML proporcionado por el equipo de CDS ya vendrá con todos los elementos que se requieren visualizar (textos e imágenes).



#### HT011

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿La navegación del carrusel será como la de otros ribbons del home o cómo la del plan selector cuando se llega desde el cintillo azul ?*
R. No se entiende bien la duda, ya que no hay una solicitud de cambio de navegación en los carruseles.



#### HN027

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Criterio 3. se menciona opción Ingresar, pero en bocetos se muestra el avatar para ingresar al Inicio de sesión., cual es la correcta?*
R. Se debe considerar lo que está en los bocetos.



#### HN034

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*La HU indica un título de suscripción pero por cómo se visualiza en boceto puede variar de acuerdo a suscripción/compra/renta, favor de aclarar si es como viene en HU o boceto o es dependiendo del contenido y aclararlo en la HU.*
R. En el criterio 1.b indica que el titulo debe ser configurable por operación, por lo que cambiará de acuerdo a asi es suscripción/compra/renta.



#### No existe

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*No hay una HU como tal pero en bocetos se ve que la flecha esta chueca ya que esta sobre el play no sobre el botón Agregar Mi lista*
R. Aunque la flecha por algún error se haya movido de lugar, el flujo correcto es que detone desde el botón "Agregar a mi lista".



#### NA

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*usuario sin IP Telmex se registra para agregar un contenido a su lista, viene desde el reproducir , esto es correcto? no debería ser desde agregar a mi lista?*
R. El flujo correcto es que se detone desde el botón "Agregar a mi lista", tiene que pasar por el flujo de inicio de sesión o si selecciona dentro de la pantalla "Inicia de sesión" la opción "Registrar".





### AFE-172

AFE-172 https://dlatvarg.atlassian.net/browse/AFE-172

#### HT011

AFE-172 https://dlatvarg.atlassian.net/browse/AFE-172

*Plataforma AAF: La posición del carrusel siempre será hasta el final? La duda surge porque hay una complejidad en el manejo de focos (póster/ botón QUE INCLUYE) si este se encuentra entre otros carruseles listados. Podría considerarse en la primera fase que sea el último carrusel (plan selector) ? *
R. ¿Cuál sería la complejidad técnica del foco para entender con mejor detalle la solicitud?



*Si es un usuario Premium y se tiene una sesión activa se debería de mostrar la pantalla de perfiles antes de cargar el home ?*
R. Se debe considerar la pantalla de perfiles, ese flujo no tiene impacto.



*Plataforma Web break point:  en el brf de nodo menú CR --> BRF-7331: Todos | CV | Rediseño Menú 2022: Actualización del menú para Web Breakpoint (https://dlatvarg.atlassian.net/browse/BRF-7331) se pide un navegación distinta a la solicitada en bocetos de CV con publicidad. 
Dado que se está considerando el desarrollo de nodo menú para cumplir con la  navegación solicitada en rediseño , esto genera discrepancias en ambos pedidos.*
R. Se revisará con Negocio/UX.






### AFE-174

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

En el entendido que deben de convivir los servicios anteriores y los nuevos, En el diagrama se observa que el servicio StartheaderInfo se ve afectado por lo que nos surgen las siguientes dudas :

*¿El servicio user/v1/startheaderinfo  se va a consultar sin importar que este activa la funcionalidad de claro publicidad ? (Ya que no se sabe si la funcionalidad esta activa hasta que se consume apa/metadata.*
R. 1. Se esta solicitando el cambio de versionado de API (user/v1/startheaderinfo) para todo el proyecto. Al ser el primer servicio que consulta la app no se puede determinar el cambio por alguna flag.



*¿Cómo va a convivir el hks y el userToken al iniciar la aplicación y al realizar un logout ya que se entiende que para los antiguos flujos el hks va a seguir siendo la autenticación y para los nuevo el userToken().*
R. 2: El nuevo servicio responderá con user_token y hks se deben hacer os cambios con user_token a las APIs listadas en la HT00 HT006 - Utilización user_token como parámetro de autenticación y los servicios que no estén contemplados en el listado anterior, deberían seguir trabajando con el hks.



*¿El servicio apa/launcher es obligatorio o depende de si se está enviando actualmente?*
R. 3: Se deja en color rojo en el diagrama ya que no es un flujo mandatorio, se agregó para los dispositivos que hoy en día lo consultan.


*¿El servicio loginbytoken queda descartado para esta implementación? Ya que no se observa en el diagrama proporcionado*
R. 4: Se cambio por el nuevo versionado del servicio v1/ott/isloggeding el cual también ya se encuentra en el diagrama.


*Si se va a utilizar un nuevo servicio user/v1/ott/isloggedin de nuestro lado vemos viable que este nuevo servicio ya contenga la información de superhighlight y payway profile, Es posible considerar esto? ¿De no ser posible podrían darnos un poco más de contexto para entender esa parte y así poder dar alguna opción de mejora?*
R. 5: Es un servicio que no traerá los atributos que hoy en día devuelve la user/isloggeding por eso es necesario que se manden a llamar las siguientes dos APIs.



#### HT034

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*Se observa que se va a tener dos URL una para pre-roll y otra para post roll. Esto complica el desarrollo para los dispositivos AAF y WPLAY debido a que estamos utilizando el SDK y nosotros solo requerimos una url en donde se tenga toda la configuración del Ad ya sea que tenga pre, post, mid etc. Por lo cual para estos dispositivos no es viable que se tenga esa separación. ¿Se puede considerar que para estos dispositivos se tenga solo una url?*
R.Se va a solicitar al equipo de AYM que proporcione una url donde se tengan post roll y pre roll en un solo item, dependiendo la configuración
Considerando los tres escenarios:

- pre roll solo
- post roll solo
- ambos en uno solo



#### HT028 - Render HTML Se tiene las siguientes dudas :

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*Quíen va a definir cual va a ser la ruta?*
R. En web y aaf no habría tema en recibir el HTML directo del servicio pero en los móviles s si tendría tema por eso se requiere una nueva ruta para que los móviles la rendericen un web view



*El Equipo de web va a ser el encargado de proporcionar el html ?*
R. La URL la define el dispositivo Web.



*Del lado de web se tiene que tener las 2 opciones es decir se tiene que tener la ruta y cuando el usuario ingrese al nodo premium en la web se debe de consultar la llave y hacer el render del html ?*
R. UX debería de proporcionar el HTML como en el figma o invision. Cualquier cambio diferente se estaría notificando lo más pronto posible.







### AFE-175

AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175



#### HT006 

AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175

*El HKS ya se elimina de todos los servicios como parámetro de entrada? al menos para usuario anónimo ya que la startheaderinfo con el nuevo versionado ya no lo regresa *
R. Utilización user_token como parámetro de autenticación se anexa el documento BRF-11088 - FE - IPTV | Uso de user_token como autenticación principal donde se indica las APIs, que tendrían ese impacto y los servicios que no estén contemplados en el listado anterior, deberían seguir trabajando con el hks.



#### ?? - preroll postroll

AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175

*Se tendrá más de un vast para la publicidad de un mismo contenido? porque vemos que están separando pre roll y post roll, pero el deber ser para el SDK de IMA (junto con los ejemplos que vienen en: IMA sample tags  |  IMA SDK for Android  |  Google for Developers ) es que tanto pre roll como post roll, e incluso mid roll vienen en el mismo archivo vast. Si lo separan el SDK de android (TV y móviles) no lo va a reconocer. Ahí en el link que proporcionamos viene el ejemplo:*
R. Se va a solicitar al equipo de AYM que proporcione una url donde se tengan post roll y pre roll en un solo item, dependiendo la configuración
Considerando los tres escenarios:

- pre roll solo
- post roll solo
- ambos en uno solo





### AFE-182

#### AFE-182

https://dlatvarg.atlassian.net/browse/AFE-182


TO VALIDATE


### AFE-183

#### AFE-183

https://dlatvarg.atlassian.net/browse/AFE-183



Actualmente para desplegar la pantalla de check out se necesita del servicio workflow el cual nos regresa información para pintar dicha pantalla , Derivado de esto surgen las siguientes dudas:

- Actualmente el servicio requiere que el usuario este logueado por lo que si lo hacemos desde un usuario fremium que aun no esta registrado nos estara mandando un error el servicio. ¿Como se va a atacar este error ? 
- ¿Se planea un nuevo servicio ? 
- ¿Se tienen consideradas nuevas llaves ?

Ya que de momento no vemos en ninguna HUT como se estara atacando este problema.



R. Buenas @Erick Hernandez como lo hablamos ayer se detecta la necesidad de tener un nuevo servicio que nos devuelva la información para mostrar esta pantalla.

No se puede usar la **workflowstart** porque esta API lo que devuelve son los medios de pago con los que se puede transaccionar y esta pantalla no es un checkout, sino una previa informativa.

Se quedo hoy tener cerrado ese tema para compartir la solución con uds. 






### AFE-184

#### AFE-184

https://dlatvarg.atlassian.net/browse/AFE-184


IN ANALYSIS


### AFE-186

#### AFE-186

https://dlatvarg.atlassian.net/browse/AFE-186


PENDIENTE


### AFE-187

AFE-187 https://dlatvarg.atlassian.net/browse/AFE-187

*La implementación que actualmente se tiene de suspención al usuario se seguira considerando con la implementación de claro publicidad ?  De ser asi como quedarian los flijos para mostrar dichas pantallas.*
R. Con respecto a la duda planteada y nuestra conversación anterior por llamada, quiero informarte que el flujo de usuario suspendido se mantendrá de la misma manera que se trabajó en un requerimiento anterior. En este nuevo proyecto, no se consideró realizar cambios en el flujo, ya que no hay impacto por el momento. No obstante, cualquier cambio o actualización que surja será compartido contigo lo más pronto posible.



### AFE-194



### AFE-209



##### HT018

AFE-209 https://dlatvarg.atlassian.net/browse/AFE-209

*Y entendiendo que el BRF habla sobre contenidos VOD se tiene la duda de si apican o no estos puntos ya que se enfocan en contenidos live.  ¿Se tendran que considerar los flujos de live?*

T. Tal cual como lo mencionas, las otras aplicaciones no cuentan con este comportamiento con un usuario anónimo y que aparentemente como lo comentas es algo que podría derivarse de un tema que se viene atrasando con un rediseño tiempo atrás. De tal forma, que con este proyecto “Claro publicidad” se solventaría este escenario al no contemplar estas opciones de “Añadir canales favoritos y Programar recordatorios” y homologare con las demás aplicaciones.

Así que de nuestro estamos de acuerdo en no aplicar un comportamiento para live con los anteriores opciones y homologarse al comportamiento de las demás aplicaciones con usuario anónimo.





## Del grupo 



### ANL+ARQ+TL

**06-03-2024**

Buenas Todos, solo para notificarles que se tuvo el día de hoy SD referente al BRF de Claro video con Publicidad, en la cual les listare los servicios que se dieron el OK, por lo que ya pueden hacer uso de los mismos:

- https://dlatvarg.atlassian.net/browse/EPC-13956
  - /services/payway/v2/purchasebuttoninfo: https://app.swaggerhub.com/apis/ClaroVideo/v1_purchasebuttoninfo/2.0.0
- https://dlatvarg.atlassian.net/browse/EPC-13896
  - /services/campaign/v1/: https://app.swaggerhub.com/apis/ClaroVideo/campaign/1.0.0
  - Como ultimas pruebas se pidio que nos cambiaran la URL para validar que es posible el cambio, el día de mañana se buscara que quede la de **PLAYER** compartida por el equipo de AYM
  - Erick esta API va a tener una modificación para el tema de Bitmovin (VMAP) lo cual se empezara a estimar una vez que nos den el GO que ya con eso no hay mas temas de implementación y se planificara el cambio y posterior lo validaremos en la System Demo correspondiente.



### Team WEB,Win,Roku

**06-03-2024**



Erick me pidió que les pase la siguiente información:

 

Anteriormente se obtenia el valor de superhighlight desde user/v1/isloggedin ya que lo regresaba aun cuando el usuario era anonimo:



<img src="img/dacbd050-ae16-4b7f-8120-049c493c6ba6.jpg" alt="dacbd050-ae16-4b7f-8120-049c493c6ba6" />



y se enviaba como user_status en cms/superhighlight:



![7d9e8828-c9f3-44df-bb4a-86d0e46c274e](img/7d9e8828-c9f3-44df-bb4a-86d0e46c274e.jpg)



la nueva llamada al servicio user/v1/ott/isloggedin no contiene esta información, por lo que se tendra que hacer una llamada a [**/services/user/v1/superhighlight**](https://app.swaggerhub.com/apis/ClaroVideo/Superhighlight/1.0.0#/default/get_services_user_v1_superhighlight)**:**



![9694fd48-a4d7-479b-97b7-b8ffbc246224](img/9694fd48-a4d7-479b-97b7-b8ffbc246224.jpg)



y concatenar con comas todos los valores del arreglo y mandarlo en cms/superhiglight:



![762fe589-0ddd-45f8-9d4a-c79de333348c](img/762fe589-0ddd-45f8-9d4a-c79de333348c.jpg)





**15-03-2024**

Todos nos compartieron los flujos transaccionales se los paso para que me ayuden a echarle un ojo por favor.

Es prioridad revisarlos por si algo esta mal.

 

Les comparto los diagramas relacionados al CR (https://dlatvarg.atlassian.net/browse/BRF-12113) y en general al flujo de compra/renta/suscripción de un usuario Freemium.

- Suscripción Addon: https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4137484306
- Compra y Renta: https://dlatvarg.atlassian.net/wiki/spaces/AF/pages/4138467757/Compra+y+Renta+de+Contenidos+VOD



**21-03-2024**

[13:00] Erick Jonathan Hernandez Sanchez

Hola Todos queda confirmado cual va a ser la url a utilizar para este servicio 

![d99887a3-3deb-49b9-a296-a8090989c292](img/d99887a3-3deb-49b9-a296-a8090989c292.jpg)

 

