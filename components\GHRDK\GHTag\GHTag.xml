<?xml version="1.0" encoding="utf-8" ?>
<component name="GHTag" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHTag.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- text -->
    <field id="text" type="string" value="X" onChange="updateFieldText"/>
    <field id="wrap" type="boolean" value="false" onChange="updateFieldWrap"/>
    <field id="font" type="node" alias="label.font" />
    <field id="backMap" type="string" value="pkg:/images/back_tag.9.png" alias="background.uri" />
    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="366" onChange="recalcLabelSizeAndPadding" />
    <field id="height" type="string" value="314" onChange="recalcLabelSizeAndPadding" />
    <!-- align -->
    <field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign" alwaysNotify="true"/>
    <field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign" alwaysNotify="true"/>
    <!-- colors -->
    <field id="backColor" type="string" value="0xFFFFFF" onChange="recalcColors" />
    <field id="color" type="string" value="0x000000" onChange="recalcColors" />
    <!-- padding and margin -->
    <field id="vertPadding" type="integer" value="2" onChange="recalcLabelSizeAndPadding" />
    <field id="horizPadding" type="integer" value="5" onChange="recalcLabelSizeAndPadding" />
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Poster id="background" uri="" blendColor="0x0000ff" translation="[0,0]"/>
    <Label lineSpacing="0" id="label" text="Ok" color="0x000000" translation="[0,0]" vertAlign="center" horizAlign="center"/>
  </children>

</component>
