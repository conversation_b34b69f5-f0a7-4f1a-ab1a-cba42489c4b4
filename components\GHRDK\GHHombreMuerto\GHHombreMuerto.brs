' GHVideoPanel
' buscar
' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
' --------------------------
sub Init()
  m.top.debug = true
  if m.top.debug then print ghLogHead();"Init ***"

  initData() ' obtencion de parametros
  initFormat() ' dibujo de la pantalla

  if ghGetChild(m.data, "enable", false) then ' si esta habilitado..
    DeadManInit() ' configuro el timer del hombre muerto
    PantallaInit() ' configuro el timer de la pantalla
    EpisodeReset() ' inicializo el conteo de episodios
    DeadManStart() ' arranco a contar
  else
    print "-----------------------------------"
    print "::: HOMBRE MUERTO DESHABILITADO :::"
    print "-----------------------------------"
  end if
end sub
sub initData()
  if m.top.debug then print ghLogHead();"initData **"
  m.data = m.global.hombreMuerto
  if m.top.debug then
    print " "
    print "%%[GLOBAL HOMBREMUERTO]%%%%%%%%%%%%%"
    print m.data
    print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
    print " "
  end if
  if ghGetCHild(m.data, "enable") = invalid then
    m.data = {
      "enable": false, ' para pruebas true,
      "totalEpisodes": 2,
      "totalTimePlayer": 240, ' 1 para 1 minuto
      "modalActiveTime": 60,
      "returnPath": {
        "vod": "",
        "npvr": "returnPath_npvr",
        "live": "returnPath_live"
      },
      "inactivityPath": {
        "vod": "",
        "npvr": "inactivityPath_npvr",
        "live": "inactivityPath_live"
      }
    }
    print "%%[USING DEFAULT CONFIG -- HOMBRE MUERTO]%%%%"
    print m.data
    print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  end if
  if m.data.totalTimePlayer <> invalid then m.data.totalTimePlayer = m.data.totalTimePlayer * 60
  if m.data.modalActiveTime <> invalid then m.data.modalActiveTime = m.data.modalActiveTime * 60
  ' parametros de pantalla
  m.isShowingPantalla = false
  if m.top.debug then
    print ghLogHead();"------------------------------"
    print ghLogHead();m.data
    print ghLogHead();"------------------------------"
  end if
end sub
sub initFormat()
  if m.top.debug then print ghLogHead();"initFormat **"
  m.pantalla = m.top.findNode("pantalla")
  ' m.pantalla.ObserveField("visible", "onPanelVisibleChange")
  m.pantalla.setFields({
    translation: [0, 0]
    width: 1280
    height: 720
    visible: false
  })
  m.top.findNode("background").setFields({
    color: "0x00000080"
    translation: [0, 0]
    width: 1280
    height: 720
  })
  m.top.findNode("logo").setFields({
    uri: ghGetAssetByMode("landing_head_logoClarovideo")
    translation: [60, 23]
    width: "154"
    height: "25"
    visible: true
  })



  m.top.findNode("title").setFields({
    focusable: "false"
    translation: "[0,128]"
    width: "1280"
    height: "56"
    horizAlign: "center"
    text: ghTranslate("continuePlayback_modal_title_label", "** ¿Sigues ahí? **")
    font: ghGetFont(30, "bold")
  })
  m.btnContinuar = m.top.findNode("btnContinuar")
  m.btnContinuar.setFields({
    value: "continuar"
    translation: "[464,217]"
    width: "368"
    height: "72"
    text: ghTranslate("continuePlayback_modal_option_button_continueToSee", "** CONTINUAR VIENDO **")
    backcolor: "#981C15"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    selBackColor: "#981C15"
    focusColor: "0xFFFFFFFF"
  })
  m.btnRegresar = m.top.findNode("btnRegresar")
  m.btnRegresar.setFields({
    value: "regresar"
    width: "368"
    height: "72"
    text: ghTranslate("continuePlayback_modal_option_button_back", "** REGRESAR **")
    translation: "[464,288]"
    backcolor: "#2E303D"
    color: "#FFFFFF"
    selColor: "#FFFFFF"
    selBackColor: "#2E303D"
    focusColor: "0xFFFFFFFF"
  })
  m.botonera = m.top.findNode("botonera")
  ' m.botonera.obs
  ' <field id="backSelEnable" type="boolean" value="true" />
  m.botonera.setFields({
    backSelEnable: false
  })
  m.botonera.ObserveField("value", "onBotoneraSelected")
  ' <field id="value" type="string" value="" alwaysNotify="true" onChange="updateValue" />
end sub

' EVENTOS
' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
' sub onPlayerType(event)
'   data = event.getData()
'   if m.top.playerType = "live" then
'     msg = "[VIVO] "
'   else if m.top.playerType = "vod" then
'     msg = "[VOD] "
'   else
'     msg = "[????] "
'   end if
'   m.top.findNode("title").text = msg + ghTranslate("continuePlayback_modal_title_label", "** ¿Sigues ahí? **")
' end sub

sub onKeyReset(event)
  if m.top.debug then print ghLogHead();"onKeyReset RESET !!!"
  if ghGetChild(m.data, "enable", false) then DeadManReset()
end sub
sub onNewEpisode()
  if m.top.debug then print ghLogHead();"onNewEpisode ** DETECTED !!"
  if ghGetChild(m.data, "enable", false) then EpisodeCount()
end sub

' FOCUS
' -----------------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  ' if press then
  '   if key = "back" then ' back no actualiza posicion !!!
  '     handled = true
  '     ' m.top.visible = false
  '     m.top.cmd = { "command": "exit" }
  '   else if key = "play" then ' pido pausa
  '     ' manejo en ghVideo
  '     handled = false
  '   else
  '     TimerReset()
  '     turnFocusTo(guessFocusTo(key))
  '     handled = true
  '     if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
  '   end if
  ' end if
  return handled
end function
' TIMER - DeadMan
' -----------------------------
sub DeadManInit()
  if m.top.debug then print ghLogHead();"DeadManInit **"
  m.tmrDead = CreateObject("roSGNode", "Timer")
  m.tmrDead.ObserveField("fire", "DeadManTrigger")
  m.tmrDead.repeat = true ' una vez
  m.tmrDead.duration = m.data.totalTimePlayer
end sub
sub DeadManReset()
  if m.top.debug then print ghLogHead();"DeadManReset **"
  m.tmrDead.control = "stop"
  m.tmrDead.control = "start"

  'reset de episodios
  EpisodeReset()
end sub
sub DeadManStart()
  if m.top.debug then print ghLogHead();"DeadManStart **"
  m.tmrDead.control = "start"
end sub
sub DeadManStop()
  if m.top.debug then print ghLogHead();"DeadManStop **"
  m.tmrDead.control = "stop"
end sub
sub DeadManTrigger()
  if m.top.debug then print ghLogHead()"DeadManTrigger ::::::::::::::::::::"
  m.top.isShowing = true
  PrenderPantalla()
end sub
' TIMER - Pantalla
' -----------------------------
sub PantallaInit()
  if m.top.debug then print ghLogHead();"PantallaInit **"
  m.tmrPantalla = CreateObject("roSGNode", "Timer")
  m.tmrPantalla.ObserveField("fire", "PantallaTrigger")
  m.tmrPantalla.repeat = true ' una vez
  m.tmrPantalla.duration = m.data.modalActiveTime
end sub
sub PantallaReset()
  if m.top.debug then print ghLogHead();"PantallaReset **"
  m.tmrPantalla.control = "stop"
  m.tmrPantalla.control = "start"
end sub
sub PantallaStart()
  if m.top.debug then print ghLogHead();"PantallaStart **"
  m.tmrPantalla.control = "start"
end sub
sub PantallaStop()
  if m.top.debug then print ghLogHead();"PantallaStop **"
  m.tmrPantalla.control = "stop"
end sub
sub PantallaTrigger()
  if m.top.debug then print ghLogHead()"PantallaTrigger ::::::::::::::::::::"
  ' POR AHORA!!!!
  m.top.pressKey = false
  doRegresar()
end sub
' CONTEO - Episodios
' -----------------------------
sub EpisodeReset()
  if m.top.debug then print ghLogHead();"EpisodeReset **"
  m.cuentaDeEpisodios = 0
end sub
sub EpisodeCount() ' un nuevo episodio
  if m.top.debug then print ghLogHead();"EpisodeCount **"
  m.cuentaDeEpisodios += 1
  if m.cuentaDeEpisodios > m.data.totalEpisodes then
    PrenderPantalla()
  end if
end sub
' MANEJO de pantalla
' -----------------------------
sub PrenderPantalla()
  if m.top.debug then print ghLogHead();"PrenderPantalla **"
  DeadManStop() ' ya no cuento
  PantallaStart()
  EpisodeReset() ' vuelvo atras los episodios

  m.pantalla.visible = true
  m.isShowingPantalla = true

  ' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
  if m.top.playerType = "live" then
    m.top.videoObj.control = "stop" ' para la ejecucion del canal
    m.top.videoObj.visible = false ' para que no se vea la info y el buffering
  end if

  m.previusFocus = m.global.currFocus
  print "FOCO >>>>>>>> ";m.previusFocus.id
  m.botonera.setFocus(true)
  m.botonera.focus = true
end sub
sub ApagarPantalla()
  if m.top.debug then print ghLogHead();"ApagarPantalla **"
  PantallaStop()
  DeadManStart()
  EpisodeReset() ' vuelvo atras los episodios

  m.pantalla.visible = false
  m.isShowingPantalla = false

  ' hombremuerto !!! GOOSE !!! Para usar en https://dlatvarg.atlassian.net/browse/ROKUCL-24
  if m.top.playerType = "live" then
    m.top.videoObj.control = "play"
    m.top.videoObj.visible = true ' para que se vea el canal
  end if

  print "FOCO >>>>>>>> ";m.previusFocus.id
  m.botonera.setFocus(false)
  m.botonera.focus = false
  m.previusFocus.setFocus(true)
end sub
' BOTONETA
' -----------------------------
sub onBotoneraSelected(event)
  boton = event.getData()
  if m.top.debug then print ghLogHead();"onContinuar ** [";boton;"]"

  m.top.pressKey = true
  if boton = "regresar" then doRegresar()
  if boton = "continuar" then doContinuar()
end sub

sub doContinuar()
  if m.top.debug then print ghLogHead();"doContinuar **"

  m.top.isShowing = false
  ApagarPantalla()
end sub
sub doRegresar()
  if m.top.debug then print ghLogHead();"doRegresar **"
  ' ApagarPantalla()
  doShutDown()
  m.top.cmdSalir = true
end sub
sub doShutDown()
  if m.top.debug then print ghLogHead();"onShutDown -- SHUTTING DOWN SYSTEM"
  m.pantalla.visible = false
  m.isShowingPantalla = false
  if m.tmrDead <> invalid then
    m.tmrDead.control = "stop"
    m.tmrDead = invalid
  end if
  if m.tmrPantalla <> invalid then
    m.tmrPantalla.control = "stop"
    m.tmrPantalla = invalid
  end if
end sub

' sub onPanelVisibleChange(event)
'   visible = event.getData()
'   if visible then
'     ' me prendo
'     print "FOCO >>>>>>>> ";m.global.currFocus
'     m.previusFocus = m.global.currFocus
'     m.botonera.setFocus(true)
'     m.botonera.focus = true
'   else
'     ' me apago
'     m.botonera.setFocus(true)
'     m.botonera.focus = true
'     m.previusFocus.setFocus(true)
'   end if
' end sub

' "continuePlayback_modal_config": {
'   "default": {
'     "enable": true,
'     "totalEpisodes": 4,
'     "totalTimePlayer": 240,
'     "modalActiveTime": 60,
'     "returnPath": {
'       "vod": "",
'       "npvr": "returnPath_npvr",
'       "live": "returnPath_live"
'     },
'     "inactivityPath": {
'       "vod": "",
'       "npvr": "inactivityPath_npvr",
'       "live": "inactivityPath_live"
'     }
'   }
' }

' function guessFocusTo(direction) as string
'   if m.mapFocus = invalid then
'     focusTo = "playbuttons"
'   else ' a donde voy?
'     if m.map[m.mapFocus][direction] <> invalid then
'       focusTo = m.map[m.mapFocus][direction]
'       if focusTo = "ointro" and not m.oIntro.visible then
'         focusTo = m.mapFocus
'       end if
'     else
'       focusTo = m.mapFocus
'     end if
'   end if
'   return focusTo
' end function
' sub turnFocusTo(id)
'   ' if m.mapFocus <> id then
'   if m.top.debug then print ghLogHead();"turnFocusTo -- "; m.mapFocus; " -> "; id
'   oFrom = m.top.findNode(m.mapFocus)
'   oTo = m.top.findNode(id)
'   if oFrom <> invalid then ' apago el actual
'     oFrom.setFocus(false)
'     oFrom.focus = false
'   else
'     if m.top.debug then print ghLogHead();"turnFocusTo -- no encuentro";m.mapFocus
'   end if
'   if oTo <> invalid then
'     m.mapFocus = id
'     oTo.focus = true
'     oTo.setFocus(true)
'   else
'     if m.top.debug then print ghLogHead();"turnFocusTo -- no encuentro";id
'   end if
'   ' else
'   '   if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; m.mapFocus
'   ' end if
' end sub


