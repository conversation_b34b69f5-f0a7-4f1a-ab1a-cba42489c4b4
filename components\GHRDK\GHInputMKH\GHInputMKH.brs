' GHLabel
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  m.top.setFocus(true)
  ' parametros
  m.keyboardMap = [
    { mode: "letter", "keys": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R"] },
    { mode: "letter", "keys": ["S", "T", "U", "V", "W", "X", "Y", "Z", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0"] },
    { mode: "equal", "keys": [ghTranslate("search_keyboard_button_delete_all", "Borrar todo"), ghTranslate("search_keyboard_button_space", "Espacio"), ghTranslate("search_keyboard_button_delete", "Borrar"), "Ñ"] } ' Evaluar colocar Keys con ghTranslate
  ]
  m.keyFont = ghGetComponentFont("GHInputMKH_keys")
  m.inputFont = ghGetComponentFont("GHInputMKH_input")

  ' componentes
  ' m.inputBack = m.top.findNode("inputBack")
  m.inputText = m.top.findNode("inputText")
  m.inputText.hintText = ghTranslate("search_placeholder_input_text", "Títulos, géneros, artistas", {})
  m.keyBack = m.top.findNode("keyBack")
  m.keySelect = m.top.findNode("keySelect")
  m.keyBoard = m.top.findNode("keyBoard")
  m.selAnimation = m.top.findNode("selAnimation")
  m.selMover = m.top.findNode("selMover")
  m.selWidther = m.top.findNode("selWidther")
  m.PosHandler = ClassPositionHandler(m.top, m.keyBoard, m.keySelect)

  ' el orden es importante
  buildKeyboard()
  buildKeyboardBack()
  buildInput()
  m.PosHandler.moveToFirst() ' me paro en la primera key, solo la primera vez
  buildTimer()
end function

' EVENTSa
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if m.top.debug then print "***** ON"
    fLogHead = " | " + m.top.id + " | " + key + " | " + press.ToStr()
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- OK. ";fLogHead
      handled = handleActionKey() ' key
    else if key = "up" or key = "down" or key = "left" or key = "right" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- Direction key ";fLogHead
      handled = m.PosHandler.handleDirection(key)
      m.repeatLauncher.control = "start"
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent -- bubbling.. ";fLogHead
      handled = false
    end if
  else
    m.repeatLauncher.control = "stop"
    m.repeat.control = "stop"
    if m.top.debug then print "***** OFF"
  end if
  return handled
end function
function handleActionKey() ' event
  currId = m.PosHandler.currId()
  if m.top.debug then print ghLogHead();"hangleActionKey -- OK on ";currId
  if currId = "kBorrartodo" then
    m.inputText.text = ""
  else if currId = "kEspacio" then
    m.inputText.text += " "
  else if currId = "kBorrar" then
    m.inputText.text = Left(m.inputText.text, Len(m.inputText.text) - 1)
  else
    m.inputText.text += m.top.findNode(currId).text
  end if
  m.inputText.cursorPosition = Len(m.inputText.text)
  return true
end function
' TIMER
' ------------------------------
sub buildTimer()
  m.repeatLauncher = CreateObject("roSGNode", "Timer")
  m.repeatLauncher.duration = 0.3
  m.repeatLauncher.repeat = false
  m.repeatLauncher.ObserveField("fire", "triggerLaunchTimer")
  m.repeat = CreateObject("roSGNode", "Timer")
  m.repeat.duration = 0.1
  m.repeat.repeat = true
  m.repeat.ObserveField("fire", "triggerTimer")
end sub
sub triggerLaunchTimer()
  m.repeat.control = "start"
end sub
sub triggerTimer()
  m.PosHandler.handleRepeat()
end sub
' INPUT
' ------------------------------
sub buildInput()
  m.inputText.width = m.top.inputWidth
  m.inputText.translation = [(m.top.width - m.top.inputWidth) / 2, 0]
  ' m.inputText.font = m.inputFont
end sub
' KEYBOARD
' ------------------------------
sub buildKeyboardBack()
  rowCount = m.keyboardMap.Count()
  boardHeight = (m.top.keyHeight * rowCount) + (m.top.keyPadding * rowCount - 1)
  m.keyBack.width = m.top.width ' recBoard.width -- ya tengo el valor justo
  m.keyBack.height = boardHeight + (2 * m.top.keyboardPadding)
  m.keyBack.loadDisplayMode = "scaleToZoom"
  ' m.keyBack.uri = "pkg:/images/mkh_back.jpg"
  m.keyBoard.width = m.top.width ' recBoard.width -- ya tengo el valor justo
  m.keyBoard.height = boardHeight + (2 * m.top.keyboardPadding)
  ' m.keyBoard.color = m.top.KeyboardBackColor
end sub
sub buildKeyboard()
  if m.top.debug then print ghLogHead();"buildKeyboard -- Creando keyboard"

  row = m.keyboardMap[0]

  keyCount = row.keys.Count()
  allKeysPadding = (m.top.keyPadding * (keyCount + 1))
  keyboardPadding = (2 * m.top.keyboardPadding)
  width = ((m.top.width - keyboardPadding - allKeysPadding) / keyCount)
  ' hWidthKeys = (width * keyCount)

  hWidth = (width * keyCount) + allKeysPadding ' del primer renglon

  for rowNum = 0 to m.keyboardMap.Count() - 1

    row = m.keyboardMap[rowNum]
    keyCount = row.keys.Count()
    allKeysPadding = (m.top.keyPadding * (keyCount + 1))
    width = (hWidth - allKeysPadding) / row.keys.Count()
    if m.top.debug then
      print ghLogHead();"-----------------------------------"
      print ghLogHead();"keyCount =";keyCount
      print ghLogHead();"hWidth =";hWidth
      print ghLogHead();"allKeysPadding =";allKeysPadding
      print ghLogHead();"width =";width
      print ghLogHead();"-----------------------------------"
    end if

    for colNum = 0 to m.keyboardMap[rowNum].keys.Count() - 1
      txt = m.keyboardMap[rowNum].keys[colNum]
      mode = m.keyboardMap[rowNum].mode

      if m.top.debug then print "k> ";rowNum;", ";colNum;", ";txt;", ";mode;", ";width
      key = buildKey(txt, colNum, rowNum, width)
      m.keyBoard.appendChild(key)
      sep = buildColSep(colNum, rowNum, width)
      m.keyBoard.appendChild(sep)
    end for
    sep = buildColSep(colNum, rowNum, width)
    m.keyBoard.appendChild(sep)

    hSep = buildRowSep(rowNum, hWidth)
    m.keyBoard.appendChild(hSep)

  end for

  hSep = buildRowSep(rowNum, hWidth)
  m.keyBoard.appendChild(hSep)

end sub
function buildKey(kText, col, row, width)
  key = CreateObject("roSGNode", "Label")
  key.id = "k" + ghReplaceStr(kText, " ", "")
  key.text = kText
  key.font = m.keyFont
  key.width = width
  key.height = m.top.keyHeight
  key.horizAlign = "center"
  key.vertAlign = "center"
  key.translation = [(width * col) + (m.top.keyPadding * (col + 1)) + m.top.keyboardPadding, ((m.top.keyHeight + m.top.keyPadding) * row) + m.top.keyboardPadding]
  m.PosHandler.addKey(col, row, key.id)
  return key
end function
function buildColSep(col, row, width)
  sep = CreateObject("roSGNode", "Rectangle")
  sep.translation = [(col * m.top.keyPadding) + (col * width) + m.top.keyboardPadding, ((m.top.keyHeight + m.top.keyPadding) * row) + m.top.keyboardPadding]
  sep.width = m.top.keyPadding
  sep.height = m.top.keyheight
  sep.color = m.top.borderColor
  return sep
end function
function buildRowSep(row, width)
  sep = CreateObject("roSGNode", "Rectangle")
  sep.translation = [m.top.keyboardPadding, ((m.top.keyHeight + m.top.keyPadding) * (row)) - m.top.keyPadding + m.top.keyboardPadding]
  sep.width = width
  sep.height = m.top.keyPadding
  sep.color = m.top.borderColor
  return sep
end function

' Manejo de posiciones
' -----------------------------
function ClassPositionHandler(topObj, keybObj, selObj) as object
  myObj = {
    obTop: topObj,
    obKey: keybObj,
    obSel: selObj,
    keys: [],
    row: 0,
    col: 0,
    id: "",
    lastDir: "",

    init: sub()
      print ghLogHead();"PH::init"
      ' first
    end sub

    currPos: function()
      return [m.col, m.row]
    end function
    currId: function()
      return m.keys[m.row][m.col]
    end function

    addKey: sub(col, row, id)
      if m.keys[row] = invalid then
        m.keys[row] = []
      end if
      m.keys[row][col] = id
      ' print ghLogHead();"PH::addKey ";row, col, id
    end sub

    handleDirection: function(dir)
      m.lastDir = dir
      oldCol = m.col
      oldRow = m.row
      oldcolCount = m.keys[m.row].Count()
      ' movimientos
      ' ------------------------------
      if dir = "down" then
        m.row += 1
      else if dir = "up" then
        m.row -= 1
      else if dir = "left" then
        m.col -= 1
      else if dir = "right"
        m.col += 1
      end if
      ' chequeos
      ' ------------------------------

      ' limite row
      rowLast = m.keys.Count() - 1
      if m.row > rowLast then
        m.row = oldRow
        return false ' me voy por abajo...
      else if m.row < 0 then
        m.row = oldRow
        return false ' me voy por arriba...
      end if

      ' limite row
      colCount = m.keys[m.row].Count()
      if m.col < 0 then
        m.col = colCount - 1
      end if
      if m.col > oldcolCount - 1 ' verifico en la vieja, el pase a la nueva viene despues
        m.col = 0
      end if

      ' ratios
      if colCount > oldcolCount then
        aMas = true
        colRatio = colCount / oldcolCount
      else
        aMas = false
        colRatio = oldcolCount / colCount
      end if
      if m.obTop.debug then print ghLogHead();"PH::handleDirection colRatio ";colRatio;" : ";colCount;" ";oldcolCount;" aMas=";aMas

      ' calculos especiales
      if dir = "up" or dir = "down" then
        if aMas then ' vengo de un row de mas keys
          m.col = Int(m.col * colRatio)
        else ' vengo de un row de menos keys
          m.col = Int(m.col / colRatio)
        end if
      end if

      if m.obTop.debug then print ghLogHead();"PH::handleDirection FROM ";oldCol;":";oldRow;" TO ";m.col;":";m.row
      m.moveTo(m.currId())
      return true
    end function
    handleRepeat: function()
      m.handleDirection(m.lastDir)
    end function
    moveToFirst: sub()
      row = 0
      col = 0
      first = m.keys[row][col]
      m.moveTo(first)
    end sub
    moveTo: sub(id)
      m.moveSelTo(id)
      m.id = id
    end sub
    moveSelTo: sub(id)
      if m.obTop.debug then print ghLogHead();"PH::moveSelTo ";id
      key = m.obTop.findNode(id)
      if key <> invalid then
        rect = key.boundingRect()
        m.id = key.id
        animMove(m.obSel.translation, [rect.x, rect.y], m.obSel.width, rect.width)
        ' m.obSel.translation = [rect.x, rect.y]
        ' m.obSel.width = rect.width
        m.obSel.height = rect.height
      end if
    end sub
  }
  myObj.init()
  return myObj
end function
' Utilidades
' -----------------------------
sub cleanKeyboard()
  chs = m.keyBoard.getChildren(m.keyBoard.GetChildCount() - 1, 1)
  print ghLogHead();"cleanKeyboard -- ";m.keyBoard.removeChildren(chs)
  chs = invalid
end sub
' Animacion
' -----------------------------
sub animMove(fromTranslation, toTranslation, fromWidth, toWidth)
  m.selAnimation.control = "stop"
  m.selMover.keyValue = [fromTranslation, toTranslation]
  m.selWidther.keyValue = [fromWidth, toWidth]
  m.selAnimation.control = "start"
end sub
' FIELDS
' -----------------------------
sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.setFocus(true)
    m.keySelect.visible = true
    m.inputText.active = true
  else
    m.keySelect.visible = false
    m.inputText.active = false
  end if
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub
sub updateFieldText()
  if m.top.debug then print ghLogHead();"updateFieldText >> [";m.inputText.text;"]"
end sub
sub updateKeyColor()
  for k = 1 to m.keyboard.GetChildCount() - 1
    m.keyboard.GetChild(k).color = m.top.keyColor
  end for
end sub
sub refreshKeyboard()
  if m.top.debug then print ghLogHead();"refreshKeyboard (!)"
  cleanKeyboard()
  m.PosHandler = ClassPositionHandler(m.top, m.keyBoard, m.keySelect)
  buildKeyboard()
  buildKeyboardBack()
  buildInput()
  m.PosHandler.moveToFirst()
end sub
' END FILE ------------------