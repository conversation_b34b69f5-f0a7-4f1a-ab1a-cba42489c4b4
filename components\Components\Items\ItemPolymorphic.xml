<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemPolymorphic" extends="Group">
  <script type="text/brightscript" uri="ItemPolymorphic.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="index" type="integer" />
    <field id="rowIndex" type="integer" />
    <!-- <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" /> -->
    <field id="width" type="float" alias="rec.width" />
    <field id="height" type="float" alias="rec.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- da un porcentaje de foco, para poder poner opacity -->
    <field id="focusPercent" type="float" onChange="focusPercent" />
    <field id="rowHasFocus" type="boolean" onChange="rowHasFocus" />
    <field id="rowListHasFocus" type="boolean" onChange="rowListHasFocus" />

    <!-- no es confiable -->
    <field id="itemHasFocus" type="boolean" onChange="itemHasFocus" />

    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Rectangle id="rec" color="0x00FF0088" />
  </children>

</component>