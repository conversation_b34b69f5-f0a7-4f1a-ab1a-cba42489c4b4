sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/payway/linealchannels"
  m.api.query.Append({
    "region": ghGetRegistry("region")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  channels = ghGetChild(response, "paqs.paq")

  if channels = invalid then
    m.top.error = {
      message: "No se encontraron canales"
      raw: raw
    }
    return
  end if

  groupChannels = {}
  infoChannels = {}
  for i = 0 to channels.Count() - 1
    channel = channels[i]
    groups = ghSplit(ghGetChild(channel, "groups", ""), ",")
    for i2 = 0 to groups.Count() - 1
      groupChannels[groups[i2]] = { 
        payway_token : channel.payway_token
        offerid: channel.offerid
        purchaseid: channel.purchaseid
      }
      infoChannels[groups[i2]] = channel
    end for
  end for

  channels = ghGetChild(m.global, "channels.list", [])

  grid = { result: setGrid(channels) }

  m.global.setFields({
    channels: { 
      list: channels, 
      grid: grid, 
      payway: groupChannels, 
      info: infoChannels 
    }
  })

  ' m.top.content = response
  m.top.content = { list: channels, grid: grid }
end sub

' epg sin eventos ( muestra loading )
function setGrid(data)
  ' result = []
  rootChildren = CreateObject("roSGNode", "ContentNode")
  for i = 0 to data.Count() - 1
    item = data[i]
    ' events = data[i].events
    channel = CreateObject("roSGNode", "ContentNode")
    channel.id = item.group_id
    channel.title = item.name
    ghUtils_ForceSetFields(channel, {
      number: item.number
    })
    channel.HDSMALLICONURL = item.image
    rootChildren.appendChild(channel)
  end for
  return rootChildren
end function

  ' respuesta = {
  '   "entry": {
  '     "api_version": "v5.91",
  '     "authpn": "roku",
  '     "authpt": "IdbIIWeFzYdy",
  '     "device_category": "stb",
  '     "device_manufacturer": "roku",
  '     "device_model": "generic",
  '     "device_type": "generic",
  '     "format": "json",
  '     "HKS": "665771baa4f2d",
  '     "region": "argentina",
  '     "user_id": "73156552"
  '   },
  '   "response": {
  '     "paqs": {
  '       "paq": [
  '         {
  '           "name": "TVREG,TVGRT",
  '           "offerid": "14328764",
  '           "purchaseid": "",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODQsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzI4NzY0IiwicHVyY2hhc2UiOm51bGwsInByb2R1Y3R0eXBlIjpudWxsLCJwbGF5Ijp7ImVuYWJsZWQiOnRydWUsImRldmljZXMiOm51bGwsInN0cmVhbXMiOm51bGx9LCJkbCI6eyJlbmFibGVkIjpudWxsLCJkZXZpY2VzIjpudWxsLCJleHAiOm51bGx9LCJucHZyIjp7InN0b3JhZ2UiOm51bGwsInRpbWVzaGlmdCI6bnVsbH0sIm9iamVjdCI6IjQwODAiLCJncm91cHMiOlsiNzYyMjczIiwiNzYyMjM0Il19fQ.em8GUtmtJCwk8UYZtV1nsDQCr4IT7Tg2LvikTs0LpwU",
  '           "groups": "762273,762234",
  '           "key": "",
  '           "paymentmethod": "null"
  '         },
  '         {
  '           "name": "CV_MENSUAL",
  '           "offerid": "14326887",
  '           "purchaseid": "1454895209",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODUsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzI2ODg3IiwicHVyY2hhc2UiOiIxNDU0ODk1MjA5IiwicHJvZHVjdHR5cGUiOiJDVl9NRU5TVUFMIiwicGxheSI6eyJlbmFibGVkIjp0cnVlLCJkZXZpY2VzIjpudWxsLCJzdHJlYW1zIjpudWxsfSwiZGwiOnsiZW5hYmxlZCI6bnVsbCwiZGV2aWNlcyI6bnVsbCwiZXhwIjpudWxsfSwibnB2ciI6eyJzdG9yYWdlIjpudWxsLCJ0aW1lc2hpZnQiOm51bGx9LCJvYmplY3QiOiI5ODA3NDgiLCJncm91cHMiOlsiNzkxMjc0IiwiNzc1MzMyIl19fQ.ssE_tCFBZ2-Zhbo5Ge2pK_g09avCHagKi3R--mdg5jI",
  '           "groups": "791274,775332",
  '           "key": "Telmexmexico_Subscription_SVOD_30d",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "HBO",
  '           "offerid": "14364181",
  '           "purchaseid": "1454897444",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODUsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzY0MTgxIiwicHVyY2hhc2UiOiIxNDU0ODk3NDQ0IiwicHJvZHVjdHR5cGUiOiJIQk8iLCJwbGF5Ijp7ImVuYWJsZWQiOnRydWUsImRldmljZXMiOm51bGwsInN0cmVhbXMiOm51bGx9LCJkbCI6eyJlbmFibGVkIjpudWxsLCJkZXZpY2VzIjpudWxsLCJleHAiOm51bGx9LCJucHZyIjp7InN0b3JhZ2UiOm51bGwsInRpbWVzaGlmdCI6bnVsbH0sIm9iamVjdCI6IjI1NDA3NDgiLCJncm91cHMiOlsiNzkxNDQ2IiwiNzkxNDQ1IiwiNzkxNDIxIiwiNzkxNDAxIiwiNzkxNDAwIiwiNzkxMzg2IiwiNzkxMzg1IiwiNzkxMzg0Il19fQ.YZZ0D2HigmUhyl0GqMrCZyBTDusNzYG6mhYSZ2y-Z_A",
  '           "groups": "791446,791445,791421,791401,791400,791386,791385,791384",
  '           "key": "Telmexmexico_abono_HBO",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "TV_EN_VIVO",
  '           "offerid": "14391388",
  '           "purchaseid": "1454893817",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODUsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzkxMzg4IiwicHVyY2hhc2UiOiIxNDU0ODkzODE3IiwicHJvZHVjdHR5cGUiOiJUVl9FTl9WSVZPIiwicGxheSI6eyJlbmFibGVkIjp0cnVlLCJkZXZpY2VzIjpudWxsLCJzdHJlYW1zIjpudWxsfSwiZGwiOnsiZW5hYmxlZCI6bnVsbCwiZGV2aWNlcyI6bnVsbCwiZXhwIjpudWxsfSwibnB2ciI6eyJzdG9yYWdlIjpudWxsLCJ0aW1lc2hpZnQiOm51bGx9LCJvYmplY3QiOiI0NzUzNzQ4IiwiZ3JvdXBzIjpbIjExNTE3NDMiLCIxMTUxNTI4IiwiMTExNDcxMSIsIjExMTQwMjciLCIxMTE0MDI2IiwiMTEwNzM3NCIsIjk4MDU2OSIsIjk3MTY2NSIsIjk2NjkyMyIsIjkzMjEzOCIsIjkyODY2MiIsIjkwOTM2MCIsIjkwODk1NSIsIjg4ODE5NCIsIjg4ODE3OCIsIjg4ODE1OSIsIjg4ODE0MiIsIjg4ODA2OCIsIjg4ODA2NyIsIjg4ODA2NiIsIjc4NTQyMSIsIjc4NTM3NCIsIjc4NDU3NSIsIjc3NTMzMiIsIjc3NDA0MyIsIjc3NDA0MiIsIjc2NjIxMiIsIjc2NjI0MiIsIjc2NjI0MSIsIjc2NjIyOSIsIjc2NjIwNyIsIjc2NjIwNiIsIjc2NjIwNSIsIjc2NjIwNCIsIjc2NjIwMyIsIjc2NjEzNiIsIjc2NDUxMSIsIjc2NDE4OCIsIjc2NDE4NiIsIjc2NDE4NSIsIjc2NDE4NCIsIjc2NDE4MiIsIjc2NDE4MSIsIjc2NDE2OCIsIjc2NDE1MiIsIjc2NDE1MCIsIjc2NDEyMyIsIjc2NDEyMiIsIjc2NDExMyIsIjc2MzQ3NiIsIjc2MzM0NyIsIjc2MzM0NiIsIjc2MjM1OCIsIjc2MjMyMSIsIjc2MjMyMCIsIjc2MjMwOSIsIjc2MjI4OSIsIjc2MjMzOSIsIjc2MjMzOCIsIjc2MjMxOCIsIjc2MjMwNiIsIjc2MjMwNSIsIjc2MjMwMyIsIjc2MjMwMSIsIjc2MjI4NiIsIjc2MjI4NCIsIjc2MjI4MSIsIjc2MjI4MCIsIjc2MjI3OCIsIjc2MjI3NyIsIjc2MjI3NiIsIjc2MjI3NSIsIjc2MjI3NCIsIjc2MjI3MCIsIjc2MjI2NyIsIjc2MjI2NiIsIjc2MjI2NSIsIjc2MjI2NCIsIjc2MjI2MyIsIjc2MjI2MiIsIjc2MjI2MSIsIjc2MjI2MCIsIjc2MjI1OSIsIjc2MjI1OCIsIjc2MjI1NSIsIjc2MjI1NCIsIjc2MjI1MyIsIjc2MjI1MiIsIjc2MjI0NyIsIjc2MjI0NiIsIjc2MjI0MSIsIjc2MjI0MCIsIjc2MjIzOSIsIjc2MjIzNyIsIjc2MjIzNiIsIjc2MjIzNCIsIjc2MjIzMyIsIjc2MjIyNyIsIjc2MjIyNiIsIjc2MjIyNSIsIjc2MjIyMyIsIjc2MjIyMiIsIjc2MjIxNyIsIjc2MjIxNiIsIjc2MjIxMiIsIjc2MjIxMSIsIjc2MjIwOSIsIjc2MjIwNyIsIjc2MjIwNiIsIjc2MjIwNCIsIjc2MjE5NiIsIjc2MjE5NSIsIjc2MjE5NCJdfX0.4CO0NyD9udF_wDXe1cWfM9r6sa8zacbGw13_DSTqAHQ",
  '           "groups": "1151743,1151528,1114711,1114027,1114026,1107374,980569,971665,966923,932138,928662,909360,908955,888194,888178,888159,888142,888068,888067,888066,785421,785374,784575,775332,774043,774042,766212,766242,766241,766229,766207,766206,766205,766204,766203,766136,764511,764188,764186,764185,764184,764182,764181,764168,764152,764150,764123,764122,764113,763476,763347,763346,762358,762321,762320,762309,762289,762339,762338,762318,762306,762305,762303,762301,762286,762284,762281,762280,762278,762277,762276,762275,762274,762270,762267,762266,762265,762264,762263,762262,762261,762260,762259,762258,762255,762254,762253,762252,762247,762246,762241,762240,762239,762237,762236,762234,762233,762227,762226,762225,762223,762222,762217,762216,762212,762211,762209,762207,762206,762204,762196,762195,762194",
  '           "key": "PQT_AR_Paquete_TV_en_Vivo",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "CLAROTV20",
  '           "offerid": "14384979",
  '           "purchaseid": "1454888165",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODUsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0Mzg0OTc5IiwicHVyY2hhc2UiOiIxNDU0ODg4MTY1IiwicHJvZHVjdHR5cGUiOiJDTEFST1RWMjAiLCJwbGF5Ijp7ImVuYWJsZWQiOnRydWUsImRldmljZXMiOm51bGwsInN0cmVhbXMiOm51bGx9LCJkbCI6eyJlbmFibGVkIjpudWxsLCJkZXZpY2VzIjpudWxsLCJleHAiOm51bGx9LCJucHZyIjp7InN0b3JhZ2UiOm51bGwsInRpbWVzaGlmdCI6bnVsbH0sIm9iamVjdCI6IjY4MDA3NDgiLCJncm91cHMiOlsiMTEzNTAzMiIsIjExMjczNzEiLCIxMTIyMTg2IiwiMTEwOTQyOSIsIjExMDczNzQiLCIxMDM5Mjg0IiwiOTgwNTY5IiwiOTgwNTY4IiwiOTI4NjgxIiwiOTI4NjYyIiwiODg4MTU5IiwiNzkxMDUzIiwiNzg1NDIxIiwiNzg1Mzc0IiwiNzg0NTc1IiwiNzgzODMyIiwiNzc1MzMyIiwiNzY0MTg1IiwiNzY0MTg0IiwiNzY0MTgyIiwiNzY0MTY4IiwiNzY0MTUyIiwiNzY0MTUwIiwiNzY0MTIzIiwiNzY0MTIyIiwiNzY0MTEzIiwiNzYyMzA2Il19fQ.qlyXihtESgIyrwbKqlHALoTCBOVC8lKqGcXCQEABakk",
  '           "groups": "1135032,1127371,1122186,1109429,1107374,1039284,980569,980568,928681,928662,888159,791053,785421,785374,784575,783832,775332,764185,764184,764182,764168,764152,764150,764123,764122,764113,762306",
  '           "key": "CLARO_TV_GRILLA_20_PLUS",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "RTVEPLAY",
  '           "offerid": "14383379",
  '           "purchaseid": "1454893109",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODYsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzgzMzc5IiwicHVyY2hhc2UiOiIxNDU0ODkzMTA5IiwicHJvZHVjdHR5cGUiOiJSVFZFUExBWSIsInBsYXkiOnsiZW5hYmxlZCI6dHJ1ZSwiZGV2aWNlcyI6bnVsbCwic3RyZWFtcyI6bnVsbH0sImRsIjp7ImVuYWJsZWQiOm51bGwsImRldmljZXMiOm51bGwsImV4cCI6bnVsbH0sIm5wdnIiOnsic3RvcmFnZSI6bnVsbCwidGltZXNoaWZ0IjpudWxsfSwib2JqZWN0IjoiNTEzMDc0OCIsImdyb3VwcyI6WyIxMTE0NzExIiwiMTExNDAyNyIsIjExMTQwMjYiLCIxMTEzMjI5Il19fQ.sJ9WqLVd2EsaMVsgpJVcMHvZjC4zhS0ufN4R9iJpp7Q",
  '           "groups": "1114711,1114027,1114026,1113229",
  '           "key": "Telmexmexico_abono_RTVEplay",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "PREMIUM_HOT",
  '           "offerid": "14391383",
  '           "purchaseid": "1454899605",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODYsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzkxMzgzIiwicHVyY2hhc2UiOiIxNDU0ODk5NjA1IiwicHJvZHVjdHR5cGUiOiJQUkVNSVVNX0hPVCIsInBsYXkiOnsiZW5hYmxlZCI6dHJ1ZSwiZGV2aWNlcyI6bnVsbCwic3RyZWFtcyI6bnVsbH0sImRsIjp7ImVuYWJsZWQiOm51bGwsImRldmljZXMiOm51bGwsImV4cCI6bnVsbH0sIm5wdnIiOnsic3RvcmFnZSI6bnVsbCwidGltZXNoaWZ0IjpudWxsfSwib2JqZWN0IjoiNDc1NTc0OCIsImdyb3VwcyI6WyI3OTE0NzEiLCI3OTE0MjUiLCI3OTE0MDMiLCI3OTEzODkiLCI3NjYyNjIiLCI3NjYyNjEiLCI3NjYyNDQiLCI3NjYyMzEiXX19.n8dsaODVyMbHpYtRTvinTSt0D_4mKTeuvSVr142Z6To",
  '           "groups": "791471,791425,791403,791389,766262,766261,766244,766231",
  '           "key": "PQT_AR_PREMIUM_HOT",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         },
  '         {
  '           "name": "PREMIUM_FUTBOL",
  '           "offerid": "14392182",
  '           "purchaseid": "1454896284",
  '           "play": "1",
  '           "npvrstorage": "0",
  '           "timeshift": "0",
  '           "payway_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTk1OTg4ODYsImlzcyI6InBheXdheVwvbGluZWFsY2hhbm5lbHMiLCJwZ3MiOnsidXNlciI6IjczMTU2NTUyIiwicGFyZW50IjoiNzMxNTY1NTIiLCJvZmZlciI6IjE0MzkyMTgyIiwicHVyY2hhc2UiOiIxNDU0ODk2Mjg0IiwicHJvZHVjdHR5cGUiOiJQUkVNSVVNX0ZVVEJPTCIsInBsYXkiOnsiZW5hYmxlZCI6dHJ1ZSwiZGV2aWNlcyI6IjEiLCJzdHJlYW1zIjoiMSJ9LCJkbCI6eyJlbmFibGVkIjpudWxsLCJkZXZpY2VzIjpudWxsLCJleHAiOm51bGx9LCJucHZyIjp7InN0b3JhZ2UiOm51bGwsInRpbWVzaGlmdCI6bnVsbH0sIm9iamVjdCI6IjQ3NjA3NDgiLCJncm91cHMiOlsiODg4MTYwIiwiODg4MTQzIiwiNzY2MTY1IiwiNzY0MTE1Il19fQ.gx3QfsVgNRAlJ8zbpNC1SmlWOinAF-y5p-2MjO478aE",
  '           "groups": "888160,888143,766165,764115",
  '           "key": "PQT_AR_PREMIUM_FUTBOL",
  '           "paymentmethod": {
  '             "gateway": "amcogate",
  '             "gateway_text": "TEXT_PGS_PAYMENT_ARGENTINA_GATEWAY_AMCOGATE"
  '           }
  '         }
  '       ]
  '     }
  '   },
  '   "status": "0",
  '   "msg": "OK"
  ' }

