sub Init()
  if m.top.debug then print ghLogHead();"Init"
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  print "itemContentChanged :" m.top.itemContent
  if m.top.debug then print ghLogHead();"itemContentChanged - gateway=";gateway
  gateway = ghGetChild(m.top.itemContent, "data.gateway")
  region = ghGetRegistry("region")
  print "gateway :" gateway

  'Region :'MDP_AgregarMediodepago_Tarjeta_TextoTitulo_{region}_{gateway} Default : MDP_AgregarMediodepago_Tarjeta_TextoTitulo_{gateway}
  m.itemText = ghTranslate("MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + region + "_" + gateway, "")
  if m.itemText = "MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + region + "_" + gateway then
    m.itemText = ghTranslate("MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + gateway, "")
    ' if m.itemText = "MDP_AgregarMediodepago_Tarjeta_TextoTitulo_" + gateway then
    '   'm.itemText = ""
    ' end if
  end if
  print "itemText :" m.itemText
  'Region : MDP_AgregarMediodepago_Tarjeta_Logo_{region}_{gateway} Default : MDP_AgregarMediodepago_Tarjeta_Logo_{gateway}
  m.itemLogo = ghGetAsset("MDP_AgregarMediodepago_Tarjeta_Logo_" + region + "_" + gateway, "invalid")
  if m.itemLogo = "invalid" then
    m.itemLogo = ghGetAsset("MDP_AgregarMediodepago_Tarjeta_Logo_" + gateway, "invalid")
    if m.itemLogo = "invalid"
      m.itemLogo = ghGetAsset("select_payment_method_default_placeholder", "pkg:/images/Placeholder_SelectorMDP-FHD.png")
    end if
  end if
  print "itemLogo :" m.itemLogo, m.top.width, m.top.height
  m.itemPoster.uri = m.itemLogo
  m.itemPoster.width = handlingSizeForHD(276)
  m.itemPoster.height = handlingSizeForHD(276)
  drawTitle()
end sub

sub drawTitle()
  m.title.setFields({
    width: m.itemPoster.width - 20
    height: m.itemPoster.height - 20
    text: m.itemText
    font: ghGetFont(16, "bold")
    translation: [10, 10]
    color: "0xCCCCCC"
    wrap: "true"
    lineSpacing: "0"
    vertAlign: "bottom"
    visible: true
  })
end sub
