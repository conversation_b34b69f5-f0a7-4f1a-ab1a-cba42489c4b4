' RokupayService
' --------------------------
sub Init()
  m.logger = CreateLogger()
  ' m.top.debug = true

  m.logger.debug("RokuService Init")

  m.workFlowReturn = 0
  m.listOffers = []

  ' PURCHASES DATA
  m.purchasesInitialized = false
  m.purchases = {} ' purchases data
  ' m.purchasesPending = 0 ' how many purchases pending verification

  doGetPurchases() ' de arranque...
end sub

' EVENTS
' --------------------------
sub onTick(event)
  ' test("RPAY TICK ------------------------------")
  data = event.getData()

  ' m.logger.debug("RokuPay tick", { data: data })

  ' purchases initialization * first time
  ' if not m.purchasesInitialized then
  '   if m.top.debug then print ghLogHead("RPay");"onTick -- initializing.."
  '   doGetPurchases()
  ' else
  '   if m.top.debug then print ghLogHead("RPay");"onTick -- already initialized."
  ' end if
  ' doGetPurchases()
end sub

' CMDs
' --------------------------
sub onCmd(event) ' comandos a generar.
  data = event.getData()

  cmd = data.cmd

  m.logger.debug("RokupayService onCmd", { cmd: cmd })

  if cmd <> invalid then
    'buscar despues de pagar
    if cmd = "getPurchases" doGetPurchases()

    ' de arranque...
    'verifications
    if cmd = "verifyPurchase" doVerifyOnePurchase(data.data)
    ' screentests
    if cmd = "testScreenOngrace" ScrOnGrace(data.data)
    if cmd = "testScreenSuspended" ScrSuspended(data.data)
    if cmd = "testScreenCanceled" ScrCanceled(data.data)
    ' recovery
    if cmd = "doRecovery" DoRecovery(data.data)
    if cmd = "verifySuscription" doVerifySubscription(data.data)

  end if
end sub

sub doVerifySubscription(data)
  m.logger.debug("doVerifySubscription ***")

  m.purchasesTest = {
    "33b21cda-0a44-11f0-a926-3ac02cf7e390": {
      "check": {
        "amount": 29,
        "cancelled": false,
        "cancelledTransactionIds": invalid,
        "channelId": 165984,
        "channelName": "ClaroVideo Dev-Mex",
        "couponCode": invalid,
        "currency": "mxn",
        "errorCode": invalid,
        "errorDetails": invalid,
        "errorMessage": "",
        "expirationDate": "/Date(1742563313466+0000)/",
        "isEntitled": false,
        "originalPurchaseDate": "/Date(1742994891842+0000)/",
        "OriginalTransactionId": "33b21cda-0a44-11f0-a926-3ac02cf7e390",
        "partnerReferenceId": "",
        "productId": "CO70003373",
        "productName": "NOGGIN",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "/Date(1742994891842+0000)/",
        "purchaseStatus": "Inactive",
        "purchaseType": invalid,
        "quantity": 1,
        "rokuCustomerId": "5faa01bbe8425196831e24be7f2bcc0c",
        "status": 0,
        "tax": 0,
        "total": 0,
        "transactionId": "33b21cda-0a44-11f0-a926-3ac02cf7e390"
      },
      "data": {
        "code": "test03",
        "cost": "$29.00",
        "expirationDate": "2025-03-21T13:21:53",
        "freeTrialQuantity": 0,
        "freeTrialType": "None",
        "inDunning": "true",
        "name": "NOGGIN",
        "productType": "MonthlySub",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "2025-02-18T13:21:53",
        "purchaseId": "33b21cda-0a44-11f0-a926-3ac02cf7e390",
        "qty": 1,
        "renewalDate": "2025-03-21T13:21:53",
        "status": "Invalid",
        "trialCost": "$0.00",
        "trialQuantity": 0,
        "trialType ": ""
      },
      "status": "checked",
      "status_subscription": "InGrace"
    },
    "afedbf74-0a3a-11f0-af51-72180f7a46cf": {
      "check": {
        "amount": 129,
        "cancelled": true,
        "cancelledTransactionIds": invalid,
        "channelId": 165984,
        "channelName": "ClaroVideo Dev-Mex",
        "couponCode": invalid,
        "currency": "mxn",
        "errorCode": invalid,
        "errorDetails": invalid,
        "errorMessage": "",
        "expirationDate": "/Date(1737808869567+0000)/",
        "isEntitled": false,
        "originalPurchaseDate": "/Date(1742990792744+0000)/",
        "OriginalTransactionId": "afedbf74-0a3a-11f0-af51-72180f7a46cf",
        "partnerReferenceId": "",
        "productId": "CO70004987",
        "productName": "INDYCAR Temporada completa",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "/Date(1742990792744+0000)/",
        "purchaseStatus": "Inactive",
        "purchaseType": invalid,
        "quantity": 1,
        "rokuCustomerId": "5faa01bbe8425196831e24be7f2bcc0c",
        "status": 0,
        "tax": 0,
        "total": 0,
        "transactionId": "afedbf74-0a3a-11f0-af51-72180f7a46cf"
      },
      "data": {
        "code": "test02",
        "cost": "$129.00",
        "expirationDate": "2025-02-05T12:41:19",
        "freeTrialQuantity": 0,
        "freeTrialType": "None",
        "inDunning": "false",
        "name": "INDYCAR Temporada completa",
        "productType": "MonthlySub",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "2024-12-25T12:41:19",
        "purchaseId": "afedbf74-0a3a-11f0-af51-72180f7a46cf",
        "qty": 1,
        "renewalDate": "",
        "status": "Invalid",
        "trialCost": "$0.00",
        "trialQuantity": 0,
        "trialType ": ""
      },
      "status": "checked",
      "status_subscription": "Canceled"
    },
    "c616a11e-04c7-11f0-83c2-caf47146c639": {
      "check": {
        "amount": 19,
        "cancelled": false,
        "cancelledTransactionIds": invalid,
        "channelId": 165984,
        "channelName": "ClaroVideo Dev-Mex",
        "couponCode": invalid,
        "currency": "mxn",
        "errorCode": invalid,
        "errorDetails": invalid,
        "errorMessage": "",
        "expirationDate": "/Date(1745607021243+0000)/",
        "isEntitled": true,
        "originalPurchaseDate": "/Date(1742391670285+0000)/",
        "OriginalTransactionId": "c616a11e-04c7-11f0-83c2-caf47146c639",
        "partnerReferenceId": "",
        "productId": "AR70004153",
        "productName": "Claro Video Mensual",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "/Date(1742391670285+0000)/",
        "purchaseStatus": "Active",
        "purchaseType": invalid,
        "quantity": 1,
        "rokuCustomerId": "5faa01bbe8425196831e24be7f2bcc0c",
        "status": 0,
        "tax": 0,
        "total": 0,
        "transactionId": "c616a11e-04c7-11f0-83c2-caf47146c639"
      },
      "data": {
        "code": "test01",
        "cost": "$19.00",
        "expirationDate": "2025-04-25T18:50:21",
        "freeTrialQuantity": 0,
        "freeTrialType": "None",
        "inDunning": "false",
        "name": "Claro Video Mensual",
        "productType": "MonthlySub",
        "purchaseChannel": "DEVICE",
        "purchaseContext": "IAP",
        "purchaseDate": "2025-02-22T18:50:21",
        "purchaseId": "c616a11e-04c7-11f0-83c2-caf47146c639",
        "qty": 1,
        "renewalDate": "2025-04-25T18:50:21",
        "status": "Valid",
        "trialCost": "$0.00",
        "trialQuantity": 0,
        "trialType ": ""
      },
      "status": "checked",
      "status_subscription": "Active"
    }
  }

  ' m.logger.debug("doVerifySubscription", { offers: ghGetChild(data, "offers", []), isCancel: ghGetChild(data, "isCanceled") })
  ' m.logger.debug("all subscriptions", { subscriptions: m.purchases })

  m.isCanceled = ghGetChild(data, "isCanceled", false)

  offers = ghGetChild(data, "offers", [])

  m.workFlowCount = offers.count()

  ' borrar listado de offers antiguas
  m.listOffers = []

  if offers.count() = 0
    m.logger.debug("no hay ninguna suscripcion")

    matchSubscription()
    return
  end if

  for i = 0 to offers.count() - 1
    item = offers[i]

    apiStart = ghCallApi("WorkflowStartLite", "WorkflowStartOk", "WorkflowStartOk", false)
    apiStart.link = ghGetChild(item, "linkworkflowstart")
    apiStart.offer = item
    apiStart.control = "run"
  end for
end sub

sub WorkflowStartOk(event)
  data = event.getData()
  root = event.getRoSGNode()

  m.workFlowReturn = m.workFlowReturn + 1

  dataTest = {
    "response": {
      "list": [
        {
          "gateway": "rokugate",
          "gatewaytext": "Amco",
          "buyLink": "\/services\/payway\/paymentservice\/v1\/buyconfirm?payway=amcogate&region=argentina&device_category=stb&device_manufacturer=roku&device_model=generic&device_type=generic&user_id=73156552",
          "product_id": "test03",
          "hubCorpEnabled": false,
          "accessCode": false,
          "msgAccessCode": "Protegé tus alquileres activando tu Pin, desde tu usuario en la WEB. Se requerirá cuando alquiles.",
          "producttype": "test03",
          "buyToken": "Q1pNaE9jWG5LYzNtN0RWV2hQc1NhWUREak1FbjFlbUErLzVHMlNSTDl1UERnVGtCbVhMVzUyVGxOZjJPN2pGS0Y0Ui96Y1UzcEtOWFdHRjFDUEZ1T2xDZm1OUVRkbGRLdnZqMmFvejg5OUNlakVObmNzQlFjeU5kd3lxYjlEOGUvL1FwMjBGZ3pUTGhYMTU0UUxqWlRYRkhQK0VZSWc0cG5KK0pmZ0Y5YTdXVFlCdnNjUHF5YnlKRm9BK1dYc2R4dVlHUzk3YVJ6T1lpYzRuRTNhUmpINUtMOVc5NnpvRlVUUm9pYmpPZlN0cFgwcGx2YmkyYVB1RWIvbmJPZHZseVJGSVdBOUdDZlEycXhJWT0=",
          "paymentMethodData": {
            "account": "*********6552"
          }
        }
      ],
      "selectedPaymentMethod": "amcogate",
      "hasSavedPayway": "1",
      "hasUserSusc": "0",
      "newWorkflow": "1"
    },
    "status": "0",
    "msg": "OK"
  }

  list = ghGetChild(data, "response.list", [])
  for i = 0 to list.count() - 1
    item = list[i]
    if item.gateway = "rokugate" then
      m.listOffers.push({
        producttype: item.product_id,
        offer: root.offer
        workflow: item
      })
      exit for
    end if
  end for

  if m.workFlowCount = m.workFlowReturn then
    m.logger.debug("lista de offer validas", { data: m.listOffers })

    m.workFlowReturn = 0
    matchSubscription()
  end if

end sub

sub matchSubscription()
  m.purchaseSelected = invalid
  m.offerSelected = invalid

  m.logger.debug("Match subscription", { offers_cant: m.listOffers.Count() })

  for i = 0 to m.listOffers.Count() - 1
    item = m.listOffers[i]

    m.logger.debug("recorriendo listOffers", { item: item })

    for each id in m.purchases

      purchase = m.purchases[id]
      m.logger.debug("recorriendo purchases", { purchase: purchase })

      m.logger.debug("comparando code", { codeoffer: item.producttype, codePurchase: ghGetChild(purchase, "data.code", "") })

      if ghGetChild(purchase, "data.code", "") = item.producttype then

        statusSelected = ghGetChild(m.purchaseSelected, "status_subscription", "")

        m.logger.debug("status item", { status: ghGetChild(m.purchaseSelected, "status_subscription", "") })

        if ghGetChild(purchase, "status_subscription", "") = "Active" then
          m.logger.debug("active")

          ' si encuentro un active selecciono y salgo
          m.purchaseSelected = purchase
          m.offerSelected = item.offer
          exit for

        else if ghGetChild(purchase, "status_subscription", "") = "InGrace" then
          m.logger.debug("inGrace")

          ' selecciono si aun no se encontro un active
          if statusSelected <> "Active" then
            m.purchaseSelected = purchase
            m.offerSelected = item.offer
          end if

        else if ghGetChild(purchase, "status_subscription", "") = "OnHold" then
          m.logger.debug("onHold")

          ' selecciono si no se encontro un active o ingrace
          if statusSelected <> "Active" and statusSelected <> "InGrace" then
            m.purchaseSelected = purchase
            m.offerSelected = item.offer
          end if

        else if ghGetChild(purchase, "status_subscription", "") = "Canceled" then
          m.logger.debug("canceled")

          ' selecciono si no encontro active, ingrace o onhold
          if statusSelected <> "Active" and statusSelected <> "InGrace" and statusSelected <> "OnHold" then
            ' si ya hay algo seleccionado ( entonces es cancel ), verifico de seleccionar la mas reciente
            if m.purchaseSelected = invalid or ghGetChild(m.purchaseSelected, "data.expirationDate", "") < ghGetChild(purchase, "data.expirationDate", "")
              ' TODO logica rango de fechas con config de apa

              ' "expirationDate": "2025-04-25T18:50:21",
              expirationDate = ghGetChild(purchase, "data.expirationDate")

              inicio = ghGetChild(m.global, "alert_rokupay_subscription.cancelada.inicio", 60)
              final = ghGetChild(m.global, "alert_rokupay_subscription.cancelada.final", 63)

              diasTranscurridos = CalcularDiasRestantes(expirationDate)

              m.logger.debug("dias de expiracion", { fechaDeExpiracion: expirationDate, inicio: inicio, final: final, diasTranscurridos: diasTranscurridos })

              if diasTranscurridos >= inicio and diasTranscurridos <= final then
                m.logger.debug("dentro de los dias para mostrar aviso")

                m.purchaseSelected = purchase
                m.offerSelected = item.offer
              end if
            end if
          end if
        end if
      end if

      ' si se selecciono un active, ya puedo salir del bucle
      if ghGetChild(m.purchaseSelected, "status_subscription") = "Active" then
        exit for
      end if

    end for
  end for

  m.logger.debug("subscription found", { purchaseSelected: m.purchaseSelected, offerSelected: m.offerSelected })

  ' if m.offerSelected <> invalid then
  '   if ghGetChild(m.purchaseSelected, "status_subscription") = "InGrace" then
  '     ScrOnGrace("")
  '     return
  '   else if ghGetChild(m.purchaseSelected, "status_subscription") = "OnHold" then
  '     ScrSuspended("")
  '     return
  '   else if ghGetChild(m.purchaseSelected, "status_subscription") = "Canceled" then
  '     ScrCanceled("")
  '     return
  '   else if ghGetChild(m.purchaseSelected, "status_subscription") = "ERROR" then
  '     m.logger.error("Error en la suscripcion")
  '   end if
  ' end if

  m.top.cmdResult = {
    verify: true
    producttype: ghGetChild(m.offerSelected, "producttype")
    status_subscription: ghGetChild(m.purchaseSelected, "status_subscription")
    ' "status": "CANCEL"
  }
end sub

' PROCEDURES
' --------------------------
' sub doRefreshPurchases()
'   if m.top.debug then print ghLogHead();"doRefreshPurchases -- init."
'   doGetPurchases()
' end sub

' #1 --------------------------
sub doGetPurchases()
  if m.top.debug then print ghLogHead("RPay");"doGetPurchases -- init."
  m.purchasesInitialized = true
  RokuGetPurchases("RokuGetPurchasesReturn")
end sub
sub RokuGetPurchasesReturn(event)
  obj = event.getData()
  if m.top.debug then print ghLogHead("RPay");"RokuGetPurchasesReturn -- return."

  ' injectMokupPurchases(obj)
  loadPurchases(obj)
  ' dumpAllPurchases(obj) ' -- debug
  print "<RPay> **************************************"
  print "<RPay> ";m.purchases
  print "<RPay> **************************************"
  ' print "<RPay> ";m.purchases["c616a11e-04c7-11f0-83c2-caf47146c639"]["status"]
  ' print "<RPay> ";m.purchases["c616a11e-04c7-11f0-83c2-caf47146c639"]["check"]
  ' print "<RPay> ";m.purchases["c616a11e-04c7-11f0-83c2-caf47146c639"]["data"]
  ' print "<RPay> **************************************"
  doValidatePurchases()
end sub
sub loadPurchases(obj)
  cant = obj.getChildCount()

  m.logger.debug("loadPurchases", { cant: cant })

  for p = 0 to cant - 1
    child = obj.getChild(p)

    m.logger.debug("item purchase", child)

    m.purchases[child.purchaseId] = loadPurchase(child)
    ' dumpPurchase(child.purchaseId, m.purchases[child.purchaseId])
  end for
end sub
function loadPurchase(p)
  objP = {
    status_subscription: "?"
    status: "pendingCheck"
    check: {}
    data: {
      "code": p.code 'string	The product identifier, as entered in the Product Identifier field on the In-App Product page in the Developer Dashboard when the product was created.
      "cost": p.cost 'string	Localized cost of the item (prior to purchase) with local currency symbol
      "expirationDate": p.expirationDate 'string	The subscription expiration date (ISO 8601 format)
      "freeTrialQuantity": p.freeTrialQuantity 'integer	If the product has a free trial offer, the length of the trial period. For example, 1 for a 1-month free trial or 7 for a 7-day free trial.
      "freeTrialType": p.freeTrialType 'string	If the product has a free trial offer, the unit of time used by the trial ("Days" or "Months")
      "inDunning": p.inDunning 'string	A flag that indicates whether the purchased subscription is past due state because of an invalid method of payment. ' ' This flag is set to "true" if the subscription is in the dunning state. In this case, check the status field to determine whether to grant the customer access to content: ' ' If the status field is set to "Valid", the subscription is in a grace period and the viewer can access content. ' ' If the status field is set to "Invalid", the subscription is on hold and the viewer cannot access content. If the viewer adds a valid method of payment, the subscription will be automatically renewed and the status will become "Valid".
      "name": p.name 'string	The item name (this name will also be set as the description).
      "productType": p.productType 'string	The product type (ex. "MonthlySub")
      "purchaseChannel": p.purchaseChannel 'string	Indicates where the Roku Pay subscription purchase was made: ' web. Subscription was purchased from Roku.com (for example, through Instant Signup during the device activation). ' device. Subscription was purchased on the Roku device (through the on-device sign-up flow).
      "purchaseContext": p.purchaseContext 'string	Indicates how the subscription purchase was made: ' isu. Subscription was purchased via Instant Signup. ' iap. Subscription was purchased via an in-application purchase.
      "purchaseDate": p.purchaseDate 'string	The purchase date (ISO 8601 format)
      "purchaseId": p.purchaseId 'string	The transaction ID
      "qty": p.qty 'integer	The quantity purchased
      "renewalDate": p.renewalDate 'string	The subscription renewal date (ISO 8601 format)
      "status": p.status 'string	Indicates whether the purchase is for a current subscription ("Valid") or for a subscription that has been canceled, expired, or terminated ("Invalid")
      "trialCost": p.trialCost 'Integer	If the product uses introductory pricing, the discounted price.
      "trialQuantity": p.trialQuantity 'integer	If the product uses introductory pricing, the number of months the discounted pricing is applicable.
      "trialType ": p.trialType 'string	Set to
    }
  }

  m.logger.debug("loadpurchase", objP)

  return objP
end function


' #2 --------------------------
sub doValidatePurchases()
  cant = m.purchases.count()
  if m.top.debug then print ghLogHead("RPay");"RokuValidatePurchases -- init. cant=";cant
  m.purchasesPending = 0
  for each pur in m.purchases
    print "<Rpay> Validating -- ";pur
    ' m.purchasesPending += 1
    validatePurchase(pur)
  end for
  if m.top.debug then print ghLogHead("RPay");"RokuValidatePurchases -- all purchases validating."
end sub
sub validatePurchase(pur)
  if m.top.debug then print ghLogHead("RPay");"validatePurchase -- purchase ";pur

  api = ghCallApi("RokuValidatePurchase", "validatePurchaseOk", "ValidatePurchaseFail", false)
  api.transaction_id = pur
  api.control = "run"
end sub
sub validatePurchaseOk(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("RPay");"validatePurchaseOk -- purchase response=";data.response.OriginalTransactionId
  if data.response.count() > 0 then
    id = ghGetChild(data, "response.OriginalTransactionId")
    if id <> invalid then
      if m.purchases[id] <> invalid then
        m.purchases[id].status = "checked"
        m.purchases[id].check = data.response
        setStatusSubscription(id)
        ' m.purchases[id].status_subscription = getStatusSubscription(response.isEntitled, response.cancelled, m.purchases[id].data.expirationDate) ' response.expirationDate)
        if m.top.debug then print ghLogHead("RPay");"validatePurchaseOk -- LOADED ";id;" check into memory.";
        if m.top.debug then dumpPurchase(id)
      else
        if m.top.debug then print ghLogHead("RPay");"validatePurchaseOk -- ERROR -- No encontré subscripcion ";id
      end if
    else
      if m.top.debug then print ghLogHead("RPay");"validatePurchaseOk -- ERROR -- No encontré el id en las subscripción"
    end if
  end if
end sub
sub ValidatePurchaseFail(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("RPay");"ValidatePurchaseFail -- purchase ";data
  if data.id <> invalid then
    pur = m.purchases[data.id]
    if pur <> invalid then
      if m.top.debug then print ghLogHead("RPay");"ValidatePurchaseFail -- encontre el purchase = ";data.id
      pur.check = data.response
      pur.status = "failedCheck"
      if m.top.debug then print ghLogHead("RPay");"ValidatePurchaseFail -- PURCHASE ACTUALIZADO = ";m.purchases[data.id].status, m.purchases[data.id].check
    else
      if m.top.debug then print ghLogHead("RPay");"ValidatePurchaseFail -- NO ENCONTRE EL PURCHASE = ";data.id
    end if
  else
    if m.top.debug then print ghLogHead("RPay");"ValidatePurchaseFail -- NO ENCONTRE EL ID = ";data
  end if
end sub

' #2 --------------------------
sub doRecovery(data)
  if m.top.debug then print ghLogHead("RPay");"doRecovery -- init."
  RokuDoRecovery(data)
end sub

' #3 --------------------------
sub doVerifyOnePurchase(data)
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  print " "
  test("RPay CMD doVerifyOnePurchase START")
  if m.top.debug then print ghLogHead("RPay");"doVerifyOnePurchase -- data=";data

  id = data.id
  if id <> invalid then
    purchase = m.purchases[id]
    if purchase <> invalid then

      setStatusSubscription(id)

      m.top.cmdResult = { ' salida
        "cmd": "verifyPurchase"
        "status": "OK"
        "purchase": purchase
      }

    else
      print ghLogHead();"doVerifyOnePurchase -- ERROR: Purchase ";id;" not found."
      m.top.cmdResult = { ' salida
        "cmd": "verifyPurchase"
        "status": "ERROR"
        "msg": "ERROR: Purchase " + id + " not found."
      }
    end if
  else
    print ghLogHead();"doVerifyOnePurchase -- ERROR: id not found."
    m.top.cmdResult = { ' salida
      "cmd": "verifyPurchase"
      "status": "ERROR"
      "msg": "ERROR: Missing id parameter."
    }
  end if

  test("RPay CMD doVerifyOnePurchase END")
end sub

' UTILS
' --------------------------
sub setStatusSubscription(id)
  if m.top.debug then print ghLogHead();"setStatusSubscription -- Init"
  purchase = m.purchases[id]
  if purchase <> invalid then
    if purchase.status = "checked" then
      isEntitled = purchase.check.isEntitled
      cancelled = purchase.check.cancelled
      expirationDate = purchase.data.expirationDate
      if isEntitled <> invalid and cancelled <> invalid and expirationDate <> invalid then

        ' | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
        ' | Active               | true      | false     | Fecha posterior a la actual |
        ' | InGrace              | true      | false     | Fecha actual o anterior     |
        ' | OnHold               | false     | false     | Fecha actual o anterior     |
        ' | Canceled             | false     | true      | Fecha anterior a la actual  |

        if m.top.debug then print ghLogHead("RPay");"setStatusSubscription -- isEntitled=";isEntitled;", cancelled=";cancelled;", expirationDate=";expirationDate
        expiration = CreateObject("roDateTime")
        expiration.FromISO8601String(expirationDate)
        ahora = CreateObject("roDateTime")
        ' print "expiration ";type(expiration);" >> ";expiration.ToISOString()
        ' print "ahora ";type(ahora);" >> ";ahora.ToISOString()

        diferencia = expiration.AsSecondsLong() - ahora.AsSecondsLong()
        posterior = (diferencia > 0)
        ' print "Diferencia = ";diferencia;" posterior=";posterior

        res = "?"
        ' isEntitled = false ' para debug
        ' cancelled = true ' para debug
        ' posterior = false ' para debug
        if m.top.debug then print ghLogHead("RPay");"setStatusSubscription -- isEntitled=";isEntitled;" cancelled=";cancelled;" posterior=";posterior

        ' REGLAS DE ESTADO DE LA SUBSCRIPCION
        ' | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
        if isEntitled and not cancelled and posterior then
          ' | Active               | true      | false     | Fecha posterior a la actual |
          res = "Active"
        else if isEntitled and not cancelled and not posterior then
          ' | InGrace              | true      | false     | Fecha actual o anterior     |
          res = "InGrace"
        else if not isEntitled and not cancelled and not posterior then
          ' | OnHold               | false     | false     | Fecha actual o anterior     |
          res = "OnHold"
        else if not isEntitled and cancelled and not posterior then
          ' | Canceled             | false     | true      | Fecha anterior a la actual  |
          res = "Canceled"
        else
          res = "ERROR"
        end if
        if m.top.debug then print ghLogHead("RPay");"setStatusSubscription -- res=";res
        ' actualizo valor
        m.purchases[id].status_subscription = res
      else
        m.purchases[id].status_subscription = "ERROR"
        print ghLogHead();"setStatusSubscription -- ERROR= id=";id;" MISSING DATA!"
      end if
    else
      m.purchases[id].status_subscription = "ERROR"
      print ghLogHead();"setStatusSubscription -- ERROR= id=";id;" INVALID STATUS!"
    end if
  else
    print ghLogHead();"setStatusSubscription -- ERROR= id=";id;" NOT FOUND!"
  end if
end sub
' DUMPS
' --------------------------
function test(data) as boolean
  print ":::::::::::::::::::::::::::::::::::::"
  print data
  print ":::::::::::::::::::::::::::::::::::::"
  return true
end function
sub dumpAllPurchases(obj)
  print "<RPay> "
  print "<RPay> "
  print "<RPay>--RokuGetPurchasesReturn--------------------------"
  print "<RPay>* obj=";obj
  print "<RPay>--------------------------------------------------"
  print "<RPay>* status=";obj.status
  print "<RPay>* statusMessage= ";obj.statusMessage
  print "<RPay>--------------------------------------------------"
  cant = obj.getChildCount()
  print "<RPay>* cant=";cant
  print "<RPay>=================================================="
  for p = 0 to cant - 1
    print "<RPay>PURCHASE ";p
    child = obj.getChild(p)
    if m.top.debug then dumpPurchase(child)
    print "<RPay>--------------------------------------------------"
  end for
  print "<RPay>=================================================="
  print "<RPay> "
  print "<RPay> "
end sub
sub dumpPurchase(id)
  purchase = m.purchases[id]
  if purchase <> invalid then
    data = purchase.data
    check = purchase.check
    print " "
    print "----------------------------------------"
    print "PURCHASE ";id
    print "----------------------------------------"
    print purchase
    print "----------------------------------------"
    print "DATA:"
    print "-----"
    print purchase.data
    print "CHECK:"
    print "-----"
    print purchase.check
  else
    print "----------------------------------------"
    print "purchaseId ";id;" NOT FOUND"
  end if

  ' print "| code ";data.code 'string	The product identifier, as entered in the Product Identifier field on the In-App Product page in the Developer Dashboard when the product was created.
  ' print "| cost ";data.cost 'string	Localized cost of the item (prior to purchase) with local currency symbol
  ' print "| expirationDate ";data.expirationDate 'string	The subscription expiration date (ISO 8601 format)
  ' print "| freeTrialQuantity ";data.freeTrialQuantity 'integer	If the product has a free trial offer, the length of the trial period. For example, 1 for a 1-month free trial or 7 for a 7-day free trial.
  ' print "| freeTrialType ";data.freeTrialType 'string	If the product has a free trial offer, the unit of time used by the trial ("Days" or "Months")
  ' print "| inDunning ";data.inDunning 'string	A flag that indicates whether the purchased subscription is past due state because of an invalid method of payment.
  ' ' This flag is set to "true" if the subscription is in the dunning state. In this case, check the status field to determine whether to grant the customer access to content:
  ' ' If the status field is set to "Valid", the subscription is in a grace period and the viewer can access content.
  ' ' If the status field is set to "Invalid", the subscription is on hold and the viewer cannot access content. If the viewer adds a valid method of payment, the subscription will be automatically renewed and the status will become "Valid".
  ' print "| name ";data.name 'string	The item name (this name will also be set as the description).
  ' print "| productType ";data.productType 'string	The product type (ex. "MonthlySub")
  ' print "| purchaseChannel ";data.purchaseChannel 'string	Indicates where the Roku Pay subscription purchase was made:
  ' ' web. Subscription was purchased from Roku.com (for example, through Instant Signup during the device activation).
  ' ' device. Subscription was purchased on the Roku device (through the on-device sign-up flow).
  ' print "| purchaseContext ";data.purchaseContext 'string	Indicates how the subscription purchase was made:
  ' ' isu. Subscription was purchased via Instant Signup.
  ' ' iap. Subscription was purchased via an in-application purchase.
  ' print "| purchaseDate ";data.purchaseDate 'string	The purchase date (ISO 8601 format)
  ' print "| purchaseId ";data.purchaseId 'string	The transaction ID
  ' print "| qty ";data.qty 'integer	The quantity purchased
  ' print "| renewalDate ";data.renewalDate 'string	The subscription renewal date (ISO 8601 format)
  ' print "| status ";data.status 'string	Indicates whether the purchase is for a current subscription ("Valid") or for a subscription that has been canceled, expired, or terminated ("Invalid")
  ' print "| trialCost ";data.trialCost 'Integer	If the product uses introductory pricing, the discounted price.
  ' print "| trialQuantity ";data.trialQuantity 'integer	If the product uses introductory pricing, the number of months the discounted pricing is applicable.
  ' print "| trialType ";data.trialType 'string	Set to "months" for all products. All products using introductory pricing use "months" as the unit of time for the trial.
  ' print " "
  print "----------------------------------------"
end sub
' MOCKS
' --------------------------
sub injectMokupPurchases(obj)
  if m.top.debug then print ghLogHead("RPay");"injectMokupPurchases -- AR70004153"
  obj.appendChild(buildMockPurchase("111", "AR70004153"))
  if m.top.debug then print ghLogHead("RPay");"injectMokupPurchases -- GG70004153"
  obj.appendChild(buildMockPurchase("222", "GG70004153"))
end sub
function buildMockPurchase(id, code)
  pur = CreateObject("roSGNode", "ContentNode")

  pur.addField("code", "string", false)
  pur.addField("cost", "string", false)
  pur.addField("expirationDate", "string", false)
  pur.addField("freeTrialQuantity", "integer", false)
  pur.addField("freeTrialType", "string", false)
  pur.addField("inDunning", "string", false)
  pur.addField("name", "string", false)
  pur.addField("productType", "string", false)
  pur.addField("purchaseChannel", "string", false)
  pur.addField("purchaseContext", "string", false)
  pur.addField("purchaseDate", "string", false)
  pur.addField("purchaseId", "string", false)
  pur.addField("qty", "integer", false)
  pur.addField("renewalDate", "string", false)
  pur.addField("status", "string", false)
  pur.addField("trialCost", "integer", false)
  pur.addField("trialQuantity", "integer", false)
  pur.addField("trialType", "string", false)

  pur.setFields({
    "code": code, ' "AR70004153" ' ;p.code 'string	The product identifier, as entered in the Product Identifier field on the In-App Product page in the Developer Dashboard when the product was created.
    "cost": "$10" ' ;p.cost 'string	Localized cost of the item (prior to purchase) with local currency symbol
    "expirationDate": "2025-05-01" ' ;p.expirationDate 'string	The subscription expiration date (ISO 8601 format)
    "freeTrialQuantity": 0 ' ;p.freeTrialQuantity 'integer	If the product has a free trial offer, the length of the trial period. For example, 1 for a 1-month free trial or 7 for a 7-day free trial.
    "freeTrialType": "" ' ;p.freeTrialType 'string	If the product has a free trial offer, the unit of time used by the trial ("Days" or "Months")
    "inDunning": "Valid" ' ;p.inDunning 'string	A flag that indicates whether the purchased subscription is past due state because of an invalid method of payment.
    ' ' This flag is set to "true" if the subscription is in the dunning state. In this case, check the status field to determine whether to grant the customer access to content:
    ' ' If the status field is set to "Valid", the subscription is in a grace period and the viewer can access content.
    ' ' If the status field is set to "Invalid", the subscription is on hold and the viewer cannot access content. If the viewer adds a valid method of payment, the subscription will be automatically renewed and the status will become "Valid".
    "name": "MockSubs1" ' ;p.name 'string	The item name (this name will also be set as the description).
    "productType": "MockSubscription" ' ;p.productType 'string	The product type (ex. "MonthlySub")
    "purchaseChannel": "device" ' ;p.purchaseChannel 'string	Indicates where the Roku Pay subscription purchase was made:
    ' web. Subscription was purchased from Roku.com (for example, through Instant Signup during the device activation).
    ' device. Subscription was purchased on the Roku device (through the on-device sign-up flow).
    "purchaseContext": "iap" ' ;p.purchaseContext 'string	Indicates how the subscription purchase was made:
    ' isu. Subscription was purchased via Instant Signup.
    ' iap. Subscription was purchased via an in-application purchase.
    "purchaseDate": "" ' ;p.purchaseDate 'string	The purchase date (ISO 8601 format)
    "purchaseId": id ' ;p.purchaseId 'string	The transaction ID
    "qty": 1 ' ;p.qty 'integer	The quantity purchased
    "renewalDate": "" ' ;p.renewalDate 'string	The subscription renewal date (ISO 8601 format)
    "status": "" ' ;p.status 'string	Indicates whether the purchase is for a current subscription ("Valid") or for a subscription that has been canceled, expired, or terminated ("Invalid")
    "trialCost": 0 ' ;p.trialCost 'Integer	If the product uses introductory pricing, the discounted price.
    "trialQuantity": 0 ' ;p.trialQuantity 'integer	If the product uses introductory pricing, the number of months the discounted pricing is applicable.
    "trialType ": "" ' ;p.trialType 'string	Set to
  })
  ' if m.top.debug then print ghLogHead("RPay");"buildMockPurchase -- object=";pur
  return pur
end function


' --------------------------
' END OF FILE


' -------------------------------------------------------------
' # Features
' ## 1
' -------------------------------------------------------------
' Feature 01: Recuperación de compras durante Startup
' 	https://dlatvarg.atlassian.net/browse/AFE-1010
' "Recuperar las suscripciones realizadas en el canal Claro video realizadas con el medio de pago Roku Pay para validar el estatus en el que se encuentran y determinar el comportamiento que tendrá la aplicación"
' ESTO ES DURANTE EL STARTUP, NOS ASEGURARON QUE SÓLO ÍBAMOS A HACER CAMBIOS EN VCARD, PREGUNTÉ EN LA REUNIÓN.
' - Durante el startup : esto va a linkear con el usuario del dispositivo, aunque sean varios los usuarios de CV, y no tiene conexión para saber cuál es cual.
' - Qué se hace con esta información?
' - Posibilidad: global especificando si tiene o no compras con rokupay: DUDOSO, porque esto hay que cambiarlo si hace una compra.
' - Posibilidad: global con la lista de suscripçiones recibidas, para usar después en [Feature02] y en el momento de la compra ?????.
' >>> PEDIDO
' * objeto "store"
' * evento "onGetPurchases"
' * comando de Roku "getAllPurchases" : store.command = "getAllPurchases"
' <<< RESPUESTA
' * "store.purchases.status" tiene un valor igual a "1" -- response OK
' * "store.purchases" no tiene elementos hijos
' 	La cuenta de Roku no tiene suscripciones en el canal realizadas con Roku Pay.
' * "store.purchases" tiene al menos un elemento hijo
' 	La cuenta de Roku tiene al menos una suscripción en el canal realizadas con Roku Pay.
' 	Se debe validar el estatus de la suscripción
' * "store.purchases.child[#].status" tiene un valor igual a <status>
' * "store.purchases.child[#].inDunning" tiene un valor igual a <inDuning>
'     | estatus_suscripcion  | inDuning | status |
'     | Vigente              | true     | false  |
'     | En días de gracia    | true     | false  |
'     | En suspención        | false    | false  |
'     | Cancelada            | false    | true   |
' -------------------------------------------------------------
' ## 2
' -------------------------------------------------------------
' Feature 02: Validación de compras durante Startup
' 	https://dlatvarg.atlassian.net/browse/AFE-1011
' ESTO ES DURANTE EL STARTUP, NOS ASEGURARON QUE SÓLO ÍBAMOS A HACER CAMBIOS EN VCARD, PREGUNTÉ EN LA REUNIÓN.
' - transaction_ids obtenidos por medio del comando getAllPurchases [Feature01], para verificar el estado de las suscripciones realizadas por medio de Roku Pay.
' - De dónde sale el {partnerAPIKey} ??
' - Si hay transacciones en período de gracia o on-hold hay que proceder al manejo de ROKUPAY ??
' * Para cada uno de los transaction_ids:
' 	* GET al servicio "validate-transaction" por cada uno de los elementos de la lista
' 	  GET https://apipub.roku.com/listen/transaction-service.svc/validate-transaction/{partnerAPIKey}/{transaction_id}
' 	  header "accept" con un valor igual a "application/json"
'     | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
'     | Vigente              | true      | false     | Fecha posterior a la actual |
'     | En días de gracia    | true      | false     | Fecha actual o anterior     |
'     | On-Hold              | false     | false     | Fecha actual o anterior     |
'     | Cancelada            | false     | true      | Fecha anterior a la actual  |
' -------------------------------------------------------------
' ## 5
' -------------------------------------------------------------
' Feature 05: Reproducción de contenido - Usuario sin suscripciones Roku Pay
' 	https://dlatvarg.atlassian.net/browse/AFE-1014
' - INCONSISTENCIA: Suscripción vigente: Given no se encontraron suscripciones realizadas por medio de Roku Pay al ejecutar el comando "getAllPurchases"
'   >>> Si el usuario CV compró por ROKUPAY desde una cuenta de ROKU diferente, entonces no va a aparecer la compra AUNQUE LA TENGA.
' * Si no tiene suscripciones de ROKUPAY [Feature01][Feature02], se ejecuta el contenido.
' -------------------------------------------------------------
' ## 6
' -------------------------------------------------------------
' Feature 06: Reproducción de contenido - Usuario con suscripción Roku Pay vigente
' 	https://dlatvarg.atlassian.net/browse/AFE-1015
' "Reproducción de forma normal a usuarios con una suscripción vigente realizada por medio de Roku Pay".
' - INCONSISTENCIA: Suscripción vigente: Given no se encontraron suscripciones realizadas por medio de Roku Pay al ejecutar el comando "getAllPurchases"
'   >>> Si el usuario CV compró por ROKUPAY desde una cuenta de ROKU diferente, entonces no va a aparecer la compra AUNQUE LA TENGA.
' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está VIGENTE, entonces PLAY.
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???
' -------------------------------------------------------------
' ## 7
' -------------------------------------------------------------
' Feature 07: Reproducción de contenido - Usuario con suscripción Roku Pay en días de gracia
' 	https://dlatvarg.atlassian.net/browse/AFE-1016
' DUDA : PARA QUÉ hacemos una pantalla propia que tiene las mismas opciones que la de ROKU, si igualmente tiene que ir a la pantalla de ROKU >> tenemos dos pantallas iguales.
' "Comportamiento esperado en la reproducción de contenido para cuando existe una suscripción en días de gracia".
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???
' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está EN DIAS DE GRACIA.
' * En vCard de un contenido
' * >>> Se ejecuta el API "payway/v2/purchasebuttoninfo"
' 	<<<  "response.playButton.visible" de la respuesta tiene un valor igual a "1"
' 	Se muestra el botón de "Play".
' * Selecciona el botón "Play"
' 	"response.playButton.paymentmethod.gateway" <> "rokugate"
' 	Se muestra el contenido.
' * Se encuentra la transacción y la misma esta EN DIAS DE GRACIA
' 	PANTALLA SUSCRIPCION EN DIAS DE GRACIA!!!
' * Selecciona la opción "Más tarde"
' 	>>> Continúa con la reproducción.
' 	EXIT del flujo
' * Selecciona la opción "Regularizar pago"
' 	* request = {}
' 	* request.params.recoveryContext = "playback"
' 	* request.command = "DoRecovery"
' 	* store.observeField("requestStatus", "onRequestStatus")
' 	* store.request = request
' 	* Se muestra la PANTALLA NATIVA de Roku "Update payment information"
' 	>>> Contiene las opciones: "Update payment information" / "Continuar viendo"
' * Usuario selecciona opción continuar viendo en pantalla de Roku
' 	Se reproduce el contenido
' * Usuario selecciona la opción "Update payment information" y registra un medio de pago válido
'     	"store.requestStatus.status" es igual a "1"
' 	"store.result.recoveryStatus" es igual a "1"
' 	Hay que actualizar la información de [Feature01] y [Feature02]
' * Continúa con la reproducción.
' -------------------------------------------------------------
' ## 8
' -------------------------------------------------------------
' Feature 08: Reproducción de contenido - Usuario con suscripción Roku Pay suspendida
' 	https://dlatvarg.atlassian.net/browse/AFE-1017
' "Establecer el comportamiento esperado en la reproducción de contenido para cuando existe una suscripción en suspensión"
' - SI tiene una compra por ROKUPAY, y NO la encontramos en el "getAllPurchases" ???
' - PROBLEMA: (???) Según documentación ( https://developer.roku.com/es-ar/docs/developer-program/roku-pay/subscription-recovery/subscription-on-hold.md#dorecovery-api ), el "store.result.recoveryStatus" puede venir también en 2, lo que significaría que tiene varias suscripciones en recuperación ( a lo mejor, de otras aplicaciones? ), y devuelve un listado "recoveryProducts" con la lista de las suscripciones que todavía están en recuperación, por lo tanto, si la que mandamos nosotros no está entre esas, se puede entender que a pesar de tener otras en recuperación, recuperó la que nos importaba.
' - SI tiene una compra por ROKUPAY, y SI la encontramos en el "getAllPurchases", y la transacción está SUSPENDIDA.
' * En vCard de un contenido
' * >>> Se ejecuta el API "payway/v2/purchasebuttoninfo"
' 	<<<  "response.playButton.visible" de la respuesta tiene un valor igual a "1"
' 	Se muestra el botón de "Play".
' * Selecciona el botón "Play"
' 	"response.playButton.paymentmethod.gateway" <> "rokugate"
' 	Se muestra el contenido.
' * Se encuentra la transacción y la misma esta SUSPENDIDA
' 	PANTALLA SUSCRIPCION EN DIAS DE GRACIA!!!
' * Selecciona la opción "Más tarde"
' 	<<< VUELVE A LA VCARD
' 	EXIT del flujo
' * Selecciona la opción "Regularizar pago"
' 	* request = {}
' 	* request.params.recoveryContext = "playback"
' 	* request.command = "DoRecovery"
' 	* store.observeField("requestStatus", "onRequestStatus")
' 	* store.request = request
' 	* Se muestra la PANTALLA NATIVA de Roku "Update payment information"
' 	>>> Contiene las opciones: "Update payment information" / "Cerrar"
' * Usuario selecciona la opción "Update payment information" y registra un medio de pago válido
'     	"store.requestStatus.status" = "1"
' 	"store.result.recoveryStatus" = "1"
' 	Hay que actualizar la información de [Feature01] y [Feature02]
' 	>>> Continúa con la reproducción.
' 	EXIT del flujo
' * Usuario selecciona la opción  "Cerrar"
'     	"store.requestStatus.status" <> "1" (VERIFICAR)
' 	"store.result.recoveryStatus" <> "1" (VERIFICAR)
' 	Hay que actualizar la información de [Feature01] y [Feature02] (VERIFICAR)
' 	<<< VUELVE A LA VCARD
' 	EXIT del flujo
' -------------------------------------------------------------

' function getStatusSubscription(isEntitled, cancelled, expirationDate)
'   ' | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
'   ' | Active               | true      | false     | Fecha posterior a la actual |
'   ' | InGrace              | true      | false     | Fecha actual o anterior     |
'   ' | OnHold               | false     | false     | Fecha actual o anterior     |
'   ' | Canceled             | false     | true      | Fecha anterior a la actual  |

'   if m.top.debug then print ghLogHead("RPay");"getStatusSubscription -- isEntitled=";isEntitled;", cancelled=";cancelled;", expirationDate=";expirationDate
'   expiration = CreateObject("roDateTime")
'   expiration.FromISO8601String(expirationDate)
'   ahora = CreateObject("roDateTime")
'   ' print "expiration ";type(expiration);" >> ";expiration.ToISOString()
'   ' print "ahora ";type(ahora);" >> ";ahora.ToISOString()

'   diferencia = expiration.AsSecondsLong() - ahora.AsSecondsLong()
'   posterior = (diferencia > 0)
'   ' print "Diferencia = ";diferencia;" posterior=";posterior

'   res = "?"
'   ' isEntitled = false ' para debug
'   ' cancelled = true ' para debug
'   ' posterior = false ' para debug
'   if m.top.debug then print "isEntitled=";isEntitled;" cancelled=";cancelled;" posterior=";posterior

'   ' REGLAS DE ESTADO DE LA SUBSCRIPCION
'   ' | estatus_suscripcion  | isEntitle | cancelled | expirationDate              |
'   if isEntitled and not cancelled and posterior then
'     ' | Active               | true      | false     | Fecha posterior a la actual |
'     res = "Active"
'   else if isEntitled and not cancelled and not posterior then
'     ' | InGrace              | true      | false     | Fecha actual o anterior     |
'     res = "InGrace"
'   else if not isEntitled and not cancelled and not posterior then
'     ' | OnHold               | false     | false     | Fecha actual o anterior     |
'     res = "OnHold"
'   else if not isEntitled and cancelled and not posterior then
'     ' | Canceled             | false     | true      | Fecha anterior a la actual  |
'     res = "Canceled"
'   else
'     res = "ERROR !!"
'   end if

'   if m.top.debug then print ghLogHead("RPay");"getStatusSubscription -- res=";res
'   return res
' end function


' PArtnerID 3F19BD5F992DCE429491A77700FF75575642