' ------------------------------------------------
' FILES
' byGoose(2021) (<EMAIL>)
' ------------------------------------------------

' JSONs
' --------------------------------------------------------------
function loadJsonFile(url) as object
  result = {}
  asciiFile = ReadAsciiFile(url)
  if asciiFile.Len() > 0 then
    jsonFile = ParseJson(asciiFile)
    if jsonFile <> invalid then
      result = jsonFile
    end if
  end if
  ' print result
  return result
end function

' UTILS para URI
' --------------------------------------------------------------
function ghArray2Query(items, headChar = "?")
  res = headChar
  for each op in items.Items()
    if op.value = invalid then
      valor = ""
    else
      ' codificar caracteres especiales como # en las url
      valor = op.value.toStr().EncodeUriComponent()
    end if
    res += "&" + op.key + "=" + valor
  end for
  return res
end function
function ghQuery2Array(query)
  res = {}
  qInitPos = Instr(1, query, "?") + 1
  if qInitPos <> 0 then
    ori = Mid(query, qInitPos)
    parts = ghSplit(ori, "&")
    cant = parts.count()
    for p = 0 to cant - 1 ' por cada parametro
      d = ghSplit(parts[p], "=") ' partes del parametro
      if d.count() = 2 then ' lo normal
        res[d[0]] = d[1] ' hacia un assocarray
      else if d.count() = 1 then ' no mando valor
        res[d[0]] = ""
      end if
    end for
  end if
  return res
end function
function ghGetPath(url)
  return Mid(url, 1, Instr(1, url, "?") - 1)
end function
' --------------------------------------------------------------
