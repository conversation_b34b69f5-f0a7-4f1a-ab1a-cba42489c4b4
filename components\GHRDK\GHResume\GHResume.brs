function init()
  m.title = m.top.findNode("titleText")
  m.title.font = ghGetFont(38, "medium")
  m.title.text = ghTranslate("playing_modal_keepWatching_title_label", "Continua con tu reproduccion")

  m.buttonOK = m.top.findNode("reanudar")
  m.buttonOK.text = ghTranslate("playing_modal_option_button_fromNow", "REANUDAR")

  m.buttonCancel = m.top.findNode("inicio")
  m.buttonCancel.text = ghTranslate("playing_modal_option_button_fromTheStart", "DESDE EL INICIO")

  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "ButtonSelected")
  m.botonera.ObserveField("backSelected", "BackSelected")
  m.botonera.map = {
    "reanudar": { "up": invalid, "right": invalid, "down": "inicio", "left": invalid },
    "inicio": { "up": "reanudar", "right": invalid, "down": invalid, "left": invalid },
  }

  ghFocusJumpTo("botonera")
end function

sub updateFieldFocus() ' event
  if m.top.focus then
    ghFocusJumpTo("botonera")
  else
    m.top.close = true
  end if
end sub

sub ButtonSelected(event)
  child = event.getRoSGNode()

  m.top.selected = child.value
  m.top.close = true
end sub

sub BackSelected() ' event
  ' data = event.getData()
  m.top.selected = ""
  m.top.close = true
end sub