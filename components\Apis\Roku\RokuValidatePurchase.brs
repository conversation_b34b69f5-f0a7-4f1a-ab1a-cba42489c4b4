' ROKUPAY Validate Purchase
' ' url = "https://apipub.roku.com/listen/transaction-service.svc/validate-transaction/{partnerAPIKey}/{transaction_id}"

sub DataInit()
  ' m.top.debug = true

  m.api.method = "GET"
  url = ghGetChild(m.config, "rokupay.url", "url")
  partnerAPIKey = ghGetChild(m.config, "rokupay.partnerAPIKey", "partnerAPIKey")
  url = ghReplaceStr(url, "{partnerAPIKey}", partnerAPIKey)
  url = ghReplaceStr(url, "{transaction_id}", m.top.transaction_id)
  m.api.url = url
  m.api.headers.AddReplace("Accept", "application/json")

  if m.top.debug then
    print ghLogHead();"DataInit -- m.top.partnerAPIKey=[ ";m.top.partnerAPIKey;" ]"
    print ghLogHead();"DataInit -- m.top.transaction_id=[ ";m.top.transaction_id;" ]"
    print ghLogHead();"DataInit -- m.api.url=[ ";m.api.url;" ]"
    print ghLogHead();"DataInit -- m.api.headers=[ ";m.api.headers;" ]"
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";raw
  end if

  if res = invalid or type(res) = "String" or res.errors <> invalid then
    print "** ERROR **********************"
    print ghLogHead();"ProcessData -- ";ghGetChild(res, "errors")
    m.top.error = {
      "id": m.top.transaction_id
      "status": "ERROR"
      "response": m.body
    }
    return
  end if

  m.top.content = {
    "id": m.top.transaction_id
    "status": "TODO !!!!!"
    "response": m.body
  }
end sub

sub HandleCode4xx()
  if m.top.debug then
    print ghLogHead("API");"HandleCode4xx OVERLOADED Code = ";m.lastResponse.code
    print ghLogHead("API");"HandleCode4xx OVERLOADED Message = ";m.body
  end if
  m.top.error = {
    "id": m.top.transaction_id
    "status": "ERROR"
    "response": m.body
  }
end sub