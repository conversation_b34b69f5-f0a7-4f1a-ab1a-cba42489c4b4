function init()
  m.title = m.top.findNode("title")
  m.fondo = m.top.findNode("fondo")
  m.icon = m.top.findNode("icon")
end function

function itemContentChanged()
  m.fondo.setFields({
    width: m.top.width,
    height: 65,
    translation: [0, 0],
  })

  if ghGetChild(m.top.itemContent, "data.icon", "") = "" then
    m.icon.visible = false
  else
    m.icon.setFields({
      translation: [10, 15]
      visible: true
      uri: m.top.itemContent.data.icon
    })
  end if

  m.title.setFields({
    translation: [60, 20]
    text: ghGetChild(m.top.itemContent.data, "title")
    font: ghGetFont(21, "regular")
  })

  recalcItem(false)
end function

sub onFocus(event)
  recalcItem(event.getData())
end sub

sub recalcItem(status = false)
  m.fondo.visible = m.top.itemHasFocus ' prendo y apago el fondo

  if status then
    m.title.color = "#FFFFFF"
  else
    m.title.color = "#FFFFFF"
  end if
end sub
