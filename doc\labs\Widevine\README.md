# Widevine



## Índice

 [Mensajes de la interacción](Mensajes.md) 



## Links de información general



### Roku

Modelos: Hardware specifications 
https://developer.roku.com/es-ar/docs/specs/hardware.md

Streaming: Streaming specifications
https://developer.roku.com/es-ar/docs/specs/media/streaming-specifications.md

Streaming: DRM & content protection
https://developer.roku.com/es-ar/docs/specs/media/content-protection.md#widevine



### Otros

Recursos de ejemplo de BitMovin *(FUNCIONA!)*
video: https://cdn.bitmovin.com/content/assets/art-of-motion_drm/mpds/11331.mpd
drm-server: https://cwip-shaka-proxy.appspot.com/no_auth

Recursos de ejemplo de Shaka (hay que sacar datos via F12 en Browser). No se probaron
https://shaka-player-demo.appspot.com/demo/#audiolang=es-ES;textlang=es-ES;uilang=es-ES;panel=HOME;build=uncompiled





## Código

### Funciones de soporte

#### Testeo de Widevine

roDeviceInfo : https://developer.roku.com/es-ar/docs/references/brightscript/components/rodeviceinfo.md

ifDeviceInfo : https://developer.roku.com/es-ar/docs/references/brightscript/interfaces/ifdeviceinfo.md#getdrminfoex-as-object


Propuesta de detección:
```basic
' FILE: source\GHRDK\WideVineUtils.brs
function ghDrmHaveL1Widevine() as boolean
  res = false
  di = CreateObject("roDeviceInfo")
  diDrm = di.GetDrmInfoEx()
  secLevel = diDrm?.Widevine?.securitylevel
  if secLevel <> invalid then
    if Val(secLevel) >= 1 then
      res = true
    end if
  end if
  return res
end function
```



#### Headers para DRM

Se aconsejó desde Roku que, en el caso de tener que mandar headers para el servidor de DRM (`video.drmHttpAgent`), se envíe también un objeto de header (aunque sea vacío) en el objeto principal (`video`)



Genera un HttpAgent Vacio

```basic
' FILE: source\GHRDK\WideVineUtils.brs
function buildEmptyHttpAgent()
  httpAgent = createObject("roHttpAgent")
  ' m.video.setHttpAgent(httpAgent)
  return httpAgent
end function
```



Genera un HttpAgent para DRM con lectura de certificados y headers

```basic
' FILE: source\GHRDK\WideVineUtils.brs
function buildDrmHttpAgent(datos)

  ' creo el objeto -----
  drmHttpAgent = CreateObject("roHttpAgent")

  ' headers -----
  headers = {
    "custom-data": FormatJson(datos.customdata)
    "content-type": datos.contentType
  }
  drmHttpAgent.SetHeaders(headers)

  ' certificate -----
  serviceCertUrl = ghGetChild(datos, "serviceCert")
  if serviceCertUrl <> invalid then

    pathname = "tmp:/certificate.der"
    newXfer = CreateObject("roUrlTransfer")
    newXfer.SetCertificatesFile("common:/certs/ca-bundle.crt")
    newXfer.AddHeader("X-Roku-Reserved-Dev-Id", "")
    newXfer.InitClientCertificates()
    newXfer.SetUrl(serviceCertUrl)
    result = newXfer.GetToFile(pathname) ' baja a disco local
    
    if result = 200 ' descargo el certificado
      drmHttpAgent.setCertificatesFile(pathname) ' lo aplico
    end if
  end if

  return drmHttpAgent
end function
```



### Implementación VOD

Para la implementación se modificaron tres lugares distintos dentro del código.

1. La determinación del tipo de streaming de acuerdo al hardware (components\Pages\Player\VideoPlayerLogic.brs)
2. La obtención de la getmedia (components\Apis\GetMedia\GetMedia.brs).
3. La inyección de la información más los headers en el objeto `video`  (components\Pages\Player\Player.brs)



#### 1. Tipo de streming

En el archivo `VideoPlayerLogic.brs` se modificaron las funciones `OpenVideoPlayerNew` y `OpenVideoPlayer`, donde se agregó código para determinar el tipo de streaming a usar.

**!! código provisional**

```basic
streamTypes = ["smooth_streaming_ma", "smooth_streaming", "dashwv_ma", "dashwv"]
hayWideVine = ghDrmHaveL1Widevine()
if hayWideVine then 
    m.currentStreamType = 2
else
    m.currentStreamType = 0
end if
```



#### 2. Obtención de la GetMedia

Para la obtención de la getMedia, se trabajó inicialmente sobre dos funciones.

La función de configuración del llamado:

```basic
' FILE: components\Apis\GetMedia\GetMedia.brs
sub DataInit()
  ...
	"stream_type": m.top.streamType
	...
end sub
```

Y la función de procesamiento de la respuesta:
```basic
' FILE: components\Apis\GetMedia\GetMedia.brs
sub ProcessData(video, raw)
  ...
  ' SEPARACION SMOOTH O WIDEVINE
  ' ----------------------------
  ' ' tanto para dashwv como para dashwv_ma
  if Left(m.top.streamType, 6) = "dashwv" then ' DRM WIDEVINE
    item.streamFormat = "dash"
    ' DRM
    ' ----------------------------
    item.drmParams = {
      keySystem: "widevine"
      licenseServerURL: ghGetChild(video, "response.media.server_url", "")
    }
    ' TOKEN - saca el token del challenge para widevine
    ' ----------------------------
    tmpChallenge = video.response.media.challenge
    if tmpChallenge <> invalid then jsonChallenge = ParseJson(tmpChallenge)
    if jsonChallenge <> invalid then token = jsonChallenge.token
    if token = invalid then token = ""
    ' DATA + CUSTOM HEADER AND CERTIFICATE
    ' ------------------------------------
    item.widevineParams = {
      customData: {
        token: token 
        device_id: video.entry.device_id 
      }
      serviceCert: ghGetChild(video, "response.media.certificate_url", "")
      contentType: "application/octet-stream"
    }
  else ' DRM SMOOTH
  	...
  end if
  ...
end sub
```

Algunos puntos importantes:

- Tanto en el caso del `streamFormat` como del `keySystem`, los strings son códigos propios de roku.
- La propiedad `drmParams` es propia del objeto de `video` de roku.
- En el `video.response.media.challenge` vienen más datos que no se usan. por eso se extrae el `token`.
- La propiedad `widevineParams` **NO** es propia del objeto `video`, se generó para poder pasar los datos necesarios al lugar donde se los debe implementar. Tampoco es posible pasar a través de esta propiedad directamente los objetos `roHttpAgent`, por lo que se pasan todos los datos para que se puedan construir en el lugar de su uso.
- El `licenseServerURL` es el servidor de DRM real, en tanto la `response.media.certificate_ur` es simplemente una dirección donde obtener el archivo binario del certificado, para alimentarlo en el header del llamado al `licenseServerURL`

#### 3. Inyección de información en `video`

En el momento de asignar el content al nodo de video, previo a hacerlo, se chequea si llegan parámetros de Widevine (los que se generaron en la `Getmedia`), y si llegan, se configuran los headers para el nodo `video` y también para el `drmHttpAgent`.

```basic
' FILE components\Pages\Player\Player.brs
if content.widevineParams <> invalid then
  m.video.setHttpAgent(buildEmptyHttpAgent())
  m.video.drmHttpAgent = buildDrmHttpAgent(content.widevineParams)
else
  print ghLogHead();"SetVideoContent -- NO ENCONTRE LOS widevineParams"
end if
```







