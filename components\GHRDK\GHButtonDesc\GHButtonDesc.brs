' GHButton
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>
' -----------------------------
function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes
  m.background = m.top.findNode("background")
  m.border = m.top.findNode("border")
  m.border.uri = ghGetImageByMode("focus01.9.png")
  m.bLabel = m.top.findNode("bLabel")
  m.bLabel.font = ghGetComponentFont("GHButtonDescBLabel")
  m.label = m.top.findNode("label")
  m.label.font = ghGetComponentFont("GHButtonDesc")
  m.label.setFocus(true)
  m.ico = m.top.findNode("ico")
  ' recalcLabelSizeAndPadding()
  ' Refresh()
end function

' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function
sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.setFocus(true)
  end if
  Refresh()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub

' FIELDS
' -----------------------------
sub updateFieldText()
  if m.top.text <> invalid then
    m.label.text = m.top.text
  end if
  if m.top.buttonText <> invalid then
    m.bLabel.text = m.top.buttonText
  end if
  Refresh()
end sub
sub updateFieldWrap()
  Refresh()
end sub
sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then
    m.background.width = m.top.width
    recalcLabelSizeAndPadding()
  end if
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then
    m.background.height = m.top.height
    recalcLabelSizeAndPadding()
  end if
end sub
sub updateFieldHorizAlign()
  if m.top.horizAlign <> invalid then m.label.horizAlign = m.top.horizAlign
end sub
sub updateFieldVertAlign()
  if m.top.vertAlign <> invalid then m.label.vertAlign = m.top.vertAlign
end sub
sub updateFieldBackColor()
  if m.top.backColor <> invalid then m.background.color = m.top.backColor
end sub
sub updateFieldColor()
  if m.top.color <> invalid then m.label.color = m.top.color
end sub
sub updateFieldFocusMap()
  if m.top.focusMap <> invalid then m.border.uri = m.top.focusMap
end sub
sub updateFieldPadding()
  if m.top.padding <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldBorder()
  if m.top.border <> invalid then recalcLabelSizeAndPadding()
end sub
' utils ------------
sub Refresh()
  recalcLabelSizeAndPadding()
  recalcColors()
end sub
sub recalcLabelSizeAndPadding()
  width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))
  paddingFocus = ghXtoAbstract(val(m.top.focusPadding))
  padding = ghXtoAbstract(val(m.top.padding))
  paddingFocusSize = paddingFocus * 2
  paddingSize = padding * 2

  ' borde select
  m.border.translation = "[0,0]" ' el unico que va en la misma posicion
  m.border.height = height
  m.border.width = width
  ' fondo del boton
  m.background.translation = ghVal2Trans(paddingFocus, paddingFocus)
  m.background.height = height - paddingFocusSize
  m.background.width = width - paddingFocusSize
  ' icono del boton / ! esta adentro del fondo
  x = ((m.background.width - m.ico.width) / 2)
  y = ((m.background.height - m.ico.height) / 2)
  m.ico.translation = [x, y]
  ' textto abajo
  m.label.translation = ghVal2Trans(paddingFocus + padding, height)
  m.label.width = width - paddingFocusSize - paddingSize

  if m.top.wrap then
    m.label.wrap = m.top.wrap
    m.label.vertAlign = "top"
    m.label.height = invalid
    m.label.lineSpacing = 0
  end if

  ' boton con precio o texto, sin icono
  if m.top.mode <> "icon" then
    m.ico.visible = false
    m.bLabel.visible = true
    m.bLabel.translation = [0, 0]
    m.bLabel.width = m.background.width
    m.bLabel.height = m.background.height
    m.bLabel.vertAlign = "center"
    m.bLabel.horizAlign = "center"
    if m.top.wrap then
      m.bLabel.wrap = m.top.wrap
      m.bLabel.lineSpacing = 0
    end if
  else
    m.ico.visible = true
    m.bLabel.visible = false
  end if

  if m.top.debug then
    print ghLogHead();"> recalcLabelSizeAndPadding -- ";m.label.text
    print ghLogHead();"border> ";m.border.translation, m.border.width, m.border.height
    print ghLogHead();"background> ";m.background.translation, m.background.width, m.background.height
    print ghLogHead();"label> ";m.label.translation, m.label.width, m.label.height
  end if

end sub
sub recalcColors()
  if m.top.focus then
    m.border.visible = true
    m.background.color = m.top.selBackColor
    m.label.color = m.top.selColor
    m.bLabel.color = m.top.selColor
  else
    m.border.visible = false
    m.background.color = m.top.backColor
    m.label.color = m.top.color
    m.bLabel.color = m.top.color
  end if
end sub
sub drawIco()
  m.ico.uri = m.top.icon
  m.ico.blendColor = m.top.iconColor
end sub
' END FILE ------------------
