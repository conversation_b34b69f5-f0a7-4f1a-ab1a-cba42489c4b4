<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLivePanelChannels" extends="Group">
  <script type="text/brightscript" uri="GHLivePanelChannels.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="focus" type="boolean" value="false" onChange="onFocusChange" alwaysNotify="true" />
    <field id="cmd" type="assocarray" alwaysNotify="true" />

    <field id="jumpTo" type="integer" onChange="handleJumpTo" alwaysNotify="true" />
    <field id="channels" type="array" onChange="onDataUpdate" />
    <field id="selected" type="assocarray" alwaysNotify="true" />

    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="panelRight">
      <Label id="title" focusable="false" translation="[0,50]" width="1280" height="48" text="*" />
      <MarkupGrid id="listChannels" imageWellBitmapUri="" itemComponentName="ItemPolymorphic" translation="[ 60, 160 ]" numColumns="4" numRows="5" itemSize="[ 274, 154 ]" itemSpacing="[ 20, 20 ]" />
    </Rectangle>
  </children>

</component>
