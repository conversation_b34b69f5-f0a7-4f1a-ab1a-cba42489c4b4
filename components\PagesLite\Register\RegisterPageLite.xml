<?xml version="1.0" encoding="utf-8" ?>

<component name="RegisterPageLite" extends="Page">

  <script type="text/brightscript" uri="RegisterPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <interface>
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />
    <Label id="title" focusable="false" translation="[416,64]" width="186" height="48" text="Regístrate" />
    <Label id="descrip" focusable="false" translation="[416,136]" width="387" height="32" text="¿Cuál es tu correo electrónico?" />
    <!-- Botonera -->
    <GHButtonGroup id="botonera" layout="map">
      <GHInput id="user" placeholder="Correo electrónico" translation="[400,200]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="false" width="504" height="72" title="Regístrate" titleColor="0xFFFFFF" message="¿Cuál es tu correo electrónico?" messageColor="0xFFFFFF"/>
      <GHInput id="pass" placeholder="Contraseña" translation="[400,280]" color="0xFFFFFF" selColor="#7F8086" focusColor="0xFFFFFF" placeholdercolor="#7F8086" password="true" width="504" height="72" title="Elige una contraseña" titleColor="0xFFFFFF" message="Tu contraseña debe incluir: Mayúsculas y minúsculas, Al menos un número, 8 o más caracteres" messageColor="0xFFFFFF"/>
      <GHCheckBox id="check" text="Acepto los Términos y Condiciones" width="248" height="72" translation="[400,352]" backColor="#212224"/>
      <GHButton value="TerminosPage" id="terminos" text="(!)Terminos" translation="[656,352]" width="248" height="72" color="0xFFFFFF" selColor="0xFFFFFF" selBackColor="#212224" focusColor="0xFFFFFF" backColor="#212224" />
      <GHButton value="registracion" id="register" text="SIGUIENTE" translation="[400,424]" width="504" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
      <GHButton value="Back" id="cancel" text="CANCELAR" translation="[400,487.33]" width="504" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" />
      <GHButton value="LoginPage" id="toLogin" text="¿Ya tienes cuenta? Inicia sesión" translation="[656,576]" width="248" height="72" color="0xFFFFFF" selColor="0xFFFFFF" selBackColor="#212224" focusColor="0xFFFFFF" backColor="#212224" />
    </GHButtonGroup>
    <!-- PopUps -->
    <GHError id="error" />
    <GHLoading id="loading" visible="false"/>
    <!-- <Label id="marca" text="LITE VERSION" translation="[0,0]" color="0x00ff00" /> -->
  </children>

</component>
