<?xml version="1.0" encoding="UTF-8"?>
<!-- Goose -->


<component name="Page" extends="Group">

  <script type="text/brightscript" uri="Page.brs" />

  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/FocusHandler.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Screen.brs" />

  <interface>
    <!-- internal interface -->
    <field id="debug" type="boolean" value="false" />

    <!-- events -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="wasShown" type="boolean" onChange="updateFieldShown" alwaysNotify="true"/>
    <field id="wasClosed" type="boolean" onChange="updateFieldClosed" alwaysNotify="true"/>

    <!-- en capilla -->
    <!-- <field id="initialFocusedNode" type="node" /> -->
    <field id="saveState" type="boolean" value="true" />

    <!-- TEST -->

    <!-- @Public : Main field for setting content         content tree is specific to each view and is handled by view itself -->
    <field id="content" type="node" />


  </interface>

  <children>

  </children>
</component>