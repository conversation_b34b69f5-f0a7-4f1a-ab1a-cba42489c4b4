<?xml version="1.0" encoding="UTF-8"?>

<component name="IntroCardLite" extends="Page">
  <script type="text/brightscript" uri="IntroCardLite.brs" />

  <interface>
    <field id="value" type="string" alwaysNotify="true"/>
  </interface>

  <children>
    <GHButtonGroup id="botonera" layout="childs" orientation="horizontal" exitUp="true" exitDown="true" exitLeft="true" exitRight="true">
      <GHButton id="btnNext" translation="[40,550]" focusMap="" width= "250" height= "70" selColor="#FFFFFF" selBackColor="#981C15" backcolor="#981C15" color="0xFFFFFF" text="OMITIR INTRO" value="btnNext" />
    </GHButtonGroup>
  </children>
</component>
