' IsLoggedInd
' -----------------------

sub DataInit()
  ' m.top.debug = true
  try
    m.api.method = "GET"
    m.api.url = m.config.mfwk.host + "/services/user/isloggedin"
    m.api.query.Append({
      ' hardcode
      "includpaywayprofile": "1",
      ' registry
      "user_id": ghGetRegistry("user_id", "user")
    })

    'hardco -----
    ' ghSetRegistry("region", "mexico")
    ' m.top.ipaddr = "***************,44908"
    'hardco -----

    ' iptelmex
    if m.top.ipaddr <> "" then
      region = ghGetRegistry("region")
      ipaddr = m.top.ipaddr

      ipAddrArr = ghSplit(ipaddr, ",") ' fix ipaddr con puerto
      if ipAddrArr.count() > 1 then ipaddr = ipAddrArr[0]
      ipAddrArr = ghSplit(ipaddr, ":") ' por las dudas
      if ipAddrArr.count() > 1 then ipaddr = ipAddrArr[0]
      m.api.query.Append({
        ipaddr: ipaddr
        "region": region
      })
      if m.top.debug then print ghLogHead();"DataInit -- IpTelmex ";ipaddr, region
    end if

  catch error
    ghErrorDumpToLog("user/isloggedin", {}, error)
    ghErrorDump(error)
    m.top.content = ghErrorGetJson(error)
    m.top.content.addReplace("isLoggedIn", false)
  end try
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  result = {}
  isLogged = false

  if m.top.debug then
    print "****************************************"
    print "IsLoggedIn"
    print "****************************************"
    print res.entry
    print res.response
    print "****************************************"
    print "****************************************"
  end if

  if res = invalid then
    result.addReplace("isLoggedIn", isLogged)
    m.top.content = result
    return
  end if

  response = res.response

  if type(response) = "roAssociativeArray" then
    if m.top.debug then
      print ghLogHead();"ProcessData -- ";response
      print ghLogHead();"ProcessData is_user_logged_in=";type(response.is_user_logged_in);"[";res.response.is_user_logged_in;"]"
    end if
    if type(response.is_user_logged_in) = "Integer" then
      if response.is_user_logged_in <> 0 then
        isLogged = true

        ' Levanto los datos del usuario
        ' user
        ghSetRegistry("isLoggedIn", "true")
        ' if ghGetRegistry("region") <> response.region then
        ghSetRegistry("region", response.region)

        ' subRegion
        subRegion = response.subregion
        if subRegion = invalid then subRegion = ""
        ghSetRegistry("subregion", subRegion, "user")

        ' end if
        ' ----------------------------------------------------
        ghSetRegistry("user_id", response.user_id, "user")
        ghSetRegistry("parent_id", ghGetChild(response,"parent_id"), "user")
        ghSetRegistry("session_userhash", response.session_userhash, "user")
        ghSetRegistry("lasttouch_seen", ghGetChild(response, "lasttouch.seen", ""), "user")
        ghSetRegistry("lasttouch_favorited", ghGetChild(response, "lasttouch.favorited", ""), "user")
        ghSetRegistry("user_token", response.user_token, "user")
        ghSetRegistry("user_session", response.user_session, "user")
        ' ghSetRegistry("language", response.language, "user")
        ghSetRegistry("username", response.username, "user")
        ghSetRegistry("firstname", response.firstname, "user")
        ghSetRegistry("lastname", response.lastname, "user")
        ghSetRegistry("email", response.email, "user")
        ghSetRegistry("region", response.region, "user")
        ghSetRegistry("country_code", response.country_code, "user")
        ' ghSetRegistry("HKS", response.session_stringvalue)
        ' ----------------------------------------------------

        ' print "++[isloggedin]+++++++++++++++++++++++++++"
        ' print ghListSectionData()
        ' print ghListSectionData("user")
        ' print "+++++++++++++++++++++++++++++++++++++++++"
      end if
    end if
  end if
  ' REGISTRY
  if isLogged then ghSetRegistry("isLoggedIn", "true") else ghSetRegistry("isLoggedIn", "false")
  m.global.paywayProfile = ghGetChild(response, "paywayProfile", {}) ' para Youbora
  ' SUPERHIGHLIGHT
  m.global.superhighlight = ghGetChild(response, "superhighlight", [])
  ' LASTTOUCH
  updateGlobalArray("lasttouch", response.lasttouch)

  ' DEVUELVO
  result.addReplace("isLoggedIn", isLogged)
  if m.top.debug then print "ACCEPTED-TERMS (isloggedin) >> "ghGetChild(response, "accepted_terms")
  result.addReplace("accepted_terms", ghGetChild(response, "accepted_terms", 1))
  if m.top.debug then print "result=";result
  m.top.content = result
end sub
