' LevelLite
' https://app.swaggerhub.com/apis/ClaroVideo/CMS_Level/1.0.1#/CMS%20Level/get_services_cms_v2_level
' https://app.swaggerhub.com/apis-docs/ClaroVideo/CMS_Level/1.0.1

sub DataInit()
  m.top.debug = false
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/cms/v2/level"

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  m.api.query.delete("user_id") ' sin user_id
  m.api.query.delete("user_token") ' sin user_id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") }) ' by CVRKS-1008

  typeNav = ghGetRegistry("nav")
  m.api.query.Append({
    "node": m.top.node,
    "region": ghGetRegistry("region"),
    "type": typeNav
    "module_version": "v2"
  })
  m.logger.debug("DataInit api", { api: m.api, params: m.api.query })
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  if res.errors <> invalid then
    m.logger.error("ProcessData -- ERROR(!)")
    if res.errors <> invalid then
      m.top.error = { "error": res.errors }
    else
      m.top.error = { "error": "ERROR GENERICO", "raw": raw }
    end if
    return
  end if

  m.logger.debug("ProcessData -----v ")
  result = []
  cintas = ghGetChild(res, "data.modules")
  m.logger.debug("ProcessData -- cintas= ", { cintas: cintas })

  tipoNivel = ghGetChild(cintas, "#0.type")
  if tipoNivel <> "listadoinfinito" then
    tipoNivel = "cintas"
  end if

  ' ----------------------------------------------
  ' si hay 1 sola cinta y la cinta dice type="listadoinfinito"
  ' ---  > preparar datos para el listado infinito
  '
  ' tipoNivel="listadoinfinito"
  ' if comp.name = "header" then
  '   obCinta.title = ghDecodeHTML(comp.properties.large)
  '   obCinta.tipo = "listadoinfinito"
  ' else if comp.name = "carrousel" and comp.type = "Listadoinfinito" then
  '   ' aca va la magia
  '   obCinta.data = comp
  ' end if

  ' sino ------------------------------------------
  for each cinta in cintas
    obCinta = CreateObject("roSGNode", "GHContent")
    obCinta.id = cinta.name
    obCinta.title = ""
    ghUtils_ForceSetFields(obCinta, {
      visible: true
    })

    comps = ghGetChild(cinta, "components.component")
    for each comp in comps
      if comp.name = "header" then
        obCinta.title = ghDecodeHTML(comp.properties.large)
      else if comp.name = "carrousel" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
        })

      else if comp.name = "plans_offer__v2" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
          longOkType: "planSelector"
        })
      else if comp.name = "premiumimage__v2" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
        })

        print "TEST OFFER "; comp
      end if
    end for

    if obCinta.data <> invalid

      ' ------------------------------
      ' children vacios
      for v = 1 to 5
        ob = CreateObject("roSGNode", "GHContent")
        ob.data = { ' datos por defecto
          "rowtype": obCinta.data.type,
          "order": v + 1,
          "properties": ghGetChild(obCinta, "data.properties")
        }
        obCinta.appendChild(ob)
      end for
      ' result.appendChild(obCinta)
      result.push(obCinta)
      ' ------------------------------

      if m.top.debug then print ":: > ";cinta.name, "(";result.Count();")"
      if result.Count() = 1 then

        navCinta = injectNavRow()
        if navCinta <> invalid
          m.logger.debug("ProcessData -- navCinta", { navCinta: navCinta })
          result.push(navCinta)
        end if

      end if
    end if
  end for
  m.logger.info("ProcessData -----^ ")

  ' aca cierro todo
  m.top.content = {
    tipo: tipoNivel
    cintas: result
  }
end sub

' #########################################################
' NAV
' #########################################################
function injectNavRow()
  m.logger.debug("injectNavRow", { node: m.top.node })
  navCinta = invalid

  childs = getNodeChilds(m.top.node)

  m.logger.debug("La magia del NAV ", { count: childs.Count() })

  if childs.Count() > 0 then
    navCinta = CreateObject("roSGNode", "GHContent")
    navCinta.id = "navchilds"
    ghUtils_ForceSetFields(navCinta, {
      visible: true
      "type": "navchilds"
      "oldType": "navchilds"
    })
    navCinta.title = ""
    navCinta.data = {
      "name": "navData",
      "properties": { "byuser": "false" },
      "type": "navchilds"
    }
    ' ------------------------------
    for v = 0 to childs.Count() - 1
      nav = childs[v]
      ob = CreateObject("roSGNode", "GHContent")
      ob.data = { ' datos por defecto
        "rowtype": "navchilds",
        "order": v + 1,
        "title": nav.text
        "data": nav
      }
      navCinta.appendChild(ob)
    end for
    for v = 1 to 5
      m.logger.debug("cinta => ", { cinta: navCinta.getChild(v) })
    end for
  end if
  return navCinta
end function

function getNodeChilds(code)
  childs = []

  nav = m.global.nav
  for each item in nav.childs
    if item.code = code and item.childs <> invalid then
      childs = item.childs
      if m.top.debug print item.code;" >  ";childs.Count()
    end if
  end for

  return childs
end function