# Certificación



Otros links

[Anotaciones iniciales sobre certificación](./notasIniciales.md) - iniciales de reunión

[Dudas de Roku](dudasRoku.md)

[Status de certificacion](statusRoku.md)



## Puntos de acción

- [ ] `<PERSON>` Revisar tema de ingreso a publicación de Roku (hay procesos de verificación que se disparan desde ahí: [Static Analysis Tool][statanalysis])
- [ ] `<PERSON>(?)` Hay que revisar si la parte del POC no está utilizando [Deprecated APIs][deprecated].
- [x] `<PERSON>` Conseguir el famoso dispositivo que no tenemos que pero que hay que testear [(modelos)][models]. Inicialmente iría a los QAs.
- [x] `David` Información, cuál era el modelo que teníamos que conseguir?
- [x] `<PERSON>` Revisar si el formato de la búsqueda directa es el mismo que el del [feed][feed].
- [x] `<PERSON>` Mover la documentación de los componentes y funciones de los directorios a el directorio `doc` para que no salte en el SA.
- [x] `Julian` Ver con Elena de reemplazar los recursos que están fuera de tamaño.





## [Criterios de ceritificación][criteria]

> **Testing with unpublished channels** When creating a new channel, an "access code" will be provided to preview the channel. This link can be used to view the most recent version of the channel that has been uploaded, even if that version has not yet been certified and published. (The channel updates will not be reviewed for certification until explicitly submitting the package for review.

- Hay que tener acceso a estos menús lo más rápido posible para poder subir paquetes, aunque sean previos, ellos ni los revisan, pero se tiene acceso a las herramientas de certificación.



>  Developers are expected to use the certification criteria and the list of certification tests as tools to guide internal quality assurance testing before submitting a channel to Roku for review.

- Hasta que no pase los testeos de las herramientas que piden, no presentar.



> **Publishing your channel** Once you’ve finished QA testing your channel and have packaged it, you can begin the certification process by running Static Analysis testing on your channel via the “My Channels” section of the Developer Dashboard. The Static Analysis tool checks the structure and syntax of your channel's code for common problems related to certification requirements..
>
> The Static Analysis tool lists any errors that must be resolved before the channel can be scheduled for publishing. For channels that can be self-published, once your channel has passed Static Analysis testing, you can schedule on which date to begin the publishing process.

> **Updating an existing channel** If you update the implementation code for an existing channel, it must be re-certified and re-published. Changing the content that the channel streams does not require re-certification and re-publishing.



- ~~Hay una forma de publicar contenido sin hacer una app. Algo que se llama *content feed*. Ver en  [Creating the content feed][feed]. **Hay que revisar si este formato no es el mismo que para el buscador.**~~





## Capítulos de la certificación

1. [Advertising](./Advertising) -- Propaganda
2. [Purchases](./Purchases ) -- Manejo de compras
3. [Performance](./Performance ) -- Temas de performance
4. [Channel Operation](./Operation ) -- operación del canal
5. [Deep Linking](./Linking ) -- Deep linking
6. [UI and Graphics](./Graphics ) -- Interfaz y gráficos



## Glosario

- AVOD : advertising-based video on demand
- SA: static analisis



## Bibliografía

### Info

Certification criteria
https://developer.roku.com/es-ar/docs/developer-program/certification/certification.md 

Certification tests
https://developer.roku.com/es-ar/docs/developer-program/certification/certification-testing.md 

Current Roku models
https://developer.roku.com/es-ar/docs/specs/hardware.md#TheRokuChannelDeveloperProgram-RokuModelsRokuModelsandFeatures

Creating the content feed
https://developer.roku.com/es-ar/docs/direct-publisher/getting-started/content-feed.md

### Reference

Deprecated APIs
https://developer.roku.com/es-ar/docs/references/deprecated-apis.md

### Tools

Static Analysis Tool
https://developer.roku.com/es-ar/docs/developer-program/dev-tools/static-analysis-tool/static-analysis-tool.md

Automated channel testing overview
https://developer.roku.com/es-ar/docs/developer-program/dev-tools/automated-channel-testing/automated-testing-overview.md

Packaging Roku channels
https://developer.roku.com/es-ar/docs/developer-program/publishing/packaging-channels.md



---

[criteria]: https://developer.roku.com/es-ar/docs/developer-program/certification/certification.md
[tests]: https://developer.roku.com/es-ar/docs/developer-program/certification/certification-testing.md
[deprecated]: https://developer.roku.com/es-ar/docs/references/deprecated-apis.md
[statanalysis]: https://developer.roku.com/es-ar/docs/developer-program/dev-tools/static-analysis-tool/static-analysis-tool.md
[models]: https://developer.roku.com/es-ar/docs/specs/hardware.md#TheRokuChannelDeveloperProgram-RokuModelsRokuModelsandFeatures
[feed]: https://developer.roku.com/es-ar/docs/direct-publisher/getting-started/content-feed.md