<?xml version="1.0" encoding="utf-8" ?>

<component name="GHJoystick" extends="Group">
  <script type="text/brightscript" uri="GHJoystick.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>

    <!-- interfaz de entrada -->

    <field id="width" type="integer" value="20" />
    <field id="height" type="integer" value="20" />
    <field id="offset" type="integer" value="20" />

    <field id="title" type="string" onChange="onTitleChange" />

    <!-- interfaz de salida -->
    <field id="action" type="string" alwaysNotify="true" />

    <!-- interno -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />

  </interface>

  <children>

    <!-- reales -->
    <Rectangle id="pBackground" />
    <Label id="titleChannel"/>

  </children>

</component>