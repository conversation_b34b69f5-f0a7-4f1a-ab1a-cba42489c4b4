<?xml version="1.0" encoding="utf-8" ?>

<component name="PlayerADLite" extends="Page">
  <script type="text/brightscript" uri="PlayerAD.brs"/>

  <!-- WIDEVINE -->
  <script type="text/brightscript" uri="pkg:/source/GHRDK/WideVineUtils.brs" />

  <script type="text/brightscript" uri="VideoPlayerLogic.brs" />
  <script type="text/brightscript" uri="TrackerNew.brs" />
  <script type="text/brightscript" uri="ContentPlaybackChecks.brs" />
  <script type="text/brightscript" uri="StateMachinePlayer.brs" />
  <script type="text/brightscript" uri="Actions.brs" />

  <interface>
    <!-- state - Same as Task states. -->
    <field id="state" type="string" alwaysNotify="true"/>
    <!-- control - "play" starts content playback -->
    <field id="control" type="string" alwaysNotify="true" onChange="controlChanged"/>
    <field id="useAd" type="boolean" value="false" alwaysNotify="true" />

    <field id="info" type="assocarray" onChange="handleInfo" />
    <field id="seasons" type="assocarray" onChange="handleSeasons" />
    <field id="languages" type="assocarray" onChange="handleLanguages" />

    <!-- funcion en archivo Tracker.brs -->
    <field id="trackInfo" type="assocarray" onChange="onTrackInfoChange" />

    <!-- avisa a vcard para que recargue la home -->
    <field id="favorited" type="boolean" alwaysNotify="true" />

    <field id="selected" type="assocarray" />
    <field id="cerrarPlayer" type="boolean" alwaysNotify="true" />
  </interface>

  <children>
    <Rectangle id="fondo" width="1280" height="720" translation="[0,0]" color="0x000000FF" />

    <GHVideo id="theVideo" visible="false"/>
    <EndCardLite id="theEndCard" visible="false"/>

    <GHHombreMuerto id="hombreMuerto"/>
    <GHLoading id="loading" visible="true" />
  </children>

</component>