<?xml version="1.0" encoding="utf-8" ?>

<component name="itemProfileManagementLite" extends="Group">
  <script type="text/brightscript" uri="ItemProfileManagementLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <!-- <LayoutGroup id="imageAndTitle" translation="[83,140]" layoutDirection="vert" itemSpacings="[-35]" visible="true" horizAlignment= "center" vertAlignment= "center">
      <Poster id="itemPoster"/>
      <Label id="title" visible="false" horizAlign="center" vertAlign="center" translation="[0,-20]" width="150" color="0xCCCCCC" wrap="true" height="90" lineSpacing="0"/>
    </LayoutGroup> -->
      <Poster id="itemPoster"/>
      <Label id="title" visible="true" translation="[10,10]" width="150" color="0xCCCCCC" wrap="true" height="90" lineSpacing="0" vertAlign="bottom" />
      <Poster id="editIcon" />
  </children>

</component>