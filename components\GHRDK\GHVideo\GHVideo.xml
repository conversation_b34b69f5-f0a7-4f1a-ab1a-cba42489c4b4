<?xml version="1.0" encoding="utf-8" ?>

<component name="GHVideo" extends="Video">

  <script type="text/brightscript" uri="GHVideo.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/WideVineUtils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />

  <interface>

    <field id="favorited" type="boolean" value="false" />
    <field id="languages" type="assocarray" onChange="handleLanguages" alwaysNotify="true" />
    <field id="info" type="assocarray" onChange="onInfoChange" alwaysNotify="true" />
    <field id="seasons" type="assocarray" onChange="onSeasonsChange" alwaysNotify="true" />
    <!-- <field id="infoChannels" type="array" onChange="onChannelsChange" /> -->

    <field id="showOmitirIntro" type="integer" value="0" alwaysNotify="true" />
    <field id="onOmitirIntro" type="boolean" alwaysNotify="true" />

    <field id="onkey" type="string" value="" />
    <field id="keyPush" type="string" onChange="onKeyPush" />

    <field id="selected" type="assocarray" alwaysNotify="true" />
    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <!-- <InfoPane id="debugPanel" visible="true" infoText="DEBUG" translation="[360,20]" width="900" /> -->
    <GHVideoPanel id="playerPanel" visible="false" />
    <GHVideoPanelAudios id="audioPanel" visible="false" />
    <GHVideoPanelSeason id="seasonPanel" visible="false" />
    <IntroCardLite id="theIntroCard" visible="false"/>
  </children>

</component>
