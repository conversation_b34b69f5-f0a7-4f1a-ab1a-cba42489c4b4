<?xml version="1.0" encoding="utf-8" ?>

<component name="WidgetService" extends="Group">

  <script type="text/brightscript" uri="WidgetService.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="tick" type="boolean" alwaysNotify="true" onChange="onTick" />
    <field id="run" type="assocarray" alwaysNotify="true" onChange="onRun" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
  </children>

</component>
