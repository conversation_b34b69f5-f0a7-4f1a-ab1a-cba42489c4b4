<?xml version="1.0" encoding="utf-8" ?>

<component name="Vcard" extends="Page">

  <!-- WIDEVINE -->
  <script type="text/brightscript" uri="pkg:/source/GHRDK/WideVineUtils.brs" />

  <script type="text/brightscript" uri="Vcard.brs" />
  <script type="text/brightscript" uri="VcardLogic.brs" />
  <script type="text/brightscript" uri="pkg:/components/Pages/PlayerLite/VideoPlayerLogic.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />


  <interface>
    <field id="seasonSelected" type="integer" />
    <!-- interfaz de entrada -->
    <field id="data" type="assocarray" onChange="handleData" />

    <field id="mediaType" type="string" value="" />
    <field id="contenido" type="assocarray" onChange="RefreshGlobal" />
    <field id="seasons" type="assocarray" onChange="RefreshGlobal" />
    <field id="favorite" type="boolean" value="false" />
    <field id="vistime" type="assocarray" onChange="RefreshGlobal" />

    <field id="deeplink" type="assocarray" onChange="onDeepLink" />

    <!-- interfaz de salida -->
    <field id="reloadHome" type="boolean" value="false" alwaysNotify="true" />
    <field id="reloadHomeUser" type="boolean" value="false" alwaysNotify="true" />
    <field id="loading" type="boolean" value="false" alwaysNotify="true" />

    <field id="payway" type="string" value="loading" alwaysNotify="true" />
    <field id="buybuttons" type="assocarray" />
    <field id="buyResult" type="boolean" alwaysNotify="true"/>
    <field id="accessCode" type="assocarray" />

    <field id="buttonSelected" type="string" value="invalid" alwaysNotify="true" />
  </interface>

  <children>
    <!-- encabezado -->
    <MenuComponent id="menu" translation="[50,20]" />
    <!-- fondos -->
    <Poster id="fondo" translation="[0,100]" width="1280" height="448" />
    <Poster id="gradient" translation="[0,100]" width="1280" height="448" uri="pkg:/images/gradient.png"/>
    <!-- info -->
    <Label id="title" translation="[58,115]"/>
    <LayoutGroup id="info" translation="[58,185]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[12]">
      <Label id="infoTitle" text="infoTitle"/>
      <Label id="infoBar1" text="|"/>
      <Label id="infoGenres" text="infoGenres"/>
      <Label id="infoYear" text="infoYear"/>
      <GHTag id="infoRating" text="infoRating"/>
      <Label id="infoDuration" text="infoDuration"/>
    </LayoutGroup>
    <LayoutGroup id="infoSerie" translation="[58,224]" layoutDirection="horiz" vertAlignment="center" itemSpacings="[10]" visible="false">
      <Label id="infoSeason" text="infoSeason"/>
      <Label id="infoBar2" text="|"/>
      <Label id="infoEpisode" text="infoEpisode"/>
      <Label id="infoBar3" text=":"/>
      <Label id="infoEpisodeTitle" text="infoEpisodeTitle"/>
    </LayoutGroup>
    <Label id="desc" translation="[58,261]"/>
    <Label id="info3" translation="[380,415]" wrap= "true" width="466" height="94" color= "#7F8282" visible="false" />
    <Label id="info4" translation="[60,520]" width="800" height="94" color= "#7F8282" visible="false" />
    <GHProgressBar id="seen" backColor="#2C2C2C" barColor="#DE1717" visible="false"/>
    <!-- action -->
    <GHButtonGroup id="botonera" layout="childs" orientation="horizontal" exitUp="true" exitDown="true" />

    <!-- <LayoutGroup id="cintas" translation="[-140,550]" layoutDirection="vert" vertAlignment="top" itemSpacings="[10]"> -->
    <GHRowList id="episodes" visible="false" translation="[0, 550]" />
    <GHRowList id="recommendations" visible="false" translation="[0, 550]" />
    <GHRowList id="talents" visible="false" translation="[0, 550]" />
    <!-- </LayoutGroup> -->

    <LayoutGroup id="groupInfoEpisode" layoutDirection="vert" itemSpacings="[15]" translation="[400, 780]" visible="false">
      <LayoutGroup layoutDirection="horiz" itemSpacings="[10]">
        <Label id="epiInfoSeason" text="" />
        <Label id="epiInfoBar" text="|" />
        <Label id="epiInfoEpisode" text="" />
        <Label id="epiInfoBar2" text=":" />
        <Label id="epiInfoEpisodeTitle" text="" />
      </LayoutGroup>
      <LayoutGroup layoutDirection="horiz" vertAlignment="center" itemSpacings="[10]">
        <Label id="epiInfoYearEpisode" text="" />
        <Label id="epiInfoCategories" text="" />
        <Label id="epiInfoYearSeason" text="" />
        <GHTag id="epiInfoRating" text="" />
      </LayoutGroup>
      <Label id="epiDesc" />
    </LayoutGroup>

    <!-- cargando -->
    <!-- width="200" height="200" translation="[20,350]" backColor="Ox00000000" -->
    <GHLoading id="spinner" visible="true" />
    <Rectangle id="rec" color="0x00FF0088" />

  </children>
</component>