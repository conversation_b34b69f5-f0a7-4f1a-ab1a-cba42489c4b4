# Performance

## Status

3.1 *(3810X)* Working

3.2 *(Homescreen within 15s)* To Check

3.3 *(Screen-to-screen within 3s)* To Check

3.4 *(Responses to user within 3s)* To Check

3.5 *(<PERSON><PERSON> press within 250ms)* To Check

3.6  *(Content starts playing within 8s)* Ok

3.7 *(file size is 4 MB)* Ok



## Items

**3.1** The channel must be available on all Roku platforms that receive the current firmware. The channel must be responsive to user launch, navigation, browse, and playback of content at a reasonable speed on all supported Roku platforms, and specifically meet the following requirements when measured on Roku Streaming Stick+ (Amarillo-2019 3810X) or Roku Express (Littlefield 37XXX). If the performance requirement is not met on these specified devices, Roku reserves the right to block launch on all other Roku device types.

**3.2** The channel launches to a **fully rendered** homescreen within 15 seconds on the Roku Streaming Stick+ (Amarillo-2019 3810X) or 20 seconds on the Roku Express (Littlefield 37XXX).

You must add a signal beacon to your application to measure launch times. Once you add the beacon, you can use the debug console to verify that your channel's launch time is meeting this requirement. See [Measuring channel performance](https://developer.roku.com/docs/developer-program/performance-guide/measuring-channel-performance.md) for how to measure channel launch times, including how to account for the loading times of any dialogs or screens displayed before the home screen is launched.

**3.3** Screen-to-screen (scene-to-scene) transitions are within 3 seconds on the Roku Streaming Stick+ (Amarillo-2019 3810X) or 5 seconds on the Roku Express (Littlefield 37XXX).

**3.4** Responses to user requests that take longer than 3 seconds require the appropriate "in progress" notification. Responses cannot take longer than 10 seconds to complete.

**3.5** The channel's response to a remote button press and tile-to-tile navigation is within 250 milliseconds.

**3.6** Content starts playing within 8 seconds of initiation on the Roku Streaming Stick+ (Amarillo-2019 3810X) or 10 seconds on the Roku Express (Littlefield 37XXX). If your channel is using the Roku video player, the Roku OS automatically fires beacons to measure and record the video start time; therefore, no additional implementation work for your application is required. If your channel is using a custom video player, your application must fire the video start beacons.

You can use the debug console to verify that video start times on your channel are meeting this requirement (See [Measuring channel performance](https://developer.roku.com/docs/developer-program/performance-guide/measuring-channel-performance.md)).

Roku's [Fast Video Start](https://developer.roku.com/docs/developer-program/media-playback/fast-video-start.md) is available to pre-buffer content and help improve playback performance.

**3.7** Channel file size is 4 MB or less.



## Anotaciones

- Roku Streaming Stick+  |mínimo
- Lanza la aplicación en menos de 15s - homescreen
- beacon para medir tiempos.
- screen to screen 3s
- si varias paginas para la home, se suma todo (?)
- respuesta del botón 250ms
- playback en 8 segundos



## Beacons



> For most channel performance metrics, the Roku OS automatically fires the beacons for the start and stop points (referred to as "initiate" and "complete" beacons, respectively)

- No hace falta agregarlo al principio y fin de la aplicación.

> Using video start time for example, the Roku OS automatically fires the video start beacons (**VODStartInitiate** and **VODStartComplete**). No additional implementation is therefore required for channel applications to measure video launch times

- No hace falta agregarlo en el inicio fin de la reproducción

> Channel applications must fire an **AppLaunchComplete** beacon when the channel home page is fully rendered. This beacon must also be fired when video playback starts after handling a [deep link](https://developer.roku.com/docs/developer-program/discovery/implementing-deep-linking.md), and the channel can respond to commands sent via the remote control.

- SI hay que dispararlo cuando se termina de cargar la home y cuando comienza a dispararse el deeplink

> If the channel UI displays a login, user selection, or end-user license agreement (EULA) dialog before the home page, the channel must fire **AppDialogInitiate** and **AppDialogComplete** beacons when the dialog loads and exits, respectively.

- SI hay que dispararlo cuando se entra y sale de: `LandingPage, LoginPage, RegisterPage`

> If your channel contains an EPG, the application must also fire beacons when the user initiates a keypress to display the EPG

- En el momento de la EPG va a haber que implementarlo

#### Momentos para disparar el beacon

- [x] cuando se termina de cargar la home
- [ ] cuando se dispara el video del deeplink
- [x] Al inicio y fin de cada pantalla previa al Home
- [ ] En el inicio y carga de EPG

#### Donde poner los disparos

Conviene, entendemos, ponerlos en el `Pages.brs` en cada uno de los eventos de inicio y cierre de las pantallas definidas.

Formato:

```basic
' para la home
myScene.signalBeacon("AppLaunchComplete")
' para las pantallas antes de la home
myScene.signalBeacon("AppDialogInitiate")
myScene.signalBeacon("AppDialogComplete")
```

#### Lectura de los tiempos

| **Statistic**                  | **Beacon Type** | **Description**                                              |
| :----------------------------- | :-------------- | :----------------------------------------------------------- |
| TimeBase                       | Initiate        | A timestamp for the beacon based on milliseconds elapsed since the initiate beacon for the channel launch was recorded. |
| Duration                       | Complete        | Milliseconds between the initiate and complete beacons.      |
| Memory Points (MiP, KiP, or p) | Complete        | Memory points provide a relative measurement for your channel's memory performance that can be used for trend analysis. You can monitor the amount of memory points reported for any complete beacon to see if it goes up or down across builds of your application.  Memory points are measured in mebipoints (MiP), kibipoints (KiP), or points (p). This is similar to how units of information are expressed as mebibytes (MiB), kibibytes (Kib), and bytes. |
| SteadyMaxMemPoints             | N/A             | The maximum amount of memory points your app used over a 5-second interval (using this interval prevents temporary spikes being recorded as the maximum). The timebase and duration denote the period when the maximum usage occurred. |

#### Casos de disparo

| **Metric**     | **Start Point**                                              | **Stop Point**                                               | **Initiate Beacon**                                          | **Complete Beacon**                                          |
| :------------- | :----------------------------------------------------------- | :----------------------------------------------------------- | :----------------------------------------------------------- | :----------------------------------------------------------- |
| Channel launch | The user presses the OK button to launch a channel from the home screen. | The channel is fully rendered and operational on its initial UI screen, or it reaches user-operable video playback. | AppLaunchInitiate  The last keypress before the beacon was signaled. If there was no prior keypress, the Initiate beacon signal time. | AppLaunchComplete  The first render pass completes after the Complete beacon has been signaled via the signalBeacon() method.  **Your application must fire this beacon to pass certification.** |
| App compile    | The channel compilation starts.                              | The channel compilation finishes.                            | AppComplieInitate  The channel compilation starts after the channel is launched. | AppComplieComplete  The channel compilation finishes.        |
| Dialog launch  | A dialog (for example, a login, user selection, or network error screen that the user must dismiss) is loaded. | The dialog exits.                                            | AppDialogInitiate  The channel enters a dialog before rendering the home screen where the app waits for user input.  **Your application must fire this beacon if your channel includes any dialogs requiring user input before rendering the home page.** | AppDialogComplete  The user dismisses the dialog.  **Your application must fire this beacon if your channel includes any dialogs requiring user input before rendering the home page.** |
| EPG launch     | The user initiates a keypress to bring up the EPG.           | The EPG is fully rendered and navigable.                     | EPGLaunchInitiate  The last keypress before the initiate beacon was signaled. If there was no prior keypress, the Initiate beacon signal time.  **Your application should fire this beacon if your channel includes an EPG.** | EPGLaunchComplete.  The first render pass completes after the Complete beacon has been signaled via the signalBeacon() method.  **Your application should fire this beacon if your channel includes an EPG.** |
| Video start    | The user initiates a keypress to play a video.               | Video playback has started and is visible to the user.       | VODStartInitiate  The last keypress that occurred between play events. If there was no prior keypress, the Initiate beacon signal time. | VODStartComplete  The first render pass completes after the Complete beacon has been signaled (when the video node receives the "firstFramePresented" event from the Media Player). |
| Live start     | After bringing up the EPG, the user initiates a keypress to play a video. | Video playback has started and is visible to the user.       | LiveStartInitiate  The last keypress that occurred between play events. If there was no prior keypress, the Initiate beacon signal time. | LiveStartComplete  The first render pass completes after the Complete beacon has been signaled (when the video node receives the "firstFramePresented" event from the Media Player). |
| Channel change | The user initiates a keypress to change the channel.         | The new video playback has started and is visible to the user. | LiveChannelChangeInitiate  The time of the last keypress that occurred between play events, which must both be live streams as reported by the media player session object when the firstFramePresented event is received. In addition, the previous keypress must have happened before the video player was destroyed for a live channel change event to have occurred. | LiveChannelChangeCompleted  The first render pass completes after the Complete beacon has been signaled (when the video node receives the "firstFramePresented" event from the Media Player upon a live channel change occurring). |
| Channel exit   | The user presses the Home button to exit a channel.          | The channel has exited and the home screen is fully rendered and operational. | AppExitInitiated  The home key handler engages app shutdown. | AppExitComplete  The first render pass completes after the Complete beacon has been signaled (when the Roku OS completes application exit) and the Home screen is displayed. |





## Bibliografía

Measuring channel performance
https://developer.roku.com/es-ar/docs/developer-program/performance-guide/measuring-channel-performance.md

