' ------------------------------------------------
' SCREEN
' by<PERSON><PERSON>e(2020) (<EMAIL>)
' ------------------------------------------------


''''''''
' GH_sayAt:
' Creates a Label in a place, with alignement
' @param  {} id Label ID
' @param  {} x=100 label x position
' @param  {} y=100 label y position
' @param  {} msg="***" label message
' @param  {} align="center" label align
' @returns pointer to label
''''''''
function ghSayAt(id, x = 100, y = 100, msg = "***", align = "center") as object
  topy = m.top.createChild("Label")
  topy.id = id
  topy.text = msg
  topy.translation = "[" + stri(x) + "," + stri(y) + "]"
  topy.width = 1280
  topy.horizAlign = align
  return topy
end function
''''''''
' GH_setBackground:
'
' @param  {} url Image url
' @param  {} id="fondo" component ID
''''''''
sub ghSetBackground(url, id = "fondo")
  fondo = m.top.findNode(id)
  if fondo <> invalid then
    fondo.loadSync = false
    fondo.loadDisplayMode = "scaleToZoom" ' limitSize | noScale | scaleToFit | scaleToFill | scaleToZoom
    fondo.uri = url
  else
    print "WARN : ghSetBackground ";id;" not found."
  end if
end sub



' FOCUS -- no anda, revisar
' -----------------------------------
' function ghGetCurrentFocus()
'   current = invalid

'   for each item in m.map
'     if m.map[item]["default"] <> invalid then
'       default = item
'       print ghLogHead()"ghGetCurrentFocus -- Encontre default ";default
'     end if
'   end for


'   if m.top.focusedChild <> invalid then
'     print ghLogHead();"ghGetCurrentFocus -- ";m.top.focusedChild
'     if m.top.focusedChild.id <> "" then
'       print ghLogHead();"ghGetCurrentFocus -- ";m.top.focusedChild.id
'       focused = m.top.findNode(m.top.focusedChild.id)
'       if m.map[focused.id] <> invalid then ' si lo tengo en el mapa
'         if focused.hasField("focus") then ' es un GH
'           current = m.top.focusedChild.id
'           print ghLogHead();"ghGetCurrentFocus -- es un GH"
'         else ' es uno comun
'           print ghLogHead();"ghGetCurrentFocus -- NO ES GH"
'         end if
'       else if default <> invalid then
'         current = default
'       else
'         current = m.map.Keys()[0]
'       end if
'     end if
'   end if
'   print ghLogHead();"ghGetCurrentFocus -- "; current
'   return current
' end function