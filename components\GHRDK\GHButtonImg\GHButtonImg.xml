<?xml version="1.0" encoding="utf-8" ?>
<component name="GHButtonImg" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHButtonImg.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- size -->
    <field id="width" type="int" value="50" onChange="refresh" />
    <field id="height" type="int" value="50" onChange="refresh" />
    <!-- icon -->
    <field id="icon" type="string" value="" onChange="refresh" alias="ico.uri" />
    <field id="iconColor" type="string" value="0xFFFFFFFF" onChange="refresh" alias="ico.blendColor" />
    <field id="iconWidth" type="int" value="20" onChange="refresh" alias="ico.width" />
    <field id="iconHeight" type="int" value="20" onChange="refresh" alias="ico.height" />
    <!-- icon OVER -->
    <field id="iconOver" type="string" value="" onChange="refresh" alias="icoO.uri" />
    <field id="iconColorOver" type="string" value="0xFFFFFFFF" onChange="refresh" alias="icoO.blendColor" />
    <field id="iconWidthOver" type="int" value="20" onChange="refresh" alias="icoO.width" />
    <field id="iconHeightOver" type="int" value="20" onChange="refresh" alias="icoO.height" />
    <!-- foco -->
    <field id="foco" type="string" value="" onChange="refresh" alias="foco.uri" />
    <field id="focoColor" type="string" value="0xFFFFFFFF" onChange="refresh" alias="foco.blendColor" />
    <field id="focoWidth" type="int" value="30" onChange="refresh" alias="foco.width" />
    <field id="focoHeight" type="int" value="30" onChange="refresh" alias="foco.height" />
    <!-- text -->
    <field id="text" type="string" value="*" onChange="refresh" alias="label.text" />
    <field id="textColor" type="string" value="#BBBBBB" onChange="refresh" alias="label.color" />
    <field id="textWrap" type="boolean" value="true" onChange="refresh" alias="label.wrap"/>
    <field id="textOffset" type="int" value="0" onChange="refresh" />


    <!-- valores de salida -->
    <field id="value" type="string" value="mybutton" onChange="debugEvent" />
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />


    <!-- internos -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />



    <!-- ==================================================== -->
    <!-- <field id="font" type="node" alias="label.font" /> -->
    <!-- <field id="mode" type="string" value="icon" onChange="updateFieldText"/> -->
    <!-- <field id="buttonText" type="string" onChange="updateFieldText"/> -->
    <!-- position -->
    <!-- <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" /> -->
    <!-- align -->
    <!-- <field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign" /> -->
    <!-- <field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign" /> -->
    <!-- colors -->
    <!-- <field id="color" type="string" value="0xFFFFFF" onChange="updateFieldColor" /> -->
    <!-- <field id="backColor" type="string" value="0x282828" onChange="updateFieldBackColor" /> -->
    <!-- <field id="buttonTextColor" type="string" value="0xFFFFFF" onChange="updateFieldColor"/> -->
    <!-- <field id="selColor" type="string" value="0xFFFFFF" onChange="updateFieldSelColor" /> -->
    <!-- <field id="selBackColor" type="string" value="0x981C15" onChange="updateFieldSelBackColor" /> -->
    <!-- <field id="selButtonTextColor" type="string" value="0xFFFFFF" onChange="updateFieldColor"/> -->
    <!-- padding and margin -->
    <!-- <field id="padding" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true" /> -->
    <!-- <field id="focusPadding" type="string" value="12" onChange="updateFieldFocusPadding" alwaysNotify="true" /> -->
    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" /> -->
    <!-- ==================================================== -->

  </interface>

  <children>
    <rectangle id="fondo" color="0x00000000" />
    <Label id="label" />
    <Poster id="foco" visible="false" />
    <Poster id="ico" visible="true" />
    <Poster id="icoO" visible="false" />
  </children>

</component>
