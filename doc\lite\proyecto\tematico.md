# Índice temático

## Modalidades

-----

#### Tips

- conviven los dos modos
- Classic = Premiun = login + suscripción (o compra VOD)
- Freemiun = sin login o login sin suscripción.
- **API** que dice la modalidad del usuario

-----

##### HN001 Modalidades Premium y Freemium

1. Debe habilitarse en la plataforma la convivencia de las ***modalidades Premium y*** ***Freemium.***
   1. Para ambas modalidades se debe actualizar la UX/UI.

##### HN002 : Se mantiene la anterior como Premium

1. Un usuario debe acceder a la ***modalidad Premium*** cuando:
   1. Previamente inició sesión en la plataforma, accedió por IP Telmex o accedió con sesión activa y
   2. ***Si cuenta*** con al menos una suscripción activa/comprada (OTT o IPTV) ó
   3. Realizó una compra o renta de contenido VOD.
2. La ***modalidad Premium*** debe mostrar la experiencia actual de la plataforma.
3. Al usuario en la ***modalidad Premium*** se le debe permitir acceder al contenido, donde:
   1. El tipo de contenido (***Freemium*** o ***Premium***) disponible en esta modalidad , dependerá de lo definido por la operación.

##### HN003 : Navega como Freemium

1. El usuario podrá navegar en la ***modalidad Freemium*** ***con*** inicio de sesión o ***sin*** inicio de sesión.
2. Al usuario en la ***modalidad Freemium*** se le debe permitir acceder al contenido, donde:
   1. El tipo de contenido (***Freemium*** o ***Premium***) disponible en esta modalidad , dependerá de lo definido por la operación.

##### HT007 : Modalidad usuario

servicio para identificar la modalidad del usuario



1. Aplica para BE.
2. El servicio debe contemplar el envío del parámetro `"user_token"` 
3. El servicio en la respuesta debe devolver el `type` que posterior será propagado al resto de servicios para proveer la navegación y experiencia correspondiente.
4. El servicio debe actualizar el tipo de usuario después de cualquier transacción efectuada (suscripción, compra, renta, cancelación, suspensión, etc…)
5. El servicio debe considerar ser invocado recurrentemente en cierto intervalo de tiempo desde diferentes dispositivos.



## Inicio

-----

#### Tips

- Si CLite no está habilitado, se muestra la app actual (Classic).
- La Classic tiene que quedar tal cual.
- Se entra directamente al modo Freemium.
- No hay mas Landing (en modo CLite, en el Classic sigue todo igual).
- IPTelmex puede mandar a Freemiun si no tiene suscripción.
- **REVISAR** tema del cintillo (no desarrollado). Entre por IPTelmex sin mail (AVERIGUAR!).
- **AVERIGUAR** Llaves APA especiales CVLite.
- **AVERIGUAR** Qué vamos a hacer con el flujo de perfiles (No implementado en Classis).

-----

##### HN016 : Inicio de la aplicación

1. Cuando el usuario acceda a ***Claro video,*** desde la URL o desde el dispositivo se le debe dirigir al nodo ***Inicio***.
2. Cuando el usuario acceda a ***Claro video,*** a través de un deeplink  se le debe dirigir al nodo correspondiente indicado en el deeplink.
3. No debe aparecer por default la pagina default actual donde se presenta Inicio de sesión, Registro y Ver gratis.

##### HN017 : Inico con IPTelmex

1. Cuando el usuario acceda a la plataforma por IP Telmex, autenticado o con sesión activa:
   1. Si tiene suscripciones activas debe ingresar a la modalidad ***Premium***
   2. Si no tiene suscripciones activas debe ingresar a la modalidad ***Freemium*** con usuario logueado.
2. Se debe asegurar que se conserve la funcionalidad actual del cintillo azul en el flujo de IP Telmex:
   1. Cuando el usuario ha ingresado a la modalidad por IP Telmex, pero aún no tiene asociado un email para navegar fuera de su casa, se debe seguir manteniendo el cintillo azul para completar el registro de datos.

##### HN018 : Landing en modo “Classic”

1. Se debe ***retirar únicamente*** de la experiencia ***de Claro video con publicidad*** la ***página de inicio*** donde actualmente mostramos las opciones Regístrate, Inicia sesión y Ver gratis.
2. En los países donde no se habilite Claro video con publicidad se debe seguir manteniendo la ***página de inicio*** de la experiencia actual.

##### HN025 : Mantener experiencia

1. Cuando el usuario acceda a la plataforma desde un  ***dispositivo no incluido*** en el alcance de la ***Etapa 1***, se debe mantener la ***experiencia actual***.
2. Cuando el usuario acceda a la plataforma en algún dispositivo ***deprecado que no soporte publicidad,*** se debe proporcionar la ***experiencia actual***.
3. Cuando el usuario acceda a la plataforma en una ***operación*** que ***no*** tenga habilitado ***Claro video con publicidad***, se debe proporcionar la ***experiencia actual.***

##### HN026 : Mantener el modo “Classic”

1. Se debe asegurar que los cambios de diseño y flujos solicitados para *Claro video con publicidad* no afecten:
   1. La experiencia de las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   2. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   3. La experiencia en los ***dispositivos no incluidos*** en el alcance de la ***Etapa 1***.

##### HT020 : APA

APA assets y metadata

1. Se debe implementar la matriz de comunicación correspondiente a los flujos de:
   1. Inicio de sesión
   2. Registro
   3. Nodo Freemium

##### HT020 - Implementar comunicación de llaves apa/metadata y apa/asset

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*pendiente conocer el listado final*
R: También está contemplada como una llave y hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.

##### HN016 - Acceso a la plataforma

AFE-16/8 https://dlatvarg.atlassian.net/browse/AFE-167 https://dlatvarg.atlassian.net/browse/AFE-168

*¿Ya no se pasa por la pantalla de selección de perfil al ingresar a la aplicación con la nueva experiencia de publicidad? Porque en la HN016 dice que tanto usuario a freemium com premium se le debe dirigir al nodo inicio:*
R: 1. Si la llave de configuración profiles_config se encuentre como "enable": true en la región, si tendría que estar mostrando la pantalla de perfiles al iniciar sesión.



## Publicidad

-----

##### Tips

- Se debe poder activar por region (cv_advertising_config).
- Publicidad a nivel contenido.
- Un contenido puede ser Freemiun y Premium a la vez. (De acuerdo a lo que tenga adquirido).
- En Freemium, si no tiene derecho de publicidad, sin publicidad; si lo iene, hay que mostrarla.
- En Premium, si no tiene derecho de reproducción, debe suscribirse o comprarlo (PBI).
- Se muestra publicidad sólo para Freemiun (por ahora).
- **AVERIGUAR** Tema de preRoll y postRoll (queda así, o se unifican?). Habría uno que sería unificado.
- **LEVANTAR LA MANO** Requerir una sola url para pre y post roll, igual que en AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174. Usar ambos unidos AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175
- **LEVANTAR MANO**: solo formato mp4. (NO GIF)
- PreRoll sólo si tiene derecho de reproducción
- Se mantiene la EndCard. Si acciona el salto, no se muestra el postRoll
- Si entra con resume o con desde el principio, también se muestra el preRoll
- **LEVANTAR MANO**: no tenemos call to action.
- **INVETIGAR** Si se puede armar el omitir publicidad. Cómo?
- **INVESTIGAR** si se puede lookear el botón de omitir anuncio.
- Sólo contenido VOD.
- No hay publicidad en Canales, Rows ni Highlight.
- Sin Bitmoving.
- Sin distinción de proveedor (a futuro sí).
- **API** servicio con el link por group_id
- Si da error, no se muestra la publicidad.
- Primero el pin, luego la publicidad.
- **INVESTIGAR** si el tiempo de omitir publicidad es automático o lo manejamos

-----

##### HN004 :  Publicidad en ambos contenidos

1. Se debe incluir en Claro video con publicidad ***contenido Premium*** y ***contenidos Freemium***

##### HN007 : Reglas de publicidad

1. A nivel de ***contenido*** se debe poder determinar si se debe ***visualizar con publicidad*** o ***sin publicidad***
   1. Para cada contenido a través de un atributo se determinará si tiene derecho de publicidad o no.
2. Un ***contenido*** puede pertenecer de manera simultánea a la modalidad Premium y Freemium.
   1. Dependiendo si el usuario tiene adquirida la oferta se le presentará una experiencia u otra.
3. Un ***contenido*** puede pertenecer de manera exclusiva a la modalidad Premium o Freemium dependiendo del atributo del contenido.
4. Para ***contenido Freemium*** donde el usuario puede o no estar logueado:
   1. Si el contenido ***no tiene el derecho de publicidad*** debe reproducirse de manera automática ***sin*** presentar la ***publicidad.***
   2. Si el contenido ***tiene el derecho de publicidad*** debe reproducirse de manera automática ***con publicidad*** incluida.
5. Para ***contenido Premium*** se deben conservar las funcionalidades actuales, donde:
   1. Si el usuario ***cuenta con derechos de reproducción*** entonces se debe iniciar la reproducción del contenido.
   2. Si el usuario ***no cuenta con derechos de reproducción*** entonces deberá previamente:
      1. Suscribirse a un add on o paquete
      2. Comprar o 
      3. Rentar el contenido VOD.

##### HN009 : Formato de publicidad

1. Para la ***Etapa 1*** de la iniciativa se debe incluir publicidad en las reproducciones de los contenidos VOD mediante **Google Ad Manager**.
2. Los formatos iniciales de publicidad son los siguientes:
   1. ***Player*** (películas, series y temporadas):
      1. Pre-roll 
      2. Post-roll
   2. ***Marketing***:
      1. Newsletter
      2. Redes sociales (Facebook)
      3. Portales de terceros (Telcel, Telmex, Etc)
3. Los formatos de publicidad en esta etapa son ***banners en imagen***, ***gif*** y ***video (mp4)***. 
4. Se debe permitir en las campañas el link ***vast tag*** para que la agencias puedan modificar la creatividad del banner sin intervención de nuestros equipos.
5. Los espacios de publicidad deben ser ***responsivos*** en cada pantalla y para cada dispositivo y compatibles con ***IAB***.
6. Se debe manejar ***publicidades similares*** para todos los usuarios independientemente del contenido visualizado.
   1. ***No*** se debe contemplar el ***perfilado*** para la visualización de la publicidad.

##### HN010 : PreRoll

1. El ***inicio*** de visualización del ***Pre-roll*** debe ocurrir cuando el usuario inicia la reproducción de un contenido para el cual tiene derechos de reproducción. 

##### HN011 : PostRoll

1. El ***inicio*** de visualización del ***Post-roll*** para un contenido debe ser al finalizar los ***créditos*** conservando la funcionalidad actual del Fin Player, es decir:
   1. Si el usuario cierra el Fin player o se cierra automáticamente sin interacción del usuario (una vez transcurrido el tiempo definido de visualización), se debe continuar visualizando los créditos, al término de estos se muestra el Post-roll y al finalizar se le dirige a la vCard del contenido visualizado.
2. Cuando el contenido que esté visualizando el usuario corresponda a una ***serie*** y ***cuente*** con ***permisos de reproducción del siguiente episodio***, 
   1. Si el usuario presiona el ***botón*** del ***Fin Player*** o deja correr el ***temporizador*** para el cierre automático:
      1. Si el contenido tiene derechos de publicidad, automáticamente se le debe dirigir al ***Pre-roll*** del siguiente episodio.
      2. Si el contenido no tiene derechos de publicidad, automáticamente se le debe dirigir a la reproducción del episodio.
3. Cuando el contenido que esté visualizando el usuario corresponda a una ***serie*** y ***no cuente*** con ***permisos de reproducción del siguiente episodio***, 
   1. Si el usuario cierra el Fin player, continúa en los créditos se debe habilitar el ***Post-roll*** y al finalizar se le dirige a la vCard del episodio que estaba visualizando.

##### HN012 : Continuar viendo

1. Cuando el usuario solicite ***reanudar*** la reproducción de un ***contenido con derechos de publicidad*** desde el flujo ***Continuar reproducción,*** al reanudar la reproducción se debe mostrar el ***Pre-roll***.
   1. Aplica para las dos opciones de reanudación de reproducción incluidas en el formulario: ***Reanudar*** y ***Desde el principio***.

##### HN013 : Saltar publicidad

1. Cada dispositivo deberá obtener en la respuesta del ***ad server*** para la campaña específica:
   1. La URL del consumo y
   2. El call to action de la publicidad
   3. Parámetro que indique si la campaña incluye salto de publicidad
   4. Tiempo de inicio de visualización del botón
2. En caso de que en la campaña se deba habilitar el ***botón para saltar la publicidad***:
   1. Se debe tomar por ***default*** el ***tiempo*** de inicio de visualización obtenido en la respuesta del ***ad server***.
   2. Si desde el ad server ***no se obtiene el tiempo*** de inicio de visualización del botón entonces, se debe configurar como valor por ***default en cada operación*** de ***5 segundos*** (default estándar del mercado).
3. Si el usuario ***selecciona*** la opción para saltar la publicidad:
   1. Desde el ***Pre-roll*** se le debe dirigir inmediatamente a la reproducción del contenido.
   2. Desde el ***Post-roll*** se debe cerrar la publicidad y dirigir al flujo correspondiente de acuerdo a las reglas de visualización.
4. Si el usuario ***no** **selecciona*** la opción para saltar la publicidad:
   1. Desde el ***Pre-roll*** se debe visualizar la publicidad completa y al finalizar ésta se debe iniciar la reproducción del contenido.
   2. Desde el ***Post-roll*** se debe visualizar la publicidad completa  y al finalizar se debe dirigir al flujo correspondiente de acuerdo a las reglas de visualización.

##### HN023 :  Etapas de publicidad

1. La iniciativa evolucionará por etapas donde la ***Etapa 1***:
   1. Debe entregarse a finales de ***Febrero*** en ambiente ***UAT*** 
   2. Debe habilitarse únicamente para la operación de ***México***
      1. El resto de las operaciones deben seguir funcionando bajo la modalidad actual. 
      2. Se debe poder activar esta funcionalidad en cada país con base a la necesidad de Negocio.
   3. La visualización de ***publicidad*** debe habilitarse únicamente en ***contenido VOD***.
      1. El alcance no incluye visualización de publicidad en canales lineales. 
   4. ***No*** se debe contemplar el ***perfilado*** de usuarios para la visualización de publicidad.
   5. ***No*** se debe implementar ***publicidad*** en ***carruseles*** y ***superdestacados***.
   6. Debe ***implementarse*** en los dispositivos: ***Web, Android, iOS, STV, Fire TV, Roku y tvOS***
      1. El alcance ***no incluye*** la implementación en los ***STB***
   7. Los dispositivos que ***no*** se ***integran*** directamente con ***Google add manager*** no debe ser condicionante la entrega en esta primera etapa. 
      1. Esto dependerá del análisis técnico de los equipos.
   8. Se debe generar un listado de los ***dispositivos que no soportan técnicamente la nueva funcionalidad***, para poder notificar a las operaciones.
      1. Este listado se debe obtener antes de finalizar enero.
   9. Para esta primera etapa no es condicionante la implementación del player de ***Bitmovin,*** esto dependerá de cada gerente responsable.
2. Se debe asegurar que las reglas y funcionalidades solicitadas para ***Claro video con publicidad*** no afecten:
   1. La experiencia de las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   2. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   3. La experiencia en los ***dispositivos no incluidos*** en el alcance de la ***Etapa 1***.
3. En esta Etapa 1 se debe mostrar la misma publicidad en todos los contenidos Freemium, sin distinción de proveedor o type, permitiendo desde el **Google Ad Manager** configurar la publicidad por dispositivo y operación.

##### HN035 : Omitir anuncio

1. Aplica para ***contenido Freemium*** con derechos de visualización de publicidad.
2. Cuando se inicie la visualización de la publicidad en la reproducción del contenido:
   1. Si el contenido ***incluye la opción para saltar la publicidad*** se debe visualizar la opción OMITIR ANUNCIO a partir del tiempo (segundos) establecido ***en Reglas para saltar publicidad***.
   2. Si el contenido ***no incluye la opción para saltar la publicidad*** no se debe visualizar la opción OMITIR ANUNCIO.
3. La opción OMITIR ANUNCIO debe visualizarse en la publicidad con base a los insumos de diseño:
   1. Con la estructura definida y
   2. En la posición esperada
4. Si el usuario decide ***no saltar la publicidad*** entonces la opción OMITIR ANUNCIO debe permanecer visible hasta que finalice la publicidad. 
5. Si el usuario selecciona la opción OMITIR ANUNCIO:
   1. Desde el Pre roll se le debe dirigir a la reproducción del contenido.
   2. Desde el Post roll se le debe dirigir al flujo establecido con base a las ***Reglas de reproducción del Post roll al término del contenido***

##### HT001 : Switch publicidad

Si no está la key, apagada.

```json
{
  "cv_advertising_config": {
    "default": {
      "enable": false
    },
    "mexico":{
     "enable": true,
     "skip_offset": 5
    }
  }
}
```

##### HT019 : Google Ad Manager

Google Ad Manager

1. Aplica para FE.
2. Se debe integrar el SDK  considerando la siguiente configuración y ejemplos de video: [Insumos Google Ad Manager](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4069785894) 
3. Para esta primera integración las URLs van fijas tal cual se comparten en el archivo del punto 2, es decir, ninguno de los query params va a ser dinámico.

 [Claro video Lite_tags_v2.pdf](Claro video Lite_tags_v2.pdf) 

##### HT022 : Reglas publicidad

Reglas de publicidad

1. Aplica para BE
2. Generar un servicio que por group_id devuelva las reglas de publicidad contempladas para la primera fase:
   1. Pre-roll
   2. Post-roll
3. Generar un servicio que por group_id permita modificar las reglas de publicidad contempladas para la primera fase:
   1. Pre-roll
   2. Post-roll
4. Considerar los siguientes niveles de jerarquía a aplicar, de lo general a lo particular:
   1. Region
   2. Proveedor
   3. Type
   4. Group Id
5. En una siguiente fase se deberá integrar mediante un Gestor.

##### HT034 : AdServer

Nuevo servicio para obtener la URL de Ad Server

1. Aplica para BE
2. El servicio debe contemplar el envío de los parámetros:
   1. user_token
   2. payway_token
   3. group_id
   4. region
   5. Conjunto de parámetros que identifican al dispositivo
3. Debe considerar que hay URLs específicas para los ambientes de UAT y PROD
4. Se deben tomar las URLs especificadas en la **HT019** 
5. El servicio en la respuesta debe devolver la url para el pre-roll y post-roll. Ejemplo:

```
{
	...
  "pre_roll": "http://...",
  "post_roll": "http://...",
  ... 
}
```

##### HN007 - Reglas para visualización de publicidad en contenidos 

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Que pasa si un contenido tiene publicidad, Esta se le presentara a usuarios Premium y Freemium o solo a los Freemium?* 
R. Para la Fase 1 solo los contenidos Freemium son los que van a tener la publicidad. Pero al ser un atributo del contenido puede aplicarse a cualquier tipo de contenido en etapas posteriores 

##### HN010 - Reglas para visualización del Pre-roll

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165


*En caso de error en la carga de la publicidad, cómo se comporta/maneja este error a nivel experiencia de usuario?* 
R. Pendiente, se va revisar los errores que se pueden tener del ad manager para su validación con negocio/ux con el fin determinar cual es la mejor experiencia para el usuario. 

##### HN010 - Reglas para visualización del Pre-roll

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*En los flujos de VOD->Pin parental, primero el pin luego publicidad?* 
R. Si.  Primero el PIN y luego la publicidad

##### HN011 - Reglas para visualización del Post-roll

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*En el punto 3.a ¿A qué botón del fin player se refiere (VER AHORA o CERRAR)?* 
R. Es el tache o el botón cerrar que se muestra abajo en el caso de las Smart TV y en ningún lado del criterio 3.a. mencionan algo sobre un botón o hacen referencia al criterio 2a?

##### HN012 - Reglas para visualización al Continuar reproducción del contenido

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Qué acción se tomará en caso de que al reanudar la reproducción y se detone la publicidad Pre-roll el bookmark sea casi llegando al final y se empalme con la publicidad Post-roll?* 
R. No se empalmaría. Si tu contenido dura 45 minutos con 59 segundos y le das reanudad por mas al final que te quedes seria 45 minutos con 59 segundos y el post Roll siempre se muestra después de los créditos, es decir cuando tu contenido este finalizado, por lo tanto, no se empalmaría.

##### HN013 - Reglas para Saltar publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Respecto al tiempo indicado en el punto 2.b no es controlable desde el FE, es decir lo que indique el ad server es lo que se respetará.* 
R: Es controlable desde FE, la llave de conf "cv_advertising_config"cuenta con ese parámetro en caso de que el XML no devuelva ese valor. 

##### HN010 - Reglas para visualización del Pre-roll

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Tema técnico: Antes de iniciar la publicidad hay unos segundos en los que se podría mostrar la modal de error de getmedia*
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.

##### HN011 - Reglas para visualización del Post-roll

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Tema técnico: En pruebas del SDK en la web no se pude controlar el postroll para mostrar en el fin player, es decir podría haber inconsistencias con los criterios q indican una interacción entre ambas funcionalidades. Se entiende que el Post roll es el final de la reproducción.*
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.

##### HT021 - No considerar la implementación de Bitmovin

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*en el caso de AAF en la poc se definió si usar, modifican la HU?*
R. Está en proceso de modifiación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.

##### No existe

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*No hay una HU como tal pero en bocetos se ve que la flecha esta chueca ya que esta sobre el play no sobre el botón Agregar Mi lista*
R. Aunque la flecha por algún error se haya movido de lugar, el flujo correcto es que detone desde el botón "Agregar a mi lista".

##### HT034

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*Se observa que se va a tener dos URL una para pre-roll y otra para post roll. Esto complica el desarrollo para los dispositivos AAF y WPLAY debido a que estamos utilizando el SDK y nosotros solo requerimos una url en donde se tenga toda la configuración del Ad ya sea que tenga pre, post, mid etc. Por lo cual para estos dispositivos no es viable que se tenga esa separación. ¿Se puede considerar que para estos dispositivos se tenga solo una url?*
R.Se va a solicitar al equipo de AYM que proporcione una url donde se tengan post roll y pre roll en un solo item, dependiendo la configuración
Considerando los tres escenarios:

- pre roll solo
- post roll solo
- ambos en uno solo

##### ?? - preroll postroll

AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175

*Se tendrá más de un vast para la publicidad de un mismo contenido? porque vemos que están separando pre roll y post roll, pero el deber ser para el SDK de IMA (junto con los ejemplos que vienen en: IMA sample tags  |  IMA SDK for Android  |  Google for Developers ) es que tanto pre roll como post roll, e incluso mid roll vienen en el mismo archivo vast. Si lo separan el SDK de android (TV y móviles) no lo va a reconocer. Ahí en el link que proporcionamos viene el ejemplo:*
R. Se va a solicitar al equipo de AYM que proporcione una url donde se tengan post roll y pre roll en un solo item, dependiendo la configuración
Considerando los tres escenarios:

- pre roll solo
- post roll solo
- ambos en uno solo



## Live

-----

#### Tips

- **OJO** que estoy hay que modificarlo

-----

##### HT018 : ACCIONES

acciones de usuario



1. Aplica para FE.
2. Cuando un usuario Freemium seleccione dentro de la plataforma alguna acción que requiera contexto de usuario, se le debe dirigir al formulario de ***Inicio de Sesión***.
3. Se tienen detectadas las siguientes acciones
   1. Mi lista
   2. Añadir canales a favoritos
   3. Programar recordatorios

##### HT018

AFE-209 https://dlatvarg.atlassian.net/browse/AFE-209

*Y entendiendo que el BRF habla sobre contenidos VOD se tiene la duda de si apican o no estos puntos ya que se enfocan en contenidos live.  ¿Se tendran que considerar los flujos de live?*

R. Se tendrán que considerar el criterio 3 con los 3 incisos (a, b, y c), es decir, si se consideraría los flujos de live con el inciso b y c. Cuando el usuario seleccione las opciones de los incisos antes mencionadas tendría que aplicar el criterio 2, llevarte a la pantalla de Inicio de sesión.





## Contenidos

-----

#### Tips

- Premium si tiene que suscribirse, comprar o rentar
- Pueden ser todos los contenidos.
- **AVERIGUAR** El tipo de componente se define por operación? Esto lo maneja BE?

-----

##### HN005 : Contenido premium

1. Un ***contenido premium*** es aquel para el cual el usuario necesita suscribirse, comprar o rentar para su reproducción.

##### HN006 : Contenido freemium

 Un ***contenido Freemium*** es aquel que el usuario puede visualizar sin necesidad de suscripción, compra o renta.Un ***contenido Freemium*** puede incluir o no publicidad en su reproducción dependiendo de las características del contenido.

##### HN014 : Conteidos alcanzados

1. Los ***contenidos*** que deben ser incluidos en ambos formatos (Freemium y Premium) pueden ser:
   1. Contenidos ***propios***, es decir ingestados a través del laboratorio.
   2. Contenidos de los ***addons*** (AtresPlayer, MGM, Edye, etc).
   3. Contenidos de ***terceros*** (Disney, Amazón, etc) 
      1. Para estos contenidos solo tendremos metadata y posters
      2. La reproducción de estos contenidos será en la app del proveedor para los dispositivos que tengamos derecho.

##### HN015 : Configuración de contenidos

1. La ***operación***  debe poder ***configurar*** si debe visualizarse en la modalidad del usuario únicamente contenido Freemium o Premium o la mezcla de Freemium y Premium.
2. Si la operación requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO aparezcan en***:
   1. Carruseles
   2. Secciones propias del usuario (mis grabaciones, mis compras) 
   3. Buscador 
   4. Recomendadores.
3. Si la operación requiere mostrar los contenidos ***Freemium*** y ***Premium*** al mismo tiempo:
   1. En caso de acceder al contenido Premium, si el usuario no está logueado se debe presentar la pantalla de identificación  y una vez logueado el usuario, también deberán presentarse los contenidos Freemium y mostrar la publicidad si el contenido tiene el derecho correspondiente.



## Analytics

-----

#### Tips

- Cómo sigue este tema de la implementación?

-----

##### HN008 : Información sobre contenidos freemium

1. Para los ***contenidos*** **Freemium** la plataforma debe identificar al menos la siguiente información:
   1. Dispositivo desde el cual está accediendo el usuario (marca, modelo)
   2. IP desde la cual se visualizó el contenido Freemium
   3. Fecha de visualización
   4. Hora, minuto y segundo de visualización del contenido.

##### HN024 : Google Analytics

1. Adicional a los reportes y métricas de requeridas en la sección de ***Consideraciones*** se deben generar reportes sobre las campañas publicitarias considerando impresiones, clicks y CTR. Considerando el siguiente Set Up:[Insumos Analytics](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4085252586) 
   https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4085252586/Insumos+Analytics

##### HN008 - Registro de reproducción de contenido Freemium

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿En que herramienta se estan considerando enviar las metricas para que sea enviada la información? (YB, G analytics)* 
R. La información que BE va a entregar al equipo de DAT se va alimentar de los servicios de tracking que hoy en día ya se ejecutan desde las aplicaciones.  Se va a revisar con el equipo de DAT si se requiere una HT para dicho reporte. De lado de FE no se requiere hacer algo adicional, mas que asegurar que el tracking se continúe enviando. 

##### HN008 - Registro de reproducción de contenido Freemium

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Para el punto 1.d a qué tiempo se hace referencia , Es decir al iniciar la reproducción , finalizar o mientras se esta reproduciendo ?* 
R. Hace referencia al tiempo total de reproducción de contenido que llego a hacer el usuario. Ejemplo: Si un contenido dura 2 horas, pero solo visualizo 1 hora con 40 minutos, la visualización de contenido fue de 1 hora con 40 minutos 

##### HN009 - Formatos de publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*A qué se refiere con el perfilado? (también se indica en HN023) ya que la publicidad puede no ser apta para un perfil kids.* 
R. Se refiere a que, dependiendo de su navegación, los contenidos que ha visualizado el usuario, los favoritos, se va haciendo un perfilado de que es lo que le gusta. No entra en esta fase, entrará posterior con el proyecto de inteligencia artificial



## Diseño

-----

#### Tips

- **AVERIGUAR** cómo es el tema de que el arte es configurable?
- La pantalla de login NO es la misma que en el modo Classic
- La pantalla de registración NO es la misma que en el modo Classic.

-----

##### HN019 : Arte en Login

1. El ***arte*** del formulario de *Inicio de sesión* debe ser ***configurable***.
2. Se debe asegurar que el ***cambio de arte*** en el formulario de *Inicio de sesión* para la iniciativa de *Claro video con publicidad* ***no se replique*** en:
   1. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   2. Las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   3. En dispositivos ***STB***

##### HN020 : Arte en Register

1. El ***arte*** del formulario de *Registro* debe ser ***configurable***.
2. Se debe asegurar que el ***cambio de arte*** en el formulario de *Registro* para la iniciativa de *Claro video con publicidad* ***no se replique*** en:
   1. La experiencia de ***dispositivos deprecados*** que no soportan publicidad. 
   2. Las operaciones donde ***no*** se tenga ***activo Claro video con publicidad.*** 
   3. En dispositivos ***STB***



## Nav

-----

#### Tips

- El árbol va a variar entre Freemiun y Premiun
- **AVERIGUAR** si el cambio se va a dar en ``"type": typeNav``. O se va devolver por el ``type`` y el ``user_token``.
- Para el modo Classic, quedan los ároles actuales.
- Default para Freemiun: *Buscador, Inicio, Canales, Premium y la opción Ingresar*.
- Default para Premiun: *Buscador, Inicio, Canales, Premium,* *Mis contenidos* y *Menú avatar*.
- **AVERIGUAR** Si la posición de los nodos *Premium, Buscador, Mis contenidos y Menu avatar* son fijas, o vienen de API. Si es así, cuáles son los códigos para identificarlos (ya que no son nodos sino páginas específicas de la app (como live))
- **AVERIGUAR** La pantalla puede contener más de un módulo ?? Esto se refiere a sub-módulos?
- **AVERIGUAR** Un primer módulo que inicialmente incluya una imagen. Cómo es esto? Hay Diseño?
- Manejo de ``user_token`` en la nav/data
- **AVERIGUAR** la nav/data es nueva o se mantiene la misma?
- El texto del nodo PREMIUM aparentemente se toma de APA.
- **REVISAR** bocetos del formato de los nodos del menu.
- **API**  Va a haber un servicio que te va a indicar que tipo de modalidad tienes (que no es la nav)
- Lo que antes era un nodo fijo para el nodo por defecto, ahora sería el primero que viene en el API.

-----

##### HT020 : APA

APA assets y metadata

1. Se debe implementar la matriz de comunicación correspondiente a los flujos de:
   1. Inicio de sesión
   2. Registro
   3. Nodo Freemium

##### HN021 : Nav

1. Se debe preparar el producto para poder manejar un ***árbol*** de navegación para la modalidad ***Premium*** y otro distinto para la modalidad ***Freemium***.
2. La operación podrá definir si se deben visualizar árboles de navegación totalmente diferentes.
3. Para los dispositivos deprecados se deberán mantener los árboles actuales.
4. Para las operaciones donde no esté habilitado Claro video con publicidad se deberán mantener los árboles actuales.
5. Para los dispositivos STB se deberán mantener los árboles actuales.

##### HN027 : Nav

1. El menú de navegación debe ser ***dinámico*** de acuerdo a la modalidad a la cuál esté accediendo el usuario.
   1. Cada operación podrá definir los nodos del menú de navegación para cada modalidad ***Premium*** o ***Freemium***.
2. De acuerdo a la operación a la que acceda el usuario:
   1. En la modalidad ***Premium*** se deben visualizar todos los nodos del producto actual
   2. En la modalidad ***Freemium*** con usuario no logueado se deben visualizar los nodos configurados por la operación.
      1. Si la operación no ha configurado los nodos entonces los nodos default deben ser: *Buscador, Inicio, Canales, Premium y la opción Ingresar*.
   3. En la modalidad ***Freemium*** con usuario logueado se deben visualizar los nodos configurados por la operación.
      1. Si la operación no ha configurado los nodos entonces los nodos default deben ser: *Buscador, Inicio, Canales, Premium,* *Mis contenidos* y *Menú avatar*.
   4. En la modalidad ***Freemium*** para el ***nodo Premium*** cada operación:
      1. Puede habilitar o deshabilitar el nodo.
      2. Puede cambiar el nombre del nodo.
3. Cuando el usuario seleccione la opción ***Ingresar*** se le debe dirigir al formulario de ***Inicio de Sesión***.

##### HN028 : Nav

1. Cuando el usuario seleccione el nodo Premium en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes/imágenes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
2. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.

##### HT008 : nav

nav/data



1. Aplica para BE.
2. Considerar como query param ***OBLIGATORIO*** el `user_token`
3. Considerar como query param ***NO OBLIGATORIO*** el `type`
4. Dependiendo de los parámetros definidos en los puntos anteriores se debe poder tener navegaciones diferenciadas:
   1. Freemium: Con `user_token` firmado como Freemium
   2. Freemium registrado: Con `user_token` con contexto de usuario
   3. Premium: Con `user_token` y `type`
5. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HN021 - Árbol de navegación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Para dispositivos donde se tienen soportado el perfil kids, con la nueva version de nav/data como se manejaria? ejemplo si soy kids y soy premium a cual le daria prioridad? habra que revisar las distintas combinaciones de types q se tendrán.*
R: En la HT008 se menciona que se identificará con el user_token y el type.

##### HN027 - Menú de navegación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*el punto 3. La opción ingresar vendrá como parte de las keys de traducción? para user freemium.*
R: También está contemplada como una llave y hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.

##### HT008 - Modificaciones al servicio nav/data

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?

##### HN027

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿Hay algún documento donde vendrán los nodos que se mostrarán en la SD México, dependiendo de si es usuario Freemium o Premium o se deberán mostrar tal y como están en el boceto?*
R. Se deberán mostrar como están en los bocetos.

AFE-172 https://dlatvarg.atlassian.net/browse/AFE-172

*Plataforma Web break point:  en el brf de nodo menú CR --> BRF-7331: Todos | CV | Rediseño Menú 2022: Actualización del menú para Web Breakpoint (https://dlatvarg.atlassian.net/browse/BRF-7331) se pide un navegación distinta a la solicitada en bocetos de CV con publicidad. 
Dado que se está considerando el desarrollo de nodo menú para cumplir con la  navegación solicitada en rediseño , esto genera discrepancias en ambos pedidos.*
R. Se revisará con Negocio/UX.

##### HN007 - Reglas para visualización de publicidad en contenidos 

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Para el punto 2.a se menciona una experiencia diferenciada para Premium y Freemium , sin embargo no queda claro cuál es la diferencia de esa experiencia ?* 
R. Va a haber un servicio que te va a indicar que tipo de modalidad tienes (Premium o Freemium) y de ahí solo se tiene que consultar la nav/data para saber que árbol se tiene que mostrar y la level para saber que contenidos vas a mostrar, al final se puede tener en freemium con contenidos premium y un premium con contenidos freemium y un mix entre ambas



AFE-194 https://dlatvarg.atlassian.net/browse/AFE-194

*Actualmente para los dispositivos AAF se tiene una llave de configuracion para determinar cual es el nodo que se cargara al entrar a la aplicación, ¿Para la parte de claro video con publicidad se seguira utilizando alguna llave similar o se cargara el primer nodo que nos regrese la nav/data?*

R. Cargar siemrpe el primero que nos regresan. Estamos de acuerdo con la primera opción que nos comentas, siempre se tomaría el primer nodo que se regrese en el servicio.



## Login / Logout / Registro

UserToken (APIS QUE CAMBIAN)

https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4026892333/BRF-11088+-+FE+-+IPTV+Uso+de+user+token+como+autenticaci+n+principal#PLY-/-BYR-/-CMS-/-PGS-/-USR

-----

#### Tips

- Sólo se usa para ClaroLite, en Classic tiene que seguir todo igual.
- **NO SOPORTADO** Text link Inicia chat.
- **AVERIGUAR** qué hace el texto *¿Necesitas ayuda?* o es sólo un texto para *Inicia chat* (no soportado)
- **VALIDAR** en boceto el tema anterior. Si no está es porque ya se dieron cuenta.
- **API** apis especiales para login y register.
- **LEVANTAR MANO**: v2/loginbytoken no está implementado. 
- **(!!) AVERIGUAR**: v2/loginbytoken Documentación de qué es esto, cómo funciona.
- El ``user_token`` se obtiones por el ``loginbytoken``
- Con el ``user_token`` se llama al servicio para obtener el ``type`` de app.
- El ``user_token`` tiene que llamarse automáticamente en todas las apis.
- Si falla un API, hay que renovar el ``user_token`` con el ``refresh_token``.
- **IMPORTANTE** Hay acciones dentro de la app que fuerza a que se llame al login (!)
- **AVERIGUAR** qué acciones fuerzan al login. >> "Agregar a mi lista" es una.
- El ``user/logout`` invalida el ``user_token``.
- **(!!) AVERIGUAR** *Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?* R. Se compartirá el diagrama de los nuevos servicios.
- **ATENCION** **la ``user/v1/startheaderinfo`` va a cambiar para los dos modos AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174**
- Limpiar el código de APA LAUNCHER, ya que es optativo y no nos sirve.
- **CHEQUEAR** *¿El servicio loginbytoken queda descartado para esta implementación? Ya que no se observa en el diagrama proporcionado* R. 4: Se cambio por el nuevo versionado del servicio v1/ott/isloggeding el cual también ya se encuentra en el diagrama.
- **LEVANTAR LA MANO** Se debe considerar la pantalla de perfiles: esto hay que agregarlo a la lista.
- **(!!) AVERIGUAR** info sobre los flujos y manejos de los perfiles.
- Se sigue manteniendo el usuario suspendido

-----

##### HN029 : Login

1. Se debe actualizar la pantalla actual (rediseño) de ***Inicio de Sesión*** por cambios de diseño en:
   1. Texto *¿Nuevo en Claro video?*
   2. Opción *REGÍSTRATE*
      1. Incluye una modificación en la morfología del text link a botón.
   3. Texto *¿Necesitas ayuda?*
   4. Text link *Inicia chat*
2. Se deben implementar los cambios con base a los insumos de diseño.

##### HN030 : Registro

1. Se debe actualizar la pantalla actual (rediseño) de ***Registro*** por cambios de diseño en:
   1. Texto *¿Ya tienes cuenta?*
   2. Opción *INICIA SESIÓN*
      1. Incluye una modificación en la morfología del text link a botón.
   3. Texto *¿Necesitas ayuda?*
   4. Text link *Inicia chat*
2. Se deben implementar los cambios con base a los insumos de diseño.

##### HT002 : Login-register

user/login y user/register

https://app.swaggerhub.com/apis/ClaroVideo/Login_ott/1.0.0#/login/get_services_user_v1_ott_login

?? sin cambios

##### HT003 : LoginByToken

v2/loginbytoken



> AVERIGUAR SI ESTO ES UN NUEVO LOGIN, O ES ANTERIOR (DICE MODIFICACION).
>
> CUÁNDO SE USA?



**No está implementado ????**



1. Aplica para BE.
2. Considerar como query param ***NO OBLIGATORIO*** el `token`
3. Si el servicio es invocado ***sin token***, se debe devolver uno para identificar al usuario *Freemium* y no dejar huecos de seguridad en la plataforma.
4. El servicio debe contemplar los escenarios relacionados a IP Telmex.
5. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HT004 : user_token

**Quiero** Que se preserve en las aplicaciones el ***"user_token"***

1. Aplica para FE
2. La obtención del ***user_token*** se realizará al inicio de la aplicación mediante el servicio ***loginbytoken***.
3. Cuando el parámetro ***user_token*** se tenga, se ejecutará el servicio solicitado en la **HT007** para identificar la modalidad de suscripción del usuario.
4. Los únicos escenarios en donde el ***user_token*** estará vacío son:
   1. Usuario primer vez que ingresa a la plataforma
   2. Borrado de datos o cache
5. Solo se actualizara en los casos de renovación, el cual se detalla en la **HT005**

##### HT005 : Renovación token

Renovación del token

https://app.swaggerhub.com/apis/ClaroVideo/refreshtoken/1.0.0#/default/get_services_user_v1_refresh_token

1. Aplica para FE.
2. Al recibir un status code 401 y el siguiente error se debe renovar el token:

```json
{  
  "errors": [
    {
      "code": "USR_JWT_00002",
      "detail": "Token is invalid",
      "source": ""
    }
  ]
}
```

1. Para renovar el token se requiere invocar el servicio
   https://app.swaggerhub.com/apis/ClaroVideo/refreshtoken/1.0.0#/default/get_services_user_v1_refresh_token

##### HT006 : user_token en APIS

**Quiero** que el parámetro ***"user_token"*** se propague automáticamente a todos los servicios que tienen contexto de usuario.



1. Aplica para FE y BE.
2. La propagación del parámetro `"user_token"` debe realizarse por medio de una nueva versión de api, asegurando la retrocompatibilidad en las aplicaciones.
   1. Considerar el siguiente listado de apis a implementar:
      https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4026892333/BRF-11088+-+FE+-+IPTV+Uso+de+user+token+como+autenticaci+n+principal#PLY-/-BYR-/-CMS-/-PGS-/-USR
3. Remover el envío del parámetro **HKS** en todos los servicios que ya aceptan `“user_token"`, del punto **2 inciso a**

##### HT007 : Modalidad usuario

servicio para identificar la modalidad del usuario



1. Aplica para BE.
2. El servicio debe contemplar el envío del parámetro `"user_token"` 
3. El servicio en la respuesta debe devolver el `type` que posterior será propagado al resto de servicios para proveer la navegación y experiencia correspondiente.
4. El servicio debe actualizar el tipo de usuario después de cualquier transacción efectuada (suscripción, compra, renta, cancelación, suspensión, etc…)
5. El servicio debe considerar ser invocado recurrentemente en cierto intervalo de tiempo desde diferentes dispositivos.

##### HT018 : ACCIONES

acciones de usuario

1. Aplica para FE.
2. Cuando un usuario Freemium seleccione dentro de la plataforma alguna acción que requiera contexto de usuario, se le debe dirigir al formulario de ***Inicio de Sesión***.
3. Se tienen detectadas las siguientes acciones
   1. Mi lista
   2. Añadir canales a favoritos
   3. Programar recordatorios

##### HT023 : logout

user/logout

1. Aplica para BE.
2. Generar una versión que cierre sesión e invalide el `"user_token"`
3. Incluir el cierre de sesión de todos los Devices.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HN017 - Acceso con IP Telmex

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Plataforma WEB: ¿Cómo quedará la definición cuando se tenga activa la pantalla para el logueo automático de ip telmex al cerrar sesión ? Nota: Es configurable, en producción se encuentra ahora apagada , pero es importante revisar este escenario.*
R: Esta pantalla se encuentra obsoleta para este flujo, se agregará una HT posterior con lo anterior. 

##### HN029 - Formulario de Inicio de sesión HN030 - Formulario de Registro

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Se menciona rediseño actual , sin embargo no se entiende si habrá registro e inicio de sesión nuevo para la experiencia de claro video con publicidad ?. Cuando se indica el rediseño actual es el productivo o es el esperado de deuda técnica de rediseño (otros BRF’s)?* 
R: Revisar los insumos de diseño que ya fueron entregados, ya que ahí viene la experiencia 

*CA 1c y 1d - Ayuda y chat no aplica para AAF correcto? si es así por favor mencionarlo.*
R: Por eso se puso el punto 2, donde se deben de implementar los cambios con base en los insumos de diseño. En el caso de AAF se validó que no vienen estos elementos. 

##### HN019 - Arte de formulario de Inicio de sesión

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

##### HN020 - Arte de formulario de Registro

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Cual va a ser la llave para este nuevo arte? , es decir en las keys q se tienen hasta ahora son solo de traduccion, se desconoce si hay para imagenes , etc )*
R: Hay una HT020 se dejan las llaves que se emplearán pero hasta el día jueves el equipo de UX estará entregandolas y estarán siendo compartidas el día viernes.

##### HT002 - Modificaciones al servicio user/login y user/register

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Se van a utilizar los servicios solo cuando este encendido claro video con publicidad ?*
R. Todo lo relacionado con las historias técnicas depende de esa llave, cuando está encendida debe mostrar los flujos de CV con publicidad y cuando está apagada debe mostrar los flujos actuales. No se deben hacer modificaciones a los flujos/componentes actuales se debe generar una nueva lógica.

##### HT003 - Modificaciones al servicio v2/loginbytoken

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 *¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 *3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.

##### HT023 - Modificaciones al servicio user/logout

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*El nuevo servicio se va a implementar y solo va a aplicar para lo nuevo de claro publicidad o aplica para todas las regiones sin importar si esta activo o no ?*
R: Todos los servicios solo se van a aplicar si esta activo claro video con publicidad.

 *El criterio 1 debería indicar que tambien es para FE ya que es un cambio de versión, esto se repite en casi todas las HUT , por lo que deberían aplicar para ambos.*
R. Está en proceso de modificación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.

##### HT018 - Acciones con contexto de usuario en modalidad Freemium

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Esta funcionalidad solo aplica si esta encendida la parte de claro Publicidad, o aplica con usuario anónimo?*
R: Esta funcionalidad aplica solo para claro video con publicidad, el usuario anónimo es parte de la experiencia productiva no parte de esta nueva.

##### HN019 - Arte en login

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*Las pantallas de Inicio de sesión/Registro aparentemente son iguales a las actuales de producción, sin embargo, se observan diferencias en tipografía, fuente de teclado y botones. Se requiere contar con los specs o indicarnos si se mantendrá lo productivo?*
R. No son las mismas pantallas y por lo que se comento la experiencia productiva debe estar separada de esta nueva por el hecho de que pueden permanecer ambas en una misma partición. Adicional los Specs fueron entregados, favor de revisar.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentran estas pantallas.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 

##### HN020 - Arte en Register

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*Las pantallas de Inicio de sesión/Registro aparentemente son iguales a las actuales de producción, sin embargo, se observan diferencias en tipografía, fuente de teclado y botones. Se requiere contar con los specs o indicarnos si se mantendrá lo productivo?*
R. No son las mismas pantallas y por lo que se comento la experiencia productiva debe estar separada de esta nueva por el hecho de que pueden permanecer ambas en una misma partición. Adicional los Specs fueron entregados, favor de revisar.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentran estas pantallas.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 

##### No existe

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Una vez que el usuario se registra (freemium con sesión), en los bocetos ya no se muestra el cintillo azul de los paquetes para contratar para mostrar el plan selector. ¿Esto es correcto o debe seguirse mostrando? No hay una HN que lo especifique.*
R. El comportamiento del cintillo azul se comportará de manera similar al como está actualmente, se mostrará unicamente a los usuarios Freemium con sesión.

##### AFE-174

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

En el entendido que deben de convivir los servicios anteriores y los nuevos, En el diagrama se observa que el servicio StartheaderInfo se ve afectado por lo que nos surgen las siguientes dudas :

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*¿El servicio user/v1/startheaderinfo  se va a consultar sin importar que este activa la funcionalidad de claro publicidad ? (Ya que no se sabe si la funcionalidad esta activa hasta que se consume apa/metadata.*
R. 1. Se esta solicitando el cambio de versionado de API (user/v1/startheaderinfo) para todo el proyecto. Al ser el primer servicio que consulta la app no se puede determinar el cambio por alguna flag.

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*¿Cómo va a convivir el hks y el userToken al iniciar la aplicación y al realizar un logout ya que se entiende que para los antiguos flujos el hks va a seguir siendo la autenticación y para los nuevo el userToken().*
R. 2: El nuevo servicio responderá con user_token y hks se deben hacer os cambios con user_token a las APIs listadas en la HT00 HT006 - Utilización user_token como parámetro de autenticación y los servicios que no estén contemplados en el listado anterior, deberían seguir trabajando con el hks.

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*¿El servicio apa/launcher es obligatorio o depende de si se está enviando actualmente?*
R. 3: Se deja en color rojo en el diagrama ya que no es un flujo mandatorio, se agregó para los dispositivos que hoy en día lo consultan.

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*¿El servicio loginbytoken queda descartado para esta implementación? Ya que no se observa en el diagrama proporcionado*
R. 4: Se cambio por el nuevo versionado del servicio v1/ott/isloggeding el cual también ya se encuentra en el diagrama.

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*Si se va a utilizar un nuevo servicio user/v1/ott/isloggedin de nuestro lado vemos viable que este nuevo servicio ya contenga la información de superhighlight y payway profile, Es posible considerar esto? ¿De no ser posible podrían darnos un poco más de contexto para entender esa parte y así poder dar alguna opción de mejora?*
R. 5: Es un servicio que no traerá los atributos que hoy en día devuelve la user/isloggeding por eso es necesario que se manden a llamar las siguientes dos APIs.

##### NA

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*usuario sin IP Telmex se registra para agregar un contenido a su lista, viene desde el reproducir , esto es correcto? no debería ser desde agregar a mi lista?*
R. El flujo correcto es que se detone desde el botón "Agregar a mi lista", tiene que pasar por el flujo de inicio de sesión o si selecciona dentro de la pantalla "Inicia de sesión" la opción "Registrar".

AFE-172 https://dlatvarg.atlassian.net/browse/AFE-172

*Si es un usuario Premium y se tiene una sesión activa se debería de mostrar la pantalla de perfiles antes de cargar el home ?*
R. Se debe considerar la pantalla de perfiles, ese flujo no tiene impacto.

##### HN027

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Criterio 3. se menciona opción Ingresar, pero en bocetos se muestra el avatar para ingresar al Inicio de sesión., cual es la correcta?*
R. Se debe considerar lo que está en los bocetos.


##### AFE-187

AFE-187 https://dlatvarg.atlassian.net/browse/AFE-187

*La implementación que actualmente se tiene de suspención al usuario se seguira considerando con la implementación de claro publicidad ?  De ser asi como quedarian los flijos para mostrar dichas pantallas.*
R. Con respecto a la duda planteada y nuestra conversación anterior por llamada, quiero informarte que el flujo de usuario suspendido se mantendrá de la misma manera que se trabajó en un requerimiento anterior. En este nuevo proyecto, no se consideró realizar cambios en el flujo, ya que no hay impacto por el momento. No obstante, cualquier cambio o actualización que surja será compartido contigo lo más pronto posible.



Compra

-----

#### Tips

- En modo Freemiun, cuando el tipo quiere comprar algo, hay que mandarlo a una pantalla de CheckOut (hay que ver qué es).
- La pantalla de CheckOut parece que es un aviso sobre que va a tener que logearse o registrarse.
- **(!!) ATENCION** En el momento de la compra, si el usuario no tiene un medio de pago, tendríamos que implementar el flujo de Agregar Medio de Pago (!!). Nosotros no lo tenemos implementado, hay que ver si no ponemos la pantalla de que lo cargue en otro dispositivo, o si sólo va a ser con RokuPay, o qué.
- Para el punto anterior, hay que ver que en Android no tienen compra, puede ser un buen argumento.
- **API** la ``plan/offers`` parece que cambia, para que se le mande el ``user_token``.
- La parte de canales sería igual que la actual.
- Si la pantalla de CheckOut ya la tenemos, hay que hacer una nueva para el modo CVLite.

-----

##### HN031 : Compra en Freemiun > CheckOut

1. Cuando el usuario que accede a la ***modalidad Freemium*** seleccione la opción para iniciar una transacción (*suscripción, compra o renta de contenido VOD*), se le debe dirigir a la pantalla de ***Check out*** para *Claro video con publicidad.*
2. Este flujo aplica cuando el usuario decida iniciar un flujo transaccional desde:
   1. Selector de planes
   2. vCard
   3. Fin player
   4. Formulario de oferta de contratación de canal.

##### HN032 : Pantalla CheckOut

1. Aplica cuando el usuario ingresó a la ***modalidad*** ***Freemium*** con ***usuario no logueado***.
2. Se debe implementar para la iniciativa de Claro video con Publicidad la pantalla actual (rediseño) de ***Check out*** con las siguientes actualizaciones:
   1. La pantalla solo debe tener un botón accionable
   2. El texto del botón accionable debe ser: **INGRESA PARA CONTINUAR**
      1. El texto del botón debe ser configurable por operación.
   3. Cuando el usuario seleccione el botón accionable se le debe dirigir a la pantalla de ***Inicio de Sesión***.
3. Se deben implementar los cambios con base a los insumos de diseño.

##### HN033 : Chequeo de medio de pago

1. Aplica cuando el usuario ingresó a la ***modalidad*** ***Freemium*** con ***usuario logueado***.
2. Si el usuario cuenta con medio de pago registrado entonces se le debe dirigir a la nueva pantalla ***Continúa con tu contratación*** que le permitirá efectuar la transacción.
3. Si el usuario no cuenta con medio de pago registrado entonces se le debe dirigir a la pantalla para ***Agregar medio de pago*** que le permitirá dar de alta un medio de pago en la plataforma para continuar en el proceso transaccional. 
   1. La pantalla y flujo para ***Agregar medio de pago*** debe ser el mismo que se tiene actualmente en la plataforma (rediseño).

##### HN034 : Flujo de compra

1. La pantalla para continuar la contratación debe incluir los mismos elementos de la pantalla actual de Check out, aplicando la siguiente actualización:
   1. El título debe ser: ***Continúa con tu contratación***
   2. El título debe ser configurable por operación
2. Los información visualizada en la pantalla debe cumplir con las reglas, diseño y estructura implementada en la experiencia actual.
3. Los botones accionables deben dirigir al usuario al flujo o pantalla de la experiencia actual.

##### HT010 : Plan/Offers

v1/plan/offers

1. Aplica para BE.
2. Realizar los cambios internos y de performance necesarios, ya que servicio va ser consumido para mostrar un ***carrusel*** en la pantalla de ***Inicio*** de las aplicaciones.
3. Debe considerar el ***user_token*** de un usuario Freemium para mostrar todas las ofertas disponibles.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HN031 - Flujo transaccional en modalidad Freemium

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Criterio 2.d: En VOD no aplica la modal de Formulario de oferta de contratación de canal. para VOD faltaría agregar la landing comercial de vcard/selector de planes/carrusel_selector de planes o esta se eliminará ?.* 
R: En esta fase no irán canales con publicidad pero puede haber canales free y canales premium, esto haría que cuando el usuario lo intente ver le muestra la oferta. El componente de landing conercial no se ve afectado por eso no se incluyo. 

##### HN032 - Pantalla Check out para Claro video con publicidad

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿La pantalla de checkout sera una nueva para CV con publicidad? Se indica "Se debe implementar para la iniciativa de Claro video con Publicidad la pantalla actual (rediseño )", si es como esta productiva en cada plataforma ok, pero si es la esperada a nivel BRF de transacciones requeriríamos tener últimos bocetos e HU's esperadas. A nivel boceto entenderiamos solo tiene un componente adicional , es decir el boton “Ingresa para continuar” (cuando el usuario no se a logueado).*
R: Revisar los insumos que se acaban de entregar y construir el componente de acuerdo a ello. Nota: se debe asegurar que la pantalla de checkout productiva no tenga afectación. 

##### HN032 - Pantalla Check out para Claro video con publicidad

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*e requiere de una llave de configuracion para prender y apagar el botón.*
R: No, solo si el texto viene de la llave de traducción se visualiza, de lo contrario no se visualizará.

##### HT010 - Modificaciones al servicio v1/plan/offers

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?

##### HN016 - Acceso a la plataforma

AFE-16/8 https://dlatvarg.atlassian.net/browse/AFE-167 https://dlatvarg.atlassian.net/browse/AFE-168

*Se debe considerar que con android no hay asociación de método de pago. Para un usuario anónimo/freemium la pbi regresa la bandera purchasable=false, con lo cual no se muestran los botones de renta/compra/suscripción, se muestra el mensaje que indica que vaya a web para realizar la transacción.*
R: 2. Lo tomamos en cuenta y lo agregaremos como criterio de aceptación en las HT que tienen que ver con ese servicio.

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*El ticket no se mostraría ya que en México tenemos one click, pero al activarse en otras regiones puede caer en el mismo escenario donde no se tengan bocetos/specs correctos de AAF, requerimos los bocetos/specs de pantallas checkout/ticket.*
R. En los insumos de diseño para AAF si se cuentan con las pantallas de check out y ticket.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentra la pantalla de ticket.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 

AFE-169 https://dlatvarg.atlassian.net/browse/AFE-169

*El ticket no se mostraría ya que en México tenemos one click, pero al activarse en otras regiones puede caer en el mismo escenario donde no se tengan bocetos/specs correctos de AAF, requerimos los bocetos/specs de pantallas checkout/ticket.*
R. En los insumos de diseño para AAF si se cuentan con las pantallas de check out y ticket.
Respuesta AAF: En specs hoy 16 de feb a las 9:22 pm no se encuentra la pantalla de ticket.
https://amcomx.invisionapp.com/overview/Specs_CV_AAF_HD_TelmexComercial-clsf63h86049h01abbtr7gzek/screens?sortBy=1&sortOrder=1&viewLayout=2 

##### HN034

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*La HU indica un título de suscripción pero por cómo se visualiza en boceto puede variar de acuerdo a suscripción/compra/renta, favor de aclarar si es como viene en HU o boceto o es dependiendo del contenido y aclararlo en la HU.*
R. En el criterio 1.b indica que el titulo debe ser configurable por operación, por lo que cambiará de acuerdo a asi es suscripción/compra/renta.

##### HN033 - Inicio de sesión desde un flujo transaccional

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*¿Qué pasa si el usuario tiene un código promocional? Actualmente al seleccionar el botón de código promocional no pasa por el checkout, se va directo al formulario de código promocional.* 
R: Se definió en listado de medios de pagos, si tiene un código promocional debería venir en la payway/workflowtstar y si viene este item dentro de las opciones, se tiene que mostrar



## Niveles

-----

#### Tips

- No se muestra la chapita [VER AHORA]
- Nuevo tipo de row para el selector de planes (puede que sea el que tenemos).
- **AVERIGUAR** el código de row en plan selector.
- **OJO!** que las keys de configuración de chapitas se mantiene, hay que hacer la conversión en código. (BUSINESS IN APP)
- Los componentes que vengan para cada modo lo maneja CMS, para nosotros es transparente.
- **OJO!** que las apis de level cambian (hay nuevas para este modo)
- No hay carrouseles con publicidad.

-----

##### HN036 : Chapitas

1. Aplica para modalidad ***Premium*** y ***Freemium.***
2. Ningún contenido debe mostrar la chapita Ver ahora en todos los nodos de la plataforma.

##### HT011 : Level

level

1. Aplica para BE y CDS.
2. Generar un nuevo type que se pueda configurar en cualquier layout.
3. Debe integrarse en el servicio de ***cms/level***
4. Dentro de las propiedades debe contener la url del servicio **plan/offers**. Ejemplo:

```
"properties": { ... "url": "\/services\/plans\/v1\/offers?&region=mexico&user_token="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IOle4d7lx17JpzBhsY_7GCNiD28qmEZwY-E2_DaQJu0, ... }
```

##### HT030 : Chapitas

chapitas 

1. Aplica para FE
2. Adaptar los nuevos tipos de usuario a los ya existentes para que al construir las chapitas de los contenidos se mantenga la misma lógica y no se afecte dicha funcionalidad. Ejemplo:

```
freemium = anonymous freemium_registrado = no_susc preemium = susc
```

1. Se debar seguir usando la key `providers_label_configuration`
2. En una siguiente fase se deberá refactorizar esta integración.

##### HN015 - Configuración de contenidos por operación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*El listado de contenidos dependiendo de la modalidad , se realizaria del lado de BE o FE?*
R: Depende la operación y BE categorizará pero del lado de FE se mostrará lo que devuelva el servicio cms/level.

##### HT009 - Versionado de CMS

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

Relacionada a HUN021.
R. ¿Cuál sería la consulta?

##### HT0011 - Carrusel Selector de planes

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*No queda claro el uso de los valores carousel_version*
R: Se revisará a mayor detalle en la llamada de las 4, del día de hoy martes 13.

 *Se va a implementar la fase 2 de la homologación de llaves(BRF-10022)? Qué llaves se van a utilizar para la información del carrusel plan selector o va a seguir siendo el mismo carrusel ?*
R. Dentro del servicio de la cms/level se encontrará el nuevo type con la URL del servicio de offers.

 *carousel_version solo se va a enviar si esta prendida la funcionalidad de CV con publicidad ?*
R. Si.

 *Validar si hay mock’s de ejemplo del servicio antes del swagger, esto nos ayudaría en ir maquetando.*
R. Se entrega el jueves al final del día.

##### HT012 - Carruseles con publicidad

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Esta HU fue cancelada por lo que se tendría que eliminar, dado que se indicó el BRF es el MVP no debería estar dentro del BRF.*
R. Está en proceso de elminación pero se esta esperando el resultado de las POC, ya que hay varias HT a modificar.

##### HN030

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Plataforma AAF: El botón de Telmex solo tiene el texto "Conoce las promociones", cambiará? si es así se requiere conocer máximo caracteres necesarios para el botón.*
R. Se comparten los insumos de rediseño 2023:
https://amcomx.invisionapp.com/freehand/CVSTVOnboarding2023-Llaves-gLMNhK3DH
En estos se indican las siguientes medidas para este botón:

```txt
¿Tienes Telmex o Telcel?  28 chars
Conoce las promociones 28 chars
```

##### HN030

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*Criterio 1.d no aplica para AAF ni Roku, favor de aclarar en HU.*
R. Como se ha mencionando en reuniones, actualmente no se están actualizando las HN para dispositivos específicos. Por otro lado, en los insumos de diseño no se están considerando estos dispositivos.

##### HT011

AFE-172 https://dlatvarg.atlassian.net/browse/AFE-172

*Plataforma AAF: La posición del carrusel siempre será hasta el final? La duda surge porque hay una complejidad en el manejo de focos (póster/ botón QUE INCLUYE) si este se encuentra entre otros carruseles listados. Podría considerarse en la primera fase que sea el último carrusel (plan selector) ?*
R. ¿Cuál sería la complejidad técnica del foco para entender con mejor detalle la solicitud?

##### HT011

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿La navegación del carrusel será como la de otros ribbons del home o cómo la del plan selector cuando se llega desde el cintillo azul ?*
R. No se entiende bien la duda, ya que no hay una solicitud de cambio de navegación en los carruseles.



#### Level

https://app.swaggerhub.com/apis/ClaroVideo/CMS_Level/1.0.1#/CMS%20Level/get_services_cms_v2_level

https://dlatvarg.atlassian.net/wiki/spaces/CMS/pages/821231683/Cms+level+v6

https://dlatvarg.atlassian.net/wiki/spaces/ADM/pages/636158231/Arquitectura+Tenant



#### User-level

https://app.swaggerhub.com/apis/ClaroVideo/CMS_Leveluser/2.0.0#/CMS%20Leveluser/get_services_cms_v2_leveluser







## VCard

-----

#### Tips

- Acá va a vernir si el contenido tiene preRoll y/o postRoll
- **API** Hay version nueva de la `payway/v1/purchasebuttoninfo`
- **OJO!** Considerar que el campo ***waspurchased*** sea 1, para aquellos contenidos marcados como Free.

-----

##### HT013 : Content data

content/data 

1. Aplica para BE
2. Integrar un objeto por contenido que indique el tipo de publicidad habilitada para el contenido, consultando el servicio definido en la **HT022**. Para la primera fase se contemplan:
   1. Pre-roll
   2. Post-roll
3. La estructura del objeto debe ser:
   1. Siendo 1, encendido.
   2. Siendo 0, apagado.

```
"publicity": {
	"pre_roll": 1,  
	"post_roll": 0, 
}
```

1. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HT014 : PurchaseButtonInfo

payway/v1/purchasebuttoninfo

1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para habilitar la reproducción.
3. Considerar que el campo ***waspurchased*** sea 1, para aquellos contenidos marcados como Free.
4. Considerar que el objeto ***listButtons*** no debe tener afectación para ningún tipo de usuario y debe listar las ofertas que el contenido tenga.
5. Se agrega la siguiente tabla de referencia sobre las diversas posibilidades:

<img src="img/ht014-1711034476174-1.png" alt="ht014" style="zoom:50%;" />



1. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HN005 - Contenidos Premium

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*De acuerdo al servicio de la HT022 ¿Como vamos a identificar si un contenido es Premium o Freemium?*
R: La HT022 es para BE pero se identificará como hoy en día está con el servicio de la PBI y esta indicado en la HT014.

 *¿Qué parámetro se va a utilizar para saber si el contenido requiere publicidad y donde va a venir la url de la publicidad? De acuerdo a la HT013 viene un atributo publicity: si viene informado es que tiene derecho a publicidad (aunque tenga prerrol/postroll apagado)?? o si viene url es que tiene derecho de publicidad. No queda claro que atributo identificaria que se muestra o no*
R: ¿Cuál sería la duda? Ya que el atributo "publicity" tendra los parámetros "post_roll" y "pre_roll", si estos vienen como 1, entonces nos indica que contará con publicidad, de lo contrario vendra en 0.

##### HT014 - Modificaciones al servicio payway/v1/purchasebuttoninfo

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Si no se tiene el listbutton como vamos a pintar el boton play , la tabla no queda clara.*
R: Se revisará a mayor detalle en la llamada de las 4.



## Search

-----

#### Tips

- `La search/predictive` y la `search/linealpredictive` tienen nuevas versiones
- **BOCETO** hay recomendations para nosotros??

-----

##### HT016 : Predictive/LinealPredictive

search/predictive y search/linealpredictive



1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para mostrar contenido específico.
3. Si la región requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO se devuelvan*** en el response.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.

##### HT017 : Recomendations

content/recommendations

1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para mostrar contenido específico.
3. Si la región requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO se devuelvan*** en el response.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



##### HT016 - Modificaciones a los servicios search/predictive y search/linealpredictive

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*¿Este filtrado estaria considerado del lado de FE o BE ?*
R: Considerado del lado de BE.



## Diseño

##### HN037 : Insumos

1. Se debe actualizar la plataforma para la experiencia de Claro video con publicidad considerando los siguientes insumos de diseño:[Insumos de diseño Claro video con publicidad](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4066082896) 
   https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4066082896/Insumos+de+dise+o+Claro+video+con+publicidad



https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Ffreehand%2FCV-ROKU-FHD-TelmexComercial-SGdhbtUGW

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Ffreehand%2FCVROKUFHDTelmexComercial-4wUtMhFAo

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Foverview%2FSpecs_CV_Roku_HD_TelmexComercial-clsi52lhk00r501999wli0d9d%2Fscreens%3FsortBy%3D1%26sortOrder%3D1%26viewLayout%3D1

https://amcomx.invisionapp.com/auth/sign-in?redirectTo=%2Foverview%2FSpecs_CV_ROKU_FHD_TelmexComercial-clsi7mw2h00sn01990qnjfngo%2Fscreens%3FsortBy%3D3%26sortOrder%3D1%26viewLayout%3D1



## GHCallApi

-----

#### Tips



-----

##### HT004 - Preservación del user_token

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



##### HT005 - Renovación del token

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



##### HT006 - Utilización user_token como parámetro de autenticación

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*Como seria el flujo de las llamadas a estos nuevos serivios (Orden)?*
R. Se compartirá el diagrama de los nuevos servicios.

 

*¿Los servicios van a ser aplicados para todas las regiones sin importar que este activada la funcionalidad de CV con publicidad?*
R. No, si está activada CV con publicidad se debe seguir los nuevos flujos, de otra forma se debe seguir los flujos actuales.

 

*3.Plataforma AAF validar la versión del servicio ya que en BRF-11107 se utilizo la versión 2 e identificar en el flujo de APIs si cuadra para OTT.*
R. No se hace mención a una versión, si no, a la que actualmente se encuentra productiva con la v2.



##### HT006 

AFE-175 https://dlatvarg.atlassian.net/browse/AFE-175

*El HKS ya se elimina de todos los servicios como parámetro de entrada? al menos para usuario anónimo ya que la startheaderinfo con el nuevo versionado ya no lo regresa*
R. Utilización user_token como parámetro de autenticación se anexa el documento BRF-11088 - FE - IPTV | Uso de user_token como autenticación principal donde se indica las APIs, que tendrían ese impacto y los servicios que no estén contemplados en el listado anterior, deberían seguir trabajando con el hks.



## NO SOPORTADO

##### HT027 : --

carrusel html

**NO SOPORTADO**



1. Aplica para BE y CDS.
2. Generar un nuevo type que se pueda configurar en cualquier layout.
3. Debe integrarse en el servicio de ***cms/level***
4. Dentro de las propiedades debe contener el **HTML** configurado, el cual será proporcionado al equipo de CDS.



##### HT028 : --

ruta html

**NO SOPORTADO**



1. Aplica para Web.
2. Generar una ruta que pueda ser editorializad y tomada  dentro de las aplicaciones móviles que permita la visualización de contenido HTML.
3. La vista web debe ser receptiva y adaptarse a diferentes tamaños de pantalla para garantizar una experiencia de usuario consistente en diferentes dispositivos.
4. La vista web debe ser capaz de cargar lo que se haya editorializado en la cms/level de la  **HT027.**



##### HT029 : ??

**REVISAR**

**NO SOPORTADO**



1. Aplica para CDS  
2. Se deberá editorializar en el árbol de navegación el nodo Premium en las regiones y dispositivos que se requiera por la operación.
3. Se deberán editorializar 2 carruseles para ese nodo:
   1. Nuevo carrusel HTML,  especificado en la **HT027**
   2. Nuevo carrusel Selector de planes especificado en la **HT011**
4. Para el carrusel HTML se deberá configurar:
   1. Para los dispositivos móviles:
      1.  Configurar una URL
   2. Para el resto de dispositivos:
      1. HTML
5. Durante el desarrollo se puede requerir un cambio sobre la configuración del punto 4 por complicaciones que se puedan tener.



##### HN028

AFE-171 https://dlatvarg.atlassian.net/browse/AFE-171

*¿Qué tipo de contenido HTML se incluirá, solo texto enriquecido? , es decir tanto imagen como texto vienen en el HTML o solo es una parte ? No queda claro respecto a como se visualiza en bocetos.*
R. El HTML proporcionado por el equipo de CDS ya vendrá con todos los elementos que se requieren visualizar (textos e imágenes).



##### HT028 - Render HTML Se tiene las siguientes dudas :

AFE-174 https://dlatvarg.atlassian.net/browse/AFE-174

*Quíen va a definir cual va a ser la ruta?*
R. En web y aaf no habría tema en recibir el HTML directo del servicio pero en los móviles s si tendría tema por eso se requiere una nueva ruta para que los móviles la rendericen un web view



*El Equipo de web va a ser el encargado de proporcionar el html ?*
R. La URL la define el dispositivo Web.



*Del lado de web se tiene que tener las 2 opciones es decir se tiene que tener la ruta y cuando el usuario ingrese al nodo premium en la web se debe de consultar la llave y hacer el render del html ?*
R. UX debería de proporcionar el HTML como en el figma o invision. Cualquier cambio diferente se estaría notificando lo más pronto posible.





## PREGUNTAR

##### HN022 : ?? Sur a Norte

1. Se debe ***mantener*** la funcionalidad actual de ***aprovisionamiento*** de ***Sur a Norte*** y ***Norte a Sur*** para:
   1. Suscripciones
   2. PPE
   3. Compras.



##### HN039 : !! Nodo Telmex

1. Aplica solo para la modalidad  Freemium.
2. Se debe incluir el nodo Telmex en el menú de navegación, donde de acuerdo a lo requerido por la operación:
   1. El nodo debe poder habilitarse o deshabilitarse
   2. El nombre del nodo debe ser configurable
   3. La posición del nodo debe ser configurable
3. Por default el nodo Telmex debe visualizarse posterior al nodo Premium, donde:
   1. Tiene prioridad la posición del nodo requerida por la operación.
   2. Si el nodo Premium no se encuentra habilitado entonces se recorre la posición a la izquierda
4. Cuando el usuario seleccione el nodo Telmex se le debe dirigir al formulario correspondiente.



##### HN040 : !! Nodo Telmex

1. Aplica solo para la modalidad  Freemium.
2. Cuando el usuario seleccione el nodo Telmex en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes/imágenes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
3. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.
4. Un botón accionable que debe dirigir al usuario al flujo transaccional definido para la modalidad Freemium.



##### HN041 :  !! Nodo Telcel

1. Aplica solo para la modalidad  Freemium.
2. Se debe incluir el nodo Telcel en el menú de navegación, donde de acuerdo a lo requerido por la operación:
   1. El nodo debe poder habilitarse o deshabilitarse
   2. El nombre del nodo debe ser configurable
   3. La posición del nodo debe ser configurable
3. Por default el nodo Telcel debe visualizarse posterior al nodo Telmex, donde:
   1. Tiene prioridad la posición del nodo requerida por la operación.
   2. Si el nodo Telmex no se encuentra habilitado entonces se recorre la posición a la izquierda.
4. Cuando el usuario seleccione el nodo Telmex se le debe dirigir al formulario correspondiente.



##### HN042 :  !! Nodo Telcel

1. Aplica solo para la modalidad  Freemium.
2. Cuando el usuario seleccione el nodo Telcel en el menú de navegación, se le debe dirigir a una pantalla que cumpla con lo siguiente:
   1. La pantalla puede contener más de un módulo
   2. Cada módulo puede incluir diferentes tipos de elementos (carrusel, artes, etc).
   3. El orden de visualización de los elementos en la pantalla deben ser dinámicos por operación.
3. Por default esta pantalla debe incluir dos módulos:
   1. Un primer módulo que inicialmente incluya una imagen.
   2. Un segundo módulo que incluya el selector de planes.
4. Un botón accionable que debe dirigir al usuario al flujo transaccional definido para la modalidad Freemium.



##### HN043 : !! Accióm nodo Telmex / Telcel

1. Aplica para la pantalla informativa de los nodos: Premium, Telmex, Telcel.
2. La pantalla informativa debe permitir la inclusión de una opción accionable, de tipo:
   1. Link
   2. Botón
3. La opción accionable debe dirigir al usuario al flujo definido por la operación.







## DESCARTAR

##### HN038 : IPs de Bitmovin

1. Se debe habilitar en el Whitelist de la aplicación las siguientes IPs de Bitmovin para llevar a cabo la iniciativa de Claro Video con Publicidad en el ambiente UAT de Global en México:
   - *************
   - ************
   - ************



##### HT009 : --

cancelada



##### HT012 : --

cancelada



##### HT015 : --

cancelada



##### HT021 : --

Bitmovin

**SOLO AAF**



1. Aplica para AAF.
2. Integrar el reproductor Bitmovin para la reproducción de contenidos VOD y Publicidad.
3. Se mantendrán los reproductores nativos para la reproducción de contenidos LIVE.
4. Se debe asegurar que la experiencia actual (UX/UI) en el Player de no se vea afectada al implementar Bitmovin en la iniciativa de Claro video con publicidad.
5. Los dispositivos y modelos contemplados dentro de la implementación son:
   1. STV Samsung Tizen 2016+
   2. STV LG Webos 2016+
   3. STV Hisense 2016+
   4. STV Zeasn
   5. STV Netrange
   6. PS4



##### HT024 : --

videos

**SOLO PARA DISEÑO**



1. Generar video con las siguientes características para poder ser integrado a las campañas del Ad Manager
   1. Formato: mp4
   2. Duración: 30 segundos 
   3. Tamaño: 800x600



##### HT026 : --

**NO SOPORTADO EN**



1. Los dispositivos que no soportan la integración y en donde no se implementara la iniciativa son:
   1. Huawei mobile y tablet
   2. STV Samsung Orsay 2012-2015
   3. STV LG Netcast 2013
   4. STV LG Webos 2014-2015
   5. STV Sony (que no son ATV)
   6. Windows 
   7. Xbox
   8. STB Coship 9085
   9. STB Coship 9090
   10. STB Kaon
   11. STB Huawei
   12. STB ZTE AOSP
   13. Arris



##### HT031 : ??

Dispositivos sin medio de pago asociado

**REVISAR, entiendo que no aplica.**



1. Aplica para ADR
2. Se deben considerar las mismas políticas de Google en dispositivos Android para transaccional en la plataforma solicitadas en su momento en el BRF: https://dlatvarg.atlassian.net/browse/BRF-6976



##### HT032 : --

navecación iOS

**NO APLICA**



##### HT033 : --

Aplica para Web

**NO APLICA**



##### HN014 - Contenidos incluidos en alcance

AFE-165 https://dlatvarg.atlassian.net/browse/AFE-165

*Del lado de OTT no queda claro el punto 1.c (No hay contenidos de terceros)* 
R Hoy no hay contenidos de terceros pero puede haber, se deja la HU por si durante el desarrollo se solicita en algún pedido operativo. 



*Del lado de IPTV no se tiene Disney y tampoco aplica para IPTV por lo que entendemos en la HUN23 - 1.f.i “El alcance no incluye la implementación en los STB“* 
R: Correcto, no hay afectación en IPTV                       



##### HN024 - Generar reportes

AFE-166 https://dlatvarg.atlassian.net/browse/AFE-166

*e entiende que esos reportes son del equipo de BE o es en conjunto con las métricas q se solicitaran a FE? Pendiente conocer las métrica que se deben enviar y en que herramienta (YB, G analytics) ?*
R: Basado en la información o navegación que hace el usuario y es para el equipo de Métricas y al inicio del BRF se encuentra el documento Métricas del producto Claro Video  y se encuentra pendiente el doc de Google Analytics al finalizar el día.



