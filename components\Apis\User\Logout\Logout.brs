sub DataInit()
  m.top.debug = true

  print "+++++++++++++++++++++++++++++++++++++++++"
  print ghListSectionData()
  print ghListSectionData("user")
  print "+++++++++++++++++++++++++++++++++++++++++"

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/logout"
  m.api.query.Append({
    "HKS": ghGetRegistry("HKS")
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
  end if

end sub

sub ProcessData(res, raw)
  response = res.response

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ghSetRegistry("isLoggedIn", "false")
  ghDeleteSectionRegistry()
  ghDeleteSectionRegistry("user")

  m.top.content = { status: "OK" }

  if m.top.debug then print ghLogHead();"Body = ";response
end sub