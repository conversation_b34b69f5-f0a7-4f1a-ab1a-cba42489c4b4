' HUBGATE

sub PurchaseHubGate()
  m.logger.debug("Purchase HubGate init")

  HubGateRun()
end sub

sub HubGateRun(newState = invalid, info = {})
  m.logger.debug("HubGateRun", { state: newState, info: info })


  setLoading(false)

  if newState = invalid then
    HubGateCheck()

  else if newState = "go" then
    setLoading(true)
    HubGateGo()

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")
  end if
end sub

sub HubGateCheck()
  data = ghGetChild(m.buy.states, "purchase.paymentMethod")

  m.logger.debug("Purchase HubGate Check", { data: data })

  if data <> invalid then
    m.buy.states["purchase"].method.parameters = {
      link: ghGetChild(data, "data.buylink", "")
      buyToken: ghGetChild(data, "data.buyToken", "")
      object_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(m.buy, "data.access_code", "")
    }
    HubGateRun("go")
  else
    HubGateRun("missingparameters")
  end if
end sub

sub HubGateGo()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")

  m.logger.debug("Purchase HubGate Go", { data: data })

  if data <> invalid then
    if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
      apiConfirm = ghCallApi("BuyConfirmLite", "HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      apiConfirm.setFields(data)
      apiConfirm.control = "run"

    else if inStr(1, ghGetChild(data, "link", ""), "/confirm") > 0 then
      apiConfirm = ghCallApi("PaywayConfirmLite", "HubGateGo_ReturnOk", "HubGateGo_ReturnFails", false)
      apiConfirm.setFields({ buylink: ghGetChild(data, "link", "") })
      apiConfirm.control = "run"

    else
      HubGateRun("missingparameters")
    end if
  end if
end sub

sub HubGateGo_ReturnOk(event)
  data = event.getData()

  m.logger.debug("Purchase HubGate Go Ok", { data: data })

  HubGateRun("ok")
end sub

sub HubGateGo_ReturnFails(event)
  data = event.getData()

  m.logger.error("Purchase HubGate Go Fails", { data: data })

  HubGateRun("error", { data: data })
end sub