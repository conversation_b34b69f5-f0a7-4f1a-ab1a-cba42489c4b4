sub Init()
  m.top.debug = true

  LogClear()
  m.logger.debug("Init")

  m.map = {
    "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid },
    "error": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  }

  m.logo = m.top.findNode("logo")
  m.title = m.top.findNode("title")
  m.userName = m.top.findNode("userName")
  m.userName.font = ghGetFont(21, "regular")
  m.avatar = m.top.findNode("avatar")
  m.theGrid = m.top.findNode("theGrid")
  m.loading = m.top.findNode("loading")

  initData()
  drawComponents() ' parámetros de los componentes
end sub

sub drawComponents()
  m.logger.debug("drawComponents.")
  ' logo
  m.logo.setFields({
    translation: [100, 20]
    width: 123

    height: 24
    loadDisplayMode: "scaleToFit"
    uri: ghGetAssetByMode("overhang_logo_cv", "pkg:/images/logo.png")
  })
  ' title
  m.title.setFields({
    text: ghTranslate("avatarSelector_access_title_label", "Seleccioná imagen para tu perfil", {})
    font: ghGetFont(32, "bold")
    horizAlign: "left"
    translation: [80, 110]
    width: 800
    height: 70
  })
  ' avatar
  m.avatar.setFields({
    translation: [1130, 85]
    width: "96"
    height: "96"
    uri: "https://clarovideocdn5.clarovideo.net/pregeneracion/cms/apa/uat_531eed34tvfy7b73a818a234/avatar01.png"
  })

  m.theGrid.translation = [0, 200]
  m.theGrid.itemComponentName = "ItemPolymorphic"
  m.theGrid.itemSize = [1280, 210]
  m.theGrid.itemSpacing = [0, 0]
  m.theGrid.rowLabelOffset = [50, 15]
  m.theGrid.showRowLabel = [true]
  m.theGrid.rowFocusAnimationStyle = "fixedFocusWrap"
  m.theGrid.rowCounterRightOffset = 0
  m.theGrid.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
  m.theGrid.ObserveField("value", "OnCardSelect")
end sub

sub initData()
  m.logger.debug("initData.")
  ghCallApi("ProfileAvatarsLite", "initDataOk", "initDataFail")
end sub

sub initDataFail(event)
  data = event.getData()
  m.logger.debug("initDataFail.", { data: data })
end sub

sub onProfile(event)
  data = event.getData()
  m.logger.debug("onProfile data=", { data: data })
  refresh()
end sub

sub refresh()
  data = m.top.profile
  m.logger.debug("refresh =", { data: data })
  if data <> invalid then ' -------------
    ' campos
    m.avatar.uri = data.user_image
    m.userName.text = data.firstname
  end if
end sub

sub onWasShown() ' event
  m.logger.debug("onWasShown.")
  turnFocusTo("botonera")
end sub

sub OnCardSelect(event)
  data = event.getData()
  print "*********** -------------------------"
  print data
  print data.data
  print data.data.data
  print "*********** -------------------------"
  m.logger.debug("OnCardSelect.", { data: data.data?.data })
  m.top.result = { cmd: "select", data: data.data?.data }
  m.top.focus = false
  m.top.close = true
end sub

sub BackTo() ' event vuelta a la landing
  m.logger.debug("BackTo")
  m.top.result = { cmd: "back", data: {} }
  m.top.focus = false
  m.top.close = true
end sub

sub BackFromError() ' vuelvo desde error
  turnFocusTo("botonera")
end sub

function onKeyEvent(key, press) as boolean
  m.logger.debug("onKeyEvent", { key: key, press: press })
  handled = false
  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
      handled = true
      m.logger.debug("onKeyEvent -- focusedChild ", { focus: m.top.focusedChild.id })
    else
      m.logger.debug("onKeyEvent -- BACK!")
      m.top.result = "cancel"
      BackTo()
    end if
  end if
  return handled
end function

sub initDataOk(event)
  data = event.getData()
  m.logger.debug("initDataOk.", { rowCount: data.rowcount })
  result = GridContent(data.content)
  refreshTypesGrid(result, m.theGrid)

  m.theGrid.numRows = 3
  m.theGrid.content = result

  turnFocusTo("theGrid")

  m.logger.debug("initDataOk.", { rowCount: data.rowcount })
end sub

function GridContent(rows)
  m.logger.debug("GridContent, cantdidad de rows =", { rowsCount: rows.Count() })

  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.type = "ProfileSelect"
  obCinta.visible = true

  grilla = CreateObject("roSGNode", "GHContent")

  for each row in rows
    rowObject = CreateObject("roSGNode", "GHContent")
    rowObject.title = ghTranslate(row.title, "* " + row.title)
    rowObject.visible = true
    rowObject.type = "ProfileAvatarLite"
    rowObject.oldType = "ProfileAvatarLite"

    for each item in row.items
      itemObject = CreateObject("roSGNode", "GHContent")
      ' itemObject.cantTotal = row.items.Count()
      itemObject.id = item.id
      itemObject.title = item.title

      data = item.data
      data.rowtype = "ProfileAvatarLite"
      data.cantTotal = row.items.Count()
      itemObject.data = data

      rowObject.appendChild(itemObject)
    end for

    grilla.appendChild(rowObject)
  end for

  return grilla
end function