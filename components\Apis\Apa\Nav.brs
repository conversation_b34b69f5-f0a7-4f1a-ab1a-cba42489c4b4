sub DataInit()
  ' m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/nav/data"

  typeNav = ghGetChild(m.global, "config.nav", "nav")
  m.api.query.Append({
    ' PRUEBA DE OTRA REGION
    ' TODO: la NAV no hay que llamarla si el tipo no esta logeada.
    ' "region": "peru",
    "region": ghGetRegistry("region"),
    "type": typeNav
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- query="m.api.query
  end if
end sub

sub ProcessData(res, raw)

  if res = invalid then
    m.top.error = {
      code: 500,
      raw: raw,
      message: "Error en la respuesta del servidor."
    }
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  if m.top.debug then
    print "."
    print "."
    print "."
    print "--------------------------------------------------"
    print "--------------------------------------------------"
    print "--------------------------------------------------"
    print ghLogHead();"ProcessData -- res = ";res
    ' print ghLogHead();"ProcessData -- raw = ";raw
    for each item in res.response.nodes
      print "--------------------------------------------------"
      print "<<< --- >>>"
      print item
      if item.childs <> invalid then
        for each ch in item.childs
          print "-----"
          print ch
          print "-----"
        end for
      end if
      print "<<< --- >>>"
    end for
    print "--------------------------------------------------"
    print "--------------------------------------------------"
    print "--------------------------------------------------"
    print "."
    print "."
    print "."
  end if

  childs = getNodeChilds(ghGetChild(res.response, "nodes", []))
  m.global.setFields({
    nav: {
      items: ghGetChild(res.response, "nodes", []),
      childs: childs
    },
  })

  m.top.content = res
end sub

function getNodeChilds(nodes)
  childs = []
  for i = 0 to nodes.count() - 1
    item = nodes[i]
    if item.childs <> invalid then
      childs.push(item)

      childs01 = item.childs
      for i01 = 0 to childs01.count() - 1
        item01 = childs01[i01]
        if item01.childs <> invalid then
          childs.push(item01)
        end if
      end for
    end if
  end for

  return childs
end function
