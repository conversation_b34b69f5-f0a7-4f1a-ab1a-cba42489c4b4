<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="RegisterPage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="ProfileEditPageLite" extends="Page">

  <script type="text/brightscript" uri="ProfileEditPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <interface>
    <field id="profile" type="assocarray" onChange="onProfileEdit" />
    <field id="checkboxUri" type="string" alias= "fondoCheckbox.uri" />
    <field id="titleCheckboxText" type="string" alias= "titleCheckbox.text" />
    <field id="titleCheckboxFont" type="string" alias= "titleCheckbox.font" />
    <field id="checkboxText1Text" type="string" alias= "checkboxText1.text" />
    <field id="checkboxText1Font" type="string" alias= "checkboxText1.font" />
    <field id="checkboxText2Text" type="string" alias= "checkboxText2.text" />
    <field id="checkboxText2Font" type="string" alias= "checkboxText2.font" />
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />
    <Poster id="logo" translation="[64,32]" width="123" height="24" loadDisplayMode="scaleToFit"/>
    <!-- Texto sobre el input -->
    <Label id="overInput" text="*" visible="true" translation="[412,308]" width="480" height="24"/>


    <Label id="title" focusable="false" translation="[416,64]" width="186" height="48" text="Regístrate" />

    <Poster id="avatar" />
    <!-- Botonera -->
    <GHButtonGroup id="botonera">
      <GHButton id="changeImage" />
      <GHInput id="name" />
      <GHCheckBox id="kid" />
      <GHButton id="cancel" />
      <GHButton id="save" />
      <GHButton id="delete" />
    </GHButtonGroup>
    <!-- PopUps -->
    <GHError id="error" />
    <GHLoading id="loading" visible="false"/>
    <!--<GHToastMessage id= "toolTip" title="Estoy probando" visible="true" translation= "[640, 600]" time= "-1"/> -->
    <Poster id="chkError" uri="pkg:/images/HD/focus01.9.png" />
    <LayoutGroup id="toolTip" translation="[466,410]" layoutDirection="vert" itemSpacings="[-80]" visible="false">
      <LayoutGroup id="horizAligner" translation="[206,220]" layoutDirection="horiz" itemSpacings="[-290]" visible="true">
        <Poster id="fondoCheckbox" height="136" width="312" uri="pkg:/images/HD/focus01.9.png" />
        <LayoutGroup id="textsAlign" translation="[800,270]" layoutDirection="vert" itemSpacings="[5]" visible="true">
          <Label id="titleCheckbox" focusable="false" color= "#1A1A1A" horizAlign="left" vertAlign="bottom" translation="[500,84]" width="240" height="30" text="**" />
          <Label id="checkboxText1" focusable="false" lineSpacing="0" wrap="true" color= "#1A1A1A" translation="[416,64]" width="225" height="50" text="**" />
          <Label id="checkboxText2" focusable="false" lineSpacing="0" wrap="true" color= "#1A1A1A" translation="[416,64]" width="290" height="50" text="**" />
        </LayoutGroup>
      </LayoutGroup>
    </LayoutGroup>

  </children>

</component>
