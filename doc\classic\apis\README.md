# APIS

Tipos y llamados de apis de la aplicación.

## Listado

- [ApaSession](ApaSession/) : push de la session para actualizar.
- [IsLoggedIn](IsLoggedIn/) : chequea estado del usuario en servidor
- [Login](Login/) : loguea al usuario contra el servidor
- [Logout](Logout/) : mata la session del usuario.
- [StartHeaderInfo](StartHeaderInfo/) : arranque del flujo de iniciacion de aplicación.
- [Translations](Translations/) : strings y recursos de imagenes desde APA

---

## Pendientes

- Generación de un nuevo dispositivo para arrancar con APA en blanco.
- Revisar si el ApaSession es el correcto `session_push`.

---

## Anotaciones

### APA/SESSION

Anteriormente se utilizaba este servicio para concatenar el appKey con la región, se deprecó y esta concatenación debe hacerla la aplicación.

### APA/LAUNCHER

Se utiliza con 3 fines:

1. Se utiliza para trackear información relacionada al dispositivo en sumologic.
2. Anteriormente, se utilizaba para obtener el URL de MFWK. (ya no se utiliza con esta finalidad)
3. Se utiliza para implementar actualizaciones forzadas.
