# Graphics

## Status

6.1 Ok

6.2 Ok

6.3 Ok

6.4 Ok

6.5 Ok



## Items

**6.1** All submitted channels must have a non-zero version number. This number must be incremented for each build submitted and updated in the [channel manifest](https://developer.roku.com/docs/developer-program/getting-started/architecture/channel-manifest.md).

**6.2** Channels in the Kids & Family category only include content that is appropriate for children, do not include ads that are targeted based on user activity (behavioral advertising) and only include ads that are appropriate for children (for example, no graphic violence, adult situations, etc.).

**6.3** Public channels may not contain pornographic content. With respect to channel information that will or may appear outside of the application (for example, in search results, in the platform user interface, or on Roku’s website), content and descriptions must be appropriate for all ages. This includes channel name, artwork, and descriptions appearing in the Channel Store and web, as well as any content titles, artwork and descriptions appearing in Roku Search.

**6.4** The Channel Store artwork and splash screen clearly represent the name/identity of the channel using only broadcast-safe colors and are properly sized. (Channel splash screen resolutions: FHD: 1920x1080px; HD: 1280x720px.) In addition, artwork must not be transparent. The splash screen's URI is listed in the package manifest file.

**6.5** If the channel is pre-checked for installation during the device activation flow, the channel must be CVAA compliant.



