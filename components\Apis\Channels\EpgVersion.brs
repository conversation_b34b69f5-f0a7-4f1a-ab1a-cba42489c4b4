sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/epg/version"
  m.api.query.Append({
    "region": ghGetRegistry("region")
    "subregion": ghGetRegistry("subregion", "user")
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.global.setFields({
    epg: { version: ghGetChild(response, "epg_version") }
  })

  m.top.content = response
end sub