<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="HomePage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="HomePageLite" extends="Page">

  <script type="text/brightscript" uri="HomePageLite.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <!-- <field id="deeplink" type="node" onChange="OnDeepLink" /> -->
    <field id="deeplink" type="assocarray" onChange="OnDeepLink" />

    <!-- interfaz de accion -->
    <field id="title" type="string" value="" onChange="OnTitleChange" alwaysNotify="true" />
    <field id="nodo" type="string" value="" onChange="OnNodoChange" alwaysNotify="true" />
    <field id="order" type="integer" value="0" />
    <field id="contenido" type="assocarray" />
  </interface>

  <children>
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />
    <MenuComponent id="menu" translation="[40,20]" />
    <LevelComponent id="clientZone" translation="[0,100]" />
  </children>

</component>
