<?xml version="1.0" encoding="utf-8" ?>

<component name="GHGroupVert" extends="Group">

  <script type="text/brightscript" uri="GHGroupVert.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- parametros -->
    <field id="itemSpacing" type="int" value="0" onChange="OnRefresh" alwaysNotify="true" />
    <field id="centered" type="assocarray" value="{}" onChange="OnCentered" alwaysNotify="true" />
    <!-- acciones -->
    <field id="refresh" type="boolean" value="false" onChange="OnRefresh" alwaysNotify="true" />
    <field id="autoRefreshField" type="string" value="" onChange="OnRefresh" alwaysNotify="true" />
    <!-- internos -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children/>

</component>
