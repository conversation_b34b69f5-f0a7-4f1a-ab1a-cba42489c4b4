' TicketScreen

sub Init()
    m.top.debug = true
    m.logger = createLogger()

    ' Constantes para tipos de compra
    m.PRODUCT_TYPE_PREBUY = "CV_PREBUY" '--- subsc
    m.PRODUCT_TYPE_PRE_ESTEXC = "CV_PRE_ESTEXC"
    m.PRODUCT_TYPE_STDBUY = "CV_STDBUY"
    m.PRODUCT_TYPE_DOWNLOAD_BUY = "download_buy"

    ' Constantes para tipos de renta
    m.PRODUCT_TYPE_PRERENT = "CV_PRERENT"
    m.PRODUCT_TYPE_STDRENT = "CV_STDRENT" ' --  renta

    ' Constantes para episodios y temporadas
    m.PRODUCT_TYPE_EPISODEBUY = "CV_EPISODEBUY"
    m.PRODUCT_TYPE_SEASONBUY = "CV_SEASONBUY"

    ' Constantes para suscripciones
    m.ADDON_TYPE_SUBSCRIPTION = "subscrition"

    m.map = {
        "botonera": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
    }

    m.botonera = m.top.findNode("botonera")
    m.accept = m.top.findNode("accept")
    m.mediodepago = m.top.findNode("mediodepago")
    m.Vigencia = m.top.findNode("Vigencia")
    m.payMethodLabel = m.top.findNode("payMethodLabel")
    m.Vigenciahour = m.top.findNode("Vigenciahour")
    m.VigenciaReproducer = m.top.findNode("VigenciaReproducer")
    m.cancelbtn = m.top.findNode("cancelbtn")
    m.buyPrice = m.top.findNode("buyPrice")
    m.periodo = m.top.findNode("periodo")
    m.movieImage = m.top.findNode("movieImage")
    m.logoAddon = m.top.findNode("logoAddon")
    m.total = m.top.findNode("total")
    m.IvaInclude = m.top.findNode("IvaInclude")
    m.expirelabel = m.top.findNode("expirelabel")
    m.expirelabel.font = ghGetFont(17, "bold")
    m.title = m.top.findNode("title")

    componentsInit()
end sub

sub componentsInit()
    m.payMethodLabel.setFields({
        horizAlign: "center"
        text: ghTranslate("Transaccionales_Ticket_TextoMedioDePago2", "")
        visible: true
        font: ghGetFont(17, "regular")
    })

    m.accept.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoBotonPrimarioReproducir", "")
    })

    m.cancelbtn.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoBotonSecundarioCerrar", "")
    })

    m.mediodepago.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoMedioDePago1", "")
        font: ghGetFont(17, "regular")
    })

    m.total.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoTotal", "")
        font: ghGetFont(17, "regular")
    })

    m.botonera.ObserveField("selected", "OnButtonSelected")
    m.botonera.ObserveField("backSelected", "closeScreen")
end sub

sub updateFieldFocus()
    turnFocusTo("botonera")
end sub

function onKeyEvent(key, press) as boolean
    handled = false

    if press then
        if key = "back" then
            ' Handle back button - close the screen
            closeScreen("BACK")
            handled = true
        else
            changeFocusBasedOnKey(key)
            handled = true
        end if
    end if
    return handled
end function

sub OnButtonSelected(event)
    data = event.getData()
    child = event.getRoSGNode()
    m.logger.debug("OnButtonSelected", { option: child.value, data: data })
    if data then
        if child.value <> invalid then
            closeScreen(child.value)
        end if
    end if
end sub

sub closeScreen(value = "BACK", data = invalid)
    if value <> invalid then
        m.top.value = {
            option: value
            data: data
        }
    end if

    m.top.close = true
end sub

sub updateBuyData(event)
    data = event.getData()

    m.logger.debug("updateBuyData", { data: data })
    productData = extractProductData(data)
    setupCommonUI(productData)

    if productData.isAddon = m.ADDON_TYPE_SUBSCRIPTION then
        m.logger.debug("isAddon")
        setupSubscriptionUI(productData)

    else if isProductTypeBuy(productData.buyProductType, productData.isAddon) then
        m.logger.debug("isBuy")
        setupBuyUI(productData)

    else if isProductTypeRent(productData.buyProductType) then
        m.logger.debug("isRent")
        setupRentUI(productData)

    else if isEpisodeOrSeasonBuy(productData.buyProductType) then
        m.logger.debug("isEpisodeOrSeasonBuy")
        setupBuyUI(productData)

    else
        m.logger.debug("default")
        setupDefaultUI(productData)
    end if
end sub

'**********************************************************************
' Funciones auxiliares para extraer y organizar datos
'**********************************************************************
function extractProductData(data as object) as object
    contentType = ghGetChild(data, "content_type", "movie")

    productData = {
        buyType: ghGetChild(data, "buyType", "compra o renta"),
        buyPrice: ghGetChild(data, "buyPrice", "00.00"),
        buyCurrency: ghGetChild(data, "buyCurrency", "$"),
        buyProductType: ghGetChild(data, "buyProductType"),
        buyPeriodo: ghGetChild(data, "buyPeriodo", ""),
        oneoffertype: ghGetChild(data, "oneoffertype", ""),
        isAddon: ghGetChild(data, "buyIsAddon", invalid),
        family: ghGetChild(data, "buyFamily", invalid),
        banner: ghGetChild(data, "buyBanner", ""),

        contentImage: ghGetChild(data, "contentImagen", ""),
        contentTitle: ghGetChild(data, "contentTitle", ""),
        contentTitleSeason: ghGetChild(data, "contentTitleSeason", ""),
        fixedLineNumber: ghGetChild(data, "fixedLineNumber", invalid),
        payment_type: ghGetChild(data, "paymentMethod", "")

        ivaText: ghTranslate("doconfirm_iva_description", "IVA incluido")
        user_type: getUserTypeGA4(true),
        country: getCountryCode(ghGetRegistry("country_code", "user")),
        user_id: ghGetRegistry("user_id", "user"),
        screen_name: "ticketscreen",
        screen_class: "/ticketscreen"
    }

    ' GA4 Event for ticket screen
    ' productData1 = {
    '     content_availability: ghGetChild(data, "buyType", "compra o renta"),
    '     content_price: ghGetChild(data, "buyCurrency", "$") + ghGetChild(data, "buyPrice", "00.00"),
    '     subscriptions: ghGetChild(data, "buyFamily", invalid),
    '     content_id: ghGetChild(data, "contentId", ""),
    '     content_name: ghGetChild(data, "content_name", ""),
    '     content_type: contentType,
    '     content_category: ghGetChild(data, "content_category", ""),
    '     payment_type: ghGetChild(data, "paymentMethod", "")
    '     user_type: getUserTypeGA4(true),
    '     country: getCountryCode(ghGetRegistry("country_code", "user")),
    '     user_id: ghGetRegistry("user_id", "user"),
    '     screen_name: "ticketscreen",
    '     screen_class: "/ticketscreen",
    '     oneoffertype: ghGetChild(data, "oneoffertype", "download_buy"),
    ' }
    GA4Event("CheckOut_ticket", productData)

    productData.fullPrice = productData.buyCurrency + productData.buyPrice
    productData.formattedPrice = ghReplaceStr(productData.fullPrice, ".00", " ")

    return productData
end function

'**********************************************************************
' Funciones para configurar la UI
'**********************************************************************
sub setupCommonUI(productData as object)
    producttype = productData.buyProductType
    banner = productData.banner
    contentImage = ghGetChild(productData, "contentImagen", "")
    region = getCountryCode(ghGetRegistry("country_code", "user"))

    ' m.logoAddon.uri = ghGetAsset("Transaccionales_Checkout_LogoAddon_" + producttype, "")
    family = ghGetChild(productData, "buyFamily", "")
    if family <> "" then
        logoUri = ghGetAsset("transactional_" + family + "_logo")
        m.logoAddon.setFields({
            uri: logoUri
        })
        print"logoaddon" logoUri
    end if
    print"logoaddon" logoUri
    m.movieImage.setFields({
        uri: contentImage,
        height: 298,
        width: 160
    })

    ' Set common labels
    m.total.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoTotal", "")
        font: ghGetFont(17, "regular")
    })

    m.mediodepago.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoMedioDePago1", "")
        font: ghGetFont(17, "regular")
    })

    ' Setup payment method display if fixed line number is available
    if productData.fixedLineNumber <> invalid then
        setupPaymentMethodDisplay(productData.fixedLineNumber)
    end if
end sub

sub setupBuyUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    m.title.setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Ticket_TextoTitulo_" + oneoffertype, "Estás por comprar"),
        horizAlign: "center"
    })

    m.buyPrice.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.formattedPrice,
        horizAlign: "left"
    })

    m.periodo.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.ivaText,
        horizAlign: "left"
    })

    m.IvaInclude.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoImpuestos_" + oneoffertype, " "),
        font: ghGetFont(17, "regular"),
        horizAlign: "left"
    })

    m.top.findNode("oferta").setFields({ visible: "false" })
    m.top.findNode("textoInformativo").setFields({ visible: "false" })
    ' m.expirelabel.setFields({
    '     font: ghGetFont(18, "bold")
    '     text: ghGetChild(m.top.data, "options.data.end_date.day", "") + " " + ghTranslate(ghGetChild(m.top.data, "options.data.end_date.month", " "), "") + " " + ghGetChild(m.top.data, "options.data.end_date.year", "")
    '     horizAlign: "center"
    ' })
end sub

sub setupRentUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    m.title.setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Ticket_TextoTitulo_" + oneoffertype, "Estás por rentar"),
        horizAlign: "center"
    })

    m.buyPrice.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.formattedPrice,
        horizAlign: "left"
    })

    m.periodo.setFields({
        font: ghGetFont(17, "regular"),
        text: "/ " + productData.buyPeriodo,
        horizAlign: "left"
    })

    m.IvaInclude.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoImpuestos_" + oneoffertype, " "),
        font: ghGetFont(17, "regular"),
        horizAlign: "left"
    })
    ' m.payMethodLabel.setFields({
    '     translation:[68,400]
    ' })

    m.top.findNode("oferta").setFields({ visible: "false" })
    m.top.findNode("textoInformativo").setFields({ visible: "false" })
    ' Show Vigencia for rent
    m.Vigencia.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoVigencia", "")
        font: ghGetFont(17, "regular")
    })

    m.Vigenciahour.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoFecha1_" + producttype, "")
        font: ghGetFont(17, "regular")
    })

    m.VigenciaReproducer.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoFecha2_" + producttype, "")
        font: ghGetFont(17, "regular")
    })
    m.top.findNode("expirelabel").setFields({ visible: "false" })

    ' m.expirelabel.setFields({
    '     font: ghGetFont(18, "bold")
    '     text: ghGetChild(m.top.data, "options.data.end_date.day", "") + " " + ghTranslate(ghGetChild(m.top.data, "options.data.end_date.month", " "), "") + " " + ghGetChild(m.top.data, "options.data.end_date.year", "")
    '     horizAlign: "center"
    ' })
end sub

sub setupSubscriptionUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    m.title.setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Ticket_TextoTitulo_" + oneoffertype, "Estás por contratar"),
        horizAlign: "center"
    })

    producttype = productData.buyProductType
    m.top.findNode("oferta").setFields({
        font: ghGetFont(25, "bold"),
        text: ghTranslate("Transaccionales_Ticket_TextoOferta_" + producttype, ""),
        horizAlign: "left"
    })

    m.top.findNode("textoInformativo").setFields({
        font: ghGetFont(20, "regular"),
        text: ghTranslate("Transaccionales_Ticket_TextoPromocion_" + producttype, ""),
        horizAlign: "left",
        visible: "true",
        color: "#FFFFFF",
        wrap: "false"
    })

    m.buyPrice.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.formattedPrice,
        horizAlign: "left"
    })

    m.periodo.setFields({
        font: ghGetFont(17, "regular"),
        text: "/ " + productData.buyPeriodo,
        horizAlign: "left"
    })
    ' m.payMethodLabel.setFields({
    '     translation: [68, 400]
    ' })
    m.IvaInclude.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoImpuestos_" + oneoffertype, " "),
        font: ghGetFont(17, "regular"),
        horizAlign: "left"
    })

    ' Show Vigencia for subscription
    m.Vigencia.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoVigencia", "")
        font: ghGetFont(17, "regular")
    })
    m.top.findNode("VigenciaHour").setFields({ visible: "false" })
    m.top.findNode("VigenciaReproducer").setFields({ visible: "false" })
    ' m.expirelabel.setFields({
    '     font: ghGetFont(18, "bold")
    '     text: ghGetChild(m.top.data, "options.data.end_date.day", "") + " " + ghTranslate(ghGetChild(m.top.data, "options.data.end_date.month", " "), "") + " " + ghGetChild(m.top.data, "options.data.end_date.year", "")
    '     horizAlign: "center"
    ' })
end sub

sub setupDefaultUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    m.title.setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Ticket_TextoTitulo_" + oneoffertype, "Estás por adquirir"),
        horizAlign: "center"
    })

    m.buyPrice.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.formattedPrice,
        horizAlign: "left"
    })

    m.periodo.setFields({
        font: ghGetFont(17, "regular"),
        text: productData.ivaText,
        horizAlign: "left"
    })

    m.IvaInclude.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoImpuestos_" + oneoffertype, " "),
        font: ghGetFont(17, "regular"),
        horizAlign: "left"
    })
    ' m.expirelabel.setFields({
    '     font: ghGetFont(18, "bold")
    '     text: ghGetChild(m.top.data, "options.data.end_date.day", "") + " " + ghTranslate(ghGetChild(m.top.data, "options.data.end_date.month", " "), "") + " " + ghGetChild(m.top.data, "options.data.end_date.year", "")
    '     horizAlign: "center"
    ' })
end sub

'**********************************************************************
' Helper functions to determine product types
'**********************************************************************
function isProductTypeBuy(buyProductType as string, isAddon as dynamic) as boolean
    return buyProductType = m.PRODUCT_TYPE_PREBUY or buyProductType = m.PRODUCT_TYPE_PRE_ESTEXC or buyProductType = m.PRODUCT_TYPE_STDBUY or isAddon = m.PRODUCT_TYPE_DOWNLOAD_BUY
end function

function isProductTypeRent(buyProductType as string) as boolean
    return buyProductType = m.PRODUCT_TYPE_PRERENT or buyProductType = m.PRODUCT_TYPE_STDRENT
end function

function isEpisodeOrSeasonBuy(buyProductType as string) as boolean
    return buyProductType = m.PRODUCT_TYPE_EPISODEBUY or buyProductType = m.PRODUCT_TYPE_SEASONBUY
end function

'**********************************************************************
' Function to setup payment method display with masked number
'**********************************************************************
sub setupPaymentMethodDisplay(fixedLineNumber as string)
    ' Format the number to show only last 8 digits with masking
    maskedNumber = formatFixedLineNumber(fixedLineNumber)

    m.payMethodLabel.setFields({
        text: ghTranslate("Transaccionales_Ticket_TextoMedioDePago2", "") + " " + maskedNumber,
        font: ghGetFont(17, "regular"),
        horizAlign: "center"
    })
end sub

'**********************************************************************
' Function to format fixed line number with masking
'**********************************************************************
function formatFixedLineNumber(fixedLineNumber as string) as string
    if fixedLineNumber = invalid or fixedLineNumber.Len() < 8 then
        return "****"
    end if
    last8Digits = Right(fixedLineNumber, 8)
    last4Digits = Right(last8Digits, 4)
    maskedNumber = "****" + last4Digits
    return maskedNumber
end function






