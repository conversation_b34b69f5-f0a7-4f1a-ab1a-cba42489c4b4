sub DataInit()
  m.top.showCurl = false

  m.top.debug = false

  ' APA Data
  if ghGetChild(m.global, "GA4.api_secret") <> invalid then
    m.GA4 = m.global.ga4
    if m.top.debug then print ghLogHead();"DataInit -- GA4 from GLOBAL"
  else ' default si no encuentro nada
    m.GA4 = {
      measurement_id: "G-QLGDPW4C9J"
      api_secret: "dvfC6cJxS0W8DuM1UWzXjg",
    }
    if m.top.debug then print ghLogHead();"DataInit -- GA4 from HARDCO"
  end if

  ' DATOS
  m.api.method = "POST"
  m.api.name = "logs"
  m.api.url = "https://www.google-analytics.com/mp/collect" ' ?measurement_id=$(measurement_id)&api_secret=${api_secret}
  m.api.query = ({
    api_secret: m.GA4.api_secret,
    measurement_id: m.GA4.measurement_id
  })
  m.api.body = FormatJSON(m.top.event)
  m.api.headers.addReplace("Content-Type", "application/json")

  ' DEBUG
  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- query="m.api.query
    print ghLogHead();"DataInit -- body="m.api.body
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then print ghLogHead();"log ProcessData=[";res;"][";raw;"]"
  if m.top.debug then
    print ghLogHead();"ProcessData -- Response= ", m.lastResponse
  end if
  m.top.content = { result: "OK", code: m.lastResponse.code }
end sub