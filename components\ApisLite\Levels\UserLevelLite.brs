' LevelUserLite
' https://app.swaggerhub.com/apis/ClaroVideo/CMS_Leveluser/2.0.0#/CMS%20Leveluser/get_services_cms_v2_leveluser
' https://app.swaggerhub.com/apis/ClaroVideo/CMS_Leveluser/2.0.0#/

sub DataInit()
  ' m.top.debug = true

  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/cms/v2/leveluser"

  typeNav = ghGetRegistry("nav")

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  m.api.query.delete("user_id") ' sin user_id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") }) ' by CVRKS-1008
  ' hardco
  m.api.query.Append({
    ' no usa lasttouch
    ' "lasttouch": nowTimestamp.toStr(), ' ghGetRegistry("lasttouch_seen", "user"),
    ' "lasttouch": ghGetRegistry("lasttouch_seen", "user"),
    "node": m.top.node,
    "region": ghGetRegistry("region"),
    ' "user_hash": ghGetRegistry("session_userhash", "user"),
    "type": typeNav
    "module_version": "v2"
  })

  'user_hash=NTM1MjM2NTd8MTcxMTU1ODgwOHw5ODg2Yzg0ZDJhN2MxMzAxMGZlNmJkMjg3ODAxOGQ1NjIzYTUyY2U2Zjk0ZjY5M2M4MA%3D%3D
  m.logger.debug("DataInit -- api= ", { api: m.api, query: m.api.query })
end sub

sub ProcessData(res, raw)
  if res.errors <> invalid then
    m.logger.error("** ERROR **********************")
    m.top.error = res.errors
    return
  end if
  m.logger.info("---------------------------------------")
  result = []
  cintas = ghGetChild(res, "data.modules")
  for each cinta in cintas
    ' print " "
    ' print " "
    ' print " userlevel"
    ' print "-- cinta ----------------"
    ' print cinta
    ' print " "
    ' print " "
    ' print " "
    obCinta = CreateObject("roSGNode", "GHContent")

    m.logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    m.logger.debug("ProcessData -- cinta.name= ", { name: cinta.name })

    obCinta.id = cinta.name
    comps = ghGetChild(cinta, "components.component")
    for each comp in comps
      ' print " "
      ' print " "
      ' print " userlevel"
      ' print "-- comp ----------------"
      ' print comp
      ' print comp.properties
      ' print " "
      ' print " "
      ' print " "
      m.logger.debug("ProcessData -- comp.name= ", { name: comp.name, type: comp.type, properties: comp.properties })

      if comp.name = "header" then
        obCinta.title = comp.properties.large
      else if comp.name = "carrousel" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
          LongOKType: ghGetChild(comp, "properties.type")
        })

        if m.top.debug then
          for each prop in comp.properties.Items()
            m.logger.info("key", { key: prop.key, value: prop.value })
          end for
        end if

      else if comp.name = "plans_offer__v2" then
        obCinta.data = comp
        ghUtils_ForceSetFields(obCinta, {
          type: ghGetChild(comp, "type", "carrouselhorizontal")
          oldType: ghGetChild(comp, "type", "carrouselhorizontal")
          LongOKType: ghGetChild(comp, "properties.type", "")
        })

      else
        if m.top.debug then
          for each prop in comp.properties.Items()
            m.logger.info("key", { key: prop.key, value: prop.value })
          end for
        end if
      end if
    end for

    ' ------------------------------
    ' children vacios
    for v = 1 to 10
      vacio = CreateObject("roSGNode", "GHContent")
      vacio.title = "" ' "[vacio]"
      vacio.hdposterurl = "pkg:/images/loading.png"
      obCinta.appendChild(vacio)
    end for
    ' result.appendChild(obCinta)
    ' print "**************** >>>>>>>>>>>>>>> ";obCinta
    result.push(obCinta)
    m.logger.debug("cinta.name ", { name: cinta.name })

  end for
  m.logger.info("---------------------------------------")
  m.top.content = { cintas: result }
end sub



