sub Init()
  m.top.debug = true
  ' m.currentPos = 0
  m.top.ObserveField("visible", "onVisibleChange")
  ' -----------------------------
  m.map = {
    "joystick": { "up": invalid, "right": "botonera", "down": invalid, "left": invalid }
    "botonera": { "up": invalid, "right": "joystick", "down": invalid, "left": "joystick" }
  }
  ' -----------------------------
  ' joystick
  m.joy = m.top.findNode("joystick")
  m.joy.ObserveField("action", "onJoyStickAction")
  ' botonera
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnBotoneraSelected")
  m.botonera.ObserveField("backSelected", "OnBotoneraBack")
  ' definiciones
  ' -----------------------------
  m.top.findNode("pBackground").setFields({
    translation: [0, 0]
    width: 1280
    height: 170
    color: "#00000FCC"
  })
  m.top.findNode("lblAudios").setFields({
    translation: [760, 600]
    width: 500
    font: ghGetFont(24, "regular")
    focusable: false
    horizAlign: "right"
    text: ghTranslate("xxLBLCanales", "* Subtítulos")
    color: "#FFFFFF"
  })
  ' -----------------------------
  ' m.title = m.top.findNode("title")
  ' m.title.setFields({
  '   font: ghGetFont(48, "regular")
  '   horizAlign: "center"
  '   text: gh Translate("Canales", "Canales")
  '   color: "#999999"
  ' })
  ' -----------------------------
  ' m.languages = m.top.findNode("languages")
  ' m.languages.ObserveField("itemSelected", "onItemSelected")
  ' -----------------------------
end sub

sub onVisibleChange(event)
  data = event.getData()
  if data then ' entro----
    print ghLogHead();"onVisibleChange -ON- ";m.top.Xresult
    turnFocusTo("botonera")
    print
    print
    print
    print
    print
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "JUMPTO"
    print m.top.jumpto
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "CHANNELS"
    print m.top.channels
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "EVENTOS"
    print m.top.data
    print "canal----"
    print m.top.data.getChild(0)
    print "episodio---"
    print m.top.data.getChild(0).getChild(0)
    ' print "episodio---item"
    ' print m.top.data.getChild(0).getChild(0).item
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "INFO"
    print m.top.info
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "ACTUAL >>>>"
    print m.top.channels[m.top.jumpto]
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
    print
    print
    print
    print
    print

    actual = m.top.channels[m.top.jumpto]
    m.joy.title = actual.label_large

  else ' salgo ------------------
    print ghLogHead();"onVisibleChange -OFF- ";m.top.Xresult
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub
' sub onResult(event)
'   print ghLogHead();"onResult : ";m.top.Xresult, event.getData()
' end sub

sub onJoyStickAction(event)
  key = event.getData()
  print ghLogHead();"onJoyStickAction ((( key=";key;" )))"
  ' -----------------------------
  if key = "up" then
    m.top.jumpto++
    if m.top.jumpto > m.top.channels.Count() then
      m.top.jumpto = 0
    end if
    refreshJoystick()
  else if key = "down" then
    m.top.jumpto--
    if m.top.jumpto <= 0 then
      m.top.jumpto = m.top.channels.Count()
    end if
    refreshJoystick()
  else if key = "back" or key = "left" then ' left es igual a back
    m.top.visible = false
  else if key = "right" then
    turnFocusTo(guessFocusTo(key))
  end if
  m.joy.title = key
  ' -----------------------------
end sub
sub refreshJoystick()
  ' actual = m.top.channels[m.top.jumpto]
  ' print "***", m.top.jumpto
  ' print "***", actual
  ' m.joy.title = actual.label_large
end sub

sub OnBotoneraSelected(event)
  child = event.getRoSGNode()
  if child.selected then
    child.selected = false
    print "--------------------------------------------------"
    if m.top.debug then print ghLogHead();">>>>>>>>>> OnBotoneraSelected -- ";child
    print "--------------------------------------------------"
    if child.value = "masUno" then ' +1
      m.top.cmd = {
        cmd: "variable",
        xresult: m.top.Xresult + 1
      }
    else if child.value = "masDiez" then ' +10
      m.top.cmd = {
        cmd: "variable",
        xresult: m.top.Xresult + 10
      }
    else if child.value = "menosDiez" then ' -10
      m.top.cmd = {
        cmd: "variable",
        xresult: m.top.Xresult - 10
      }
    else if child.value = "menosUno" then ' -1
      m.top.cmd = {
        cmd: "variable",
        xresult: m.top.Xresult - 1
      }
    end if
    print m.top.cmd
    if m.top.cmd.cmd = "variable" then
      m.top.findNode("title").text = ">>>> Result [" + Str(m.top.cmd.xresult) + "]"
    end if
  end if
end sub
sub OnBotoneraBack(event)
  data = event.getData()
  if data then
    print "--------------------------------------------------"
    if m.top.debug then print ghLogHead();">>>>>>>>>> OnBotoneraBack -- ";data
    print "--------------------------------------------------"
    m.top.visible = false
  end if
end sub

' -----------------------------
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  if m.top.visible = true then
    if press then
      if key <> "back" then
        turnFocusTo(guessFocusTo(key))
        if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
      else
        m.top.visible = false
      end if
      handled = true
    end if
  end if
  return handled
end function
function guessFocusTo(direction)
  focusTo = invalid
  current = getCurrentFocus()
  if current <> invalid then
    ' a donde voy?
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    else
      focusTo = current
    end if
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
      m.top.findNode(id).setFocus(true)
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if

  return current
end function
' FIELDS
' -----------------------------
sub onChannelsUpdate() 'event
end sub
sub onJumpTo(event)
  data = event.getData()
  m.currentPos = data
end sub
' sub onItemSelected(event)
'   data = event.getData()
'   if data <> invalid then
'     print ghLogHead();"onItemSelected >>----> ";m.languages.content.getChild(data)
'     print ghLogHead();"onItemSelected >>----> ";data
'     m.languages.setFocus(false)
'     m.currentPos = data
'     data = ghGetChild(m.languages.content.getChild(data), "data")
'     m.top.selected = data
'   end if
' end sub
