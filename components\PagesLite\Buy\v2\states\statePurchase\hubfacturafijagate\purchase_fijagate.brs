' FijaGate

sub PurchaseHubFacturaFijaGate()
    m.logger.debug("Purchase PromoGate Init")
    FijaGateRun(invalid)
end sub

sub FijaGateRun(newState = invalid, info = {})
    ' m.logger.debug("PromoGateRun", { state: newState, info: info })
    print "FijaGateRunlogs" ; newState
    setLoading(false)
    if newState = invalid then
        ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
        ' ScrCodigo.checkoutFieldType = "hubfacturafijagate"
        ScrCodigo.id = "FijaGate"
        ScrCodigo.ObserveField("wasClosed", "FijaGate_Return")

        ' Get buyData from the current buy flow
        buyData = {
            buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
            buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
            buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
            buyProductType: ghGetChild(m.buy, "data.button.producttype")
            buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
            buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
            buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
            buyBanner: ghGetChild(m.buy, "data.button.banner", "")
            oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

            contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
            contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
            contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
            contentId: ghGetChild(m.buy, "data.contentId", "")
            content_name: ghGetChild(m.buy, "data.content_name", "")
            content_type: ghGetChild(m.buy, "data.content_type", "")
            content_category: ghGetChild(m.buy, "data.content_category", "")

            paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
            screen_name: "hubfacturafijagate",
            screen_class: "/hubfacturafijagate",
        }

        GA4Event("purchase_fijagate", buyData)

        m.top.routerChild = { page: ScrCodigo,
            fields: {
                checkoutFieldType: "hubfacturafijagate"
                buyData: buyData
            }
        }
    else if newState = "back" then
        JumpTo("checkout")

    else if newState = "go" then
        setLoading(true)
        apiConfirm = ghCallApi("BuyConfirmLite", "FijaGate_Payway_Return", "FijaGate_Payway_ReturnError", false)

        if ghGetChild(info, "serviceId", "") <> invalid and ghGetChild(info, "serviceId", "") <> ""
            if ghGetChild(info, "document", "") <> invalid and ghGetChild(info, "document", "") <> ""
                ' Colombia case with document
                extraParams = {
                    "claveServicio": ghGetChild(info, "pin", ""),
                    "service_id": ghGetChild(info, "serviceId", ""),
                    "document": ghGetChild(info, "document", "")
                }
            else
                ' Regular case with serviceId (Dominicana or standard)
                extraParams = {
                    "claveServicio": ghGetChild(info, "pin", ""),
                    "service_id": ghGetChild(info, "serviceId", "")
                }
            end if
        else
            ' Default case with only pin
            extraParams = { "claveServicio": ghGetChild(info, "pin", "") }
        end if

        apiConfirm.setFields({
            link: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
            buyToken: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buyToken")
            extra_params: extraParams
        })
        apiConfirm.control = "run"

    else if newState = "ok" then
        JumpTo("purchase", "ok")

    else if newState = "error" then
        showMessageError(ghGetChild(info, "data", {}))

    else
        JumpTo("purchase", "fail")

    end if
end sub

sub FijaGate_Return(event)
    scr = event.getRoSGNode()

    ' Add null check to prevent "Dot" operator error
    if scr <> invalid and scr.value <> invalid then
        if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
            FijaGateRun("back")

        else if scr.value.opcion = "SELECT" then
            if scr.value.multiLineValue <> invalid and scr.value.multiLineValue <> " "
                FijaGateRun("go", { pin: scr.value.fixedLineValue, serviceId: scr.value.multiLineValue })
            else if scr.value.fixedLineValueDomnicana <> invalid and scr.value.multiLineValueDomnicana <> invalid
                FijaGateRun("go", { pin: scr.value.fixedLineValueDomnicana, serviceId: scr.value.multiLineValueDomnicana })
            else if scr.value.fixedLineValueColombia <> invalid and scr.value.multiLineValueColombia <> invalid and scr.value.tripleLineValueColombia <> invalid
                FijaGateRun("go", { document: scr.value.fixedLineValueColombia, pin: scr.value.multiLineValueColombia, serviceId: scr.value.tripleLineValueColombia })
            else if scr.value.fixedLineValueEcuador <> invalid
                FijaGateRun("go", { pin: scr.value.fixedLineValueEcuador })
            else
                FijaGateRun("go", { pin: scr.value.fixedLineValue })
            end if
        end if
    else
        ' Handle invalid scr object - default to back action
        print "FijaGate_Return: Invalid scr object, defaulting to back"
        FijaGateRun("back")
    end if
end sub

sub FijaGate_Payway_Return(event)
    data = event.getData()
    print "FijaGate_Payway_Return" ; data

    m.logger.debug("CodigoPromo_Payway_return", { data: data })

    FijaGateRun("ok", { data: data })
end sub

sub FijaGate_Payway_ReturnError(event)
    data = event.getData()
    print "FijaGate_Payway_ReturnError" ; data

    m.logger.error("CodigoPromo_Payway_returnError", { data: data })

    FijaGateRun("error", { data: data })
end sub