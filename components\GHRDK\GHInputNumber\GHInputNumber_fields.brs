' GHButton
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

' INPUT
' -----------------------------
sub updateFieldText()
  ' nada
end sub
sub updateFieldmaxTextLength() ' TODO -- revisar, no hace nada?
  ' print "*** maxText ";m.input.maxTextLength
  if m.top.maxTextLength <> invalid then
    m.input.maxTextLength = val(m.top.maxTextLength, 10)
    ' print "*** maxText ";m.input.maxTextLength
  end if
end sub
sub updateFieldColor()
  recalcColors()
end sub
sub updateFieldSelColor()
  recalcColors()
end sub
sub updateFieldPlaceholderColor()
  recalcColors()
end sub
' Input + dialog
' -----------------------------
sub updateFieldPassword()
  m.input.secureMode = m.top.password
end sub
sub updateFieldTranslation()
  if m.top.translation <> invalid then
    recalcSizeAndPadding()
  end if
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then
    recalcSizeAndPadding()
  end if
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then
    recalcSizeAndPadding()
  end if
end sub
'  BORDER
' -----------------------------
sub updateFieldFocusPadding()
  recalcSizeAndPadding()
end sub
' GENERAL
' -----------------------------
sub updateFieldFocus() ' event
  if m.top.focus then
    m.top.setFocus(true)
    m.input.setFocus(true)
  end if
  recalcColors()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub
' FUNCIONES
' -----------------------------
sub recalcColors()
  if m.top.focus then
    m.input.textColor = m.top.selColor
    m.input.hintTextColor = m.top.selColor
    m.border.visible = true
  else
    m.input.textColor = m.top.color
    m.input.hintTextColor = m.top.placeholdercolor
    m.border.visible = false
  end if
end sub
sub recalcSizeAndPadding()
  if m.top.debug then print ghLogHead();"recalcSizeAndPadding"
  width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))
  paddingFocus = ghXtoAbstract(val(m.top.focusPadding))
  paddingFocusSize = paddingFocus * 2

  m.border.translation = "[0,0]" ' el unico que va en la misma posicion
  m.border.height = height
  m.border.width = width

  m.input.translation = ghVal2Trans(paddingFocus, paddingFocus)
  m.input.width = width - paddingFocusSize
  m.input.height = height - paddingFocusSize
end sub
' END FILE