<?xml version="1.0" encoding="utf-8" ?>
<component name="GHProgressBar" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHProgressBar.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <field id="value" type="float" value="100" onChange="refresh" />
    <!-- position -->
    <!-- <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" /> -->
    <field id="width" type="float" value="150" alias="fondo.width" onChange="refresh"/>
    <field id="height" type="float" value="8" alias="fondo.height" onChange="refresh"/>
    <field id="padding" type="float" value="2" onChange="refresh"/>

    <field id="backColor" type="string" value="#2C2C2C" alias="fondo.color" />
    <field id="barColor" type="string" value="#DE1717" alias="barra.color" />
    <!-- interfaz interna -->
    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" /> -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="fondo" color="#2C2C2C"/>
    <Rectangle id="barra" color="#DE1717"/>

  </children>

</component>
