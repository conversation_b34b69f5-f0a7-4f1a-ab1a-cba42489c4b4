<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLive" extends="Video">
  <script type="text/brightscript" uri="GHLive.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="showError" type="assocarray" alwaysNotify="true" onChange="onShowError" />
    <field id="panelVisible" type="boolean" value="false" alwaysNotify="true" onChange="onChangeVisible" />
    <field id="focus" type="boolean" value="false" alwaysNotify="true" onChange="onFocusChange" />

    <!-- <field id="languages" type="assocarray" onChange="handleLanguages" alwaysNotify="true" /> -->
    <field id="selected" type="assocarray" alwaysNotify="true" />
    <field id="channelPosition" type="integer" value="0" />
    <field id="showGridChannels" type="boolean" alwaysNotify="true" onChange="showGridChannels" />
    <field id="showMainPanel" type="boolean" alwaysNotify="true" onChange="showMainPanel" />

    <field id="visibleScreen" type="string" alwaysNotify="true" onChange="onChangeVisibleScreen" />

    <!-- info de listado de canales y de la EPG -->
    <field id="channels" type="array" onChange="onChannelsChange" alwaysNotify="true" />
    <field id="events" type="assocarray" onChange="onEventsChange" />

    <field id="keypressed" type="string" alwaysNotify="true"/>
  </interface>

  <children>
    <Rectangle id="msgError" translation="[0,0]" width="1920" height="1080" color="#121212" visible="false">
      <Poster id="error_image" translation="[614,192]" width="52" height="52" />
      <Label id="error_title" translation="[0,288]" width="1280" height="48" horizAlign="center" text="" />
      <Label id="error_description" translation="[0,336]" width="1280" height="100" vertAlign="center" horizAlign="center" text="" />
    </Rectangle>

    <GHLivePanelMiniEPG id="panel" translation="[0,550]" visible="false" />
    <TimeGridView id="timeGrid" visible="false" />
    <GHLivePanelChannels id="channelsGrid" visible="false" />

    <GHLiveOptions id="options" visible="false" />
  </children>

</component>
