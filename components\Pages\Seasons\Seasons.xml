<?xml version="1.0" encoding="utf-8" ?>

<component name="Seasons" extends="Page">
  <script type="text/brightscript" uri="Seasons.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="data" type="assocarray" />
    <field id="item" type="assocarray" />

    <!-- para refresh la vcard -->
    <field id="group_id" type="string" />
  </interface>

  <children>
    <Label id="title" focusable="false" translation="[0,171]" width="1280" height="48" text="*" />
    <GHRowList id="seasons" visible="false" translation="[-140, 249]" />
    <GHLoading id="spinner" visible="true"/>
  </children>
</component>
