# Dudas sobre ROKU



## Graphics



## Operation

**4.3** Channels that require authentication (SVOD, TVE, and other subscription services) use the [Roku Event Dispatcher](https://developer.roku.com/docs/developer-program/discovery/search/prioritizing-authenticated-channels-in-roku-search.md) to communicate authentication status.



## Linking

#### feed

- [x] El buscador tiene que tener TODOS los contenidos? Se puede inicialmente poner sólo algunos? Los FREE por ejemplo?

```
El feed no es para ahora, SI la implementación. 
Le mandamos un par de playID de ejemplo.
```

- [x] Va todo en un solo file? Es gigante. Aún pregenerado es inmenso. Se Zipea?
- [x] Metemos todo en una url, esto lo bajan por http? o lo levantan por ftp? 
- [x] El `playId` lo generamos nosotros? 

SI

- [x] Que diferencia hay entre `Poster Art` y `Box Art`? el tamaño es el mismo?
- [x] Se pueden incorporar algunos ID de TMS y otros no? o si se usa todos tienen que tenerlo? Aparentemente se hace el submit por separado.
- [x] Cómo funciona el tema del multiregión con un solo idioma?
- [x] El DeepLink sí o sí debe reaccionar a actores/directores/géneros? Nosotros en esta versión no tenemos implementadas las pantallas.

No hace falta.



## bif

- [x] Herramientas de creación de bif. Hay actualización? Usa librerías viejas. Hay fuente que se pueda recompilar?







