' SearchLite
' https://app.swaggerhub.com/apis-docs/ClaroVideo/SEARCH_Predictive/2.0.0#/Search%20Predictive/PredictiveSearch

sub DataInit()
  ' m.top.debug = true
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/search/v1/predictive"

  m.api.query.delete("api_version") ' sin api version
  ' m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })



  m.api.query.Append({
    ' "node": m.top.node
    nodos: m.top.node
    region: ghGetRegistry("region"),
    user_id: ghGetRegistry("user_id", "user"),
    filterlist: ghGetChild(m.global.filter_list, ghGetRegistry("region") + ".filterlist", ""),
    value: m.top.query
    user_token: ghGetRegistry("user_token", "user")
  })
  m.api.query.Append(ghGetChild(m.global, "search.search_full_quantity", {}))

  m.logger.debug("DataInit -- api=", { api: m.api, query: m.api.query })
  ' if m.top.debug then
  '   print ghLogHead();"DataInit -- api="m.api
  '   print ghLogHead();"DataInit -- query="m.api.query
  ' end if
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  neterror = ghGetChild(res, "neterror")
  if neterror <> invalid then
    m.logger.error("ProcessData -- NET_ERROR ", { error: neterror })
    res.raw = raw
    m.top.error = res
    return
  end if

  ' if m.top.debug then print ghLogHead();"Body = ";raw
  if res.errors <> invalid then
    if m.top.debug then print "** ERROR **********************"
    m.top.error = res.errors
    return
  end if

  'todo = ghGetChild(res, "response")
  todo = { "resultados": BuildContent(res) }

  if m.top.debug then
    print ghLogHead();"CONTENT = ";todo
    print ghLogHead();"CONTENT.resultados = ";todo.resultados
  end if
  m.top.content = todo
end sub
' **********************************
' Procesamiento especifico
' **********************************
function BuildContent(res)
  m.logger.debug("BuildContent.")

  ' VARS
  content = CreateObject("roSGNode", "GHContent")
  priority = m.global.search.searchPriority

  m.logger.debug("content=", { content: content })
  m.logger.debug("priority=", { priority: priority })

  print priority
  for each pri in priority
    path = getArrayPath(pri) ' path en el array del resultado
    if pri = "suggest" then ' el unico que no esta en prediction
      datos = buildSuggest(ghGetChild(res.data, path, []))
    else
      datos = ghGetChild(res.data, "prediction." + path, [])
    end if
    if datos.count() > 0 then
      roContent = CreateObject("roSGNode", "GHContent")
      roContent.id = pri ' nombre

      ' roContent.title = gh Translate("search_title_" + pri, "Search_" + pri)
      tKey = pri
      tKey = ghReplaceStr(tKey, "movies_", "")
      tKey = ghReplaceStr(tKey, "series_", "")
      roContent.title = ghTranslate("predictSearch_" + tKey + "_label", "(!) predictSearch_" + tKey + "_label")
      ' print "*** roContent.title=";roContent.title, pri

      ' items
      for each item in datos
        itContent = CreateObject("roSGNode", "GHContent")
        itContent.id = item.id ' nombre
        itContent.TITLE = item.title
        itContent.data = item
        roContent.appendChild(itContent)
      end for

      ' agrego
      content.appendChild(roContent)
    else
      m.logger.debug("NO HAY resultados en ", { pri: pri })
    end if
  end for
  m.logger.debug("-----------------------------")
  return content
end function
function getArrayPath(nombre)
  return ghReplaceStr(nombre, "_", ".")
end function
function buildSuggest(datos) ' saco el common que trae el suggest
  res = []
  for each d in datos
    res.push(ghGetChild(d, "common"))
  end for
  return res
end function

