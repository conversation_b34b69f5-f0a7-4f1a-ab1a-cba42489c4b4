' getMedia
' https://app.swaggerhub.com/apis/ClaroVideo/getmedia/2.00#/default/getmedia
' ------------------------------------

sub DataInit()
  m.top.debug = true

  if m.top.debug then
    log<PERSON><PERSON><PERSON>(50)
    print ghLogHead();"Init. "
  end if

  ' m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/player/v2/getmedia"

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  ' m.api.query.delete("user_id")

  m.api.query.Append({
    "device_id": "f7785395-3dc0-5ca4-b2bd-b4e6346221e3"
    "stream_type": m.top.streamType
    "quality": "HD"
    "appversion": ghGetAppVersion()
    "group_id": m.top.groupId
    "content_id": m.top.contentId
    "preview": m.top.preview
    "user_id": ghGetRegistry("user_id", "user"),
    "user_token": ghGetRegistry("user_token", "user")
    "payway_token": m.top.paywayToken
    ' "region": ghGetRegistry("region")
  })


  if m.top.preferred_audio <> invalid and m.top.preferred_audio <> "" then
    m.api.query.Append({
      "preferred_audio": m.top.preferred_audio
    })
  end if
  if m.top.preferred_subtitle <> invalid and m.top.preferred_subtitle <> "" then
    m.api.query.Append({
      "preferred_subtitle": m.top.preferred_subtitle
    })
  end if

  ' m.api.body = ghArray2Query({
  ' }, "")

  ' ---
  ' if m.top.debug then
  '   print ghLogHead();"DataInit -- api=";m.api
  '   ' print ghLogHead();"DataInit -- params=";m.api.params
  '   print ghLogHead();"DataInit -- query=";m.api.query
  '   print ghLogHead();"DataInit -- body=";m.api.body
  ' end if
end sub

' sub ProcessError(name, url, response, raw)
'   if m.top.debug then
'     print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
'     print "ERROR"
'     print name, url
'     print response
'     print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
'     print raw
'     response = ParseJson(raw)
'     if response <> invalid then
'       if response.errors <> invalid then
'         if response.error[0].message <> invalid then
'           err = response.error[0].message
'           print "ERR MSG=";err
'           hay = Instr(0, err, "ORA-01403: no data found")
'           if hay > 0 then
'             print "NO DATA FOUND."
'           else
'             print "HAY OTRO ERROR."
'           end if
'           m.top.error = err
'         end if
'       end if
'     end if
'     print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
'   end if
' end sub

sub ProcessData(video, raw)

  if m.top.debug then
    print ghLogHead();"**********************************"
    print ghLogHead();"GetItemData -- RAW:";raw
    ' print ghLogHead();"**********************************"
    print ghLogHead();"GetItemData -- response:";video?.response
    print ghLogHead();"GetItemData -- response.media:";video?.response?.media
    print ghLogHead();"**********************************"
  end if

  ' ERRORs
  ' ----------------------------
  'Hubo un contenido que salia status 200 pero traia un mensaje de error por eso esta condicion
  errors = ghGetChild(video, "errors")
  if errors <> invalid then
    video.raw = raw
    m.top.error = video
    return
  end if
  if video = invalid then
    m.top.error = { msg: "error html" }
    return
  end if

  ' INFO INICIAL
  ' ----------------------------
  item = {}
  item.id = video.id
  item.groupId = m.top.groupId
  item.description = ghGetChild(video, "response.group.common.description")
  item.hdPosterURL = ghGetChild(video, "response.group.common.image_large")
  item.status = "Duración: " + ghGetChild(video, "response.group.common.duration", "")
  ' languajes
  ' ----------------------------
  item.languages = ghGetChild(video, "response.language.options", [])
  ' obtener content_id
  ' ----------------------------
  item.contentId = ""
  cant = item.languages.count()
  for x = 0 to cant - 1
    lang = ghGetChild(item.languages, "#" + x.toStr())
    if lang.is_current = true then
      contentId = ghGetChild(lang, "content_id")
      if contentId <> invalid then
        item.contentId = contentId.toStr()
      end if
    end if
  end for
  ' VIDEO
  ' ----------------------------
  item.url = ghGetChild(video, "response.media.video_url") ' original de la getmedia

  ' print "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"
  ' print "DECISIONES"
  ' print "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"
  isVOD = ghGetChild(video, "response.group.common.extendedcommon.media.islive") <> "1"
  isHBO = ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "HBO"
  if m.top.debug then print "isVOD=";isVOD;" isHBO=";isHBO

  ' test: resultado para obtener pelicula
  ' https://aws-us-east-2-cross-cvideo-castlabs-iptv.kb-cross-aws-us-east-2.pool.clarovideo.net/metadata/?group_id=67ab48eccc185550e27f0280&stream_type=dashwv&device_id=f7785395-3dc0-5ca4-b2bd-b4e6346221e3

  ' test = {
  '   "data": {
  '     "media": {
  '       "video_url": "https://media-proxy-cert.tbxapis.com/v1/dash/7dba457853209964f07004c37c291899/67ab48eccc185550e27f0280/f8ec67eec757a0def0d3a389806de5cc/ee3db674f40042a785dda6e640a8ca11/stream.mpd?embeddedSubtitles=true",
  '       "server_url": "https://proxy-cert.tbxdrm.com/v1/drm-proxy/widevine/modular/turner?contentId=60c6d7d0-e7ac-11ef-a651-49dbcf4b9268&ed=&p=false&ip=**************&nva=2025-05-07T20%3A36%3A15.103Z&d=6fa272b16800f5cef28ede77a6935184189cf8b0&token=ad7dc2f24d30a8c721f774be61a37bfe3d81c91f43e475cedcfce29d5a4fefc2",
  '       "challenge": ""
  '     },
  '     "group": {
  '       "common": {
  '         "extendedcommon": {
  '           "media": {
  '             "drm_provider": {
  '               "id": "4",
  '               "desc": "toolbox"
  '             }
  '           }
  '         }
  '       }
  '     }
  '   }
  ' }

  ' item.url = test.data.media.video_url
  ' video.response.media.video_url = test.data.media.video_url
  ' video.response.media.server_url = test.data.media.server_url
  ' video.response.media.challenge = invalid
  ' video.response.group.common.extendedcommon.media.drm_provider = {}
  ' video.response.group.common.extendedcommon.media.drm_provider.desc = "toolbox"

  if ghGetChild(video, "response.group.common.extendedcommon.media.drm_provider.desc", "") = "toolbox" then
    if Left(m.top.streamType, 6) = "dashwv" then
      item.streamFormat = "dash"
      item.widevineParams = { headers: invalid }
      item.drmParams = {
        keySystem: "widevine"
        licenseServerURL: ghGetChild(video, "response.media.server_url")
      }
    else
      item.streamFormat = "smooth"
      castServerUrl = ghGetChild(video, "response.media.server_url")
      item.widevineParams = { headers: invalid }
      item.drmParams = {
        keySystem: "PlayReadyLicenseAcquisitionAndChallenge"
        licenseServerURL: castServerUrl
      }
      item.EncodingType = "PlayReadyLicenseAcquisitionUrl"
      item.EncodingKey = castServerUrl

    end if

  else if isVOD and isHBO then ' HBO VOD
    print "CASO DE HBO"
    ' SEPARACION SMOOTH O WIDEVINE
    ' ----------------------------
    if Left(m.top.streamType, 6) = "dashwv" then ' tanto para dashwv como para dashwv_ma
      print "HBO-VOD -- dashwv"
      ' ----------------------------
      ' DRM WIDEVINE FAMILY
      ' ----------------------------
      ' {
      '   audio: <Component: roAssociativeArray>
      '   challenge: "tokenID=00024-jUP9exJCyxv564ZIDf1VDjvZckHdLkU4h4B_hVJnRiAANQJStEsShujognW0INRRNzz0S7y3u2hDuIcsCrZNM8GDkYl0uWqUTQNhxQFqosbZjbLM_h-nrh92-p5xVzbjTkDWkAZwn12qX_F3AEv66nkcGwxXtQlfw9s3z99exo8|keyid={keyid}|operatorID=claro|countryID=MX"
      '   duration: <Component: roAssociativeArray>
      '   initial_playback_in_seconds: 0
      '   server_url: "https://vode.hbopaseo.com/DRMHub/wv/GetLicense"
      '   subtitles: <Component: roAssociativeArray>
      '   video_url: "https://l3edvwdclmxp01-secure-footprint.akamaized.net/WM/BGCXP_11EWVMD10C/BGCXP_11EWVMD10C_m7.mpd"
      ' }
      hboDataCustom = DataCustomWideVineHBO(video.response.media.challenge, video.entry.device_id)
      hboHeaders = { "dt-custom-data": hboDataCustom }
      castServerUrl = ghGetChild(video, "response.media.server_url")
      ' DRM
      ' ----------------------------
      item.streamFormat = "dash"
      item.widevineParams = { headers: hboHeaders }
      item.drmParams = {
        keySystem: "widevine"
        licenseServerURL: castServerUrl
      }
      if m.top.debug then
        print "***---------------------------------***"
        print "*** GETMEDIA video ***"
        print "***---------------------------------***"
        print ghGetChild(video, "response.media")
        print ghLogHead();"***---------------------------------***"
        print ghLogHead();"video -- media.challenge="; castChallenge
        print ghLogHead();"video -- item.widevineParams"; item.widevineParams
        print ghLogHead();"video -- item.url"; item.url
        print ghLogHead();"ProcessData drmParams=";item.drmParams
        print ghLogHead();"ProcessData widevineParams=";item.widevineParams
        print ghLogHead();"ProcessData headers=";item.widevineParams?.headers
        print ghLogHead();"***---------------------------------***"
      end if
    else
      print "HBO-VOD -- smooth"
      ' ----------------------------
      ' DRM SMOOTH FAMILY
      ' ----------------------------
      ' {
      '   audio: <Component: roAssociativeArray>
      '   challenge: "tokenID=00024-rLeijwv2F_fQqPa4h8tuxiZzHKZEt0DnamGS4IXwpMToAmKz3zXgQAGyjbAczy36VuLU11FyQWtwIRnPTEtPr3DchDr92LV_xAQOgXkk9pCkbuBXZ9e3Juw7P3koNGb_8aLKKAzBe5YStBIuUOmFFUmvgHHdbgZR9bIUY5VAgTo"
      '   duration: <Component: roAssociativeArray>
      '   initial_playback_in_seconds: 0
      '   server_url: "https://vode.hbopaseo.com/DRMHub/pr/GetLicense"
      '   subtitles: <Component: roAssociativeArray>
      '   video_url: "http://l3edvpsclmxp01-cust-footprint.akamaized.net/WM/BGCXP_11EPRSS10C/BGCXP_11EPRSS10C_m7.ism/manifest"
      ' }
      ' customData = DataCustomSmoothHBO(video.response.media.challenge, video.entry.device_id)
      ' print "castServerUrl=";castServerUrl
      ' print "customData=";customData
      ' especifico para smooth
      item.streamFormat = "smooth"
      castServerUrl = ghGetChild(video, "response.media.server_url")
      item.EncodingType = "PlayReadyLicenseAcquisitionAndChallenge"
      item.EncodingKey = castServerUrl + "%%%" + ghGetChild(video, "response.media.challenge")
      ' print "item.EncodingType=";item.EncodingType
      ' print "item.EncodingKey=";item.EncodingKey
    end if
    print "FIN CASO DE HBO"
  else ' CASO NORMAL
    print "CASO NORMAL"
    ' SEPARACION SMOOTH O WIDEVINE
    ' ----------------------------
    if Left(m.top.streamType, 6) = "dashwv" then ' tanto para dashwv como para dashwv_ma
      ' ----------------------------
      ' DRM WIDEVINE FAMILY
      ' ----------------------------
      item.streamFormat = "dash"
      castServerUrl = ghGetChild(video, "response.media.server_url")
      ' CHALLENGE
      castChallenge = ghGetChild(video, "response.media.challenge")
      if castChallenge <> invalid then castChallenge = ParseJson(castChallenge)
      ' DRM
      ' ----------------------------
      item.widevineParams = { headers: castChallenge }
      item.drmParams = {
        keySystem: "widevine"
        licenseServerURL: castServerUrl
      }
      if m.top.debug then
        print "***---------------------------------***"
        print "*** GETMEDIA video ***"
        print "***---------------------------------***"
        print ghGetChild(video, "response.media")
        print ghLogHead();"***---------------------------------***"
        print ghLogHead();"video -- media.challenge="; castChallenge
        print ghLogHead();"video -- item.widevineParams"; item.widevineParams
        print ghLogHead();"video -- item.url"; item.url
        print ghLogHead();"ProcessData drmParams=";item.drmParams
        print ghLogHead();"ProcessData widevineParams=";item.widevineParams
        print ghLogHead();"ProcessData headers=";item.widevineParams?.headers
        print ghLogHead();"***---------------------------------***"
      end if
    else
      ' ----------------------------
      ' DRM SMOOTH FAMILY
      ' ----------------------------
      item.streamFormat = "smooth"
      castServerUrl = ghGetChild(video, "response.media.server_url")
      ' CHALLENGE
      castChallenge = ghGetChild(video, "response.media.challenge")
      if castChallenge <> invalid then castChallenge = ParseJson(castChallenge)
      ' DRM
      ' ----------------------------
      item.widevineParams = { headers: castChallenge }
      item.drmParams = {
        keySystem: "PlayReadyLicenseAcquisitionAndChallenge"
        licenseServerURL: castServerUrl
      }
      ' especifico para smooth
      item.EncodingType = "PlayReadyLicenseAcquisitionUrl"
      item.EncodingKey = castServerUrl
      if m.top.debug then
        print "***---------------------------------***"
        print "*** GETMEDIA video ***"
        print "***---------------------------------***"
        print ghGetChild(video, "response.media")
        print ghLogHead();"***---------------------------------***"
        print ghLogHead();"video -- media.challenge="; castChallenge
        print ghLogHead();"video -- item.widevineParams"; item.widevineParams
        print ghLogHead();"video -- item.url"; item.url
        print ghLogHead();"ProcessData drmParams=";item.drmParams
        print ghLogHead();"ProcessData widevineParams=";item.widevineParams
        print ghLogHead();"ProcessData headers=";item.widevineParams?.headers
        print ghLogHead();"***---------------------------------***"
      end if
    end if
    print "FIN CASO NORMAL"
  end if
  ' print "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"
  ' print " "
  ' print " "
  ' print " "


  ' tracking
  ' ----------------------------
  item.trackInfo = ghGetChild(video, "response.tracking")
  item.rollingcreditstime = ghGetChild(video, "response.group.common.extendedcommon.media.rollingcreditstime", 0)

  ' OmitirIntro
  ' ----------------------------
  item.trackInfo.omitirIntro = {
    languages: getOmitirInfoData(ghGetChild(video, "response.group.common.extendedcommon.media.language.options.option", []))
  }

  languageWithInfo = ghGetChild(video, "response.group.common.extendedcommon.media.language.options.option", [])
  m.logger.debug("language options", { data: languageWithInfo })

  langFinal = []
  for each l in languageWithInfo
    l.is_current = false

    if l.group_id = m.top.groupId and l.content_id = m.top.contentId then
      l.is_current = true
    end if
    langFinal.push(l)
  end for

  item.languages = {
    audios: ghGetChild(video, "response.media.audio", {}),
    subtitles: ghGetChild(video, "response.media.subtitles", {})
    options: langFinal,
  }

  ' test para live
  ' langFinal.push ({
  '   "audio": "eng",
  '   "audio_track": "",
  '   "content_id": "", "credits_start_time": "-30",
  '   "current_content": "false",
  '   "desc": "ingles",
  '   "group_id": "791384",
  '   "id": "EN",
  '   "intro_finish_time": "",
  '   "intro_start_time": "",
  '   "is_current": false,
  '   "label_large": "Idioma ingles",
  '   "label_short": "ingles",
  '   "option_id": "O-EN",
  '   "option_name": "eng",
  '   "resume_finish_time": "",
  '   "resume_start_time": "",
  '   "subtitle": "",
  '   "subtitle_track": ""
  ' })
  ' langFinal.push ({
  '   "audio": invalid,
  '   "audio_track": "",
  '   "content_id": "", "credits_start_time": "-30",
  '   "current_content": "false",
  '   "desc": "ingles",
  '   "group_id": "791384",
  '   "id": "EN",
  '   "intro_finish_time": "",
  '   "intro_start_time": "",
  '   "is_current": false,
  '   "label_large": "sub spa",
  '   "label_short": "ingles",
  '   "option_id": "O-EN",
  '   "option_name": "eng",
  '   "resume_finish_time": "",
  '   "resume_start_time": "",
  '   "subtitle": "spa",
  '   "subtitle_track": ""
  ' })

  ' item.languages = {
  '   options: langFinal,
  '   audios: { options: { "eng": "eng", } },
  '   subtitles: { options: { "spa": { internal: "spa" } } },
  ' }


  ' datos de la serie
  ' ----------------------------
  datSerie = ghGetChild(video, "response.group.common.extendedcommon.media.serie")

  ' Preview o no?
  ' ----------------------------
  item.is_trailer = m.top.preview ' para tracker
  if m.top.preview = "0" then
    item.title = ghGetChild(video, "response.group.common.title")
    item.BookmarkPosition = ghGetChild(video, "response.media.initial_playback_in_seconds", 0) ' -- GOOSE
    item.trackInfo.hasEndCard = invalid
  else
    ' Titulos serie trailer - serie episodio - pelicula
    if datSerie <> invalid then
      item.title = ghGetChild(video, "response.group.common.extendedcommon.media.serie.title")
    else
      item.title = ghGetChild(video, "response.group.common.title")
    end if
    item.BookmarkPosition = 0
    item.trackInfo.hasEndCard = false
  end if

  ' next
  ' ----------------------------
  item.next_group = ghGetChild(video, "response.next_group")
  item.stream_uuid = ghGetChild(video, "response.stream_uuid")

  ' trickplay
  ' ----------------------------
  ' Se reemplaza el dominio para poder levantar los .bif en test
  trickplay = ghGetChild(video, "response.group.common.image_trickplay")
  ' newUrl = ghReplaceStr(trickplay, "https://clarovideocdn4.clarovideo.net", "http://img-amazon-roku.clarovideo.com")
  item.hdbifurl = trickplay

  ' ???
  ' ----------------------------
  datSerie = ghGetChild(video, "response.group.common.extendedcommon.media.serie", invalid)

  ' YOUBORA
  ' ----------------------
  item.ybPublishyear = ghGetChild(video, "response.group.common.extendedcommon.media.publishyear", "****")
  item.ybGenres = _getGenres(ghGetChild(video, "response.group.common.extendedcommon.genres.genre", []))
  ybProfile = ghGetChild(video, "response.group.common.extendedcommon.media.profile.hd.enabled", "")
  if ybProfile = "true" then
    item.ybRendition = "HD"
  else
    item.ybRendition = "SD"
  end if
  ' Tipo de Contenido visualizado
  ' Si es VOD -- player/getmedia response.group.common.extendedcommon.format.name
  ' Si es Canal -- player/getmedia response.group.common.extendedc.ommon.format.name
  item.tipo_contenido_visualizado = ghGetChild(video, "response.group.common.extendedcommon.format.name")
  ' titulo para youbora
  if ghGetChild(video, "response.group.common.extendedcommon.media.islive") = "1" ' en vivo
    ' Cuando es Live
    '   title: group_id del canal-nombre del canal
    '   title2: id del programa-nombre del programa (se seteará de manera asíncrona). El impacto es que no se podrá filtrar.
    '     Para implementarse, se setearía el dato después de llamar la EPG: En el 1er envío, llegaría el title2 vacío -  Se mostrará como Undefined
    '     En el 2do envío, se llama la línea del plugin.setoptions a gregando nuevamente el parámetro:
    '       title2: events.name
    '     NOTA: En el caso de que puedan enviar plugin.
    '       setoptions cuando acabe un programa y empiece otro durante la misma reproducción, en la vista de detalle de la reproducción, negocio podrá ver los diferentes programas que vió el usuario durante esa reproducción.
    liveId = ghGetChild(video, "response.group.common.id")
    liveTitle = ghGetChild(video, "response.group.common.title")
    item.ybTitle = liveId + "-" + liveTitle' YB
    item.ybTitleEpisode = ""
  else
    ' VOD
    if datSerie <> invalid then ' es serie
      ' Cuando es Serie
      '   title: id de la serie-nombre de la serie
      '   title2: id de la temporada-número de temporada-group_id del episodio-nombre de episodio
      item.ybSerie = true
      serieId = ghGetChild(video, "response.group.common.extendedcommon.media.serie.id", "")
      serieTitle = ghGetChild(video, "response.group.common.extendedcommon.media.serie.title", "")
      seasonId = ghGetChild(video, "response.group.common.extendedcommon.media.serieseason.id", "")
      seasonNumber = ghGetChild(video, "response.group.common.extendedcommon.media.serieseason.number", "")
      episodeId = ghGetChild(video, "response.group.common.id", "")
      episodeTitle = ghGetChild(video, "response.group.common.title", "") ' NO ESTA!!!
      item.ybTitle = serieId + "-" + serieTitle ' YB
      item.ybTitleEpisode = seasonId + "-" + seasonNumber + "-" + episodeId + "-" + episodeTitle ' YB
      '// PREVIEW ES IGUAL
    else
      ' Cuando es Película
      '   title: group_id de la pelicula-nombre de la película
      '   title2: vacío aparecerá cómo Undefined.
      item.ybSerie = false
      peliId = ghGetChild(video, "response.group.common.id", "[falta commonid]")
      peliTitle = ghGetChild(video, "response.group.common.title", "[falta common.title]")
      item.ybTitle = peliId + "-" + peliTitle ' YB
      item.ybTitleEpisode = ""
      '// PREVIEW ES IGUAL
    end if
  end if
  item.ybData = ghGetChild(video, "response.group.common.extendedcommon", {})
  ' ----------------------

  ' lasttouch
  ' ----------------------
  lasttouch = ghGetChild(video, "response.user.lasttouch")
  if lasttouch <> invalid
    ' ghSetRegistry("lasttouch_seen", lasttouch, "user")
    updateGlobalArray("lasttouch", { favorited: lasttouch })
  end if

  if m.top.debug then
    ' print ghLogHead();"**********************************"
    ' print ghLogHead();"content:";FormatJson(item)
    ' print ghLogHead();"**********************************"
  end if

  ' asignacion final
  ' ----------------------------
  m.top.content = item
end sub

' Utilidades OmitirIntro
' ----------------------------
function getOmitirInfoData(langs)
  data = {}
  for l = 0 to langs.Count() - 1
    iId = ghGetChild(langs[l], "option_id")
    iStart = ghGetChild(langs[l], "intro_start_time")
    iFinish = ghGetChild(langs[l], "intro_finish_time")
    ' me conviene que esten todos...
    ' if iId <> invalid and iStart <> invalid and iFinish <> invalid then
    data[iId] = { intro_start_time: iStart, intro_finish_time: iFinish }
    ' end if
  end for
  return data
end function
function _getGenres(genres)
  genreString = ""
  ' genres = ghGetChild(video, "response.group.common.extendedcommon.genres.genre", [])
  if genres.Count() > 0 then
    for tmpG = 0 to genres.Count() - 1
      genreString += genres[tmpG].name + ", "
    next
    if genreString <> "" then
      genreString = Left(genreString, Len(genreString) - 2)
    end if
  end if
  return genreString
end function

' Utilidades SMOOTH
' ----------------------------
' (Se podría pasar a UTILS)
' function DataCustom(challenge, deviceid)
'   cDataString = "{" + chr(34) + "customdata" + chr(34) + ":" + challenge + "," + chr(34) + "device_id" + chr(34) + ":" + chr(34) + deviceid + chr(34) + "}"
'   if m.top.debug then print ghLogHead();" ------------------------------------------- "
'   if m.top.debug then print ghLogHead();"DataCustom - challenge =";challenge
'   if m.top.debug then print ghLogHead();"DataCustom - deviceid =";deviceid
'   if m.top.debug then print ghLogHead();"DataCustom - cDataString =";cDataString
'   ba = CreateObject("roByteArray")
'   ba.FromAsciiString(cDataString)
'   customData = ba.ToBase64String()
'   if m.top.debug then print ghLogHead();"DataCustom - customData =";customData
'   if m.top.debug then print ghLogHead();" ------------------------------------------- "
'   return customData
' end function

' function DataCustomSmoothHBO(challenge, deviceid)
'   ' cDataString = "{" + chr(34) + "customdata" + chr(34) + ":" + challenge + "," + chr(34) + "device_id" + chr(34) + ":" + chr(34) + deviceid + chr(34) + "}"
'   cDataString = challenge
'   if m.top.debug then print ghLogHead();" ------------------------------------------- "
'   if m.top.debug then print ghLogHead();"DataCustom - challenge =";challenge
'   if m.top.debug then print ghLogHead();"DataCustom - deviceid =";deviceid
'   if m.top.debug then print ghLogHead();"DataCustom - cDataString =";cDataString
'   ba = CreateObject("roByteArray")
'   ba.FromAsciiString(cDataString)
'   customData = ba.ToBase64String()
'   if m.top.debug then print ghLogHead();"DataCustom - customData =";customData
'   if m.top.debug then print ghLogHead();" ------------------------------------------- "
'   return customData
' end function

function DataCustomWideVineHBO(challenge, deviceid)
  newChallenge = challenge
  newChallenge = ghReplaceStr(newChallenge, "{keyid}", deviceid)
  cDataString = newChallenge
  ' cDataString = "{" + chr(34) + "customdata" + chr(34) + ":" + challenge + "," + chr(34) + "device_id" + chr(34) + ":" + chr(34) + deviceid + chr(34) + "}"
  if m.top.debug then print ghLogHead();" ------------------------------------------- "
  if m.top.debug then print ghLogHead();"DataCustom - challenge =";challenge
  if m.top.debug then print ghLogHead();"DataCustom - deviceid =";deviceid
  if m.top.debug then print ghLogHead();"DataCustom - cDataString =";cDataString
  ba = CreateObject("roByteArray")
  ba.FromAsciiString(cDataString)
  customData = ba.ToBase64String()
  if m.top.debug then print ghLogHead();"DataCustom - customData =";customData
  if m.top.debug then print ghLogHead();" ------------------------------------------- "
  return customData
end function



' Utilidades Varias
' ----------------------------
sub logEspacio(cant = 20)
  for r = 0 to cant
    print " "
  end for
end sub



' CASTLABS -- AHORA NO SE USA, PERO POR LAS DUDAS
function GetCastLabToken(group_id, content_id, stream_type, device_id)
  print "GetCastLabToken"
  ' url = "http://34.236.95.18:5002/metadata/?group_id=" + group_id + "&stream_type=" + stream_type + "&content_id=" + content_id
  ' url = "http://34.236.95.18:5002/metadata/?group_id=" + group_id + "&stream_type=" + stream_type
  url = "http://34.236.95.18:5002/metadata/?group_id=" + group_id + "&stream_type=" + stream_type + "&device_id=" + device_id + "&drm_provider=AMCO"
  print "URL=";url


  m.port = createObject("roMessagePort")
  http = createObject("roUrlTransfer")
  http.SetCertificatesFile("common:/certs/ca-bundle.crt")
  http.AddHeader("X-Roku-Reserved-Dev-Id", "")
  http.InitClientCertificates()
  http.setRequest("GET")
  http.RetainBodyOnError(true)
  http.setMessagePort(m.port)
  http.setUrl(url)
  print "llamando..."
  raw = http.GetToString()
  print "volvi."
  print "RAW=";raw

  ' -----------------------
  ' si funciono -----------
  if raw <> invalid and raw <> "" then
    textHtml = Instr(1, raw, "<html>")
    ' si en la respuesta vien html, es un error del server
    if textHtml > 0 then
      if m.top.debug then print ghLogHead();"GetCastLabToken GetSync error api -- posible internal server error"
      return invalid
    else
      if m.top.debug then print ghLogHead();"GetCastLabToken OK"
      body = ""
      if Len(raw) > 0 then
        body = ParseJson(raw)
      end if
      return body
    end if
  else
    print ghLogHead("API");"GetCastLabToken -- NOT WORKING! "
    return invalid
  end if
end function

' TOKEN CASTLABS
' ----------------------------
' respuesta = {
'   "entry": {
'     "content_id": "1184817",
'     "group_id": "1061677",
'     "stream_type": "dashwv"
'   }
'   "response": {
'     "media": {
'       "challenge": "{\"x - dt - auth - token \ ": \"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ez0T4z_46qaoU_uZC3Rz65WRq9N3iV7FpTvuIdLG9j3wiLjeT01xid2q4IucO7IQLsEZI1fDtg7N6vzrvADMLQ \ "}",
'       "server_url": "https://lic.staging.drmtoday.com/license-proxy-widevine/cenc/?specConform=true"
'       "video_url": "http://arclarovideo.akamaized.net/multimediav81/plataforma_vod/MP4/202208/WMP4H18841MTDP_full/WMP4H18841MTDP_full_WV_DASH.ism/.mpd"
'     }
'   }
' }


' -------------------------------------------------
' -------------------------------------------------
' DE LA PARTE DE SMOOTH ANTERIOR Y DE PRUEBAS
' -------------------------------------------------
' -------------------------------------------------
' ' ----------------------------
' ' ----------------------------
' ' DRM WIDEVINE
' ' ----------------------------
' ' ----------------------------
' item.StreamFormat = "smooth"
' ' TOKEN CASTLABS
' ' ----------------------------
' tokenData = GetCastLabToken(item.groupId, item.contentId, m.top.streamType, video.entry.device_id)
' ' VIDEO
' ' ----------------------------
' item.url = ghGetChild(tokenData, "response.media.video_url")
' ' DRM SERVER
' ' ----------------------------
' castServerUrl = ghGetChild(tokenData, "response.media.server_url")

' castChallenge = ghGetChild(tokenData, "response.media.challenge")
' if castChallenge <> invalid then castChallenge = ParseJson(castChallenge)
' item.widevineParams = { headers: castChallenge }

' if m.top.debug then
'   print "***---------------------------------***"
'   print "*** tokenData ***"
'   print "***---------------------------------***"
'   print tokenData
'   print ghLogHead();"***---------------------------------***"
'   print ghLogHead();"tokenData -- media.challenge="; tokenData.response.media.challenge
'   print ghLogHead();"tokenData -- item.widevineParams"; item.widevineParams
'   print ghLogHead();"tokenData -- item.url"; item.url
'   print ghLogHead();"***---------------------------------***"
' end if

' ' DRM
' ' ----------------------------
' item.drmParams = {
'   keySystem: "PlayReadyLicenseAcquisitionAndChallenge"
'   ' licenseServerURL: ghGetChild(video, "response.media.server_url", "")
'   licenseServerURL: castServerUrl
' }
' if m.top.debug then
'   print ghLogHead();"ProcessData drmParams=";item.drmParams
'   print ghLogHead();"ProcessData widevineParams=";item.widevineParams
'   print ghLogHead();"ProcessData headers=";item.widevineParams?.headers
' end if

' ' item.EncodingType = "PlayReadyLicenseAcquisitionAndChallenge"
' ' item.EncodingKey = ghGetChild(video, "response.media.server_url", "") + "%%%" + customData
' item.EncodingType = "PlayReadyLicenseAcquisitionUrl"
' item.EncodingKey = castServerUrl







' ' ----------------------------
' ' ----------------------------
' ' DRM SMOOTH
' ' ----------------------------
' ' ----------------------------
' item.StreamFormat = "smooth"

' 'Armado customData para la licencia (VOD y LIVE)
' ' Si otro proveedor da error en la licencía, consultar con Player!
' if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "HBO"
'   if ghGetChild(video, "response.group.common.extendedcommon.media.islive") = "1" 'esto viene en la getmedia
'     ' HBO LIVE se arma así como las peliculas que no son HBO
'     customData = DataCustom(video.response.media.challenge, video.entry.device_id)
'   else
'     ' HBO EN PELICULA/SERIE, el custom data se tiene que pasar así
'     customData = ghGetChild(video, "response.media.challenge")
'   end if
' else if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "PICARDIA2"
'   ' Para picardía el customData va vacio
'   customData = ""
' else
'   'Resto de peliculas/series se arma así
'   customData = DataCustom(video.response.media.challenge, video.entry.device_id)
' end if

' item.EncodingType = "PlayReadyLicenseAcquisitionAndChallenge"
' if ghGetChild(video, "response.group.common.extendedcommon.media.proveedor.nombre") = "PICARDIA2"
'   item.EncodingKey = ghGetChild(video, "response.media.server_url", "")
' else
'   ' pruebas
'   ' if m.top.groupId = "1039284" then 'tyc sport con error de certificado
'   '   item.EncodingKey = ghGetChild(video, "response.media.server_url") + "%%%" + customData + "asdf"
'   ' else
'   item.EncodingKey = ghGetChild(video, "response.media.server_url", "") + "%%%" + customData
'   ' end if
' end if

