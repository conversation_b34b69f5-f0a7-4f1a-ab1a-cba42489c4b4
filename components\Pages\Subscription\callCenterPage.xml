<?xml version="1.0" encoding="utf-8"?>
<!-- <component name="callCenterPage" extends="SGDEXComponent" xsi:noNamespaceSchemaLocation="https://devtools.web.roku.com/schema/RokuSceneGraph.xsd"> -->
<component name="callCenterPage" extends="Page">
	<script type="text/brightscript" uri="callCenterPage.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<interface>
		<field id="data" type="node" onChange="handleData" />
		<field id="providerCode" type="string" onChange="getProviderCode" />
	</interface>
	<children>
		<!-- Fondo y Textos -->
		<Poster id="logo" translation="[61,22]" width="162" height="33" />
		<LayoutGroup id="callinfo" translation="[500,100]" layoutDirection="vert" horizAlignment="center" vertAlignment="center" itemSpacings="[30]">
			<Label id="callCenterTitle" width="554" height="32" vertAlign="center" horizAlign="center" />
			<Label id="callCenterinfo" wrap="true" width="336" vertAlign="center" horizAlign="center" />
			<Label id="callDescription" width="336" height="21" vertAlign="center" horizAlign="center" />
			<Label id="phoneNumber" vertAlign="center" horizAlign="center" wrap="true" width="336" height="41" />
			<Label id="numberDescription" vertAlign="center" horizAlign="center" wrap="true" width="336" />
			<GHButtonGroup id="botonera" layout="map" orientation="vertical" handleKey="false">
				<GHButton value="aceptar" id="aceptar" text="ACEPTAR" translation="[500,527]" color="0xFFFFFF" selColor="#FFFFFF" width="304" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
			</GHButtonGroup>
		</LayoutGroup>
	</children>
</component>
