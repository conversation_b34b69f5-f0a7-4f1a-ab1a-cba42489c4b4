sub DataInit()
  m.api.method = "POST"
  ' m.api.url = m.config.mfwk.host + "/services/user/favoritedlive"
  m.api.url = m.config.mfwk.host + "/services/user/favorited"

  epg = ghGetChild(m.global, "epg", {})
  m.api.query.Append({
    "live": "true",
    "user_hash": ghGetRegistry("session_userhash", "user")
    "epg_version": epg.version
    ' "user_id": ghGetRegistry("user_id", "user"),
    "lasttouch": ghGetChild(m.global, "lastTouch.favorited"),
    "region": ghGetRegistry("region")
    "api_version": ghGetChild(m.global.config, "api.version.favoritedlive", m.global.config.api.versions.default),
  })

  m.api.body = ghArray2Query({
    "user_token": ghGetRegistry("user_token", "user")
  }, "")

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  favorites = {}
  items = ghGetChild(response, "groups", [])
  cantidad = items.count()
  for i = 0 to cantidad - 1
    item = ghGetChild(items, "#" + i.toStr())

    favorites[item.id] = item.id
  end for

  m.global.setFields({
    favorites: {
      channels: favorites
    }
  })

  m.top.content = response
end sub