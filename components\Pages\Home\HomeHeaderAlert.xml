<component name="HomeHeaderAlert" extends="Group">
  <script type="text/brightscript" uri="HomeHeaderAlert.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/FocusHandler.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />


  <interface>
    <field id="routerChild" type="assocarray" alwaysNotify="true" />
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
  </interface>

  <children>
    <!-- Blue Ribbon Background -->
    <Rectangle id="ribbonBg" translation="[0,0]" width="1281" height="69" color="#0A77CC" cornerRadius="6.6" />

    <!-- Ribbon Text -->
    <Label id="ribbonText" translation="[40,20]" width="800" height="40" text="¿Quieres ver Claro fuera de casa? Elige OK para completar tus datos" color="#FFFFFF" font="Medium" focusable="false" />

    <!-- OK Button -->
    <GHButton id="btnOk" translation="[950,12]" width="130" height="48" text="OK" cornerRadius="6.6" />

    <!-- CERRAR Button -->
    <GHButton id="btnCerrar" translation="[1100,12]" width="130" height="48" text="CERRAR" cornerRadius="6.6" />
  </children>
</component>
