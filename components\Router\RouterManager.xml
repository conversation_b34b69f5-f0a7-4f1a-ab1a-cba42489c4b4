<?xml version="1.0" encoding="UTF-8"?>

<component name="RouterManager" extends="Group">

  <script type="text/brightscript" uri="RouterManager.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <field id="ViewCount" type="integer" />
    <!-- Assign UI before any procedure to call -->
    <field id="currentView" type="node" />

    <field id="allowCloseChannelWhenNoViews" type="bool" alwaysNotify="true" value="false" />

    <!-- <field id="procedureObject" type="assocarray" onChange="procedureObjectChange" /> -->

    <!-- You can use it to call specific function(assocarray format = {fp : ['Array with Parameters']}) OR append procedureObjects as children -->
    <field id="addView" type="assocarray" onChange="procedureObjectChange" />
    <field id="closeView" type="assocarray" onChange="procedureObjectChange" />
    <field id="closeToView" type="assocarray" onChange="procedureObjectChange" />
    <field id="replaceCurrentView" type="assocarray" onChange="procedureObjectChange" />
    <field id="saveState" type="assocarray" onChange="procedureObjectChange" />
    <function name="runProcedure" />

    <!-- internal managemend -->
    <field id="debug" type="boolean" value="false" />

  </interface>


  <children>
    <!-- <ViewStack id="ViewStack" /> -->
    <Group id="ViewStack" />
  </children>

</component>
