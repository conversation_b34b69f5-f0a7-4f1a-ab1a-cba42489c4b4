' Copyright (c) 2019 Roku, Inc. All rights reserved.

sub Init()
    m.title = m.top.findNode("title")
    m.poster = m.top.findNode("poster")
    m.candado = m.top.findNode("candado")
    m.corazon = m.top.findNode("corazon")
    ' events
    m.top.ObserveField("content", "OnContentSet")
    m.top.ObserveField("width", "OnLayoutChanged")
    m.top.ObserveField("height", "OnLayoutChanged")
    ' defaults
    m.horizontalMargin = 3
    m.verticalMargin = 1
    ' m.title.translation = [m.horizontalMargin, m.verticalMargin]
    m.title.translation = [-60, -10]
    m.title.font = ghGetFont(15, "regular")
    ' m.candado.translation = [m.horizontalMargin, m.verticalMargin]
    m.candado.translation = [140, 5]
    m.corazon.translation = [22, 26]
end sub

' EVENTS
' --------------------------
sub OnContentSet()
    m.candado.visible = false
    m.corazon.visible = false
    m.poster.opacity = 1

    content = m.top.content
    if content <> invalid
        ' m.title.text = content.title
        m.title.text = content.number
        posterUrl = content.HDSMALLICONURL

        if posterUrl = invalid or posterUrl = "" then
            posterUrl = content.HDPOSTERURL
        end if

        m.poster.uri = posterUrl
        m.candado.uri = ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))
        m.corazon.uri = ghGetAsset("playingLive_alert_channelFavorite_icon")
    end if

    showFavoritedBloqued(ghGetChild(m.top, "content.id", ""))
end sub

sub showFavoritedBloqued(groupId)
    if groupId <> "" then
        ' si esta bloqueado
        channels = ghGetChild(m.global, "parental.channels", {})
        if channels[groupId] <> invalid and channels[groupId] <> "" then
            m.candado.visible = true
            m.poster.opacity = 0.2
        else
            m.candado.visible = false
            m.poster.opacity = 1
        end if

        ' si esta en favoritos
        favorites = ghGetChild(m.global, "favorites.channels", {})
        if favorites[groupId] <> invalid and favorites[groupId] <> "" then
            m.corazon.visible = true
        else
            m.corazon.visible = false
        end if
    end if
end sub

sub OnLayoutChanged() ' event as object
    showFavoritedBloqued(ghGetChild(m.top, "content.id", ""))

    renderingWidth = m.top.width - m.horizontalMargin * 2
    renderingHeight = m.top.height - m.verticalMargin * 2
    m.poster.setFields({
        width: renderingWidth
        height: renderingHeight
        loadWidth: renderingWidth
        loadheight: renderingHeight
    })
    m.candado.setFields({
        width: 17
        height: 17
    })
    m.corazon.setFields({
        width: 17
        height: 17
    })
    m.title.setFields({
        width: renderingWidth
        height: renderingHeight
        font: ghGetFont(18, "regular")
        translation: [-62, -5]
    })
end sub