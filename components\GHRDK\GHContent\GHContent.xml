<?xml version="1.0" encoding="UTF-8"?>
<component name="GHContent" extends="ContentNode">
  <script type="text/brightscript" uri="GHContent.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <field id="data" type="assocarray" />
    <field id="visible" type="boolean" />
    <field id="type" type="string" />
    <field id="oldtype" type="string" />
    <field id="longOkType" type="string" />
    <field id="rowtype" type="string" />
    <field id="canttotal" type="integer" />
    <field id="debug" type="boolean" value="false" />
  </interface>

</component>