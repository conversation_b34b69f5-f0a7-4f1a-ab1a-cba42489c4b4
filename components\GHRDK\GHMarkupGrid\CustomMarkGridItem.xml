<?xml version="1.0" encoding="utf-8"?>
<component name="CustomMarkGridItem" extends="Group">
	<script type="text/brightscript" uri="CustomMarkGridItem.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
	<interface>
		<!-- Item content -->
		<field id="itemContent" type="node" onChange="itemContentChanged" />
		<field id="width" type="float" />
		<field id="height" type="float" />
	</interface>
	<children>
		<!-- Main item rectangle -->
		<Rectangle id="box" color="#2E303D">
			<LayoutGroup id="customLayout" layoutDirection="vert" itemSpacings="[2]">

			</LayoutGroup>
		</Rectangle>
	</children>
</component>
<!-- <Label id="centeredText" wrap="true" text="" horizAlign="center" vertAlign="center" color="0xFFFFFFFF" translation="[0,0]" /> -->