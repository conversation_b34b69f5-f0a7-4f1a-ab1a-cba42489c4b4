# Historias técnicas

 [volver](README.md) 



### HT001 : Switch publicidad

Si no está la key, apagada.

```json
{
  "cv_advertising_config": {
    "default": {
      "enable": false
    },
    "mexico":{
     "enable": true,
     "skip_offset": 5
    }
  }
}
```



### HT002 : Login-register

user/login y user/register



https://app.swaggerhub.com/apis/ClaroVideo/Login_ott/1.0.0#/login/get_services_user_v1_ott_login

?? sin cambios



### HT003 : LoginByToken

v2/loginbytoken



> AVERIGUAR SI ESTO ES UN NUEVO LOGIN, O ES ANTERIOR (DICE MODIFICACION).
>
> CUÁNDO SE USA?



**No está implementado ????**



1. Aplica para BE.
2. Considerar como query param ***NO OBLIGATORIO*** el `token`
3. Si el servicio es invocado ***sin token***, se debe devolver uno para identificar al usuario *Freemium* y no dejar huecos de seguridad en la plataforma.
4. El servicio debe contemplar los escenarios relacionados a IP Telmex.
5. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT004 : user_token

**Quiero** Que se preserve en las aplicaciones el ***"user_token"***



1. Aplica para FE
2. La obtención del ***user_token*** se realizará al inicio de la aplicación mediante el servicio ***loginbytoken***.
3. Cuando el parámetro ***user_token*** se tenga, se ejecutará el servicio solicitado en la **HT007** para identificar la modalidad de suscripción del usuario.
4. Los únicos escenarios en donde el ***user_token*** estará vacío son:
   1. Usuario primer vez que ingresa a la plataforma
   2. Borrado de datos o cache
5. Solo se actualizara en los casos de renovación, el cual se detalla en la **HT005**



### HT005 : Renovación token

Renovación del token



https://app.swaggerhub.com/apis/ClaroVideo/refreshtoken/1.0.0#/default/get_services_user_v1_refresh_token



1. Aplica para FE.
2. Al recibir un status code 401 y el siguiente error se debe renovar el token:

```json
{  
  "errors": [
    {
      "code": "USR_JWT_00002",
      "detail": "Token is invalid",
      "source": ""
    }
  ]
}
```

1. Para renovar el token se requiere invocar el servicio
   https://app.swaggerhub.com/apis/ClaroVideo/refreshtoken/1.0.0#/default/get_services_user_v1_refresh_token



### HT006 : user_token en APIS

**Quiero** que el parámetro ***"user_token"*** se propague automáticamente a todos los servicios que tienen contexto de usuario.



1. Aplica para FE y BE.
2. La propagación del parámetro `"user_token"` debe realizarse por medio de una nueva versión de api, asegurando la retrocompatibilidad en las aplicaciones.
   1. Considerar el siguiente listado de apis a implementar:
      https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4026892333/BRF-11088+-+FE+-+IPTV+Uso+de+user+token+como+autenticaci+n+principal#PLY-/-BYR-/-CMS-/-PGS-/-USR
3. Remover el envío del parámetro **HKS** en todos los servicios que ya aceptan `“user_token"`, del punto **2 inciso a**



### HT007 : Modalidad usuario

servicio para identificar la modalidad del usuario



1. Aplica para BE.
2. El servicio debe contemplar el envío del parámetro `"user_token"` 
3. El servicio en la respuesta debe devolver el `type` que posterior será propagado al resto de servicios para proveer la navegación y experiencia correspondiente.
4. El servicio debe actualizar el tipo de usuario después de cualquier transacción efectuada (suscripción, compra, renta, cancelación, suspensión, etc…)
5. El servicio debe considerar ser invocado recurrentemente en cierto intervalo de tiempo desde diferentes dispositivos.





### HT008 : nav

nav/data



1. Aplica para BE.
2. Considerar como query param ***OBLIGATORIO*** el `user_token`
3. Considerar como query param ***NO OBLIGATORIO*** el `type`
4. Dependiendo de los parámetros definidos en los puntos anteriores se debe poder tener navegaciones diferenciadas:
   1. Freemium: Con `user_token` firmado como Freemium
   2. Freemium registrado: Con `user_token` con contexto de usuario
   3. Premium: Con `user_token` y `type`
5. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT009 : --

cancelada



### HT010 : Plan/Offers

v1/plan/offers



1. Aplica para BE.
2. Realizar los cambios internos y de performance necesarios, ya que servicio va ser consumido para mostrar un ***carrusel*** en la pantalla de ***Inicio*** de las aplicaciones.
3. Debe considerar el ***user_token*** de un usuario Freemium para mostrar todas las ofertas disponibles.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT011 : Level

level



1. Aplica para BE y CDS.
2. Generar un nuevo type que se pueda configurar en cualquier layout.
3. Debe integrarse en el servicio de ***cms/level***
4. Dentro de las propiedades debe contener la url del servicio **plan/offers**. Ejemplo:

```
"properties": { ... "url": "\/services\/plans\/v1\/offers?&region=mexico&user_token="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IOle4d7lx17JpzBhsY_7GCNiD28qmEZwY-E2_DaQJu0, ... }
```



### HT012 : --

cancelada



### HT013 : Content data

content/data 



1. Aplica para BE
2. Integrar un objeto por contenido que indique el tipo de publicidad habilitada para el contenido, consultando el servicio definido en la **HT022**. Para la primera fase se contemplan:
   1. Pre-roll
   2. Post-roll
3. La estructura del objeto debe ser:
   1. Siendo 1, encendido.
   2. Siendo 0, apagado.

```
"publicity": {
	"pre_roll": 1,  
	"post_roll": 0, 
}
```

1. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT014 : PurchaseButtonInfo

payway/v1/purchasebuttoninfo



1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para habilitar la reproducción.
3. Considerar que el campo ***waspurchased*** sea 1, para aquellos contenidos marcados como Free.
4. Considerar que el objeto ***listButtons*** no debe tener afectación para ningún tipo de usuario y debe listar las ofertas que el contenido tenga.
5. Se agrega la siguiente tabla de referencia sobre las diversas posibilidades:

<img src="../../../../../../Users/<USER>/Desktop/CVLite/CLdoc/img/ht014.png" alt="ht014" style="zoom:50%;" />

1. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT015 : --

cancelada



### HT016 : Predictive/LinealPredictive

search/predictive y search/linealpredictive



1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para mostrar contenido específico.
3. Si la región requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO se devuelvan*** en el response.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT017 : Recomendations

content/recommendations



1. Aplica para BE.
2. Debe considerar el ***user_token*** de un usuario Freemium para mostrar contenido específico.
3. Si la región requiere mostrar ***únicamente*** los contenidos **Freemium**, se debe asegurar que los contenidos ***Premium NO se devuelvan*** en el response.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT018 : ACCIONES

acciones de usuario



1. Aplica para FE.
2. Cuando un usuario Freemium seleccione dentro de la plataforma alguna acción que requiera contexto de usuario, se le debe dirigir al formulario de ***Inicio de Sesión***.
3. Se tienen detectadas las siguientes acciones
   1. Mi lista
   2. Añadir canales a favoritos
   3. Programar recordatorios



### HT019 : Google Ad Manager

Google Ad Manager



1. Aplica para FE.
2. Se debe integrar el SDK  considerando la siguiente configuración y ejemplos de video: [Insumos Google Ad Manager](https://dlatvarg.atlassian.net/wiki/spaces/BRIEF/pages/4069785894) 
3. Para esta primera integración las URLs van fijas tal cual se comparten en el archivo del punto 2, es decir, ninguno de los query params va a ser dinámico.



 [Claro video Lite_tags_v2.pdf](Claro video Lite_tags_v2.pdf) 



### HT020 : APA

APA assets y metadata



1. Se debe implementar la matriz de comunicación correspondiente a los flujos de:
   1. Inicio de sesión
   2. Registro
   3. Nodo Freemium



**(!) todo pendiente**



### HT021 : --

Bitmovin

**SOLO AAF**



1. Aplica para AAF.
2. Integrar el reproductor Bitmovin para la reproducción de contenidos VOD y Publicidad.
3. Se mantendrán los reproductores nativos para la reproducción de contenidos LIVE.
4. Se debe asegurar que la experiencia actual (UX/UI) en el Player de no se vea afectada al implementar Bitmovin en la iniciativa de Claro video con publicidad.
5. Los dispositivos y modelos contemplados dentro de la implementación son:
   1. STV Samsung Tizen 2016+
   2. STV LG Webos 2016+
   3. STV Hisense 2016+
   4. STV Zeasn
   5. STV Netrange
   6. PS4



### HT022 : Reglas publicidad

reglas de publicidad



1. Aplica para BE
2. Generar un servicio que por group_id devuelva las reglas de publicidad contempladas para la primera fase:
   1. Pre-roll
   2. Post-roll
3. Generar un servicio que por group_id permita modificar las reglas de publicidad contempladas para la primera fase:
   1. Pre-roll
   2. Post-roll
4. Considerar los siguientes niveles de jerarquía a aplicar, de lo general a lo particular:
   1. Region
   2. Proveedor
   3. Type
   4. Group Id
5. En una siguiente fase se deberá integrar mediante un Gestor.



### HT023 : logout

user/logout



1. Aplica para BE.
2. Generar una versión que cierre sesión e invalide el `"user_token"`
3. Incluir el cierre de sesión de todos los Devices.
4. Para asegurar la no afectación a versiones anteriores se requiere que los cambios se integren en una nueva versión.



### HT024 : --

videos

**SOLO PARA DISEÑO**



1. Generar video con las siguientes características para poder ser integrado a las campañas del Ad Manager
   1. Formato: mp4
   2. Duración: 30 segundos 
   3. Tamaño: 800x600



### HT025 : Deadline

**DEADLINE**



1. Los dispositivos en los que se tienen complicaciones técnicas y no estaría lista la integración para la primer Fase son:
   1. Roku
   2. Chromecast
2. En una siguiente fase se deberán contemplar.





### HT026 : --

**NO SOPORTADO EN**



1. Los dispositivos que no soportan la integración y en donde no se implementara la iniciativa son:
   1. Huawei mobile y tablet
   2. STV Samsung Orsay 2012-2015
   3. STV LG Netcast 2013
   4. STV LG Webos 2014-2015
   5. STV Sony (que no son ATV)
   6. Windows 
   7. Xbox
   8. STB Coship 9085
   9. STB Coship 9090
   10. STB Kaon
   11. STB Huawei
   12. STB ZTE AOSP
   13. Arris



### HT027 : --

carrusel html

**NO SOPORTADO**



1. Aplica para BE y CDS.
2. Generar un nuevo type que se pueda configurar en cualquier layout.
3. Debe integrarse en el servicio de ***cms/level***
4. Dentro de las propiedades debe contener el **HTML** configurado, el cual será proporcionado al equipo de CDS.



### HT028 : --

ruta html

**NO SOPORTADO**



1. Aplica para Web.
2. Generar una ruta que pueda ser editorializad y tomada  dentro de las aplicaciones móviles que permita la visualización de contenido HTML.
3. La vista web debe ser receptiva y adaptarse a diferentes tamaños de pantalla para garantizar una experiencia de usuario consistente en diferentes dispositivos.
4. La vista web debe ser capaz de cargar lo que se haya editorializado en la cms/level de la  **HT027.**



### HT029 : ??

**REVISAR**

**NO SOPORTADO**



1. Aplica para CDS  
2. Se deberá editorializar en el árbol de navegación el nodo Premium en las regiones y dispositivos que se requiera por la operación.
3. Se deberán editorializar 2 carruseles para ese nodo:
   1. Nuevo carrusel HTML,  especificado en la **HT027**
   2. Nuevo carrusel Selector de planes especificado en la **HT011**
4. Para el carrusel HTML se deberá configurar:
   1. Para los dispositivos móviles:
      1.  Configurar una URL
   2. Para el resto de dispositivos:
      1. HTML
5. Durante el desarrollo se puede requerir un cambio sobre la configuración del punto 4 por complicaciones que se puedan tener.



### HT030 : Chapitas

chapitas 



1. Aplica para FE
2. Adaptar los nuevos tipos de usuario a los ya existentes para que al construir las chapitas de los contenidos se mantenga la misma lógica y no se afecte dicha funcionalidad. Ejemplo:

```
freemium = anonymous freemium_registrado = no_susc preemium = susc
```

1. Se debar seguir usando la key `providers_label_configuration`
2. En una siguiente fase se deberá refactorizar esta integración.



### HT031 : ??

Dispositivos sin medio de pago asociado

**REVISAR, entiendo que no aplica.**



1. Aplica para ADR
2. Se deben considerar las mismas políticas de Google en dispositivos Android para transaccional en la plataforma solicitadas en su momento en el BRF: https://dlatvarg.atlassian.net/browse/BRF-6976



### HT032 : --

navecación iOS

**NO APLICA**



### HT033 : --

Aplica para Web

**NO APLICA**



### HT034 : AdServer

Nuevo servicio para obtener la URL de Ad Server



1. Aplica para BE
2. El servicio debe contemplar el envío de los parámetros:
   1. user_token
   2. payway_token
   3. group_id
   4. region
   5. Conjunto de parámetros que identifican al dispositivo
3. Debe considerar que hay URLs específicas para los ambientes de UAT y PROD
4. Se deben tomar las URLs especificadas en la **HT019** 
5. El servicio en la respuesta debe devolver la url para el pre-roll y post-roll. Ejemplo:

```
{
	...
  "pre_roll": "http://...",
  "post_roll": "http://...",
  ... 
}
```

