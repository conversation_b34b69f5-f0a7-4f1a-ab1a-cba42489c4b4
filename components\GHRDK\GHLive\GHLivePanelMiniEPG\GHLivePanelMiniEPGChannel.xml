<?xml version="1.0" encoding="utf-8" ?>

<component name="GHLivePanelMiniEPGChannel" extends="Group">
  <script type="text/brightscript" uri="GHLivePanelMiniEPGChannel.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="width" type="integer" value="100" onChange="Draw"/>
    <field id="height" type="integer" value="100" onChange="Draw"/>
    <field id="cardPaddingX" type="integer" value="5" onChange="Draw" />
    <field id="cardPaddingY" type="integer" value="15" onChange="Draw" />
    <field id="back_color" type="string" value="0x000000" onChange="Draw" alias="backpanel.color" />
    <field id="content" type="assocarray" onChange="onContentChange" />
    <!-- interno -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="backpanel" />
    <LayoutGroup id="card">
      <Label id="title" text="title" />
      <Poster id="image"/>
    </LayoutGroup>

    <Poster id="candado" loadDisplayMode="scaleToFit" visible="false" />
    <Poster id="corazon" height="16" width="20" loadDisplayMode="scaleToFit" visible="false" />
  </children>

</component>
