<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->

<component name="ProfileManagementPageLite" extends="Page">
  <script type="text/brightscript" uri="ProfileManagementPageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="data" type="assocarray" onChange="refreshData" />
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <Poster id="logo" translation="[100,20]" width="123" height="24" loadDisplayMode="scaleToFit"/>
    <Label id="title" translation="[0,120]" width="1280" horizAlign="center" text="" />
    <GHRowList id="theGrid" translation="[0, 200]" visible="true"/>
    <!-- <Label id="msgNoMethod" translation="[400,292]" visible="false" width="480" height="120" horizAlign="center" text="msgNoMethod" wrap="true" /> -->
    <GHButton id="listo" translation="[468,540]" value="LISTO" width="344" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
    <!-- <GHButton id="acept" translation="[600,412]" value="ACEPT" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />-->
    <GHLoading id="spinner" visible="true" />
  </children>

</component>
