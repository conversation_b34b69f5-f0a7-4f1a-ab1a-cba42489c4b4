<?xml version="1.0" encoding="utf-8" ?>

<component name="GHVideoPanelSeason" extends="Group">
  <script type="text/brightscript" uri="GHVideoPanelSeason.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />


  <interface>
    <field id="info" type="assocarray" onChange="onInfoUpdate" />
    <field id="seasons" type="assocarray" onChange="onSeasonsUpdate" />
    <!-- interfaz de salida     -->
    <field id="selected" type="assocarray" alwaysNotify="true" />
    <field id="keypressed" type="string" alwaysNotify="true"/>
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Rectangle id="fondoDialogo" color="#131313" translation = "[24,360]" width="1232" height="280" />
    <Label id="title" focusable="false" translation="[0,10]" width="1280" height="48" text="*" />

    <GHRowList id="seasons" visible="true" itemComponentName="ItemSeasonsMenu" itemSize="[1165,50]" numRows="1" rowItemSize="[[184,48]]" rowHeights="[184,48]" rowItemSpacing="[[18,0],[18,0]]" focusXOffset="[0,0]" rowLabelOffset="[[0,0]]" showRowLabel="[false]" showRowCounter="[false]" drawFocusFeedback="true" translation="[60,375]" focusBitmapUri="pkg:/images/2px_back.9.png" />

    <GHRowList id="episodesPlayer" visible="true" translation="[60, 440]" />
  </children>
</component>
