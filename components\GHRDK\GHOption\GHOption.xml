<?xml version="1.0" encoding="utf-8" ?>
<component name="GHOption" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHOption.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- text -->
    <field id="text" type="string" value="MyLabel" onChange="updateFieldText" alias="label.text" />
    <field id="wrap" type="boolean" value="false" onChange="updateFieldWrap"/>
    <field id="font" type="node" alias="label.font" />
    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="87" onChange="updateFieldWidth" />
    <field id="height" type="string" value="37" onChange="updateFieldHeight" />
    <!-- align -->
    <field id="horizAlign" type="string" value="center" alias="label.horizAlign" />
    <field id="vertAlign" type="string" value="center" alias="label.vertAlign" />
    <!-- color -->
    <field id="color" type="string" value="#7F8282" onChange="updateFieldColor" />
    <field id="selColor" type="string" value="0xFFFFFF" onChange="updateFieldSelColor" />
    <field id="backColor" type="string" value="#28292F" onChange="updateFieldBackColor" />
    <field id="selBackColor" type="string" value="#DE171700" onChange="updateFieldSelBackColor" />
    <field id="backColorSelected" type="string" value="#9B0F0F00" onChange="updateFieldBackColor" />
    <!-- focus -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <!-- ok button -->
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="menuSelected" type="boolean" value="false" alwaysNotify="true" />
    <field id="menuFocused" type="boolean" value="false" alwaysNotify="true" />
    <field id="value" type="string" value="mybutton"/>
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
    <!-- end -->
    <field id="padding" type="string" value="5" onChange="updateFieldPadding" alwaysNotify="true" />
    <field id="focusPadding" type="string" value="5" onChange="updateFieldFocusPadding" alwaysNotify="true" />
    <!--<field id="paddingVert" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true" />-->
    <!--<field id="paddingHoriz" type="string" value="8" onChange="updateFieldPadding" alwaysNotify="true" />-->
    <field id="focusMap" type="string" value= "PIllTopNavBarEn-bg.9.png" />
    <field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor" />
  </interface>

  <children>
    <Poster id="border" translation="[0,0]" width="360" height="120" uri="" visible="false" blendColor="0x0000ff" />
    <Rectangle id="background" color="0xffffff" translation="[0,0]" width="360" height="80">
      <Label id="label" text="MyLabel" color="0x000000" translation="[0,0]" width="0" height="0" vertAlign="center" horizAlign="center" />
    </Rectangle>
  </children>

</component>
