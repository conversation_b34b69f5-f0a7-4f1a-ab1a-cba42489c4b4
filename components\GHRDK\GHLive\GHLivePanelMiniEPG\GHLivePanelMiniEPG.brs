' GHLivePanelMiniEPG
' gdripoll - 2023
' -----------------------------

' inicial
' -----------------------------
sub Init()
  ' m.top.debug = true
  InitialData()' variables m
  InitialDraw() ' initial definition ---
  buildKeyTimer() ' timer long OK ---
  ToutTimerInit() ' timer autoclose
end sub
sub InitialData()

  m.haveEvents = false
  m.haveChannels = false

  m.eventOffset = 0

  ' m.map = {
  '   "joystick": { "up": invalid, "right": invalid, "down": invalid, "left": invalid }
  '   ' "botonera": { "up": invalid, "right": "joystick", "down": invalid, "left": "joystick" }
  ' }
  ' events
  m.top.ObserveField("visible", "onVisibleChange")
  m.msgText = m.top.findNode("msgText")
  m.msgBack = m.top.findNode("msgBack")
  m.channel = m.top.findNode("channel")
  m.current = m.top.findNode("current")
  m.later = m.top.findNode("later")
  m.loading = m.top.findNode("loading")
  m.upArrow = m.top.findNode("upArrow")
  m.downArrow = m.top.findNode("downArrow")
  m.leftArrow = m.top.findNode("leftArrow")
  m.rightArrow = m.top.findNode("rightArrow")


  m.upArrow.uri = ghGetImageByMode("ic_chevron_up.png")
  m.downArrow.uri = ghGetImageByMode ("ic_chevron_down.png")
  m.leftArrow.uri = ghGetImageByMode ("ic_chevron_left.png")
  m.rightArrow.uri = ghGetImageByMode ("ic_chevron_right.png")

  if ghGetDisplayMode() = "FHD" then
    m.upArrow.translation = "[154,-25]"
    m.leftArrow.translation = "[26,56]"
  end if

  m.currAndLater = {
    channel: invalid
    current: invalid
    later: invalid
  }
end sub
sub InitialDraw()
  if m.top.debug then print ghLogHead();"Draw -init-"
  m.msgText.setFields({
    translation: [1000, 0]
    font: ghGetFont(18, "regular")
    focusable: false
    horizAlign: "right"
    text: ghTranslate("xx", "Mantener [ok] apretado para que pase algo")
    color: "#FFFFFF"
  })
  textBound = m.msgText.boundingRect()
  m.msgText.translation = [1250 - textBound.width, 0] ' recalculando
  textBound = m.msgText.boundingRect()
  m.msgBack.setFields({
    translation: [752, 560]
    width: 456
    height: 104
  })
  ' -----------
  m.loading.setFields({
    width: 1280
    height: 170
  })
  m.channel.setFields({
    translation: [72, 20]
    width: 192
    height: 104
    back_color: "0x1A1A1A"
  })
  m.current.setFields({
    translation: [280, 20]
    width: 456
    height: 104
    border_url: ghGetImageByMode("4px_Focus2.9.png")' foco
    back_color: "0x1A1A1A"
    ' tag: "ahora"
    recordatorio: true
  })
  m.later.setFields({
    translation: [752, 20]
    width: 456
    height: 104
    back_color: "0x1A1A1A"
    ' tag: "mas tarde"
    recordatorio: true
  })
  ' -----------
  if m.top.debug then print ghLogHead();"Draw -end."
end sub
' ingreso/salida
' -----------------------------
sub onVisibleChange(event)
  data = event.getData()
  if data then ' entro----
    print ghLogHead();"onVisibleChange -ON- ";data
    if NotLoading() then
      DataRefresh()
    else
      m.top.visible = false
    end if
    ToutTimerGo()
  else ' salgo ------------------
    print ghLogHead();"onVisibleChange -OFF- ";data
    m.top.visible = false
    m.top.setFocus(false)
    m.kkey = ""
    m.kpress = false
    ToutTimerPause()
  end if
end sub
' Data events
' -----------------------------
sub DataRefresh()
  try
    if m.top.debug then print ghLogHead();"DataRefresh ** "
    if m.haveChannels and m.haveEvents then
      m.curChan = m.top.channels[m.top.jumpto]
      m.currAndLater = getChannelCurrentLaterEvent(getChannelByGroupId(m.curChan.group_id))
      ' DEBUG
      if m.top.debug then
        print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
        print ghLogHead();"DataRefresh -- ";m.curChan
        print ghLogHead();"DataRefresh -- ";m.currAndLater
        print ghLogHead();"DataRefresh -- ";m.currAndLater.current
        print ghLogHead();"DataRefresh -- ";m.currAndLater.current.item
        print ghLogHead();"DataRefresh -- ";m.currAndLater.later
        print ghLogHead();"DataRefresh -- ";m.currAndLater.later.item
        print "~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
      end if
      m.channel.content = m.curChan
      m.current.channelImage = m.curChan.image
      m.current.content = m.currAndLater.current
      if m.currAndLater.later <> invalid then
        m.later.channelImage = m.curChan.image
        m.later.content = m.currAndLater.later
        m.later.visible = true
      else
        m.later.visible = false
      end if
    end if
  catch error
    ghErrorDumpToLog("MiniEpg dataRefresh", {}, error)
    ghErrorDump(error)
  end try
end sub
sub onChannelsUpdate(event) ' carga de canales
  data = event.getData()
  if m.top.debug and data.Count() > 0 then print ghLogHead();"onChannelsUpdate -- ";data[0]
  m.haveChannels = true
  if NotLoading() then DataRefresh() ' si ya cargue, dibujo
end sub
sub onEventsUpdate(event) ' carga de eventos
  data = event.getData()
  if m.top.debug then print ghLogHead();"onEventsUpdate -- ";data
  m.haveEvents = true
  if NotLoading() then DataRefresh() ' si ya cargue, dibujo
end sub
sub onJumpTo(event) ' cambio de canal
  data = event.getData()
  maximo = m.top.channels.Count() - 1
  if m.top.debug then print ghLogHead();"onJumpTo "data;" de ";maximo
  if data < 0 then data = maximo
  if data > maximo then data = 0
  m.top.jumpto = data
  m.currentPos = data
  DataRefresh()
end sub
' key events
' -----------------------------
function onKeyEvent(key, press) as boolean
  print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  if press then
    m.ktimer.control = "start"
    m.kkey = key
    m.kpress = true
  else
    if m.kpress then
      m.ktimer.control = "stop"
      triggerKey()
    end if
  end if
  return true
end function
sub triggerKey()
  if m.top.debug then print ghLogHead();"triggerKey << ";m.kkey
  m.kpress = false ' fuera del timer de teclado
  ToutTimerReset() ' reset al timer de cerrado
  ' -------------------

  'hombre muerto
  m.top.keypressed = "x"

  if m.kkey = "down" then ' canales +1
    m.top.jumpto += 1
    m.eventOffset = 0
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "DOWN!"
    print "****************************************************************************************"
    print "****************************************************************************************"
  else if m.kkey = "up" then ' canales -1
    m.top.jumpto -= 1
    m.eventOffset = 0
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "UP!"
    print "****************************************************************************************"
    print "****************************************************************************************"
    ' m.top.cmd = { cmd: "changeChannel", channel: getChannelsElementByGroupId(m.curChan.group_id, -1) }
  else if m.kkey = "fastforward" then ' canales +5
    m.top.jumpto += 5
    m.eventOffset = 0
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "FORWARD!"
    print "****************************************************************************************"
    print "****************************************************************************************"
    ' m.top.cmd = { cmd: "changeChannel", channel: getChannelsElementByGroupId(m.curChan.group_id, 5) }
  else if m.kkey = "rewind" then ' canales -5
    m.top.jumpto -= 5
    m.eventOffset = 0
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "BACKWARD!"
    print "****************************************************************************************"
    print "****************************************************************************************"
    ' m.top.cmd = { cmd: "changeChannel", channel: getChannelsElementByGroupId(m.curChan.group_id, -5) }
  else if m.kkey = "left" then ' eventos -- anterior
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "LEFT!"
    print "****************************************************************************************"
    print "****************************************************************************************"
    m.eventOffset -= 1
  else if m.kkey = "right" then ' eventos -- siguiente
    print "****************************************************************************************"
    print "****************************************************************************************"
    print "RIGHT"
    print "****************************************************************************************"
    print "****************************************************************************************"
    m.eventOffset += 1
  else if m.kkey = "long_OK" then ' menu -- pantalla
    print "*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/"
    print "*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/"
    print "LONG OK"
    print "*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/"
    print "*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/*/"

    m.top.visible = false
    m.top.cmd = {
      cmd: "showOptions",
      channel: getChannelsElementByGroupId(m.curChan.group_id),
      program: m.currAndLater.current
    }
  else if m.kkey = "OK" then ' cambio de canal
    m.top.visible = false
    m.top.cmd = {
      cmd: "changeChannel",
      channel: getChannelsElementByGroupId(m.curChan.group_id)
      program: m.currAndLater.current
    }
  else if m.kkey = "back" then ' salida
    m.top.visible = false
  end if
  DataRefresh()
  ' -----------------------------
  if m.top.debug then print ghLogHead();"triggerKey -end- "
end sub
' channel functions
' ----------------------------------------
function ghTimeGet(unix = invalid)
  if m.top.debug then print ghLogHead();"ghTimeGet ** ";unix
  t = CreateObject("roDateTime")
  if unix <> invalid then t.fromSeconds(unix)
  return t
end function
function ghTimeFormat(unix)
  if m.top.debug then print ghLogHead();"ghTimeFormat ** ";unix
  f = ghTimeGet(unix).toISOString()
  if f <> invalid then ' 2022-05-12T15:30:00Z
    return f.mid(11, 5)
  else
    return ""
  end if
end function
function getChannelByGroupId(id, offset = 0)
  if m.top.debug then print ghLogHead();"getChannelByGroupId **"
  totChan = m.top.events.getChildCount()
  for nChan = 0 to totChan - 1
    c = m.top.events.getChild(nChan)
    if c.id = id then
      if offset <> 0 then
        nChan = nChan + offset
        if nChan > totChan - 1 then
          nChan = (nChan - (totChan - 1)) - 1 ' avanzo por encima de la frontera
        end if
        if nChan < 0 then
          nChan = ((totChan - 1) + nChan) + 1 ' retrocedo por encima de la frontera
        end if
        print "nChan=";nChan
      end if
      return m.top.events.getChild(nChan)
    end if
  end for
  return invalid
end function
function getChannelCurrentLaterEvent(chan)
  if m.top.debug then print ghLogHead();"getChannelCurrentLaterEvent -- ";chan
  curr = {
    current: invalid
    later: invalid
  }
  try
    curTime = CreateObject("roDateTime").AsSeconds()
    eCant = chan.GetChildCount()
    for eNro = 0 to eCant - 1
      e = chan.getChild(eNro)
      if e <> invalid then
        tIni = e.playstart
        tEnd = e.playstart + e.playduration
        if curTime >= tIni and curTime <= tEnd then ' es el actual
          if m.eventOffset > 0 then ' avanzo...
            showNro = eNro + m.eventOffset
            ' print "*** ";eNro;" + ";m.eventOffset;" = ";showNro
            if showNro > eCant - 1 then ' si me fui de mambo
              showNro = eCant - 1
              m.eventOffset = eCant - 1
            end if
          else if m.eventOffset < 0 then ' si me fui para la izquierda
            m.eventOffset = 0
            showNro = eNro
          else
            showNro = eNro
          end if
          ' print "*** ";eNro;" + ";m.eventOffset;" = ";showNro

          curr.current = chan.getChild(showNro)
          if eNro = showNro then ' elijo el tag
            curr.current.id = "ahora" ' el id venia vacio
          else
            curr.current.id = "masTarde"
          end if

          if showNro + 1 < eCant then
            curr.later = chan.getChild(showNro + 1)
            if eNro = (showNro + 1) then ' elijo el tag
              curr.later.id = "ahora" ' el id venia vacio
            else
              curr.later.id = "masTarde"
            end if
            ' curr.later.id = "masTarde" ' el id venia vacio / siempre masTarde
          else
            curr.later = invalid
          end if
        end if
      end if
    end for
  catch error
    ghErrorDumpToLog("miniEpg getChannelCurrentLaterEvent", {}, error)
    ghErrorDump(error)
  end try

  return curr
end function
function getChannelsElementByGroupId(id, offset = 0)
  try
    if m.top.debug then print ghLogHead();"getChannelsElementByGroupId -- ";id, offset
    totChan = m.top.channels.Count()
    for nChan = 0 to totChan - 1
      c = m.top.channels[nChan]
      if c.group_id = id then
        if offset <> 0 then
          nChan = nChan + offset
          if nChan > totChan - 1 then
            nChan = (nChan - (totChan - 1)) - 1 ' avanzo por encima de la frontera
          end if
          if nChan < 0 then
            nChan = ((totChan - 1) + nChan) + 1 ' retrocedo por encima de la frontera
          end if
          print "nChan=";nChan
        end if
        return m.top.channels[nChan]
      end if
    end for
  catch error
    ghErrorDumpToLog("miniEpg getChannelsElementByGroupId", {}, error)
    ghErrorDump(error)
  end try
  return invalid
end function
' loading
' -----------------------------
function NotLoading() ' chequea el loading
  res = false
  if m.haveChannels and m.haveEvents then
    m.loading.visible = false
    if m.top.available then
      m.top.visible = true ' mini epg aparece automaticamente
    end if
    res = true
  else
    m.loading.visible = true
  end if
  if m.top.debug then print ghLogHead();"NotLoading -- ";res
  return res
end function

' KEY Timer
' -----------------------------
sub buildKeyTimer()
  if m.top.debug then print ghLogHead();"buildKeyTimer -ini- "
  m.kkey = invalid
  m.kpress = false
  m.kTimer = CreateObject("roSGNode", "Timer")
  m.kTimer.duration = 1
  m.kTimer.repeat = false
  m.kTimer.ObserveField("fire", "triggerKeyTimer")
  if m.top.debug then print ghLogHead();"buildKeyTimer -end- "
end sub
sub triggerKeyTimer()
  if m.top.debug then print ghLogHead();"triggerKeyTimer -ini- "
  m.kkey = "long_" + m.kkey
  triggerKey()
  if m.top.debug then print ghLogHead();"triggerKeyTimer -end- "
end sub
sub updateFieldFocus(event)
  status = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -status ";status
  ' FormatJoystick()
  if status
    m.kkey = ""
    m.kpress = false
  end if
end sub
' TIMEOUT TIMER
' -----------------------------
sub ToutTimerInit()
  if m.top.debug then print ghLogHead();"ToutTimerInit **"
  m.tmrClose = CreateObject("roSGNode", "Timer")
  m.tmrClose.ObserveField("fire", "ToutTimerTrigger")
  m.tmrClose.repeat = true ' una vez
  m.tmrClose.duration = 1
  ToutTimerReset()
end sub
sub ToutTimerGo()
  if m.top.debug then print ghLogHead();"ToutTimerGo **"
  ToutTimerReset()
  m.tmrClose.control = "start"
end sub
sub ToutTimerPause()
  if m.top.debug then print ghLogHead();"ToutTimerPause **"
  ToutTimerReset()
  m.tmrClose.control = "stop"
end sub
sub ToutTimerReset()
  if m.top.debug then print ghLogHead();"ToutTimerReset **"
  m.timerCount = ghGetChild(m.global, "timers.panels.miniEPG", 10)
end sub
sub ToutTimerTrigger()
  m.timerCount -= 1
  ' if m.top.debug then print ghLogHead();"TimerTrigger ** ";m.timerCount
  if m.timerCount < 1 then
    if m.top.debug then print ghLogHead();" TIMER - GO !!"
    ToutTimerReset()
    m.kkey = "back" ' cierro con back
    triggerKey()
    ' handleContinue()
  end if
end sub
' ENF FILE
' -----------------------------
