' ********** Copyright 2020 Roku Corp.  All Rights Reserved. **********

function Init()
  m.initiazed = false
  ' m.top.debug = true
  m.topView = m.top.findNode("contentTimeGrid")
  m.view = m.top.findNode("contentTimeGrid")
  m.top.viewContentGroup = m.top.FindNode("viewContentGroup")
  InitTimeGridViewNodes()
  m.spinner = m.top.FindNode("spinner")
  m.spinner.setFields({
    uri: "pkg:/images/loading.png"
    translation: [590, 450]
    visible: true
  })
  ' events
  m.top.ObserveField("visible", "OnVisibleChange")
  m.top.ObserveField("focusedChild", "OnFocusedChild")
  m.top.ObserveField("wasShown", "OnWasShown")
  m.top.ObserveField("content", "OnContentChange")
  m.top.ObserveField("jumpToRow", "OnJumpToRowChanged")
  m.top.ObserveField("jumpToRowItem", "OnJumpToRowItemChanged")
  ' m.top.ObserveField("posterShape", "OnPosterShapeChange")
  ' m.top.posterShape = "4x3"
  m.view.observeField("content", "onTimeGridViewContentChange")
  m.view.observeField("channelFocused", "channelFocused")
  m.view.observeField("programFocused", "programFocused")
  ' m.view.observeField("programSelected", "OnProgramSelected")
  m.view.observeField("cmd", "onCmd")

  currentTime = CreateObject("roDateTime") ' roDateTime is initialized
  t = currentTime.AsSeconds() ' to the current time
  t = t - (t mod 1800) ' RDE-2665 - TimeGrid works best when contentStartTime is set to a 30m mark
  m.view.contentStartTime = t
  m.view.leftEdgeTargetTime = currentTime.AsSeconds()
  m.view.programTitleFont = ghGetFont(24, "bold")
  m.view.timeLabelFont = ghGetFont(18, "medium")
  m.view.timeLabelColor = "#FFFFFF"
  m.view.channelNoDataText = ghTranslate("epg_loading", "Cargando...")
  m.view.loadingDataText = ghTranslate("epg_loading", "Cargando...")
  m.view.automaticLoadingDataFeedback = false

  m.view.numRows = 7
  m.view.fillProgramGaps = true

  ' View constants
  m.detailsTimeGridSpacing = 25
  m.timeGridWasMoved = false

  m.currentRow = 0
  m.chaprogIndex = [0, 0]

end function

sub onFocusChange(event)
  data = event.getData()
  m.view.setFocus(data)
end sub


sub InitTimeGridViewNodes()
  ' --------------
  m.details = m.top.viewContentGroup.CreateChild("ItemDetailsView")
  m.details.Update({
    id: "details"
    translation: [100, 60]
    maxWidth: 666
  })
  ' --------------
  m.poster = m.top.viewContentGroup.CreateChild("StyledPoster")
  m.poster.Update({
    id: "poster"
    translation: [125, 0]
    maxWidth: 357
    maxHeight: 201
  })
  ' --------------
  m.view = m.top.findNode("contentTimeGrid")
  m.view.Reparent(m.top.viewContentGroup, false)
  m.view.setFields({
    showPastTimeScreen: false
    channelInfoComponentName: "TimeGridChannelItemComponent"
    chanelInfoFont: ghGetFont(24, "bold")
    width: "30"
    height: "50"
  })
end sub

sub OnVisibleChange(event)
  data = event.GetData()
  if m.top.debug then print ghLogHead();"OnVisibleChange -- ", data

  if m.spinner.visible = false then
    ' if m.top.debug then print ghLogHead();"OnVisibleChange -- m.top.data= ";m.top.data.getChild(m.currentRow)
    ' if m.top.debug then print ghLogHead();"OnVisibleChange -- getChannelCurrentLaterEvent= ";getChannelCurrentLaterEvent(m.top.data.getChild(m.currentRow))
    m.top.jumpToRowItem = [m.currentRow, getChannelCurrentEvent(m.top.data.getChild(m.currentRow))]
    onTimeGridViewContentChange()
  end if

  m.view.setFocus(data)
end sub

sub handleJumpTo(event)
  data = event.GetData()
  if m.top.debug then print ghLogHead("jumpTo");"handleJumpTo -- ";data
  m.currentRow = data
  m.view.jumpToChannel = data
  m.view.jumpToProgram = 0
end sub

sub handleData(event)
  data = event.GetData()

  InitContentGetterValues()
  m.MAX_RADIUS = 45
  ' m.debug = false
  m.Handler_ConfigField = "HandlerConfigTimeGrid"
  m.SectionKeyField = "CM_row_ID_Index"

  m.view.content = data
end sub

function onKeyEvent(key, press) as boolean
  handled = true
  if m.top.visible = true then
    if press then
      if key = "back" then
        handled = true
        m.top.visible = false
      end if
    end if
  end if
  return handled
end function


' TODO -- GOOSE !!!!!!!
sub ShowSpinner(show)
  m.spinner.visible = show
  if m.top.debug then print ghLogHead();"ShowSpinner -- now:";m.spinner.visible

  if show
    m.spinner.control = "start"
    m.topView.visible = false
  else
    m.spinner.control = "stop"
    m.topView.visible = true
    if m.top.debug then print ghLogHead();"ShowSpinner -- STOP:"
    if m.top.debug then print ghLogHead();"ShowSpinner -- m.top.jumpTo= ";m.top.jumpTo
    if m.top.debug then print ghLogHead();"ShowSpinner -- m.currentRow= ";m.currentRow
    m.top.jumpToRow = m.currentRow ' actualizo el channel
  end if
end sub

function channelFocused() ' event as object
  if m.view <> invalid
    ChannelProgramFocused(m.view.channelFocused, m.view.programFocused)
  end if
end function

function programFocused() ' event as object
  if m.view <> invalid
    ChannelProgramFocused(m.view.channelFocused, m.view.programFocused)
    ' hombre muerto
    m.top.keypressed = "x"
  end if
end function

sub OnJumpToRowChanged(event as object)
  row = event.getData()
  if m.view <> invalid
    m.view.jumpToChannel = row
  end if
end sub

sub OnJumpToRowItemChanged(event as object)
  jumpToRowItem = event.getData()
  if m.view <> invalid
    m.view.jumpToChannel = jumpToRowItem[0]
    m.view.jumpToProgram = jumpToRowItem[1]
  end if
end sub

' ChannelProgramFocused is invoked when either channelFocused or programFocused was changed
' Used for loading content when user navigates vertically
' And updating the item details panel
sub ChannelProgramFocused(currentRowIndex as integer, currentItemIndex as integer)
  m.chaprogIndex = [currentRowIndex, currentItemIndex]
  row = invalid
  if m.view.content <> invalid then
    row = m.view.content.GetChild(currentRowIndex)
  end if
  if row <> invalid
    ' focusIndexToSet = currentItemIndex
    if currentItemIndex < 0 then currentItemIndex = 0
    UpdateItemDetails(currentRowIndex, currentItemIndex)
  end if
  m.previousFocusedRow = currentRowIndex
  m.previousFocusedItemIndex = currentItemIndex
end sub

sub UpdateItemDetails(channelIndex, programIndex)
  if m.top.debug then print ghLogHead();"UpdateItemDetails -- (";channelIndex;": , ";programIndex;")"

  content = m.view.content
  if content = invalid then return ' ----->
  channel = content.GetChild(channelIndex)
  if channel = invalid then return ' ----->
  program = channel.GetChild(programIndex)

  shouldClearMeta = false
  if program = invalid then
    shouldClearMeta = true
  else
    diff = m.view.leftEdgeTargetTime - program.PLAYSTART
    bIsInPast = diff > 0 and diff - program.playduration > 0
    bIsInFuture = diff + m.view.duration < 0

    if bIsInPast then ' need to account duration that item might be partly visible
      ' focused item is in past
      shouldClearMeta = true
    else if bIsInFuture then ' item might be partly visible in future
      ' focused item is in future
      shouldClearMeta = true
    end if
  end if

  if shouldClearMeta
    program = CreateObject("roSGNode", "ContentNode")
    ' don't set any title here
    ' in some cases content wouldn't even be loaded in future,
    ' as there might be no config for channel
    program.title = ""
  end if

  m.details.content = program
  m.poster.uri = program.hdposterurl
  if m.poster.uri.Len() > 0 then
    ' m.details.translation = [m.poster.translation[0] + m.poster.width + 15, m.poster.translation[1]]
    AlignTimeGrid()
  else
    ' m.details.translation = m.poster.translation
  end if
end sub

sub AlignTimeGrid()
  if not m.timeGridWasMoved
    posterHeight = m.poster.maxHeight
    posterBottomY = m.poster.boundingRect().y + posterHeight

    ' calculate timeBarHeight because translation of TimeGrid component
    ' somehow is not calculated from the top of TimeBar
    timeBarHeight = m.view.timeBarHeight
    if timeBarHeight = 0 then timeBarHeight = 50

    timeGridY = posterBottomY + timeBarHeight + m.detailsTimeGridSpacing
    m.view.translation = [m.view.translation[0], timeGridY]

    m.timeGridWasMoved = true
  end if
end sub

function OnPosterShapeChange() as object
  m.poster.shape = m.top.posterShape
  if m.top.content <> invalid then
    ChannelProgramFocused(m.view.channelFocused, m.view.programFocused)
  end if
end function

sub onTimeGridViewContentChange()
  if m.initiazed = false then
    m.initiazed = true
    if m.top.debug then print ghLogHead();"onTimeGridViewContentChange --"

    ' m.spinner.visible = (m.view.content = invalid or m.view.content.GetChildCount() = 0)
    ShowSpinner(m.view.content = invalid or m.view.content.GetChildCount() = 0)

    if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- m._isContentFocusResetDone:";m._isContentFocusResetDone

    ' This logic will reset focus to current time or to
    ' first valid item if there are no content for current time
    ' if m._isContentFocusResetDone = true then return

    content = m.view.content
    if content = invalid then return

    channel = content.getChild(0)
    if channel = invalid or channel.getChildCount() = 0 then return

    isNowProgramAvailable = false
    currentTime = m.view.leftEdgeTargetTime

    if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- currentTime:";currentTime

    progsCount = channel.getChildCount()
    for p = 0 to progsCount - 1
      program = channel.GetChild(p)
      ' for each program in channel.GetChildren(-1, 0)
      ' if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- program.PlayStart:";program.PlayStart
      ' if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- program.PlayDuration:";program.PlayDuration
      if program.PlayStart <= currentTime and program.PlayStart + program.PlayDuration >= currentTime then
        isNowProgramAvailable = true
        ' /////////////////////////
        ' if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- ACTUAL!!!:";program
        ' if m.top.debug then print ghLogHead();"onTimeGridViewContentChange -- SALTAR! A:"; p
        ' ChannelProgramFocused(currentRowIndex as integer, currentItemIndex as integer)
        ' /////////////////////////
        exit for
      end if
    end for

    if not isNowProgramAvailable then ' focus to begin on content
      m.view.jumpToProgram = 0
      m.view.leftEdgeTargetTime = channel.GetChild(0).PlayStart
    end if

    m._isContentFocusResetDone = true
  end if
end sub

function AlignTimeToHours(timestamp as integer) as integer
  return timestamp - timestamp mod 3600
end function
function NewCycleNodeChildrenIterator(node, startIndex, count) as object
  maxIndex = node.GetChildCount() - 1

  while startIndex < 0
    startIndex = maxIndex + startIndex
  end while

  if startIndex > maxIndex then
    startIndex = maxIndex
  end if

  return {
    _node: node

    _max_index: maxIndex
    _index: startIndex

    _max_count: count
    _current_count: 0

    IsNextAvailable: function() as boolean
      return m._current_count < m._max_count
    end function

    next: function() as integer
      if m.IsNextAvailable() then
        if m._index >= m._max_index then
          m._index = 0
        else if m._index < m._max_index then
          m._index++
        end if
        m._current_count++
      end if
      return m._index
    end function

    GetIndex: function() as integer
      return m._index
    end function
  }
end function

' copia contentmanager
sub InitContentGetterValues()
  m.Handler_ConfigField = "HandlerConfigGrid"
  'change poster url for items that are added to queue
  ' m.debug_loadingItems = false
  ' Tells how many extra rows should be loaded, so we have more that visible rows loaded
  ' to prefetch data when developer will need it
  m.EXTRA_ROWS = 2
  m.VISIBLE_ITEMS = 5

  resetIdleValues() 'reset all idle values
  m.MAX_RADIUS = 30 'maximum radius for loading content
  m.ContentManager_id = -1 ' internal fields
  m.ContentManager_callbacks = {} 'callbacks map for each task that is executed
  m.ContentManager_TaskIds = {} 'map of current pending tasks so we don't add task to queue again
  m.ContentManager_Page_IDs = {} 'map for holding current loading page per row so we don't load it again
  m.ContentManager_Page_Fails = {} 'map for holding number of fails per row and page,  {"0" (row_index):{"1"(page_index):0 (count)}}

  ' Fields for queue
  m.waitingQueue = []
  'tasks that are running now
  m.runningQueue = []

  m.previousFocusedRow = -1
  m.previousFocusedItemIndex = -1
  'background update timer
  m.IdleUpdateTimer = CreateObject("roSGNode", "Timer")
  if m.top.IDLE_ROW_LOAD_TIME <> invalid then
    m.IdleUpdateTimer.duration = m.top.IDLE_ROW_LOAD_TIME
  else
    m.IdleUpdateTimer.duration = 5
  end if
  m.IdleUpdateTimer.ObserveField("fire", "OnIdleLoadExtraContent")
  m.IdleUpdateTimer.ObserveField("control", "OnIdleStateChanged")
  m.IdleUpdateTimer.repeat = true
  m.CanLoadContent = true
  'image url that should be replaced for items that are added to queue
  m.hdposterUrl = "https://dummyimage.com/25x25/ff0000/2b3299/locked.png&text=locked"

  m.CM_row_ID_Index = 0
  m.uniqueRowIndex = 0
end sub

'This function creates task and starts it
'no max number of tasks check is performed use @see QueueGetContentData
function GetContentData(callback, HandlerConfig, content = invalid, additionalFields = invalid as object)
  task = CreateTask(callback, HandlerConfig, content, additionalFields)
  if task <> invalid then
    task.control = "RUN"
  end if
  return task
end function

'Creates new tasks and populates all fields
'@param callback - [AA] callback that will be called
'@param HandlerConfig - [AA] - config that will be used to load content
'@param content - [AA] - content that will be passed to developer
'@param additionalFields - [AA] - any additional fields map that will be set to task interface
function CreateTask(callback as object, HandlerConfig as object, content as object, additionalFields as object) as object
  task = invalid
  if HandlerConfig <> invalid and HandlerConfig.name <> invalid then
    task = GetNodeFromChannel(HandlerConfig.name)
    if task <> invalid then
      if HandlerConfig.fields <> invalid then task.SetFields(HandlerConfig.fields)
      if HandlerConfig.query <> invalid then task.SetFields({ query: HandlerConfig.query })
      if additionalFields <> invalid then task.SetFields(additionalFields)
      RegisterTaskCallback(callback, task)
      if m.top.debug then print ghLogHead();"CreateTask -- GetContentData: " HandlerConfig.name' ":" task.functionName
      if not task.hasField("content") then task.AddField("content", "node", true)
      task.content = content
      ' To avoid warning prints in console for HandlerRAF
      task.HandlerConfig = HandlerConfig
    end if
  end if
  return task
end function
'Registers tasks callback to provided tasks
'callback will be called when task is finished, @see OnTaskContentReceived
sub RegisterTaskCallback(callback as object, task as object)
  if m.ContentManager_id = invalid then m.ContentManager_id = 0
  if not task.HasField("ContentManager_id") then
    m.ContentManager_id++
    task.AddFields({ ContentManager_id: m.ContentManager_id })
  end if
  key = m.ContentManager_id.ToStr()
  if m.ContentManager_callbacks = invalid then m.ContentManager_callbacks = {}
  if m.ContentManager_callbacks[key] = invalid then m.ContentManager_callbacks[key] = { task: task, callbacks: [] }
  m.ContentManager_callbacks[key].callbacks.Push(callback)

  task.ObserveField("result", "OnTaskContentReceived")
  task.ObserveField("state", "OnTaskContentReceived")
  task.ObserveField("finished", "OnTaskContentReceived")
end sub
'Observer function that is executed when task is finished
'@see RegisterTaskCallback for fields that can trigger this function
sub OnTaskContentReceived(event as object)
  field = event.GetField()
  task = event.GetRoSGNode()
  'fields that say that task has finished
  FinishedFieldsMap = {
    result: ""
    state: "stop"
    finished: ""
  }

  if FinishedFieldsMap[field] <> invalid then
    if FinishedFieldsMap[field].Len() > 0 then
      data = event.GetData()
      ' check if we received proper event
      if GetInterface(data, "ifString") <> invalid and FinishedFieldsMap[field] <> data then return
    end if
    if task <> invalid then
      if task.ContentManager_id <> invalid and m.ContentManager_callbacks[task.ContentManager_id.ToStr()] <> invalid then
        content = task.content
        key = task.ContentManager_id.ToStr()

        ' clear task registration before processing so callback can add new loading if task failed
        for each observedFields in FinishedFieldsMap
          task.UnobserveFieldScoped(observedFields)
        end for
        callbacks = m.ContentManager_callbacks[key].callbacks
        m.ContentManager_callbacks.Delete(key)

        for each callback in callbacks
          callback.task = task
          childcount = content.GetChildCount()

          isFailed = task.failed <> invalid and task.failed

          if not isFailed and (childcount > 0 or (callback.mAllowEmptyResponse <> invalid and callback.mAllowEmptyResponse)) then
            if callback.OnReceive <> invalid then callback.OnReceive(content)
          else
            if callback.OnError <> invalid then callback.OnError(content)
          end if
          if callback.OnComplete <> invalid then callback.OnComplete()
        end for
      end if
    else
      ? "ERROR: Content Manager, task was destroyed before data was set"
    end if
  end if
end sub
'This function is called by RunNextTaskFromQueue, it creates tasks and adds it to queue
'@see RunNextTaskFromQueue
function ExecuteGetContentData(callback as object, HandlerConfig as object, content = invalid as object, additionalFields = invalid as object)
  newCallback = {
    callback: callback

    onReceive: sub(data)
      m.RemoveItemFromQueue()
      if m.callback.onReceive <> invalid then m.callback.OnReceive(data)
    end sub

    onError: sub(data)
      m.RemoveItemFromQueue()
      if m.callback.onError <> invalid then m.callback.OnError(data)
    end sub

    ' removes this task from queue
    ' call this before callback so callback can retry same content
    removeItemFromQueue: sub()
      gThis = GetGlobalAA()
      for index = 0 to gThis.runningQueue.Count() - 1
        task = gThis.runningQueue[index]
        if task <> invalid and task.Callback.ContentManager_id = m.ContentManager_id
          gThis.runningQueue.Delete(index)

          key = task.id[0].Tostr() + "_" + task.id[1].Tostr()
          ' tmp = gThis.ContentManager_TaskIds.Delete(key)
          gThis.ContentManager_TaskIds.Delete(key)
        end if
      end for
    end sub

    OnComplete: sub()
      if m.callback.OnComplete <> invalid then m.callback.OnComplete()
      RunNextTaskFromQueue()
    end sub

    OnStart: sub()
      if m.callback.OnStart <> invalid then m.callback.OnStart()
    end sub
  }
  task = CreateTask(newCallback, HandlerConfig, content, additionalFields)
  if task <> invalid then
    newCallback.ContentManager_id = task.ContentManager_id
    task.control = "run"
  end if

  return {
    task: task
    callback: newCallback
  }
end function
'This function should be used for loading a lot of data and keep track of number parallel tasks
'@see RunNextTaskFromQueue
sub QueueGetContentData(callback as object, HandlerConfig as object, content = invalid as object, additionalFields = invalid as object, isHighPriority = true as boolean)
  taskObject = {
    callback: callback
    HandlerConfig: HandlerConfig
    content: content
    additionalFields: additionalFields
  }
  ' adding id fields that can be used for sorting tasks
  ' content can have  CM_row_ID_Index
  ' or if not present it will use "rowIndex" from callback
  if content <> invalid and content.CM_row_ID_Index <> invalid then
    taskObject.id = [content.CM_row_ID_Index, -1]
  else if taskObject.callback.rowIndex <> invalid then
    taskObject.id = [taskObject.callback.rowIndex, -1]
  else
    taskObject.id = [-1, -1]
  end if
  ' we should always use item id not page id here
  ' This item is used for sorting tasks according to focused item position
  if taskObject.callback.itemIndex <> invalid then
    taskObject.id[1] = taskObject.callback.itemIndex
  end if
  isShouldAdd = true
  if taskObject.id[0] >= 0 then
    key = taskObject.id[0].Tostr() + "_" + taskObject.id[1].Tostr()
    ' this will add rows task to map so we can know which row tasks are alredy there
    isShouldAdd = not m.ContentManager_TaskIds[key] <> invalid
    m.ContentManager_TaskIds[key] = ""
  end if
  if isShouldAdd then
    taskObject.callback.executionTimer = CreateObject("roTimespan")
    if isHighPriority then
      m.waitingQueue.Push(taskObject)
    else
      m.waitingQueue.Unshift(taskObject)
    end if
  else if false ' this is blocked as we are calculating priority
    index = 0
    taskToDelete = invalid
    for each task in m.waitingQueue
      if task.id <> invalid and task.id[0] = taskObject.id[0] and task.id[1] = taskObject.id[1] then
        taskToDelete = task
        exit for
      end if
      index++
    end for
    if taskToDelete <> invalid then
      m.waitingQueue.Delete(index)
      m.waitingQueue.Push(taskToDelete)
    end if
    if m.top.debug then print ghLogHead();"QueueGetContentData -- TASK already in QUEUE!!!!, size is " m.waitingQueue.Count()
  end if
  RunNextTaskFromQueue()
end sub
'This function gets last tasks that was added to queue and runs it
'It can start up to m.top.MAX_SIMULTANEOUS_LOADINGS tasks at a time
' if implemented doPrioritySort() function is responsible for sorting tasks
'@See doPrioritySort
sub RunNextTaskFromQueue()
  if not m.waitingQueue.IsEmpty() and m.CanLoadContent then
    timer = CreateObject("roTimespan")
    if doPrioritySort <> invalid then doPrioritySort()
    if m.top.debug then print ghLogHead();"RunNextTaskFromQueue -- sorted in "timer.TotalMilliseconds() " size = "m.waitingQueue.Count()
    for index = 1 to m.top.MAX_SIMULTANEOUS_LOADINGS - m.runningQueue.Count()
      taskToRun = m.waitingQueue.Pop()
      if taskToRun <> invalid then
        callback = taskToRun.callback
        HandlerConfig = taskToRun.HandlerConfig
        content = taskToRun.content
        additionalFields = taskToRun.additionalFields
        newTaskToRun = ExecuteGetContentData(callback, HandlerConfig, content, additionalFields)
        newTaskToRun.id = TaskToRun.id
        if m.top.debug then
          print ghLogHead();"RunNextTaskFromQueue -- task: loading ";taskToRun.id[0];"x";taskToRun.id[1]
          taskToRun.callback.priorityID = taskToRun.id
          taskToRun.callback.RunNextTaskFromQueue_orig_onstart = taskToRun.callback.OnStart
          taskToRun.callback.RunNextTaskFromQueue_orig_onReceive = taskToRun.callback.OnReceive
          taskToRun.callback.RunNextTaskFromQueue_orig_onError = taskToRun.callback.OnError
          taskToRun.callback.RunNextTaskFromQueue_orig_onComplete = taskToRun.callback.onComplete
          taskToRun.callback.OnStart = sub()
            m.taksStartedTime = m.executionTimer.TotalMilliseconds()
            m.executionTimer.mark()
            if m.RunNextTaskFromQueue_orig_onstart <> invalid then m.RunNextTaskFromQueue_orig_onstart()
          end sub
          taskToRun.callback.OnReceive = sub(data)
            m.taksExecutedIn = m.executionTimer.TotalMilliseconds()
            if m.RunNextTaskFromQueue_orig_OnReceive <> invalid then m.RunNextTaskFromQueue_orig_OnReceive(data)
          end sub
          taskToRun.callback.OnError = sub(data)
            m.taksExecutedIn = m.executionTimer.TotalMilliseconds()
            if m.RunNextTaskFromQueue_orig_onError <> invalid then m.RunNextTaskFromQueue_orig_onError(data)
          end sub
          taskToRun.callback.onComplete = sub()
            if m.taksExecutedIn then
              m.taksExecutedIn = m.executionTimer.TotalMilliseconds()
            end if
            ?"SGDEX CH task: finished "m.priorityID[0] "x"m.priorityID[1] " started after "m.taksStartedTime " task execution time "m.taksExecutedIn
            if m.RunNextTaskFromQueue_orig_onComplete <> invalid then m.RunNextTaskFromQueue_orig_onComplete()
          end sub
        end if
        if taskToRun.callback.OnStart <> invalid then taskToRun.callback.OnStart()
        m.runningQueue.Push(newTaskToRun)
      end if
    end for
    if m.top.debug then print ghLogHead();"RunNextTaskFromQueue -- # of running tasks "m.runningQueue.Count() " of "m.top.MAX_SIMULTANEOUS_LOADINGS" # of waiting tasks "m.waitingQueue.Count()
  end if
end sub
'This is utility function to create components defined in developer channel
'If you call createObject in library it will try to find it in library
function GetNodeFromChannel(name as string) as object
  result = invalid
  if m.top.GetScene() <> invalid then
    result = m.top.GetScene().CallFunc("createObjectOnDemand", name)
  else
    ' try to create it in local scope
    result = m.top.CreateChild(name)
  end if
  return result
end function
'returns if this row is serial pagination
function IsPaginationRow(row as object) as boolean
  return row <> invalid and row[m.Handler_ConfigField] <> invalid and row[m.Handler_ConfigField].hasMore <> invalid and row[m.Handler_ConfigField].hasMore
end function
' checks is this row has placeholdre items
' if yes, it check if all items are marked
'marking is done once so we don' t do it very often when user navigates
function CheckIfLazyRow(row as object) as boolean
  result = row <> invalid and row[m.Handler_ConfigField] <> invalid and row[m.Handler_ConfigField].pageSize <> invalid and row[m.Handler_ConfigField].pageSize > 0
  if result and (row.CM_Lazy_IsMarked = invalid or not row.CM_Lazy_IsMarked) and row.GetchildCount() > 0 then
    pageSize = row[m.Handler_ConfigField].pageSize
    pageNum = 0
    pageIndex = 0
    ' mark children as not loaded, not loaded child is when CM_pageNum >=0
    children = row.GetChildren(-1, 0)
    for each child in children
      if not child.HasField("CM_pageNum") then child.AddField("CM_pageNum", "int", false)
      if not child.HasField("CM_orig_pageNum") then child.AddField("CM_orig_pageNum", "int", false)
      child.CM_pageNum = pageNum
      child.CM_orig_pageNum = pageNum

      pageIndex++
      if pageIndex >= pageSize
        pageIndex = 0
        pageNum++
      end if
    end for
    if not row.HasField("CM_Lazy_IsMarked") then row.AddField("CM_Lazy_IsMarked", "bool", false)
    row.CM_Lazy_IsMarked = true
  end if
  return result
end function
' tells if this row has needed info to load metadata
function ShouldLoadDataForRow(row) as boolean
  result = false
  if row <> invalid then
    isLoaded = row.isLoaded <> invalid and row.isLoaded
    isLoading = IsRowAlreadyLoading(row)
    isFailed = row.isFailed <> invalid and row.isFailed
    hasHandlerConfig = row[m.Handler_ConfigField] <> invalid
    if not isLoading and not isLoaded then
      if isFailed
        result = hasHandlerConfig
      else
        'wasn' t loaded yet
        result = true
      end if
    end if
  end if
  return result
end function
'tells if already loading flag is populated use isAlreadyInQueue to know if row and item are already in queue
'@See isAlreadyInQueue
function IsRowAlreadyLoading(row) as boolean
  return (row.isLoading <> invalid and row.isLoading)
end function
'Is used to know if item of the row is already in waiting queue
function isAlreadyInQueue(rowIndex as object, itemIndex as object) as boolean
  key = rowIndex.Tostr() + "_" + itemIndex.Tostr()
  return m.ContentManager_TaskIds[key] <> invalid
end function
'populate row loading flags to provided row
sub PopulateLoadingFlags(row)
  if not row.HasField("isLoading") then row.AddField("isLoading", "bool", true): row.isLoading = false
  if not row.HasField("isLoaded") then row.AddField("isLoaded", "bool", true): row.isLoaded = false
  if not row.HasField("isFailed") then row.AddField("isFailed", "bool", true): row.isFailed = false
end sub
'reset all idle values
'is called in init and when developer navigate so we start new background loading based on focused position
sub resetIdleValues()
  m.currentIdleRadius = 1
  m.ContentManager_Page_Fails = {}
end sub
' get page number from this item
' this is populated by CheckIfLazyRow
'when page is loaded it' s reseted to < 0 value
function getPageNum(item) as integer
  if item <> invalid and item.HasField("CM_pageNum") then return item.CM_pageNum
  return -1
end function
sub OnIdleStateChanged(event as object)
  data = event.GetData()
  if data = "stop" then
    resetIdleValues()
  end if
end sub
sub addPageToQueue(row, page as integer)
  ' add this row and page to loading map
  map = m.ContentManager_Page_IDs
  rowindex = getPageKey(row)
  pageIndex = page.Tostr()

  if map[rowindex] = invalid then
    map[rowindex] = {}
  end if
  ' add this item to row map
  if map[rowindex][pageIndex] = invalid
    map[rowindex][pageIndex] = ""
  end if
end sub
sub RemovePageFromQueue(row, page as integer)
  gThis = GetGlobalAA()
  map = gThis.ContentManager_Page_IDs
  rowKey = getPageKey(row)
  if map[rowKey] <> invalid then
    rowFails = map[rowKey]
    rowFails.Delete(page.Tostr())
    rowFails.Delete(page.Tostr().trim())
    if map[rowKey].count() = 0 then
      map.Delete(rowKey)
    end if
  end if
end sub
' This will tell if page is already in queue
'So we don' t start new loading
function isPageAlreadyInQueue(rowIndex as object, itemIndex as integer) as boolean
  rowIndex = getPageKey(rowIndex).trim()
  return m.ContentManager_Page_IDs[rowIndex] <> invalid and m.ContentManager_Page_IDs[rowIndex][itemIndex.Tostr().trim()] <> invalid
end function
function IsToManyPageFails(row as object, page as integer) as boolean
  max_fails = 5
  if GetGlobalAA().top.MAX_NUMBER_OF_FAILS <> invalid then
    max_fails = GetGlobalAA().top.MAX_NUMBER_OF_FAILS
  end if
  return GetPageFails(row, page) > max_fails
end function
function GetPageFails(row as object, page as integer) as integer
  gThis = GetGlobalAA()
  map = gThis.ContentManager_Page_Fails
  rowKey = getPageKey(row)
  if map[rowKey] <> invalid then
    rowFails = map[rowKey]
    pageFailsNumber = rowFails[page.Tostr()]
    if pageFailsNumber <> invalid then
      return pageFailsNumber
    end if
  end if
  return 0
end function
' increment current page fails counter
sub AddPageFail(row as object, page as integer)
  gThis = GetGlobalAA()
  rowKey = getPageKey(row)
  map = gThis.ContentManager_Page_Fails
  if map[rowKey] = invalid then map[rowKey] = {}
  rowFails = map[rowKey]
  pageFailsNumber = rowFails[page.Tostr()]
  if pageFailsNumber = invalid then
    rowFails[page.Tostr()] = 0
  else
    rowFails[page.Tostr()] = pageFailsNumber + 1
  end if
end sub
' clear any page fails
' This should be called in OnReceive even if page failed previously
sub ClearPageFails(row as object, page as integer)
  gThis = GetGlobalAA()
  map = gThis.ContentManager_Page_Fails
  rowKey = getPageKey(row)
  if map[rowKey] <> invalid then
    rowFails = map[rowKey]
    rowFails.Delete(page.Tostr())
  end if
end sub
function getPageKey(value as object) as string
  result = "-1"
  if value <> invalid
    if Type(value) = "Integer" or Type(value) = "roInteger" or Type(value) = "roInt"
      result = value.ToStr()
    else if type(value) = "String" or type(value) = "roString"
      result = value
    else if value <> invalid and value.CM_row_ID_Index <> invalid then
      result = value.CM_row_ID_Index.ToStr()
    end if
  end if
  return result
end function
function getFocusedItem(row, defaultIndex = 0 as integer) as integer
  if row.CM_focusedItem <> invalid then
    if row.getChildCount() > 0 and row.CM_row_ID_Index = 2 then return 13
    return row.CM_focusedItem
  else
    setFocusedItem(row, defaultIndex)
    return defaultIndex
  end if
end function
sub setFocusedItem(row, newIndex)
  if not row.Hasfield("CM_focusedItem") then row.AddField("CM_focusedItem", "int", false)
  row.CM_focusedItem = newIndex
end sub
sub MarkRows()
  if m.uniqueRowIndex = invalid then m.uniqueRowIndex = 0

  ' Try to use m.top.content or m.topView.content as alternative source of content if it wasn't set yet
  ' This will fix priority sorting as unmarked rows are treated as -1
  content = m.view.content
  if content = invalid then content = m.top.content
  if content = invalid and m.topview <> invalid then content = m.topview.content

  if content <> invalid then
    children = content.Getchildren(-1, 0)
    ' TODO if rows were added/deleted/inserted we have to change map references too
    'TODO add another references field to hold in map so we don' t use row index

    for each row in children
      if not row.HasField("CM_row_ID_Index") then
        row.AddFields({ CM_row_ID_Index: m.uniqueRowIndex })
        m.uniqueRowIndex++
      end if
    end for
  end if
end sub
function IsInsertionMode(rowConfig as object) as boolean
  ' pageSize should be specified for the insertion mode
  return (rowConfig <> invalid and rowConfig.pageSize <> invalid)
end function
' copia contentmanagerTimeGrid
sub OnFocusedChild()
  if m.topView <> invalid and m.topView.isInFocusChain() and not m.view.hasFocus() then
    m.view.setFocus(true)
  end if
end sub
sub OnContentChange()
  if m.topView <> invalid and m.topView.content <> invalid
    if not m.topView.content.IsSameNode(m.view.content) and not m.topView.content.IsSameNode(m.content) then
      m.content = m.topView.content
      PopulateLoadingFlags(m.topView.content)
      if m.topView.content[m.Handler_ConfigField] <> invalid and m.topView.content.GetChildCount() = 0
        config = m.topView.content[m.Handler_ConfigField]
        callback = {
          config: config
          content: m.topView.content
          onReceive: sub() ' data
            OnRootContentLoaded()
          end sub

          onError: sub() ' data
            config = m.config
            gthis = GetGlobalAA()
            if m.content[gthis.Handler_ConfigField] <> invalid then
              config = m.content[gthis.Handler_ConfigField]
            end if

            GetContentData(m, config, m.content)
          end sub
        }
        GetContentData(callback, config, m.topView.content)
      else if m.topView.content.GetChildCount() > 0
        OnRootContentLoaded()
      end if
    end if
  end if
end sub
sub OnRootContentLoaded()
  ' remove root config
  m.topView.content[m.Handler_ConfigField] = invalid
  m.topView.content.isLoaded = true
  requiredRowsCount = 3
  numrowsToLoad = GetNumRowsToLoad()
  ' nonEmptyRows = 0
  if requiredRowsCount > m.topView.content.getChildCount() then
    requiredRowsCount = m.topView.content.getChildCount()
  end if
  MarkRows()
  for each row in m.topView.content.GetChildren(-1, 0)
    PopulateLoadingFlags(row)
    if row.GetChildCount() > 0 then
      ' nonEmptyRows++
      row.isLoaded = true
      row.isLoading = false
      row.isFailed = false
    else
      if row[m.Handler_ConfigField] <> invalid then
        ' row needs to be loaded
        callback = {
          row: row
          onReceive: sub() ' data
            ClearPageFails(m.row, 0)
            RemovePageFromQueue(m.row, 0)
            TryToSetContent()
          end sub
        }
        LoadContentForRow(row, callback)
      end if
    end if
    if numrowsToLoad <= 0 then
      ' we are loading start amount of rows
      exit for
    end if
    numRowsToLoad--
  end for
  canSetContent = true 'nonEmptyRows >= requiredRowsCount
  if canSetContent then
    m.view.content = m.topView.content
  else
    ShouldTryToSetContentOnLoad(true)
  end if
end sub
sub LoadContentForRow(row, callback = invalid as object)
  aditionalParams = {}
  shouldDeleteHandlerConfig = not IsInsertionMode(row[m.Handler_ConfigField])
  simpleLoadFunction = sub(data, isSuccess)
    if GetGlobalAA().debug then ? "received row content="m.row.title
    m.row.isLoading = false
    m.row.isLoaded = isSuccess
    if m.callback <> invalid
      if isSuccess or m.callback.onError = invalid
        if m.callback.onReceive <> invalid then m.callback.OnReceive(data)
      else if m.callback.onError <> invalid then
        m.callback.OnError(data)
      end if
      ' if no extra callback was passed and we failed then we have to restore config
    else if not isSuccess then
      Handler_ConfigField = GetGlobalAA().Handler_ConfigField
      if m.row[Handler_ConfigField] = invalid then
        m.row[Handler_ConfigField] = m.config
      end if
    end if
  end sub
  functionToUse = simpleLoadFunction
  ' rowIndex = invalid
  itemIndex = -2
  config = row[m.Handler_ConfigField]
  callback = {
    itemIndex: itemIndex ' this will be used for sorting
    callback: callback ' extra callback if needed
    row: row ' current loading row
    simpleLoadFunction: simpleLoadFunction ' simple load function that executes basic functionality
    config: config ' config that is used to retrieve content
    functionToUse: functionToUse
    onReceive: sub(data)
      m.FunctionToUse(data, true)
    end sub
    onError: sub(data)
      m.FunctionToUse(data, false)
    end sub
  }
  QueueGetContentData(callback, config, row, aditionalParams, true)
  addPageToQueue(row, 0)
  if shouldDeleteHandlerConfig then row[m.Handler_ConfigField] = invalid
  row.isLoaded = false
  row.isLoading = true
  row.isFailed = false
end sub
function GetNumRowsToLoad()
  return 5
end function
sub doPrioritySort()
  if m.topView.content <> invalid then
    focusedChannel = m.topView.channelFocused
    focusedItem = m.topView.programFocused
    if focusedItem = invalid or focusedItem < 0 then focusedItem = 0
    if focusedChannel = invalid or focusedChannel < 0 then focusedChannel = 0
    if m.top.debug then print ghLogHead();"doPrioritySort -- new priority for rows near ";focusedChannel
    rowPriority = 10
    itemPriority = 10
    rows = m.topView.content.GetChildren(-1, 0)
    for each taskObject in m.waitingQueue
      ' set new priority
      rowId = taskObject.id[0]
      itemId = taskObject.id[1]
      row = rows[rowId]
      if row <> invalid then
        focusedItem = getFocusedItem(row, 0)
        diffRow = Abs(focusedChannel - rowId)
        diffItem = Abs(focusedItem - itemId)
        secondItemDiff = diffItem ' calculate distance from end of row
        secondItemDiff = row.GetChildCount() - itemId + focusedItem
        if diffItem > secondItemDiff then diffItem = secondItemDiff ' take smallest distance
        diff = diffItem
        if diff < diffRow then diff = diffRow
        taskObject.priority = diff + rowPriority * diffRow + diffItem * itemPriority
      end if
    end for
    m.waitingQueue.SortBy("priority", "r") ' sort the queue
  end if
end sub
sub ShouldTryToSetContentOnLoad(should as boolean)
  m._tryToSetContentOnLoad = should
end sub
sub TryToSetContent()
  if m._tryToSetContentOnLoad = true and not IsContentSet() then
    requiredRowsCount = 3
    nonEmptyRows = 0
    content = m.topView.content
    rowCount = content.getChildCount()

    for each row in content.GetChildren(-1, 0)
      if row.getChildCount() > 0 or row.isFailed = true then nonEmptyRows++
    end for

    if nonEmptyRows >= requiredRowsCount or nonEmptyRows = rowCount then
      m.topView.content = content
    end if
  end if
end sub
function IsContentSet() as boolean
  return m.topView.content <> invalid
end function



' Busqueda de programas
' --------------------------
function getChannelCurrentEvent(chan)
  current = 0
  curTime = CreateObject("roDateTime").AsSeconds()
  eCant = chan.GetChildCount()
  for eNro = 0 to eCant - 1
    e = chan.getChild(eNro)
    if e <> invalid then
      tIni = e.playstart
      tEnd = e.playstart + e.playduration
      ' print "?? ";curTime;" ";tIni;" ";tEnd;
      if curTime >= tIni and curTime <= tEnd then
        ' print "?? ";chan.getChild(eNro)
        current = eNro 'e
        return current
      end if
    end if
  end for
  return current
end function

sub onCmd(event)
  ' para que refresque los iconos bloquado y favoritos
  m.view.content = m.topView.content

  data = event.getData()
  timeGrid = event.GetRoSGNode()

  m.top.visible = false

  if timeGrid = invalid then
    return
  end if

  if timeGrid.content = invalid then
    return
  end if

  channel = timeGrid.content.getChild(timeGrid.channelSelected)

  if channel = invalid then
    return
  end if

  program = channel.getChild(timeGrid.programSelected)

  action = data.action
  if action = "long_OK" then
    action = "showOptions"
  else
    action = "changeChannel"
  end if

  ' hombre muerto
  m.top.keypressed = "x"

  m.top.cmd = {
    cmd: action,
    channel: channel,
    program: program
  }
end sub
sub refresh() ' event
  ' para que refresque los iconos bloquado y favoritos
  m.view.content = m.topView.content
end sub