sub DataInit()
  m.top.debug = false
  m.api.method = "GET"
  m.api.url = getUrlWithLastTouch(m.config.mfwk.host + "" + m.top.url)
  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  m.api.query.delete("user_id")
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })
  m.api.payload = {
    "id": m.top.id
  }

  ' print "TTTTT m.top.id=";m.top.id;" m.top.rowtype=";m.top.rowType;"  TTTTTTTTTTTTTTTT"
  if m.top.id = "Mis_Canales_Favoritos" then
    newPath = ghGetPath(m.api.url)
    newUriArray = ghQuery2Array(m.api.url)
    newUriArray["live"] = "true"
    newUriArray["epg_version"] = m.global.epg.version
    ' if newUriArray["filterlist"] = invalid
    '   newUriArray["filterlist"] = m.global.filter_list[ghGetRegistry("region")].filterlist ' Gonela Vinay
    ' end if
    newUri = ghArray2Query(newUriArray)
    m.api.url = newPath + newUri
  end if

  if m.top.order_id <> "" then
    newPath = ghGetPath(m.top.url)
    newUriArray = ghQuery2Array(m.top.url)
    newUriArray["order_id"] = m.top.order_id
    newUriArray["order_way"] = m.top.order_way
    newUri = ghArray2Query(newUriArray)
    m.api.url = m.config.mfwk.host + "/" + newPath + newUri
  end if

  m.logger.debug("rowLite init", { api: m.api, params: m.api.query, filters: m.top.order_id, urlOriginal: m.top.url, urlReal: m.api.url })
end sub

function getUrlWithLastTouch(url)
  cambios = m.global.config.api.lastTouch.rowUrls
  for each chunk in cambios
    token = cambios[chunk]
    if inStr(1, url, chunk) > 0 then
      ' print ghLogHead();"getUrlWithLastTouch -- encontre! >> ";chunk;" / ";url
      ' print ghLogHead();"getUrlWithLastTouch -- reemplazo >> ";token;" / ";m.global.lastTouch
      newTouch = ghGetChild(m.global, "lastTouch." + token)
      if newTouch <> invalid then
        newUrl = replaceQueryParams(url, { "lasttouch": newTouch })
        ' print ghLogHead();"getUrlWithLastTouch -- ";token;":";newTouch;" >>> ";newUrl.urlout
        return newUrl.urlout
      else
        m.logger.debug("getUrlWithLastTouch -- NO LO ENCUENTRO!!! ", { token: token })
      end if
      exit for
    end if
  end for
  return url
end function

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  if res = invalid or type(res) = "String" or res.errors <> invalid then
    if m.top.debug then print "** ERROR **********************"
    print ghLogHead();"ProcessData -- ";ghGetChild(res, "errors")
    ' m.top.error = { id: m.top.id, error: ghGetChild(res, "errors") }
    m.top.error = { id: m.top.id }
    return
  end if

  ' creo la respuesta
  result = CreateObject("roSGNode", "GHContent")
  result.id = m.top.id

  response = ghGetChild(res, "response")
  if response = invalid then
    if m.top.debug then print "SOY DISTINTA!!!! -- yo llego en data, y no en desponse."
    response = ghGetChild(res, "data")
  end if

  ' PLAN SELECTOR
  'planSelector = ghGetChild(response, "listButtons")
  offers = ghGetChild(res, "offers")
  if offers <> invalid then
    result = handlePlanSelectorV2(offers)


    'if planSelector <> invalid then
    '  if m.top.debug then print ghLogHead();"ProcessData -- PLAN SELECTOR"
    '  planSelector = ghGetChild(response, "listButtons")
    '  result = handlePlanSelector(response)
  else ' es el row normal
    m.logger.debug("ProcessData -- ROW NORMAL")
    ' m.top.debug = false
    result = handleCommon(response)
    ' m.top.debug = true
  end if

  if result <> invalid then
    m.top.content = { cinta: result }
  end if

  ' m.top.debug = false

end sub

' renglon comun
function handleCommon(response)
  ' creo la respuesta
  result = CreateObject("roSGNode", "GHContent")
  result.id = m.top.id
  result.rowType = m.top.rowType
  result.longOKType = m.top.longOKType

  m.logger.debug("DataProcess -- response=", { response: response })

  items = invalid
  highlight = ghGetChild(response, "highlight")
  if highlight <> invalid then
    m.logger.debug("DataProcess", { highlight: response.highlight })
    items = highlightFilterByProvider(highlight)
  end if
  groups = ghGetChild(response, "groups")
  if groups <> invalid then
    m.logger.debug("DataProcess", { groups: response.groups })
    items = groups
  end if

  ' si no viene nada, doy error
  if items = invalid then
    m.top.error = { id: m.top.id }
    return invalid
  end if

  m.logger.debug("DataProcess items", { counts: items.Count(), items: items })

  for i = 0 to items.Count() - 1
    it = items[i]
    it.rowType = m.top.rowType
    it.longOKType = m.top.longOKType
    it.order = i + 1 ' arrancamos de 1
    ' para los talentos ( card redondas ),
    ' con esta info se si tengo que desplazar la imagen de la card
    it.cantTotal = items.Count()
    ' pongo el id del carousel
    it.carouselId = m.top.id
    m.logger.debug("DataProcess -- item=", { it: it })

    it.curr_filter = m.top.currFilter
    it.curr_filter_title = m.top.currFilterTitle

    item = CreateObject("roSGNode", "GHContent")
    item.title = it.title
    item.rowType = m.top.rowType
    item.longOKType = m.top.longOKType
    item.sdposterurl = it.image_small
    item.hdposterurl = it.image_medium
    item.fhdposterurl = it.image_large
    item.data = it
    ' saber si es serie en la home, para al entrar a la vcard si es serie hay que llamar primero a lastSeen
    contentType = "movie"
    season_number = ghGetChild(it, "season_number")
    is_serie = ghGetChild(it, "is_serie")
    ' SERIES -- encontrar manera mas simple
    if is_serie <> invalid and (type(is_serie) = "Boolean" or type(is_serie) = "roBoolean") then
      if is_serie <> false
        contentType = "series"
      end if
    end if
    if is_serie <> invalid and type(is_serie) = "roString" then
      if is_serie = "true" then
        contentType = "series"
      end if
    end if
    if season_number <> invalid and type(season_number) = "roInteger" then
      contentType = "series"
    end if
    if season_number <> invalid and (type(season_number) = "String" or type(season_number) = "roString") then
      if season_number <> "" then
        contentType = "series"
      end if
    end if

    ghUtils_ForceSetFields(item, {
      ContentType: contentType
    })

    result.appendChild(item)
  end for
  return result
  ' channel_number: invalid
  ' date: "20200518170905"
  ' description: "Un mágico juego de mesa hace surgir peligros que solo pueden detenerse terminando el juego."
  ' description_large: "Jumanji es un juego de mesa encantado que funciona como portal a un universo de fantasía selvática. Cuando dos niños lo encuentran y deciden jugarlo se ven obligados a enfrentar una serie de peligros, animales y criaturas provenientes del juego."
  ' duration: "01:44:07"
  ' encoder_tecnology: <Component: roAssociativeArray>
  ' episode_number: invalid
  ' format_types: "susc"
  ' id: "560945"
  ' image_background: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995_e-1280x720.jpg"
  ' image_base_horizontal: "https://clarovideocdn8.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg"
  ' image_base_square: "https://clarovideocdn6.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WCUADRADO.jpg"
  ' image_base_vertical: "https://clarovideocdn6.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WVERTICAL.jpg"
  ' image_clean_horizontal: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WHORIZONTAL.jpg"
  ' image_clean_square: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WCUADRADO.jpg"
  ' image_clean_vertical: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WVERTICAL.jpg"
  ' image_external: invalid
  ' image_frames: "https://clarovideocdn8.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SPRITES/JUMANJI1995-00h-00m-00s-00f.jpg"
  ' image_large: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg?size=675x380"
  ' image_medium: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WVERTICAL.jpg?size=200x300"
  ' image_small: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg?size=290x163"
  ' image_sprites: "https://clarovideocdn2.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SPRITES/JUMANJI1995-SPRITEBAR.jpg"
  ' image_still: "https://clarovideocdn0.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/STILLS/JUMANJI1995-STILL-01.jpg"
  ' is_series: false
  ' live_enabled: "0"
  ' live_ref: invalid
  ' live_type: invalid
  ' preview: "false"
  ' proveedor_code: "amco"
  ' proveedor_name: "AMCO"
  ' rating_code: "PG"
  ' recorder_technology: <Component: roAssociativeArray>
  ' resource_name: invalid
  ' rollingcreditstime: invalid
  ' rollingcreditstimedb: invalid
  ' season_number: invalid
  ' short_description: invalid
  ' timeshift: invalid
  ' title: "Jumanji"
  ' title_episode: invalid
  ' title_original: "Jumanji (1995)"
  ' title_uri: "Jumanji"
  ' url_imagen_t1: "https://clarovideocdn2.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WVERTICAL.jpg?size=200x300"
  ' url_imagen_t2: "https://clarovideocdn4.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WHORIZONTAL.jpg?size=290x163"
  ' votes_average: 4
  ' year: "1995"
end function
' renglo de tipo panel selector
function handlePlanSelector(response)

  m.logger.debug("handlePlanSelector -- ")

  items = ghGetChild(response, "listButtons.button")
  if items <> invalid then
    ' creo la respuesta
    result = CreateObject("roSGNode", "GHContent")
    result.id = m.top.id
    for i = 0 to items.Count() - 1
      it = items[i]
      if ghGetChild(it, "waspurchased", "0") = "0" then 'Esconde el item dependiendo de si el usuario tiene o no la oferta disponible
        it.rowType = m.top.rowType
        it.longOKType = "planSelector"
        item = CreateObject("roSGNode", "GHContent")
        item.setFields({
          id: it.offerid
          title: it.oneoffertype
          rowType: m.top.rowType
          longOKType: "planSelector"
          'longOKType: m.top.longOKType
          data: it
        })
        result.appendChild(item)
      end if
      ' -----
    end for
  else '  otra vez no viene nada !!
    m.logger.debug("handlePlanSelector -- VIENE VACIO!!")
    result = invalid
  end if

  return result
  '   "waspurchased": "0",
  '   "purchasechecked": "0",
  '   "price": "115",
  '   "offerid": "14327289",
  '   "currency": "$",
  '   "paymentmethod": null,
  '   "purchasable": true,
  '   "purchaseid": null,
  '   "oneoffertype": "subscrition",
  '   "linkworkflowstart": "/services/payway/workflowstart?object_type=A&offer_id=14327289&suscription_id=980&device_category=stb&device_manufacturer=roku&device_model=generic&device_type=generic&region=mexico&user_id=53508611",
  '   "oneofferdesckey": "offer_button_desc_subscription_cv_mensual",
  '   "gateway": "default",
  '   "oneofferdesc": "Suscríbete Mensual",
  '   "producttype": "CV_MENSUAL",
  '   "key": "Telmexmexico_Subscription_SVOD_30d",
  '   "bannerpromo": "cv_mensual_subscrition_mexico_wp0",
  '   "family": "clarovideo",
  '   "banner": "banner_cv_mensual",
  '   "bonus": "bonus_cv_mensual",
  '   "includes": "includes_cv_mensual",
  '   "buy": "buy_cv_mensual",
  '   "style": "style_clarovideo",
  '   "periodicity": "month",
  '   "frequency": 1
end function

function handlePlanSelectorV2(items)

  m.logger.debug("handlePlanSelector V2 -- ")
  if items <> invalid then
    ' creo la respuesta
    result = CreateObject("roSGNode", "GHContent")
    result.id = m.top.id
    result.longOkType = "planSelector"
    result.type = m.top.rowType
    result.oldType = m.top.rowType
    for i = 0 to items.Count() - 1
      it = items[i]
      it.rowType = m.top.rowType
      it.longOKType = m.top.longOKType
      item = CreateObject("roSGNode", "GHContent")
      item.setFields({
        id: it.product_id
        title: it.producttype
        rowType: m.top.rowType
        longOKType: m.top.longOKType
        data: it
      })
      result.appendChild(item)
      'end if
      ' -----
    end for
  else '  otra vez no viene nada !!
    m.logger.debug("handlePlanSelector -- VIENE VACIO!!")
    result = invalid
  end if

  return result
  '   "waspurchased": "0",
  '   "purchasechecked": "0",
  '   "price": "115",
  '   "offerid": "14327289",
  '   "currency": "$",
  '   "paymentmethod": null,
  '   "purchasable": true,
  '   "purchaseid": null,
  '   "oneoffertype": "subscrition",
  '   "linkworkflowstart": "/services/payway/workflowstart?object_type=A&offer_id=14327289&suscription_id=980&device_category=stb&device_manufacturer=roku&device_model=generic&device_type=generic&region=mexico&user_id=53508611",
  '   "oneofferdesckey": "offer_button_desc_subscription_cv_mensual",
  '   "gateway": "default",
  '   "oneofferdesc": "Suscríbete Mensual",
  '   "producttype": "CV_MENSUAL",
  '   "key": "Telmexmexico_Subscription_SVOD_30d",
  '   "bannerpromo": "cv_mensual_subscrition_mexico_wp0",
  '   "family": "clarovideo",
  '   "banner": "banner_cv_mensual",
  '   "bonus": "bonus_cv_mensual",
  '   "includes": "includes_cv_mensual",
  '   "buy": "buy_cv_mensual",
  '   "style": "style_clarovideo",
  '   "periodicity": "month",
  '   "frequency": 1
end function

function highlightFilterByProvider(items)
  res = []
  status = []
  superhighlight = m.global.superhighlight
  for i = 0 to items.Count() - 1
    it = items[i]
    status.Push(it.user_status)
    if it.user_status = invalid or findInArray(it.user_status, superhighlight) then
      res.Push(it)
    end if
  end for
  return res
end function

function findInArray(element, array) as boolean
  element = ghReplaceStr(element, "anonymous,", "")
  for each arrayElement in array
    arrayElement = ghReplaceStr(arrayElement, "anonymous,", "")
    if element = arrayElement then
      return true
    end if
  end for
  return false
end function

' channel_number: invalid
' date: "20200518170905"
' description: "Un mágico juego de mesa hace surgir peligros que solo pueden detenerse terminando el juego."
' description_large: "Jumanji es un juego de mesa encantado que funciona como portal a un universo de fantasía selvática. Cuando dos niños lo encuentran y deciden jugarlo se ven obligados a enfrentar una serie de peligros, animales y criaturas provenientes del juego."
' duration: "01:44:07"
' encoder_tecnology: <Component: roAssociativeArray>
' episode_number: invalid
' format_types: "susc"
' id: "560945"
' image_background: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995_e-1280x720.jpg"
' image_base_horizontal: "https://clarovideocdn8.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg"
' image_base_square: "https://clarovideocdn6.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WCUADRADO.jpg"
' image_base_vertical: "https://clarovideocdn6.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WVERTICAL.jpg"
' image_clean_horizontal: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WHORIZONTAL.jpg"
' image_clean_square: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WCUADRADO.jpg"
' image_clean_vertical: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WVERTICAL.jpg"
' image_external: invalid
' image_frames: "https://clarovideocdn8.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SPRITES/JUMANJI1995-00h-00m-00s-00f.jpg"
' image_large: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg?size=675x380"
' image_medium: "https://clarovideocdn9.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WVERTICAL.jpg?size=200x300"
' image_small: "https://clarovideocdn1.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SS/JUMANJI1995WHORIZONTAL.jpg?size=290x163"
' image_sprites: "https://clarovideocdn2.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/SPRITES/JUMANJI1995-SPRITEBAR.jpg"
' image_still: "https://clarovideocdn0.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/STILLS/JUMANJI1995-STILL-01.jpg"
' is_series: false
' live_enabled: "0"
' live_ref: invalid
' live_type: invalid
' preview: "false"
' proveedor_code: "amco"
' proveedor_name: "AMCO"
' rating_code: "PG"
' recorder_technology: <Component: roAssociativeArray>
' resource_name: invalid
' rollingcreditstime: invalid
' rollingcreditstimedb: invalid
' season_number: invalid
' short_description: invalid
' timeshift: invalid
' title: "Jumanji"
' title_episode: invalid
' title_original: "Jumanji (1995)"
' title_uri: "Jumanji"
' url_imagen_t1: "https://clarovideocdn2.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WVERTICAL.jpg?size=200x300"
' url_imagen_t2: "https://clarovideocdn4.clarovideo.net/PELICULAS/JUMANJI1995/EXPORTACION_WEB/CLEAN/JUMANJI1995WHORIZONTAL.jpg?size=290x163"
' votes_average: 4
' year: "1995"
