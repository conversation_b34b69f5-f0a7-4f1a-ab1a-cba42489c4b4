sub MnuLogout()
  if m.top.debug then print gh<PERSON>og<PERSON>ead("MENU");"MnuLogout"

  m.global.setFields({ navSelect: "" }) ' para que al loguear de nuevo no tenga un menu seleccionado
  ghCallApi("Logout" + m.versionSuffix, "apiLogoutOk", "apiLogoutOk")
end sub

sub apiLogoutOk()
  if m.top.hasField("routerReset") then
    GA4Event("screen_view", {
      screen_class: "/login"
      screen_name: "logout",
    })
  end if

  scene = m.top.getScene()
  scene.callFunc("warmReboot", { logout: true})
end sub

sub MnuLogin() 'para loguear si está navegando desconectado
  if m.top.debug then print ghLogHead("MENU");"MnuLogin"

  m.global.setFields({ navSelect: "" })

  if m.top.hasField("routerReset") then
    GA4Event("screen_view", {
      screen_class: "/Login"
      screen_name: "Login",
    })
    m.top.routerChild = { page: "LoginPageLite" }
  end if
end sub

sub MnuSearch()
  if m.top.debug then print ghLogHead("MENU");"MnuSearch"

  if m.top.hasField("routerReset") then
    GA4Event("screen_view", {
      screen_class: "/Search"
      screen_name: "Search",
    })

    m.top.routerChild = { page: "SearchPage" + m.versionSuffix }
  end if
end sub

sub MnuLive()
  if m.top.debug then print ghLogHead("MENU");"MnuLive"

  if m.top.hasField("routerReset") then
    GA4Event("screen_view", {
      screen_class: "/live"
      screen_name: "live",
    })

    livePage = CreateObject("roSGNode", "LivePage")
    livePage.id = "Live"
    livePage.ObserveField("reloadHome", "handleReload")
    m.top.routerChild = { page: livePage }

  end if
end sub

sub handleReload()
  ' para refresh del menu
  m.top.refresh = true

  ' para refresh de la home
  m.top.reloadHome = true
end sub

sub MnuSubscriptions()
  if m.top.hasField("routerReset") then
    m.top.routerChild = { page: "SubMenuSubscriptions" }
  end if
end sub

' sub MnuPrueba()
'   if m.top.debug then print ghLogHead("MENU");"MnuLive"
'   if m.top.hasField("routerReset") then
'     m.top.routerChild = { page: "SubMenuExample" }
'   end if
' end sub