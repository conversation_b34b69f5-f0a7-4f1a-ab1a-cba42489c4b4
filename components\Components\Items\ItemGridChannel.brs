sub Init()
  if m.top.debug then print ghLogHead();"Init"

  m.itemPoster = m.top.findNode("itemPoster")
  m.background = m.top.findNode("background")
  m.channelNumber = m.top.findNode("channelNumber")
  m.candado = m.top.findNode("candado")
  m.corazon = m.top.findNode("corazon")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  m.channelNumber.text = data.number
  m.channelNumber.font = ghGetFont(15, "regular")
  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 274
  m.itemPoster.height = 154
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading_horizontal.png")

  m.candado.translation = [240, 20]
  m.candado.width = 18.5
  m.candado.height = 18.5
  m.candado.uri = ghGetAsset("channel_blocked_icon", ghGetImageByMode("lock.png"))

  m.corazon.translation = [240, 60]
  m.corazon.width = 18.5
  m.corazon.height = 18.5
  m.corazon.uri = ghGetAsset("playingLive_alert_channelFavorite_icon")

  showFavoritedBloqued(ghGetChild(data, "group_id", ""))
end sub

sub showFavoritedBloqued(groupId)

  ' si esta bloqueado
  channels = ghGetChild(m.global, "parental.channels", {})
  if channels[groupId] <> invalid and channels[groupId] <> "" then
    m.candado.visible = true
    m.background.opacity = "0.7"
    m.itemPoster.opacity = "0.7"
    m.channelNumber.opacity = "0.7"
  else
    m.candado.visible = false
    m.background.opacity = "1"
    m.itemPoster.opacity = "1"
    m.channelNumber.opacity = "1"
  end if

  ' si esta en favoritos
  favorites = ghGetChild(m.global, "favorites.channels", {})
  if favorites[groupId] <> invalid and favorites[groupId] <> "" then
      m.corazon.visible = true
  else
      m.corazon.visible = false
  end if

end sub

sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")

  if data <> invalid then
    showFavoritedBloqued(ghGetChild(data, "group_id", ""))
    ' if m.top.rowHasFocus = true then
    '   if m.top.itemHasFocus= 1
    '     m.info.setFields({
    '       color: "0xFFFFFF"
    '     })
    '   else
    '     m.info.setFields({
    '       color: "0xFFFFFF50"
    '     })
    '   end if
    ' else
    '   m.info.setFields({
    '     color: "0xFFFFFF50"
    '   })
    ' end if
  end if
end sub