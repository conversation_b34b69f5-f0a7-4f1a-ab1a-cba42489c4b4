<?xml version="1.0" encoding="utf-8" ?>

<component name="RpayCanceledScreen" extends="Page">

  <script type="text/brightscript" uri="RpayCanceledScreen.brs" />

  <interface>
    <field id="value" type="string" value=""/>
  </interface>

 <children>
    <Rectangle id="fondoMensaje" color="#000000" translation="[0,0]" width="1280" height="720" />

    <Poster id="iconoWarning" translation="[600,164]"/>

    <Label id="title" focusable="false" translation="[0,230]" width="1250" height="56" vertAlign= "center" horizAlign = "center" text="¡Todo lo que te gusta en sólo lugar!" />

    <LayoutGroup id="textosDescriptivos" translation="[0,294]" layoutDirection="vert" itemSpacings="[-5]" visible="true">
      <Label id="descrip1" vertAlign="center" focusable="false" translation="[356,284]" wrap="true" width="1250" height="40" horizAlign = "center" text="El mejor contenido, canales de TV, películas y series." />
      <Label id="descrip2" vertAlign="center" focusable="false" translation="[356,284]" wrap="true" width="1250" height="40" horizAlign = "center" text="El mejor contenido, canales de TV, películas y series." />
    </LayoutGroup>

    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton value="OK" id="btnRegister" width= "368" height= "72" text="REGÍSTRATE" translation="[444,404]" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" />
    </GHButtonGroup>

    <Label id="textoAyuda" vertAlign="center" focusable="false" translation="[0,586]" wrap="true" width="1250" height="40" horizAlign = "center" text="El mejor contenido, canales de TV, películas y series." />
    <Label id="textoCallCenter" vertAlign="center" focusable="false" translation="[0,616]" wrap="true" width="1250" height="40" horizAlign = "center" text="El mejor contenido, canales de TV, películas y series." />
  </children>

</component>
