sub Init()
  m.logger = CreateLogger()
  m.top.debug = false

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.pstrLogoAddon = m.top.findNode("pstrLogoAddon")

  m.lblPrice = m.top.findNode("lblPrice")
  m.lblPrice.font = ghGetFont(26, "bold")

  m.lblCurrency = m.top.findNode("lblCurrency")
  m.lblCurrency.font = ghGetFont(26, "bold")

  m.lblIva = m.top.findNode("lblIva")
  m.lblIva.font = ghGetFont(18, "regular")

  m.cntrBtn = m.top.findNode("cntrBtn")
  m.cntrBtn.font = ghGetFont(11, "bold")

end sub

sub itemContentChanged(event) ' event
  data = event.getData()
  ' print "data :" data
  'data = ghGetChild(data, "data")

  'm.logger.debug(["item planOfferV2", data])
  'print "item planOfferV2 : " ghGetAsset(ghGetChild(data, "banner", ""))

  m.itemPoster.uri = ghGetAsset(ghGetChild(data, "banner", ""))
  m.itemPoster.width = 165
  m.itemPoster.height = 400

  m.pstrLogoAddon.uri = ghGetAssetByMode("plan_selector_logo_" + ghGetChild(data, "family", ""), "")
  m.lblCurrency.text = ghTranslate("subscription_periodicity_month_label_1", "") + getTraslate(ghGetChild(data, "periodicity", ""))
  m.lblPrice.text = ghGetChild(data, "currency", "") + ghGetChild(data, "price", "")
  m.lblIva.text = ghTranslate("subscription_costTaxIcluded_complement_label_1", "") 'IVA incluido
  m.cntrBtn.text = ghTranslate(ghGetChild(data, "buy", ""), "")
  if m.lblPrice.text = "" or m.lblPrice.text = invalid
    m.lblCurrency.visible = false
    m.lblIva.visible = false
  end if
end sub

sub showfocus()
  m.itemPoster.visible = true
end sub

function getTraslate(text as string)
  return ghReplaceStr(ghReplaceStr(text, "month", "mes"), "year", "año")
end function