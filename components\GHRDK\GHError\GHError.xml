<?xml version="1.0" encoding="utf-8" ?>

<component name="GHError" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHError.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- text -->
    <field id="title" type="string" value="¡Lo sentimos!" alias="titleText.text" />
    <field id="descrip" type="string" value="Hubo un error" alias="descripText.text" />
    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <!-- align -->
    <field id="backColor" type="string" value="0xFFFFFF" onChange="recalcColors" />
    <!-- focus and select -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <!-- <field id="selected" type="boolean" value="false" alwaysNotify="true" /> -->
    <field id="value" type="string" value=""/>
    <!-- componente -->
    <field id="wasClosed" type="boolean" value="false" alwaysNotify="true" />
    <field id="debug" type="boolean" value="true" />
    <field id="wrap" type="boolean" value="false" onChange="updateFieldWrap" />
  </interface>

  <children>
    <Rectangle id="background" color="#121212" translation="[0,0]" width="1280" height="720"/>
    <Label id="titleText" focusable="false" color="0xFFFFFF" translation="[10,10]" wrap="true" horizAlign="center" width="600" text="nada" />
    <Label id="descripText" focusable="false" color="0xFFFFFF" translation="[20,20]" wrap="true" horizAlign="center" width="480" text="nada" />
    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton id="bOk" text="ACEPTAR" width="504" height="72" value="OK" translation="[30,30]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" focusColor="0xFFFFFF" selBackColor="#981C15" />
      <!-- <GHButton id="bCancel" text="Cancel" value="Cancel" translation="[550,430]" horizAlign="left" color="0x000000" backcolor="0xCCCCCC" focusColor="0xFFFFFF" selBackColor="0xFF0000" /> -->
    </GHButtonGroup>
  </children>

</component>
