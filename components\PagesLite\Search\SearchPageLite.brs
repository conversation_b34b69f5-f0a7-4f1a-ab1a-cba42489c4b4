' The Hidden Search
' -----------------------

sub Init()
    m.top.debug = true
    m.focusId = invalid ' donde ir al tomar foco
    ' -- MAPA DE PANTALLA ----------------------------
    m.map = {
        "menu": { "up": invalid, "right": invalid, "down": "keyboard", "left": invalid }
        "keyboard": { "up": "menu", "right": invalid, "down": "theGrid", "left": invalid }
        "theGrid": { "up": "keyboard", "right": invalid, "down": invalid, "left": invalid },
    }
    ' -- MENU ----------------------------------------
    m.menu = m.top.findNode("menu")
    m.menu.ObserveField("backSelected", "OnExitToHome")
    ' -- KEYBOARD ------------------------------------
    m.keyboard = m.top.findNode("keyboard")
    m.keyboard.value = ""
    m.keyboard.ObserveFieldScoped("value", "OnKeyboardTextChanged")
    ' -- QUERY ---------------------------------------
    m.query = m.top.findNode("query")
    ' -- COMPONENTES ---------------------------------
    setGrid() ' valores de theGrid
    setMessages() ' configuro mensajes
    setLoading() ' espera un poco
    ghCallApi("LastTouch")
end sub
sub setGrid()
    m.theGrid = m.top.findNode("theGrid")
    ' m.theGrid.ObserveField("position", "OnCardOver")
    m.theGrid.ObserveField("value", "OnCardSelect")
    m.theGrid.ObserveField("content", "refresh")
    if m.top.debug then print ghLogHead();"setConstants *** ---------------------"
    m.rowConfig = {
        "_BASE_": { ' para todas las cintas
            itemComponentName: "ItemSearch"
            numRows: 3
            itemSize: [1280, 210]
            'rowSpacings: 150
            itemSpacing: [0, 80]
            rowLabelOffset: [0, 15]
            showRowLabel: [true]
            focusXOffset: [1]
            rowFocusAnimationStyle: "fixedFocusWrap"
            rowCounterRightOffset: 0
            loadingBitmapUri: "pkg:/images/loading.png"
            loadingBitmapOpacity: 1
        },
        "search": {
            rowItemSize: [250, 154],
            rowItemSpacing: [23, 0]
            rowHeights: 154,
            focusXOffset: 200,
            rowLabelOffset: [200, 22],
            showRowLabel: true,
            showRowCounter: false
        },
        "talents": {
            "itemSize": [1360, 210] ' tamaño de toda la cinta para que se desplace si tiene 7 items
            "focusBitmapUri": ghGetImageByMode("prueba.9.png") ' FOCO REDONDO desplazado
            "rowItemSize": [[161, 273]],
            "rowItemSpacing": [[21, 0]]
            "rowHeights": [400],
            '"rowSpacings": [70]
            "focusXOffset": [200],
            "rowLabelOffset": [[200, 10]],
            "showRowLabel": [true],
            "showRowCounter": [false]
        }
    }
end sub
sub setMessages()
    ' global mensajes
    m.message = m.top.findNode("message")
    m.message.setFields({
        width: 800
        height: 400
        translation: [240, 300]
        wrap: true
        horizAlign: "center"
        vertAlign: "center"
        visible: true
        text: "[MENSAJE DEFAULT] Ut consequat arcu sit amet augue lobortis, vitae porta sapien gravida. Mauris tincidunt metus nibh, id varius nisl sollicitudin sed. Sed elit urna, finibus vitae suscipit et, cursus ac felis. Etiam fringilla felis non laoreet accumsan."
        color: "#FFFFFF"
    })
    msgShow("msgLessInput") ' POR DEFECTO
end sub
sub setLoading()
    ' loading
    m.loading = m.top.findNode("loading")
end sub
' -------------------------------------
' Manejo de input
' -------------------------------------
sub OnKeyboardTextChanged(event)
    texto = event.getData()
    if m.top.debug then print ghLogHead();"OnKeyboardTextChanged -- ";texto;" (";texto.Len();")"
    if texto.Len() > 2
        m.loading.visible = true
        msgHide() ' no hay mensajes
        Search(texto)
    else
        msgShow("msgLessInput")
        m.theGrid.visible = false ' no hay datos validos
    end if
end sub
' -------------------------------------
' Busqueda
' -------------------------------------
sub Search(texto = invalid)
    if texto.Len() > 2        
        GA4Event("search", {
            search_term: LCase(texto),
            user_type: getUserTypeGA4(true),
            country: getCountryCode(ghGetRegistry("country_code", "user")),
            user_id: ghGetRegistry("user_id", "user"),
            screen_name: "searcher"
            screen_class: "/searcher"
        })
    end if
    m.logger.info("Search ", { texto: texto })
    if texto = invalid then texto = m.keyboard.value
    m.theGrid.visible = false
    m.apiContenido = ghCallApi("SearchLite", "Search_Ok", "Search_Error", false)
    m.apiContenido.query = texto
    m.apiContenido.control = "run"
end sub
sub Search_Ok(event)
    data = event.getData()
    m.logger.info("SearchOK -- ", { value: m.keyboard.value })
    resultados = ghGetChild(data, "resultados")

    ' ------------------------------
    ' print "+++++++++++++++++++++++++++++++++++++++++++"
    ' print "SEARCH ROW"
    ' print "+++++++++++++++++++++++++++++++++++++++++++"
    ' translations = m.global.translations
    ' cantRows = resultados.getChildCount()
    ' print "CANT=";cantRows
    ' for r = 0 to cantRows - 1
    '     print "[";r;"] ";
    '     print "Title=";resultados.getChild(r).title;" "
    '     ' resultados.getChild(r).title = ghTranslate(resultados.getChild(r).title, "xxx" + resultados.getChild(r).title)
    '     ' print ">>> =";resultados.getChild(r).title;" ";ghTranslate(resultados.getChild(r).title, "xxx" + resultados.getChild(r).title)
    ' end for
    ' print "+++++++++++++++++++++++++++++++++++++++++++"
    ' ------------------------------

    if resultados.getChildCount() > 0 then
        ' GOOSE says:
        ' en un futuro hay que cambiar esto por el modelo de level,
        ' con cintas de distintas formas dentro del mismo GHRow.
        m.theGrid.setFields(ghGetChild(m.rowConfig, "_BASE_", {})) ' seteos básicos
        m.theGrid.setFields(ghGetChild(m.rowConfig, "search", {})) ' seteos search
        m.theGrid.content = resultados
        msgHide()
    else
        msgShow("msgNoResults", m.keyboard.value)
    end if
    print ghLogHead();"SearchOK -- end! "
    m.loading.visible = false
end sub
sub Search_Error() ' event
    m.loading.visible = false
    msgShow("msgSomeError")
    m.theGrid.visible = false ' no hay datos validos
end sub
' -------------------------------------
' Select
' -------------------------------------
sub OnCardSelect(event)
    child = event.getRoSGNode()
    m.focusId = ghGetChild(child, "id") ' guardo el id, para poner foco cuando vuelvo a esta pantalla
    child.focus = false
    if m.top.debug then print ghLogHead();"OnCardSelect *** ---------------------"
    data = event.getData()
    if data <> invalid
        vcard = CreateObject("roSGNode", "Vcard")
        vcard.id = "VcardLite"
        vcard.ObserveField("wasClosed", "backSearch")
        group_id = ghGetChild(data, "data.data.group_id", invalid)
        if group_id = invalid then
            group_id = ghGetChild(data, "data.data.id", invalid)
        end if
        contenido = ghGetChild(data, "data.data")
        isSerie = ghGetChild(contenido, "is_series", false)
        if isSerie = false then
            seasonNumber = ghGetChild(contenido, "season_number", invalid)
            if seasonNumber <> invalid then
                isSerie = true
            end if
        end if
        contenido.contentType = 1
        if isSerie = true then
            contenido.contentType = 2
        end if
        if contenido <> invalid then
            contenido.groupId = group_id

            data = contenido
            contentType = "movie"
            if isSerie = true then
                contentType = "series"
            end if
            order = ghGetChild(child,"currFocusColumn")

            ' enviando a google analytics
            GA4Event("select_content", {
                content_id: data.groupId,
                content_name: ghGetChild(data, "title", "")
                content_type: contentType
                content_category: "not apply"
                content_availability: "by subscription"
                content_position: order

                content_list: "results"
                content_section: "searcher"

                user_type: getUserTypeGA4(true),
                country: ghGetRegistry("country_code", "user"),
                user_id: ghGetRegistry("user_id", "user"),
                screen_name: "content selection"
                screen_class: "/content-selection"
            })


            m.top.routerChild = {
                page: vcard,
                fields: {
                    data: contenido
                }
            }
        end if
    end if
end sub
sub backSearch() ' event
    if m.top.debug then print ghLogHead();"backSearch -- me llaman, pero no hago nada..."
end sub
' -------------------------------------
' Manejo de foco
' -------------------------------------
sub onWasShown() ' event
    if m.top.debug then print ghLogHead();"onWasShown *** "
    m.top.routerAddRouterTo = m.menu
    if m.focusId <> invalid then
        turnFocusTo(m.focusId)
        m.focusId = invalid
    else
        turnFocusTo("keyboard")
    end if
end sub
function onKeyEvent(key, press) as boolean
    if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
    handled = false
    if press then
        if key = "back" then
            ' goTohome()
            current = getCurrentFocus()
            if current = "keyboard" then
                turnFocusTo("menu")
            else
                turnFocusTo("keyboard")
            end if
            handled = true
        else
            turnFocusTo(guessFocusTo(key))
            handled = true
            if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";ghGetChild(m.top, "focusedChild.id");"]"
        end if
    end if
    return handled
end function
function guessFocusTo(direction, current = invalid) as string
    if current = invalid then current = getCurrentFocus()
    if m.top.debug then print ghLogHead();"guessFocusTo *** >>> ";current;" -- ";direction
    if m.map[current] <> invalid and m.map[current][direction] <> invalid then
        focusTo = m.map[current][direction]
        if m.top.findNode(focusTo).visible = false then
            newFocusTo = guessFocusTo(direction, focusTo)
            if newFocusTo = focusTo then
                focusTo = current
            else
                focusTo = newFocusTo
            end if
        end if
    else
        focusTo = current
    end if
    if m.top.debug then print ghLogHead();"guessFocusTo *** <<< ";focusTo
    return focusTo
end function
sub turnFocusTo(id)
    if m.top.debug then print ghLogHead();"turnFocusTo -- ";id
    current = getCurrentFocus()
    if current <> invalid then
        m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
        if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
        item = m.top.findNode(id)
        item.focus = true
    end if
end sub
function getCurrentFocus()
    if m.top.debug then print ghLogHead();"getCurrentFocus *** ---------------------"
    current = invalid
    if m.top.focusedChild <> invalid then
        if m.top.focusedChild.id <> "" then
            if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
                current = m.top.focusedChild.id
            end if
        end if
    end if
    if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
    return current
end function
' -------------------------------------
' Interfaz de salida
' -------------------------------------
sub goToHome()
    m.global.setFields({ navSelect: "" }) ' sin menu seleccionado
    m.top.routerReset = {
        page: "HomePageLite",
        fields: { nodo: "" }
    }
end sub
sub OnExitToHome(event) ' ?????
    data = event.getData()
    if data then
        if m.top.debug then print ghLogHead();"OnExitToHome *** ---------------------"
        goTohome()
    end if
end sub

' -------------------------------------
' Mensajeria
' -------------------------------------
sub msgShow(tipo, par1 = "")
    if tipo = "msgLessInput" then
        m.message.text = ghTranslate("search_few_characters", "Debe ingresar al menos tres letras.")
    else if tipo = "msgNoResults" then
        m.message.text = ghTranslate("search_empty_result_text", "Lo sentimos. Parece que no hay coincidencias para:{br}{@search}.{br}{br}Utiliza otras palabras clave, busca el nombre del actor o{br}director o prueba con un género.", { "search": par1 })
    else ' msgSomeError
        m.message.text = ghTranslate("error_generic_description", "Ha ocurrido un error con la búsqueda," + chr(10) + "por favor, reinténtalo más tarde.")
    end if
    m.message.visible = true
    m.theGrid.visible = false
end sub
sub msgHide()
    m.message.visible = false
    m.theGrid.visible = true
end sub
