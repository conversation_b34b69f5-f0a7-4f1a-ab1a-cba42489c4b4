sub DataInit()
  m.top.debug = false

  m.api.url = m.config.mfwk.host + "/profile/v1/aud/sub"

  if m.top.get = false then
    m.api.method = "POST"

    data = {}
    if m.top.vod_audio <> "" then
      data.vod = {
        "audio": m.top.vod_audio
        "subtitle": m.top.vod_subtitle
      }
    end if

    if m.top.live_audio <> "" then
      data.live = {
        "audio": m.top.live_audio
        "subtitle": m.top.live_subtitle
      }
    end if

    m.api.body = FormatJSON(data)
    m.api.headers.addReplace("Content-Type", "application/json")
  end if

  m.api.query.Append({
    "token": ghGetRegistry("user_token", "user")
    "region": ghGetRegistry("region"),
    "lasttouch": ghGetChild(m.global, "lastTouch.profile"),
  })

  m.api.headers.addReplace("Authorization", "Bearer " + ghGetRegistry("user_token", "user"))

end sub

sub ProcessData(res, raw)
  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ghSetRegistry("preference_live_audio", ghGetChild(res, "data.live.audio", ""), "user")
  ghSetRegistry("preference_live_subtitle", ghGetChild(res, "data.live.subtitle", ""), "user")

  ghSetRegistry("preference_vod_audio", ghGetChild(res, "data.vod.audio", ""), "user")
  ghSetRegistry("preference_vod_subtitle", ghGetChild(res, "data.vod.subtitle", ""), "user")

  m.top.content = ghGetChild(res, "data")
end sub