' GHOption
' by<PERSON><PERSON>e(2021) <EMAIL>

function init()
  ' m.top.debug = true
  m.top.focusable = true
  ' componentes
  m.label = m.top.findNode("label")
  m.label.setFocus(true)
  m.label.font = ghGetComponentFont("GHOption")
  m.border = m.top.findNode("border")
  m.border.uri = ghGetImageByMode(m.top.focusMap)
  m.background = m.top.findNode("background")
  m.top.ObserveField("menuSelected", "recalcColors")
  recalcLabelSizeAndPadding()
end function

'
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- OK!";
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent -- bubbling.. ";
    end if
    if m.top.debug then print " | "; m.top.id;" | " key;" | " press
  end if
  return handled
end function
sub updateFieldFocus() ' event
  if m.top.focus then m.top.setFocus(true)
  recalcColors()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub

'
' EVENTS
' -----------------------------
' FIELDS
' -----------------------------
sub updateFieldText()
  if m.label.text <> invalid then m.label.text = m.top.text
  refresh()
end sub
sub updateFieldTranslation()
  if m.top.translation <> invalid then m.background.translation = m.top.translation
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then
    m.background.width = m.top.width
    recalcLabelSizeAndPadding()
  end if
end sub
sub updateFieldBackColor()
  if m.top.backColor <> invalid then m.background.color = m.top.backColor
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then
    m.background.height = m.top.height
    recalcLabelSizeAndPadding()
  end if
end sub
sub updateFieldFocusMap()
  if m.top.focusMap <> invalid then
    m.border.uri = ghGetImageByMode(m.top.focusMap)
  end if
end sub
sub updateFieldPadding()
  if m.top.padding <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldBorder()
  if m.top.border <> invalid then recalcLabelSizeAndPadding()
end sub

sub Refresh()
  recalcLabelSizeAndPadding()
  recalcColors()
end sub

sub recalcLabelSizeAndPadding()

  ' width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))

  paddingFocus = ghXtoAbstract(val(m.top.focusPadding))
  padding = ghXtoAbstract(val(m.top.padding))
  paddingFocusSize = paddingFocus * 2
  paddingSize = padding * 2

  m.label.translation = ghVal2Trans(padding, padding)
  m.label.height = height - paddingFocusSize - paddingSize

  anchoLabel = m.label.boundingRect().width
  anchoBackground = anchoLabel + paddingFocusSize
  anchoTotal = anchoBackground + paddingSize
  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding [";m.top.text;"]*** anchoLabel >>> ";anchoLabel
  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding [";m.top.text;"]*** anchoBackground >>> ";anchoBackground
  m.background.translation = ghVal2Trans(paddingFocus, paddingFocus)
  m.background.height = height - paddingFocusSize
  m.background.width = anchoBackground
  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding [";m.top.text;"]*** background >>> ";m.background.width
  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding [";m.top.text;"]*** background >>> ";m.top.width
  m.border.translation = "[0,0]" ' el unico que va en la misma posicion
  m.border.height = height
  m.border.width = anchoTotal
  m.border.uri = ghGetImageByMode(m.top.focusMap)

  ' m.top.width = m.background.width

  ' if m.top.wrap then
  '   m.label.wrap = m.top.wrap
  '   m.label.vertAlign = "top"
  '   m.label.height = invalid
  '   m.label.lineSpacing = 0
  ' end if
  if m.top.debug then
    print ghLogHead();"recalcLabelSizeAndPadding -- [";m.top.text;"]"
    print ghLogHead();"recalcLabelSizeAndPadding -- back=";formatJson(m.background.translation);" ";m.background.width;" ";m.background.height
    print ghLogHead();"recalcLabelSizeAndPadding -- ";m.label.translation;" ";m.label.width;" ";m.label.height
  end if
end sub
sub recalcColors()
  ' carga la imagen y evita el parpadeo al inicializar e ir al menu
  m.border.uri = ghGetImageByMode("pill_002.9.png")
  if m.top.focus then
    m.background.color = m.top.selBackColor
    m.label.color = m.top.selColor
    m.border.visible = true
  else
    m.background.color = m.top.backColor
    m.label.color = m.top.color
    m.border.visible = false
    if m.top.menuSelected then
      m.label.color = m.top.selColor
      m.border.visible = true
      m.background.color = m.top.backColorSelected
      m.border.uri = ghGetImageByMode("pill_002_D.9.png")
      if m.top.menuFocused = false then
        m.border.uri = ghGetImageByMode("pill_002_D.9.png")
        m.background.color = m.top.backColorSelected
        ' m.background.color = "0x00FF00"
        m.border.visible = true
      end if
    end if
  end if
end sub

' Funciones internas
' -------------------------
function getLabelWidth()
  labelRect = m.label.boundingRect()
  return labelRect.width
end function



' END FILE ------------------