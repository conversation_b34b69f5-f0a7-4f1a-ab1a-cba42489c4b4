' stateStart
' -----------------------

sub stateStart()
  if m.top.debug then print ghloghead();"stateStart START ";m.buy.states["start"]
  substate = m.buy.states["start"].state
  if substate = invalid then
    startGetData()
  else if substate = "workflowstart" then
    startWorkflowStart()
  else if substate = "fail" then ' something failed
    JumpTo("out", "fail")
  else if substate = "error" then ' api failed
    JumpTo("out", "error")
  else if substate = "ok" then
    JumpTo("login")
  end if
end sub

' -----------------------
sub startGetData()
  if m.top.debug then print ghloghead("start");"startGetData"

  if m.top.buyB <> invalid and m.top.data <> invalid then
    if m.top.debug then print ghloghead("start");"startGetData -- tengo todo"

    oneOfferType = ghGetChild(m.top.buyB, "oneOfferType")
    hidden_config = ghGetChild(m.global, "hidden_confirm_trans_config", {})

    ' primero compurebo si la config all es true
    hidden_confirm_trans_config = ghGetChild(hidden_config, "all", false)

    if hidden_confirm_trans_config = false then
      hidden_confirm_trans_config = ghGetChild(hidden_config, "hidden_list", [])
      for each item in hidden_confirm_trans_config
        ' busco {oneOfferType}_{gateway} en la lista
        if item = ghGetChild(m.top.buyB, "oneOfferType", "") + "_" + ghGetChild(m.top.buyB, "gateway", "")
          hidden_confirm_trans_config = true
          exit for
        end if
      end for
    end if

    m.buy.data = {
      group: m.top.data
      button: m.top.buyB,
      accessCode: m.top.accessCode
      contentId: m.top.contentId
      content_name: m.top.content_name
      content_type: m.top.content_type
      content_category: m.top.content_category
      oneOfferType: oneOfferType
      hidden_confirm_trans_config: hidden_confirm_trans_config
    }

    JumpTo("start", "workflowstart")
  else
    if m.top.debug then
      print ghLogHead();"startGetData *** --------------------------"
      print ghloghead();"startGetData -- FALTAN DATOS"
      print ghLogHead();"startGetData -- buyB=";m.top.buyB
      print ghLogHead();"startGetData -- accessCode=";m.top.accessCode
      print ghLogHead();"startGetData -- data=";m.top.data
      print ghLogHead();"startGetData -- --------------------------"
    end if
    JumpTo("start", "fail")
  end if
end sub
' -----------------------
' WorkFlowStart flow
sub startWorkflowStart()
  if m.top.debug then print ghloghead("start");"startWorkflowStart"
  apiStart = ghCallApi("WorkflowStartLite", "startWorkflowStartOk", "startWorkflowStartError", false)
  ' apiStart.debug = true
  apiStart.link = ghGetChild(m.buy.data, "button.linkworkflowstart")
  apiStart.control = "run"
end sub
sub startWorkflowStartOk(event)
  data = event.getData()

  m.logger.debug("stateStart startWorkflowStartOk", { data: data })

  m.buy.states["start"].selectedPaymentMethod = ghGetChild(data.response, "selectedPaymentMethod", "")

  m.buy.states["start"].object_type = ghGetChild(data, "entry.object_type")
  ' cambia la respuesta segun el tipo de objeto
  if m.buy.states["start"].object_type = "A" then ' subscripcion
    m.buy.states["start"].methods = ghGetChild(data, "response.list", [])
  else if m.buy.states["start"].object_type = "G" or m.buy.start.object_type = "T" then ' compra-renta - temporada

    ' TODO esta info ya la tengo de la vcard, solo se usaba title de seasons
    ' m.buy.states["start"].contentInfo = ghGetChild(data, "response.workflow.contentInfo")

    country = ghGetRegistry("country_code", "user")
    methods = ghGetChild(data, "response.workflow.listBuyLinks", [])
    if LCase(country) = "mx" then
      m.buy.states["start"].methods = sortPaymentMethods(methods) ' function in Utils.brs
    else
      m.buy.states["start"].methods = methods
    end if
  else ' hay que poner un errorWasClosed
    m.buy.states["start"].methods = []
    JumpTo("start", "error")
    return ' me tengo que ir !
  end if

  if m.top.debug then print ghloghead("start");"startWorkflowStartOk -- state ";m.buy.states["start"]
  JumpTo("start", "ok")
end sub
sub startWorkflowStartError(event)
  data = event.getData()
  if m.top.debug then print ghloghead("start");"startWorkflowStartError"
  if m.top.debug then
    print "*************************************"
    print data
    print "*************************************"
  end if
  JumpTo("start", "error")
end sub
' -----------------------
