# Proceso de Certificación



## Registración temporaria

Hice una registración temporaria personal con mi email `<EMAIL>`

con esto conseguígenerar el `DevID` y el `Password` tal como se explica en el [proceso de upload][upload]

```
Password: 10gmId4dlWSrXkwFwcbJSQ==
DevID: a7b959d22a0f5563cfdf7bdb81d7257d4d6aba15
```




## Manejo del canal

Desde el [panel del desarrollador][panel], ingreso en `Administrar Mis canales`.

![panel](panel.jpeg)

Dentro de la página de canales, selecciono el ícono del canal.

![canal](canal.jpeg)

Dentro puedo ver todos los datos del canal, y la lista del proceso de certificación.

![info](info.jpeg)

En el análisis estático puedo ver los errores y advertencias encontradas.
![analisis.1](analisis.1.jpeg)





## Bibliografía

### Panel

Panel
https://developer.roku.com/es-ar/developer

Package upload window
https://developer.roku.com/es-ar/docs/developer-program/publishing/channel-publishing-guide.md#package-upload-window

Static Analysis window
https://developer.roku.com/es-ar/docs/developer-program/publishing/channel-publishing-guide.md#static-analysis-window

### Info

Channel publishing
https://developer.roku.com/es-ar/docs/developer-program/publishing/channel-publishing-guide.md#non-certified-package-upload-window

Direct Publisher quick start guide
https://developer.roku.com/es-ar/docs/direct-publisher/getting-started/getting-started.md



---

[panel]: https://developer.roku.com/es-ar/developer
[upload]: https://developer.roku.com/es-ar/docs/developer-program/publishing/channel-publishing-guide.md#package-upload-window