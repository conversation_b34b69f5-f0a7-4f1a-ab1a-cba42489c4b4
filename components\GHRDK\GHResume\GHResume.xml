<?xml version="1.0" encoding="utf-8" ?>

<component name="GHResume" extends="Page" >

  <script type="text/brightscript" uri="GHResume.brs"/>

  <interface>
    <field id="selected" type="string" />
  </interface>

  <children>
    <Rectangle id="background" color="#000000" translation="[0,0]" width="1280" height="720"/>

    <Label id="titleText" focusable="false" color="0xFFFFFF" translation="[0,214]" wrap="true" vertAlign="center" horizAlign="center" width="1280" height="72" />

    
    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton id="reanudar" value="reanudar" text="ELIMINAR" width="344" height="72" translation="[460,312]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" focusColor="0xFFFFFF" selBackColor="#981C15" />
      <GHButton id="inicio" value="inicio" text="CANCELAR" width="344" height="72" translation="[460,392]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#2E303D" focusColor="0xFFFFFF" selBackColor="#2E303D" />
    </GHButtonGroup>
  </children>

</component>
