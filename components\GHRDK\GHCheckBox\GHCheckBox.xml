<?xml version="1.0" encoding="utf-8" ?>
<component name="GHCheckBox" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHCheckBox.brs"/>
  <script type="text/brightscript" uri="GHCheckBox_fields.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>

    <!-- text -->
    <field id="text" type="string" value="MyButton" onChange="updateFieldText"/>
    <field id="textWrap" type="string" value="true" alias="label.wrap"/>
    <field id="tildeFig" type="string" value="pkg:/images/Check_HD.png" alias="tilde.uri" />
    <field id="textlineSpacing" type="string" value="true" alias="label.lineSpacing"/>

    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="350" onChange="updateFieldWidth" />
    <field id="height" type="string" value="60" onChange="updateFieldHeight" />

    <!-- align -->
    <field id="horizAlign" type="string" value="center" onChange="updateFieldHorizAlign" />
    <field id="vertAlign" type="string" value="center" onChange="updateFieldVertAlign" />
    <!-- colors -->
    <field id="color" type="string" value="0xFFFFFFFF" onChange="updateFieldColor" />
    <field id="selColor" type="string" value="0xFFFFFFFF" onChange="updateFieldSelColor" />

    <field id="backColor" type="string" value="0x212224FF" onChange="updateFieldBackColor" />
    <field id="selBackColor" type="string" value="0x212224FF" onChange="updateFieldSelBackColor" />

    <!-- padding and margin -->
    <field id="padding" type="string" value="5" onChange="updateFieldPadding" alwaysNotify="true" />
    <field id="focusMap" type="string" value="pkg:/images/focus01.9.png" alias="border.uri" />
    <field id="focusColor" type="string" value="0xFFFFFF" alias="border.blendColor" />

    <!-- focus and select -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="selected" type="boolean" value="false" onChange="updateFieldSelected" alwaysNotify="true" />
    <field id="checked" type="boolean" value="false" onChange="updateFieldChecked" alwaysNotify="true" />
    <field id="value" type="string" value="mybutton"/>

    <!-- debug -->
    <field id="debug" type="boolean" value="false" />

  </interface>

  <children>
    <Label id="label" text="Ok" color="0x000000" translation="[0,0]" width="360" height="80" vertAlign="center" horizAlign="left" />

    <Rectangle id="background" color="0x00000000" translation="[0,0]" width="48" height="48" />
    <Poster id="border" translation="[0,0]" width="48" height="48" uri="" visible="false" blendColor="0xFFFFFFFF" />
    <Poster id="tilde" translation="[0,0]" width="48" height="48" uri="" visible="false" blendColor="0xFFFFFFFF" />
  </children>

</component>
