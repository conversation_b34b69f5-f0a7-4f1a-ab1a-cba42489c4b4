sub DataInit()
    m.api.method = "GET"
    m.api.url = m.config.mfwk.host + "/services/user/remindcontrolpin"
    m.api.query.Append({
        "api_version":ghGetChild(m.global.config, "api.version.ProfileRead", m.global.config.api.versions.default)
        "region": ghGetRegistry("region")
        "user_id": ghGetRegistry("user_id", "user")
        "user_hash": ghGetRegistry("session_userhash", "user")
    })
end sub


sub ProcessData(res, raw)
    if m.top.debug then
        print ghLogHead();"ProcessData -- Body= ";res
        print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
    end if
    response = res.response
    m.top.content = response
end sub