' BuyConfirm
' ---------------------------

sub DataInit()
  ' ******************************************
  ' PAYWAYCONFIRM
  ' ******************************************
  ' /services/payway/confirm?
  ' buylink=Q1pNaE9jWG5LYzNtN0RWV2hQc1NhbHVtQkhZQUpEU2RYME5VQVdzR2JxOXNMbUtMLzAwOEFOWCtvTTdMMmQrRkRyanZhYnMzSm1pakhGOFdRUSt4NlZBRVdBZXJRNG9FMnk5NlIxQlZoaUtOVnJKLzQ4VGRmYUMxa1VlL1NSYWoxUUVla3pGWXA3TFBqd2txNGR5WklWaFF6Qkp2WTJTVmlPRXY4SGFUSmUvM0xQSlBLNGdPT0t3eERWT0hNV1d4K21nL0dQOGF6azJyUzNKTFdaTHBveUs1VjhVQ0YvdWdxNWtvaGk3R1JTTHhqSkluRWxvcFE5cGJMcEZ3QUcrN21hbGdEek4wOGF1dGF1djRBYmRMb0g0WndybHFka1BFbEJJOHVLZWIrOTg1cUNVK2tCc0lZaHJ2Z1E9PQ%3D%3D&
  ' payway=promogate&
  ' pincode=005709472524235360121&
  ' authpn=roku&
  ' authpt=IdbIIWeFzYdy&
  ' device_type=generic&
  ' device_model=generic&
  ' device_manufacturer=roku&
  ' device_category=stb&
  ' api_version=v5.91'
  ' HKS=60e318751007c&
  ' region=mexico&

  ' "/services/payway/confirm?
  ' buylink=Q1pNaE9jWG5LYzNtN0RWV2hQc1NhbHVtQkhZQUpEU2RYME5VQVdzR2JxOXNMbUtMLzAwOEFOWCtvTTdMMmQrRkRyanZhYnMzSm1pakhGOFdRUSt4NlZBRVdBZXJRNG9FMnk5NlIxQlZoaUtOVnJKLzQ4VGRmYUMxa1VlL1NSYWoxUUVla3pGWXA3TFBqd2txNGR5WklWaFF6Qkp2WTJTVmlPRXY4SGFUSmUvM0xQSlBLNGdPT0t3eERWT0hNV1d4K21nL0dQOGF6azJyUzNKTFdaTHBveUs1VjhVQ0YvdWdxNWtvaGk3R1JTTHhqSkluRWxvcFE5cGJMcEZ3QUcrN21hbGdEek4wOGF1dGF1djRBYmRMb0g0WndybHFka1BFbEJJOHVLZWIrOTg1cUNVK2tCc0lZaHJ2Z1E9PQ%3D%3D&
  ' season_id=0&
  ' payway=promogate&
  ' region=mexico&
  ' device_category=stb&
  ' device_manufacturer=roku&
  ' device_model=generic&
  ' device_type=generic&
  ' HKS=%2860e32474687e5%29"

  m.api.url = m.config.mfwk.host + m.top.buylink

  m.api.query.delete("api_version")

  m.api.headers.Append({ "user-token": ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    pincode: m.top.pincode
    "user_id": ghGetRegistry("user_id", "user")
  })
  m.api.timeout = 30000

end sub

sub ProcessData(res, raw)
  if m.top.debug then print "*********************************"
  if m.top.debug then print ghLogHead();"body = ";res
  if m.top.debug then print ghLogHead();"body.entry = ";ghGetChild(res, "entry", "! no existe")

  ' para probar errores
  ' res.errors = {
  '   code: "TEXT_PGS_PRUEBA_2"
  ' }

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  content = ghGetChild(res, "response", {})
  content.msgTitle = "¡Transacción exitosa!"
  content.msgContent = ghDecodeHTML(ghGetChild(content, "msg", "Su transacción ha sido exitosa."))

  updateGlobalArray("lasttouch", {
    purchased: ghGetChild(res, "response.lastTouch")
  })

  m.top.content = content

  if m.top.debug then print "*********************************"
end sub

