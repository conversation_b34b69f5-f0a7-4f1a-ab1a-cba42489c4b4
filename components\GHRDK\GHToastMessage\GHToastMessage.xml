<?xml version="1.0" encoding="utf-8" ?>

<component name="GHToastMessage" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHToastMessage.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- interfaz entrada -->
    <field id="time" type="int" value="12" onChange="onTimeChange" />

    <field id="title" type="string" value="***" onChange="config" />
    <field id="divisor" type="string" value="|" onChange="config" />
    <field id="backgroundImage" type="string" value="pkg:/images/fondoCuadrado.png" alias="background.uri"/>
    <field id="backgroundColor" type="string" alias="background.blendColor"/>

    <field id="titleColor" type="string" value="0xFFFFFF" />
    <field id="font" type="node" />

    <!-- <field id="iconImage" type="string" value="" onChange="onIconChange" /> -->
    <field id="iconImage" type="string" value="" onChange="config" />

    <!-- <field id="contentOffset" type="array" value="[25,15]" onChange="Redraw"/> -->
    <field id="contentOffset" type="array" value="[25,15]" onChange="config" />

    <!-- <field id="separatorWidth" type="int" value="20" onChange="Redraw"/> -->
    <field id="separatorWidth" type="int" value="20" onChange="config" />

    <field id="alignement" type="string" value="center" alias="texts.horizAlignment" onChange="config" />

    <field id="backgroundBlendColor" type="string" alias="background.blendColor" onChange="config"/>
    <field id="body" type="string" value="***" onChange="config" />
    <field id="layoutDirectionToast" type="string" onChange="config" alias="texts.layoutDirection" />
    <field id="iconWidth" type="string" value="40" onChange="config" alias="icon.width" />
    <field id="iconHeight" type="string" value="40" onChange="config" alias="icon.height" />
    <field id="iconPosition" type="string" value="middle" onChange="config" />


    <!-- interno -->
    <field id="debug" type="boolean" value="false" />

    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" /> -->
    <field id="focus" type="boolean" value="false" />

    <field id="wasClosed" type="boolean" value="false" alwaysNotify="true" />
  </interface>

  <children>
    <Poster id="background" blendColor="#080808" translation="[0,0]" />
    <LayoutGroup id="texts" layoutDirection="horiz" horizAlignment="center" vertAlignment="center" itemSpacings="[0]">
      <Poster id="icon" height="40" width="40" />
      <!-- <Label id="titleText" focusable="false" color="0xFFFFFF" visible="false" />
      <Rectangle id="separatorL" color="0xFFFFFF00" height="10" width="10" visible="true" />
      <Rectangle id="separatorR" color="0xFFFFFF00" height="10" width="10" visible="true" />
      <Label id="divisorText" focusable="false" color="0xFFFFFF" visible="false" />
      <Label id="bodyText" focusable="false" color="0xFFFFFF" visible="false" /> -->
    </LayoutGroup>
  </children>

</component>
