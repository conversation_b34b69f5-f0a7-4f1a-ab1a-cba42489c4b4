sub Init()
    m.top.debug = true
    m.top.getScene().updateTheme = m.global.config.theme
    if m.top.debug then print ghLogHead();"erroPage Init..."
    ' m.loading = m.top.findNode("loading")
    m.errorTitle = m.top.findNode("errorTitle")
    m.b01 = m.top.findNode("b01")

    m.subscribeButton = m.top.findNode("subscribeButton")
    m.b01.ObserveField("backSelected", "BackTo")
    m.b01.ObserveField("selected", "OnButtonSelected")

    m.map = { "b01": { "up": invalid, "right": invalid, "down": invalid, "left": invalid, "default": true } }
    m.description0 = m.top.findNode("description0")
    m.description = m.top.findNode("description")
    m.description0.text = ghTranslate("channel_error_generic_title", "Hubo un error inesperado")
    m.description.text = ghTranslate("channel_error_generic_message", "Por el momento no pudimos completar la acción. Por favor, intenta más tarde.")
    m.description.setFields({
        font: ghGetFont(20, "regular")
    })
    m.description0.setFields({
        font: ghGetFont(24, "bold")
    })
end sub


sub updateFieldFocus()
    if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
    turnFocusTo("b01")
    if m.top.debug then print ghLogHead();"changeFieldFocus -- init"
end sub

sub OnButtonSelected(event)
    child = event.getRoSGNode()
    if m.top.debug then print ghLogHead();"OnButtonSelected -- "
    if child.selected then
        child.selected = false
        if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
        m.top.signalBeacon("AppDialogComplete")
        BackTo()
    else if child.value = "Back" then
        BackTo()
    end if
end sub

sub BackTo()
    if m.top.debug then print ghLogHead();"BackTo."
    m.top.signalBeacon("AppDialogComplete")
    m.top.routerClose = true
end sub


' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
    if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
    handled = false
    if press then
        if key <> "back" then
            turnFocusTo(guessFocusTo(key))
            handled = true
            if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
        end if
    end if
    return handled
end function

function guessFocusTo(direction) as string
    current = getCurrentFocus()
    ' a donde voy?
    if m.map[current][direction] <> invalid then
        focusTo = m.map[current][direction]
    else
        focusTo = current
    end if
    return focusTo
end function

sub turnFocusTo(id)
    current = getCurrentFocus()
    if current <> id then
        if current <> invalid then
            m.top.findNode(current).focus = false ' apago el actual
        end if
        if m.top.findNode(id).focus <> invalid then
            if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
            m.top.findNode(id).focus = true
        end if
    else
        if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
    end if
end sub

function getCurrentFocus()
    current = invalid
    if m.top.focusedChild <> invalid then
        if m.top.focusedChild.id <> "" then
            if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
                current = m.top.focusedChild.id
            end if
        end if
    end if
    if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
    return current
end function

