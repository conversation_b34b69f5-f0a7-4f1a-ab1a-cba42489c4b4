' GHButton
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' fondo
  'm.fondo = m.top.findNode("fondo")
  ' texto
  m.scrolltext = m.top.findNode("scrolltext")
  m.scrolltext.font = ghGetComponentFont("GHScrollText")
  m.scrolltext.lineSpacing = 0
  m.scrolltext.setFocus(true)
  'm.scrolltext.scrollbarTrackBitmapUri= "."
  m.scrolltext.scrollbarThumbBitmapUri= ghGetImageByMode("barraLateral.9.png")
  ' instrucciones
  m.instructionbar = m.top.findNode("instructionbar")
  m.instructiongroup = m.top.findNode("instructiongroup")
  m.imgExit = m.top.findNode("imgExit")
  m.imgUpDn = m.top.findNode("imgUpDn")
  m.imgPgUpDn = m.top.findNode("imgPgUpDn")
  m.txtExit = m.top.findNode("txtExit")
  m.txtExit.text = ghTranslate("textscroll_exit", "!textscroll_exit")
  m.txtUpDn = m.top.findNode("txtUpDn")
  m.txtUpDn.text = ghTranslate("textscroll_updn", "!textscroll_updn")
  m.txtPgUpDn = m.top.findNode("txtPgUpDn")
  m.txtPgUpDn.text = ghTranslate("textscroll_pgupdn", "!textscroll_pgupdn")
  ' ------------
  recalcLabelSizeAndPadding()
end function
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then ' entro y salgo de la lectura
      if m.top.debug then print ghLogHead(); "onKeyEvent OK!";" | "; m.top.id;" | " key;" | " press
      m.top.reading = true
      handled = true
    else if key = "back" then ' back solo para salir de lectura
      if m.top.debug then print ghLogHead(); "onKeyEvent BACK! (reading)";" | "; m.top.id;" | " key;" | " press
      if m.top.reading then
        m.top.reading = false
        m.top.selected = true
        handled = true
      end if
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent bubbling.. ";" | "; m.top.id;" | " key;" | " press
    end if
  end if
  return handled
end function
sub updateFieldFocus() ' event
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
  if m.top.focus then
    m.top.setFocus(true)
  else
    m.top.reading = false
    m.top.selected = false
  end if
  Refresh()
end sub
sub updateFieldReading()
  if m.top.debug then print ghLogHead();"updateFieldReading [";m.top.reading;"]"
  if m.top.reading then
    m.top.focus = true
    m.scrolltext.setFocus(true)
  else
    m.scrolltext.setFocus(false)
    m.top.setFocus(true)
  end if
  ' refresh()
end sub
' FIELDS
' -----------------------------
sub updateFieldText()
  if m.top.debug then print ghLogHead();"updateFieldText -- ini."
  if m.top.text <> invalid then m.scrolltext.text = m.top.text
  if m.top.debug then print ghLogHead();"updateFieldText -- end."
end sub
' posicion y tamano
sub updateFieldTranslation()
  if m.top.translation <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldWidth()
  if m.top.width <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldHeight()
  if m.top.height <> invalid then recalcLabelSizeAndPadding()
end sub
sub updateFieldPadding()
  if m.top.padding <> invalid then recalcLabelSizeAndPadding()
end sub
' UTILS
' -----------------------------
sub Refresh()
  if m.top.debug then print ghLogHead();"Refresh"
  recalcLabelSizeAndPadding()
  recalcColors()
end sub
sub recalcLabelSizeAndPadding()
  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding"

  width = ghXtoAbstract(val(m.top.width))
  height = ghYtoAbstract(val(m.top.height))
  padding = ghYtoAbstract(val(m.top.padding))

  if m.top.debug then print ghLogHead();"recalcLabelSizeAndPadding showhelp= ";m.top.showhelp
  if m.top.showhelp then
    instrHeight = 600
  else
    instrHeight = 0
  end if

  m.scrolltext.translation = [122, 20] ' en el padding
  m.scrolltext.width = 900

  if m.top.reading then
    m.scrolltext.height = 500
    if m.top.showhelp then
      m.instructionbar.visible = true
      m.instructionbar.translation = [padding, height - padding - instrHeight]
      m.instructionbar.height = instrHeight
      m.instructionbar.width = width - (2 * padding)
      m.instructiongroup.translation = [m.instructionbar.width / 2, instrHeight / 2] ' esta centrado
    end if
  else
    m.scrolltext.height = height - (2 * padding)
    if m.top.showhelp then
      m.instructionbar.visible = false
    end if
  end if
end sub
sub recalcColors()
  ' siempre
  m.instructionbar.color = m.top.barBackColor
  m.imgExit.blendColor = m.top.barColor
  m.txtExit.color = m.top.barColor
  m.imgUpDn.blendColor = m.top.barColor
  m.txtUpDn.color = m.top.barColor
  m.imgPgUpDn.blendColor = m.top.barColor
  m.txtPgUpDn.color = m.top.barColor
end sub
' END FILE
