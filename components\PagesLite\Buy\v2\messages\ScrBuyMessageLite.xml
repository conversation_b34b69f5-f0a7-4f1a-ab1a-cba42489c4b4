<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="ScrBuyMessage" extends="SGDEXComponent"> -->
<component name="ScrBuyMessageLite" extends="Page">

    <script type="text/brightscript" uri="ScrBuyMessageLite.brs" />
    <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" /> -->
    <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" /> -->

    <interface>
        <!-- interfaz de entrada -->
        <field id="mode" type="string" value="scrError" />
        <field id="title" type="string" alias="lblTitle.text" />
        <field id="message" type="string" alias="lblDescrip.text" />
        <field id="accept" type="string" alias="btnAccept.text" />
        <field id="fwd" type="string" />
        <field id="back" type="string" />
        <!-- interfaz de salida -->
        <field id="value" type="assocarray" />
        <!-- interfaz interna -->
        <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" /> -->
        <!-- <field id="debug" type="boolean" value="true" /> -->
    </interface>

    <children>
        <Group id="scrError">
            <Label id="lblTitle" translation="[0,210]" width="1280" height="48" text="*" />
            <Label id="lblDescrip" translation="[328,256]" width="624" height="136" horizAlign="center" text="*" wrap="true" />
            <GHButton id="btnAccept" translation="[392,412]" value="COMENZAR A DISFRUTAR" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
        </Group>
    </children>

</component>
