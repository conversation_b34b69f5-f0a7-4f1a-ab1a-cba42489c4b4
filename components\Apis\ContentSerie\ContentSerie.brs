sub DataInit()
  m.api.url = m.config.mfwk.host + "/services/content/serie"
  m.api.query.Append({
    "group_id": m.top.group_id,
    "appversion": ghGetAppVersion(),
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  response = ghGetChild(res, "response")

  if response = invalid then
    m.top.error = {
      "code": "500",
      "message": "Internal Server Error",
      "raw": raw
    }
    return
  end if

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub