<?xml version="1.0" encoding="utf-8"?>

<component name="MainScene" extends="RouterBase">

	<script type="text/brightscript" uri="MainScene.brs" />
	<script type="text/brightscript" uri="logic.JumpStartClassic.brs" />
	<script type="text/brightscript" uri="logic.JumpStartLite.brs" />
	<script type="text/brightscript" uri="logic.JumpPages.brs" />
	<script type="text/brightscript" uri="logic.DeepLinking.brs" />

	<script type="text/brightscript" uri="./services/configServices.brs" />

	<script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Files.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Screen.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <script type="text/brightscript" uri="pkg:/source/GHRDK/FocusHandler.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Logger.brs" />

	<interface>
		<function name="warmReboot" />
	</interface>

	<children>
	</children>

</component>
