' GHSeekBar
' by<PERSON><PERSON><PERSON>(2023) <EMAIL>
' ---------------------------------------------
function init()
  ' m.top.debug = true
  ' componentes
  m.fondo = m.top.findNode("fondo")
  m.barra = m.top.findNode("barra")
  m.knot = m.top.findNode("knot")
  ' inicializacion
  refresh() ' dibujo inicial
  buildKeyTimer() ' inicializacion del timer
end function
function refresh()
  if m.top.debug <> invalid then
    if m.top.debug then print ghLogHead();"refresh ** "
    ' barra
    m.barra.translation = [m.top.padding, m.top.padding]
    m.barra.height = m.fondo.height - (2 * m.top.padding)
    m.barra.width = _posToBarWidth()
    ' knot
    knotBound = m.knot.boundingRect()
    ' if m.top.debug then print ghLogHead();"refresh -- knot=";knotBound
    knotRadio = knotBound.width / 2
    knotX = m.top.padding + m.barra.width - knotRadio
    knotY = ((knotBound.height / 2) - (m.fondo.height / 2)) * -1
    m.knot.translation = [knotX, knotY]
    ' if m.top.debug then print ghLogHead();"refresh -- knotTranslation=";m.knot.translation
    refreshColor(m.top.focus)
  end if
end function
sub refreshColor(foco = false)
  if m.top.debug then print ghLogHead();"refreshColor -- foco=";foco
  if foco then ' entro----
    m.fondo.color = m.top.backSelColor
    m.barra.color = m.top.barSelColor
    m.knot.setFields({
      uri: m.top.knotSelUri
      width: m.top.knotSelWidth
      height: m.top.knotSelHeight
      'blendColor: m.top.knotSelColor
    })
  else ' salgo ------------------
    m.fondo.color = ghGetChild(m.top, "backColor", "#000000")
    m.barra.color = ghGetChild(m.top, "barColor", "#8A0000")
    m.knot.setFields({
      uri: m.top.knotUri
      width: m.top.knotWidth
      height: m.top.knotHeight
      ' blendColor: m.top.knotColor
    })
  end if
end sub
' EVENTS
' --------------------------
sub updateFieldFocus(event) ' donde me paro cuando arranca la pantalla
  foco = event.getData()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- ";foco
  ' if foco then
  refresh()
end sub
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    m.lastKeyToRepeat = key
    if key = "left" then
      if m.top.debug then print ghLogHead();"onKeyEvent - [";key;"]"
      _movePosition(-m.top.stepValue)
      handled = true
    else if key = "right" then
      if m.top.debug then print ghLogHead();"onKeyEvent + [";key;"]"
      _movePosition(m.top.stepValue)
      handled = true
    else if key = "fastforward" then
      if m.top.debug then print ghLogHead();"onKeyEvent >> [";key;"]"
      _movePosition(m.top.stepValue * m.top.stepMultiplier)
      handled = true
    else if key = "rewind" then
      if m.top.debug then print ghLogHead();"onKeyEvent << [";key;"]"
      _movePosition(-m.top.stepValue * m.top.stepMultiplier)
      handled = true
    else if key = "replay" then
      ' _setPosition(0)
      _movePosition(-25)
      handled = true
    else if key = "OK" then
      if m.top.debug then print ghLogHead();"onKeyEvent -- ++ [";key;"]"
      m.top.selected = true
      handled = true
      ' else if key = "back" then
      '   if m.top.debug then print ghLogHead();"onKeyEvent -- [";key;"]"
      '   m.top.back = true
      '   handled = true
    else if key = "play" then
      if m.top.debug then print ghLogHead();"onKeyEvent -- play [";key;"]"
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead();"onKeyEvent + [";key;"] -- ME FUI!!"
      handled = false
    end if
    ' repeat -------------------
    if handled and key <> "play" then startKeyTimer()
  else
    stopKeyTimer()
  end if
  return handled
end function
sub onKeyBuffer(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onKeyBuffer -- << ";data
  if data <> invalid then
    if data.Count() > 0 then
      for k = 0 to data.Count() - 1
        onKeyEvent(data[k], true)
        onKeyEvent(data[k], false)
      next
    end if
  end if
end sub
' TIMER
' ------------------------------
sub buildKeyTimer() ' inicializacion del timer
  m.repeatLauncher = CreateObject("roSGNode", "Timer")
  m.repeatLauncher.duration = 0.3
  m.repeatLauncher.repeat = false
  m.repeatLauncher.ObserveField("fire", "triggerKeyLaunchTimer")
  m.repeat = CreateObject("roSGNode", "Timer")
  m.repeat.duration = 0.1
  m.repeat.repeat = true
  m.repeat.ObserveField("fire", "triggerKeyTimer")
end sub
sub startKeyTimer()
  m.repeatLauncher.control = "start"
end sub
sub stopKeyTimer()
  if m.top.debug then print ghLogHead();"[*] stopKeyTimer ** : ";m.lastKeyToRepeat
  m.repeatLauncher.control = "stop"
  m.repeat.control = "stop"
end sub
sub triggerKeyLaunchTimer() ' arranque del timer
  m.repeat.control = "start"
end sub
sub triggerKeyTimer() ' triger del timer
  if m.top.debug then print ghLogHead();"[*] triggerKeyTimer ** : ";m.lastKeyToRepeat
  if m.lastKeyToRepeat <> "OK" and m.lastKeyToRepeat <> "back" then
    onKeyEvent(m.lastKeyToRepeat, true) ' fake key
  else
    stopKeyTimer()
  end if
end sub
' UTILITIES
' -----------------------------
function _posToBarWidth() ' calcula el largo de la barra
  percent = m.top.curValue / m.top.maxValue * 100
  maxSize = m.fondo.width - (2 * m.top.padding)
  width = maxSize * percent / 100
  if m.top.debug then print ghLogHead();"_posToBarWidth -- pixels max=";maxSize;" %=";percent;" width=";width
  return width
end function
sub _setPosition(newval)
  if newval <> invalid then
    if newval > m.top.maxValue then newval = m.top.maxValue
    if newval < 0 then newval = 0
    m.top.curValue = newval
    m.top.newValue = newval
    if m.top.debug then print ghLogHead();"_setPosition -- ";newval
  end if
end sub
sub _movePosition(offset = 0)
  _setPosition(m.top.curValue + offset)
end sub
' END
' --------------------------
