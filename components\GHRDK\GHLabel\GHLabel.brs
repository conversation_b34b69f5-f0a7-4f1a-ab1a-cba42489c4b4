' GHLabel
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes
  m.label = m.top.findNode("label")
  m.label.setFocus(true)
end function
' EVENTS
' -----------------------------
function onKeyEvent(key, press)
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- OK!";
      m.top.selected = true
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent -- bubbling.. ";
    end if
    if m.top.debug then print " | "; m.top.id;" | " key;" | " press
  end if
  return handled
end function
sub updateFieldFocus()' event
  if m.top.focus then
    m.top.setFocus(true)
  end if
  recalcColors()
  if m.top.debug then print ghLogHead(); "updateFieldFocus id to "; m.top.focus
end sub
' END FILE