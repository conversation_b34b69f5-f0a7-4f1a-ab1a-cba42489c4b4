' purchasednodeid
' https://app.swaggerhub.com/apis/ClaroVideo/v1_purchasednodeid/1.0.0#/Microframework/get_services_payway_paymentservice_v1_purchasednodeid__object_id_

sub DataInit()
  m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/payway/paymentservice/v1/purchasednodeid/" + m.top.object_id

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("user_id") ' sin user id
  m.api.query.delete("HKS") ' sin user id
  m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    "region": ghGetRegistry("region"),
    ' "api_version": ghGetChild(m.global.config, "api.version.Purchase", m.global.config.api.versions.default),
    ' "object_type": "A",
    ' "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- headers="m.api.headers
    print ghLogHead();"DataInit -- query="m.api.query
  end if
end sub

sub ProcessData(res, raw)

  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  ' response = ghGetChild(res, "response")

  if res = invalid
    m.top.error = ghErrorNetwork(m.api.name, m.api.url, raw)
    return
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = res
end sub