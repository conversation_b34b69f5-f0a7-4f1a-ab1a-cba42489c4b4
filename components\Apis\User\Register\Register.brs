sub DataInit()
  m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/user/register"
  m.api.query.Append({
    "email": m.top.username,
    "password": m.top.password,
    "firstname": m.top.firstname, ' optativo
    "lastname": m.top.lastname, ' optativo
    ' hardcode
    "includPaywayProfile": "true",
    "accepterms": "1",
    "region": ghGetRegistry("region")
  })
  if m.top.debug then
    print ghLogHead();"DataInit -- api=";m.api
    print ghLogHead();"DataInit -- query=";m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";raw
  end if

  content = {}
  if res = invalid then
    content.error = true
    content.isLoggedIn = false
    content.error_code = ghTranslate("register_param_mail_missing_error_title", "Valida la información") ''va a ser el mismo error de parámetros
    content.error_msg = ghTranslate("register_param_mail_missing_error_description", "Para continuar, valida la información solicitada en los campos correspondientes.")
    m.top.error = content
  end if

  if res <> invalid then
    if res.msg = "ERROR" then ' ==================================================
      content.isLoggedIn = false
      content.error = true
      content.error_code = res.errors.code
      if content.error_code = "error_params" then ' errores varios
        content.error_code = ghTranslate("register_param_mail_missing_error_title", "Valida la información") ''va a ser el mismo error de parámetros
        content.error_msg = ghTranslate("register_param_mail_missing_error_description", "Para continuar, valida la información solicitada en los campos correspondientes.")
      end if
      if content.error_code = "USR_REG_00008" then
        content.error_code = ghTranslate("error_mail_modal_title_label", "Correo electrónico no válido")
        content.error_msg = ghTranslate("email_tooltip_valid_label_validation", "Ingresa un correo electrónico válido")
      end if
      if content.error_code = "USR_REG_00005" then ' el usuario existe
        content.error_code = ghTranslate("error_mail_modal_title_label", "Correo electrónico no válido")
        content.error_msg = ghTranslate("registerMail_tooltip_error_label", "Este correo ya está vinculado a la base de datos de Claro video.")
      end if
      if content.error_code = "USR_REG_00007" then 'va a ser el mismo error de parámetros
        content.error_code = ghTranslate("register_param_password_missing_error_title", "Valida la información")
        content.error_msg = ghTranslate("register_param_password_missing_error_description", "Para continuar, valida la información solicitada en los campos correspondientes.")
      end if
      if content.error_code = "USR_REG_00009" then 'va a ser el mismo error de parámetros
        content.error_code = ghTranslate("register_param_password_missing_error_title", "Valida la información")
        content.error_msg = ghTranslate("USR_REG_00009", "El mail utilizado para el registro se encuentra en la black list de la aplicación")
      end if
      if content.error_code = "USR_PSW_00012" then ' mal el password
        content.error_code = ghTranslate("register_password_tooltip_error_label", "Contraseña incorrecta")
        content.error_msg = ghTranslate("register_password_tooltip_error_description", "La contraseña no cumple con los criterios establecidos")
      end if
      ' user
      ' ----------------------------------------------------
      ghSetRegistry("isLoggedIn", "false")
      ghDeleteSectionRegistry("user")
      print ghLogHead();"ProcessData -- Register con Error"
      m.top.error = content
      return
    end if

    if res.msg = "OK" then ' ======================================================
      response = res.response
      m.global.superhighlight = ghGetChild(response, "superhighlight", [])

      content.addReplace("error", false)
      content.addReplace("isLoggedIn", true)
      ' global
      ' ----------------------------------------------------
      ghSetRegistry("isLoggedIn", "true")
      print ghLogHead();"ProcessData -- Check Region -- ";ghGetRegistry("region");" <> ";response
      if ghGetRegistry("region") <> response.region then
        content.addReplace("ReloadTranslations", true)
        ghSetRegistry("region", response.region)
      else
        content.addReplace("ReloadTranslations", false)
      end if
      ' user
      ' ----------------------------------------------------
      ghSetRegistry("user_id", response.user_id, "user")
      ghSetRegistry("parent_id", ghGetChild(response,"parent_id"), "user")
      ghSetRegistry("session_userhash", response.session_userhash, "user")
      ghSetRegistry("user_token", response.user_token, "user")
      ghSetRegistry("user_session", response.user_session, "user")
      ghSetRegistry("language", response.language, "user")
      ghSetRegistry("username", response.username, "user")
      ghSetRegistry("firstname", response.firstname, "user")
      ghSetRegistry("lastname", response.lastname, "user")
      ghSetRegistry("email", response.email, "user")
      ghSetRegistry("region", response.region, "user")
      ghSetRegistry("country_code", response.country_code, "user")
      ghSetRegistry("HKS", response.session_stringvalue)
      ' ----------------------------------------------------
      updateGlobalArray("lasttouch", ghGetChild(response, "lasttouch"))
      ' ----------------------------------------------------
      if m.top.debug then
        print "+++++++++++++++++++++++++++++++++++++++++"
        print response
        print "+++++++++++++++++++++++++++++++++++++++++"
        print ghListSectionData()
        print ghListSectionData("user")
        print "+++++++++++++++++++++++++++++++++++++++++"
      end if
      ' ----------------------------------------------------
      print "Register correcto"
      m.top.content = content
      return
    end if
  end if
end sub