# GHLoading

## Propiedades

| Nombre    | Tipo    | De<PERSON>ult    | Descripción                       |
| --------- | ------- | ---------- | --------------------------------- |
| width     | string  | 1280       | ancho del popup de carga.         |
| height    | string  | 720        | alto del popup de carga.          |
| backColor | string  | 0x000000DD | color de fondo del popup de carga |
| debug     | boolean | false      | muestra los logs del componente   |

## Ejemplo

Definición en el xml

```xml
<GHLoading id="loading" visible="false"/>
```

Cambio del valor de las propiedades

```basic
' loading
m.loading = m.top.findNode("loading")
```

Prendido y apagado a través de la función ghTutnLoading

```basic
' prendigo
ghTurnLoading(true, m.loading, m.botonera)
' apagado
ghTurnLoading(false, m.loading, m.botonera)
```
