## Screen.brs

[toc]

---

## ghSayAt

Genera un componente de tipo `<Label>` en la pantalla actual, con un id específico, con un texto específico y en la posición que se defina.



<u>Definición:</u>

```basic
function ghSayAt(id, x = 100, y = 100, msg = "***", align = "center") as object
```



<u>Parámetros:</u>

| Nombre | Tipo   | Default | Descripción                 |
| ------ | ------ | ------- | --------------------------- |
| id     | string |         | id del componente a generar |
| x      | int    | 100     | posición x del componente   |
| y      | int    | 100     | posición y del componente   |
| msg    | string | \*\*\*  | mensaje para el componente  |
| align  | string | center  | alineación del componente.  |





---

## ghSetBackground

Busca un componente existente, asume que es un `<Poster>` y le fija una url como origen de imágen.



<u>Definición:</u>

```basic
sub ghSetBackground(url, id = "fondo")
```



<u>Parámetros:</u>

| Nombre | Tipo   | Default | Descripción |
| ------ | ------ | ------- | ----------- |
| url    | string |         |             |
| id     | string |         |             |





---

