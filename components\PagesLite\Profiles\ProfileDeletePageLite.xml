<?xml version="1.0" encoding="utf-8" ?>

<component name="ProfileDeletePageLite" extends="Page">

  <script type="text/brightscript" uri="ProfileDeletePageLite.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />

  <interface>
    <field id="profile" type="assocarray" onChange="onProfile" />
    <field id="result" type="string" value="" alwaysNotify="true" />
  </interface>

  <children>
    <!-- Fondo y Textos -->
    <Poster id="fondo" translation="[0,0]" width="1280" height="720" />

    <Poster id="logo" />
    <Label id="title" />
    <Poster id="avatar" />
    <Label id="firstname"/>
    <Label id="message" width="1280"/>

    <!-- Botonera -->
    <GHButtonGroup id="botonera">
      <GHButton id="delete" />
      <GHButton id="cancel" />
    </GHButtonGroup>
    <!-- PopUps -->

    <GHLoading id="loading" visible="false"/>
  </children>

</component>
