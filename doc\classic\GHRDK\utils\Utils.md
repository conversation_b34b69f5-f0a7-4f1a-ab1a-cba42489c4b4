# Utils.brs

[toc]

## XYFunctions

### ghVal2Trans

Devuelve un string con la conformación de `translation` a partir de un `x` e `y` en formato `integer`. Convierte automáticamente a los píxeles abstractos de acuerdo a resolución.

<u>Definición:</u>

```basic
function ghVal2Trans(x, y)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
| x      | int  |         | posición x  |
| y      | int  |         | posición y  |

---

### ghXYtoAbstract

Convierte un `x` e `y` a un formato objeto pero en píxeles abstractos de acuerdo a la resolución

<u>Definición:</u>

```basic
function ghXYtoAbstract(x, y)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
| x      | int  |         | posición x  |
| y      | int  |         | posición y  |

---

### ghXtoAbstract

Convierte un `x` a un formato objeto pero en píxeles abstractos de acuerdo a la resolución.

<u>Definición:</u>

```basic
function ghXtoAbstract(x)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
| x      | int  |         | posición x  |

---

### ghYtoAbstract

Convierte un `y` a un formato objeto pero en píxeles abstractos de acuerdo a la resolución.

<u>Definición:</u>

```basic
function ghYtoAbstract(y)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
| y      | int  |         | posición y  |

---

### ghGetGlobalWH

Obtiene la información de ancho y alto del ROKU. Se utiliza para calcular las posiciones.

<u>Definición:</u>

```basic
function ghGetGlobalWH()
```

<u>Parámetros</u>

No tiene parámetros

---

### ghDumpTrans

Convierte un array de dos elementos (formato interno de `translate`) en un `string` con formato.

<u>Definición:</u>

```basic
function ghDumpTrans(trans) as string
```

<u>Parámetros</u>

| Nombre | Tipo        | Default | Descripción                               |
| ------ | ----------- | ------- | ----------------------------------------- |
| trans  | array [0,0] |         | array con formato interno de `translate`. |

---

## Date

### ghNow

Devuelve un `string` con la hora [incluídos milisegundos]. Se utiliza para los logs.

<u>Definición:</u>

```basic
function ghNow() as string
```

<u>Parámetros</u>

No tiene parámetros

---

## Logs

### ghLogHead

_lo que hace_

<u>Definición:</u>

```basic
function ghLogHead() as string
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghDumpEvent

_lo que hace_

<u>Definición:</u>

```basic
sub ghDumpEvent(event)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Errors

### ghErrorDump

_lo que hace_

<u>Definición:</u>

```basic
sub ghErrorDump(error)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghErrorGetJson

_lo que hace_

<u>Definición:</u>

```basic
function ghErrorGetJson(error) as object
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Focus

### ghFocusJumpTo

_lo que hace_

<u>Definición:</u>

```basic
sub ghFocusJumpTo(id)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Registry

### ghGetRegistry

_lo que hace_

<u>Definición:</u>

```basic
function ghGetRegistry(key as string, section = "Default" as string) as string
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghListSections

_lo que hace_

<u>Definición:</u>

```basic
function ghListSections() as dynamic
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghListSectionData

_lo que hace_

<u>Definición:</u>

```basic
function ghListSectionData(section = "Default") as dynamic
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghSetRegistry

_lo que hace_

<u>Definición:</u>

```basic
function ghSetRegistry(key as string, val as string, section = invalid as dynamic)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghDeleteRegistry

_lo que hace_

<u>Definición:</u>

```basic
function ghDeleteRegistry(key as string, section = invalid as dynamic)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghDeleteSectionRegistry

_lo que hace_

<u>Definición:</u>

```basic
function ghDeleteSectionRegistry(section = "Default")
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghDeleteALLRegistry

_lo que hace_

<u>Definición:</u>

```basic
function ghDeleteALLRegistry()
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Translations

### gh Translate

_lo que hace_

<u>Definición:</u>

```basic
function gh Translate(key as string, default as string, data = {} as object)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghReplaceStr

_lo que hace_

<u>Definición:</u>

```basic
function ghReplaceStr(origen, busco, reemplazo)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghGetAsset

_lo que hace_

<u>Definición:</u>

```basic
function ghGetAsset(key as string, default = "" as string)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Apis

### ghCallApi

_lo que hace_

<u>Definición:</u>

```basic
function ghCallApi(api as string, callback = invalid, callbackError = invalid, doRun = true as boolean) as object
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

### ghTurnLoading

_lo que hace_

<u>Definición:</u>

```basic
sub ghTurnLoading(status as boolean, loadingElement, currentElement)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---

## Otros

### Utils_forceSetFields

_lo que hace_

<u>Definición:</u>

```basic
sub Utils_forceSetFields(node as object, fieldsToSet as object)
```

<u>Parámetros</u>

| Nombre | Tipo | Default | Descripción |
| ------ | ---- | ------- | ----------- |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |
|        |      |         |             |

---
