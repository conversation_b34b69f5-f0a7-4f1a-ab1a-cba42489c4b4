sub Init()
  if m.top.debug then print ghLogHead();"Init"
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.progress = m.top.findNode("progress")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 217
  m.itemPoster.height = 320
  m.itemPoster.uri = ghGetChild(data, "image_medium", "pkg:/images/loading_vertical.png")

  drawProgress() ' antes que el titulo
  drawTitle(m.itemPoster.width, m.itemPoster.height)
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height)
  initTimer()
end sub
