<?xml version="1.0" encoding="utf-8" ?>

<component name="TerminosPage" extends="Page" initialFocus="texto">
  <script type="text/brightscript" uri="TerminosPage.brs" />

  <interface>
    <!-- interfaz de entrada alwaysNotify="true"    -->
    <field id="titleField" type="string" value="tyc_overhang_text" onChange="onTitleFieldChange" />
    <field id="textField" type="string" value="tyc_full_text_plano" onChange="onTextFieldChange" />
  </interface>

  <children>
    <Poster id="logo" translation="[500,30]" width="280" height="57" />
    <Overhang id="overhang" visible="false" showOptions="false" showClock="false"/>
      <Label id="title2" visible="false" width="1280" translation="[0,50]" vertAlign="center" horizAlign="center"/>
    <Poster id="backPage" translation="[0,613]" />
    <GHTextScroll id="texto"/>
    <Poster id="gradiente" visible="true" uri="pkg:/images/gradientMiniEpg.png" width="1280" translation="[0,540]" /> <!-- Ver si era algo de diseño para señalar si había más texto o si lo quieren de verdad, cualquier cosa comentarlo y listo    -->
  </children>

</component>
