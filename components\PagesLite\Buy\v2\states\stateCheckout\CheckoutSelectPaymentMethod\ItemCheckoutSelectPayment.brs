sub Init()
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.rating = m.top.findNode("rating")
  m.progress = m.top.findNode("progress")
end sub

sub itemContentChanged()
  gateway = ghGetChild(m.top.itemContent, "data.gateway")

  m.itemPoster.width = 480
  m.itemPoster.height = 285

  m.itemPoster.uri = ghGetAsset("select_payment_method_" + gateway, "invalid")

  if m.itemPoster.uri = "invalid" then
    m.itemPoster.uri = ghGetAsset("select_payment_method_default_placeholder", "pkg:/images/Placeholder_SelectorMDP-FHD.png") 'default
    drawTitle()
  end if
end sub

sub drawTitle()
  m.title.setFields({
    width: m.itemPoster.width - 20
    height: m.itemPoster.height - 20
    text: m.top.itemContent.data.gatewaytext
    font: ghGetFont(16, "bold")
    translation: [10, 10]
    color: "0xCCCCCC"
    wrap: "true"
    lineSpacing: "0"
    vertAlign: "bottom"
    visible: true
  })
end sub
