' GHLabel
'
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes
  m.mensaje = m.top.findNode("messageColor")
  m.border = m.top.findNode("border")
  m.border.uri = ghGetImageByMode("focus01.9.png")
  m.inDialog = false ' para el input
  m.dialog = m.top.GetScene().dialog
  m.input = m.top.findNode("input")
  m.input.focusable = true
  m.input.clearOnDownKey = false
  m.input.setFocus(true)
  recalcSizeAndPadding()
end function

'
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- OK!";
      InputDialog(m.input.text)
      m.top.GetScene().dialog.ObserveField("wasClosed", "termino")
      m.top.GetScene().dialog.ObserveField("buttonSelected", "resultado")
      handled = true
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent -- bubbling.. ";
    end if
    if m.top.debug then print " | "; m.top.id;" | " key;" | " press
  end if
  return handled
end function

sub InputDialog(texto = "" as string)
  ' result = ""
  dialog = CreateObject("roSGNode", "StandardPinPadDialog")
  dialog.pin = texto
  dialog.title = "         " + m.top.title
  dialog.message = ["" + m.top.message]
  ' dialog.backgroundColor = "0x00FF00FF"

  dialog.textEditBox.hintText = m.top.placeholder
  dialog.textEditBox.hintTextColor = "#7F8086"
  dialog.textEditBox.secureMode = m.top.password
  dialog.textEditBox.cursorPosition = Len(texto)
  dialog.textEditBox.maxTextLength = 6
  dialog.textEditBox.secureMode = m.top.password
  ' COLORES
  colors = CreateObject("roSGNode", "RSGPalette")
  colors.colors = {
    DialogBackgroundColor: "#121212",
    DialogTextColor: "0xFFFFFFFF",
    DialogFocusedTextColor: "0xFFFFFFFF",
    DialogSecondaryTextColor: "0xFFFFFFFF",
    DialogKeyboardColor: "#484848",
    DialogFocusItemColor: "0xFFFFFFFF",
    DialogFocusColor: "#981c15",
  }
  dialog.palette = colors
  dialog.getChild(2).getChild(0).text = ghTranslate("buypin_keyboard_description", "Recuerda que es de 4 a 6 dígitos.")

  if m.top.password then
    dialog.buttons = [ghTranslate("buypin_keyboard_button_next", "Continuar"), ghTranslate("buypin_keyboard_button_cancel", "Cancelar"), ghTranslate("buypin_keyboard_button_showpin", "Mostrar PIN")]
  else
    dialog.buttons = [ghTranslate("buypin_keyboard_button_next", "Continuar"), ghTranslate("buypin_keyboard_button_cancel", "Cancelar")]
  end if

  m.top.GetScene().dialog = dialog
end sub

function resultado() as boolean
  dialog = m.top.GetScene().dialog
  print
  print "----------------------------"
  print "RESULTADO -- Button [";dialog.buttonSelected;"] Text [";dialog.pin;"]"
  if dialog.buttonSelected = 0 then ' *** OK ***
    m.input.text = dialog.pin ' solo si dio OK
    dialog.close = true
  else if dialog.buttonSelected = 1 then ' *** Cancel ***
    dialog.close = true
  else if dialog.buttonSelected = 2 then ' *** mostrar/ocultar password **
    showHidePassword()
  end if
  print "----------------------------"
  return true
end function

function showHidePassword()
  dialog = m.top.GetScene().dialog
  if dialog.textEditBox.secureMode then
    m.input.secureMode = false
    dialog.textEditBox.secureMode = false
    dialog.buttons = [ghTranslate("buypin_keyboard_button_next", "Continuar"), ghTranslate("buypin_keyboard_button_cancel", "Cancelar"), ghTranslate("buypin_keyboard_button_hide_pin", "Ocultar PIN")]
  else
    m.input.secureMode = true
    dialog.textEditBox.secureMode = true
    dialog.buttons = [ghTranslate("buypin_keyboard_button_next", "Continuar"), ghTranslate("buypin_keyboard_button_cancel", "Cancelar"), ghTranslate("buypin_keyboard_button_showpin", "Mostrar PIN")]
  end if
  dialog.setFocus(true)
  return true
end function

function termino() as boolean
  dialog = m.top.GetScene().dialog
  print
  print "----------------------------"
  print "TERMINO -- Button ";dialog.buttonSelected
  ' m.top.GetScene().dialog = invalid ' mato el objeto, parece que no hace falta
  print "----------------------------"
  return true
end function

















' END FILE ------------------