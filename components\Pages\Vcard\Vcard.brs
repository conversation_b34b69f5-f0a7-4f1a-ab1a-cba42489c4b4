sub Init()
  m.top.debug = true

  ' para medir tiempo de ejecucion
  m.timerVcard = CreateObject("roTimespan")
  m.timerVcard.mark()

  ' para enviar al player si tengo que mostrar publicidad
  m.enableAds = false

  ' donde ir al tomar foco
  m.focusId = invalid
  if m.top.debug then print ghLogHead();"Init *** ---------------------"
  m.top.getScene().updateTheme = m.global.config.theme

  m.rowConfig = getConfigurationCards()

  ' general de pantalla
  m.map = {
    "menu": { "up": invalid, "right": invalid, "down": "botonera", "left": invalid }
    "botonera": { "up": "menu", "right": invalid, "down": "episodes", "left": invalid }
    "episodes": { "up": "botonera", "right": invalid, "down": "recommendations", "left": invalid },
    "recommendations": { "up": "episodes", "right": invalid, "down": "talents", "left": invalid },
    "talents": { "up": "recommendations", "right": invalid, "down": invalid, "left": invalid },
  }

  m.top.ObserveField("focus", "onFocus")

  m.top.ObserveField("buttonSelected", "handeButtonSelected")

  ' menu
  m.menu = m.top.findNode("menu")
  m.menu.ObserveField("backSelected", "OnExitToHome")
  m.menu.ObserveField("reloadHome", "reloadHomeMenu")

  ' activa el loading cuando se abre la vcard
  m.top.loading = true

  m.top.ObserveField("loading", "handleLoading")

  ' botonera
  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("value", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "OnExitToHome")

  ' spinner
  m.spinner = m.top.findNode("spinner")

  m.episodes = m.top.findNode("episodes")
  m.episodes.ObserveField("rowItemFocused", "episodeFocus")

  m.epiInfoSeason = m.top.findNode("epiInfoSeason")
  m.epiInfoEpisode = m.top.findNode("epiInfoEpisode")
  m.epiInfoEpisodeTitle = m.top.findNode("epiInfoEpisodeTitle")

  m.epiInfoYearEpisode = m.top.findNode("epiInfoYearEpisode")
  m.epiInfoBar = m.top.findNode("epiInfoBar")
  m.epiInfoBar2 = m.top.findNode("epiInfoBar2")
  m.epiInfoCategories = m.top.findNode("epiInfoCategories")
  m.epiInfoYearSeason = m.top.findNode("epiInfoYearSeason")
  m.epiInfoRating = m.top.findNode("epiInfoRating")

  m.epiDesc = m.top.findNode("epiDesc")

  m.top.ObserveField("seasonSelected", "getEpisodesBookmark")

  ' texts
  m.fondo = m.top.findNode("fondo")
  m.title = m.top.findNode("title")
  m.desc = m.top.findNode("desc")
  ' info-bars
  m.top.findNode("infoBar1").font = ghGetFont(16, "regular")
  m.top.findNode("infoBar2").font = ghGetFont(16, "regular")
  m.top.findNode("infoBar3").font = ghGetFont(16, "regular")
  ' info
  m.infoTitle = m.top.findNode("infoTitle")
  m.infoGenres = m.top.findNode("infoGenres")
  m.infoYear = m.top.findNode("infoYear")
  m.infoRating = m.top.findNode("infoRating")
  m.infoDuration = m.top.findNode("infoDuration")
  ' series
  m.infoSeason = m.top.findNode("infoSeason")
  m.infoEpisode = m.top.findNode("infoEpisode")
  m.infoEpisodeTitle = m.top.findNode("infoEpisodeTitle")
  ' descripcion
  m.desc = m.top.findNode("desc")
  ' progress
  m.seen = m.top.findNode("seen")

  ' rectangulo de debug  [1420, 210]
  ' if m.top.debug then
  '   m.top.findNode("rec").setFields({
  '     translation: [-140, 780]
  '     width: 1420
  '     height: 210
  '     visible: true
  '   })
  ' end if
end sub

sub reloadHomeMenu()
  m.logger.debug("reloadHomeMenu")

  ' pasamanos para que hago refresh de la home
  m.top.reloadHome = true
end sub

sub getEpisodesBookmark()
  ids = ""

  for each t in m.top.seasons
    season = m.top.seasons[t.toStr()]
    episodes = ghGetChild(season, "episodes", [])

    for i = 0 to episodes.count() - 1
      infoCard = ghGetChild(episodes, "#" + i.toStr())

      if ids = "" then
        ids = infoCard.id.Tostr()
      else
        ids = ids.Tostr() + "," + infoCard.id.Tostr()
      end if
    end for
  end for

  apiBookmark = ghCallApi("Bookmark", "handleBookmarkEpisodes", "handleErrorNothing", false)
  apiBookmark.group_id = ids
  apiBookmark.control = "run"
end sub
sub handleBookmarkEpisodes(event)
  data = event.getData()
  info = ghGetChild(data, "groups", [])

  changeEpisodeVistime(info)
end sub
sub changeEpisodeVistime(episodesVistime)
  if m.top.seasonSelected > 0 and episodesVistime.count() > 0 then

    seasons = m.top.seasons

    for each t in seasons
      season = seasons[t.toStr()]

      episodeArray = []
      for each episode in season.episodes

        for each episodeVistime in episodesVistime
          if ghGetChild(episodeVistime, "id", "") = episode.id then
            episode.vistime = episodeVistime.vistime
          end if
        end for

        episodeArray.Push(episode)
      end for
      seasons[t.Tostr()].episodes = episodeArray
    end for

    m.top.seasons = seasons
  end if
end sub
sub episodeFocus(event)
  data = event.getData()

  itemIndex = data[1]

  item = m.episodes.content.getChild(0).getChild(itemIndex)

  m.epiInfoSeason.setFields({
    font: ghGetFont(16, "regular")
    text: ghTranslate("vcard_access_abbreviationSeason_label", "Temporada") + " " + ghGetChild(item, "data.season_number", "")
  })
  m.epiInfoEpisode.setFields({
    font: ghGetFont(16, "regular")
    text: ghTranslate("vcard_access_abbreviationEpisode_label", "Episodio") + " " + ghGetChild(item, "data.episode_number", "")
  })
  m.epiInfoEpisodeTitle.setFields({
    font: ghGetFont(16, "bold")
    text: ghGetChild(item, "data.title", "")
  })
  m.epiDesc.setFields({
    font: ghGetFont(18, "regular")
    text: ghGetChild(item, "data.description", "")
    ' translation: [58, 810]
    width: 500
    height: 140
    wrap: true
    ellipsizeOnBoundary: true
  })

  m.epiInfoYearEpisode.setFields({
    font: ghGetFont(16, "bold")
    text: ghGetChild(item, "data.year", "")
  })
  m.epiInfoBar.setFields({
    font: ghGetFont(16, "regular")
  })
  m.epiInfoBar2.setFields({
    font: ghGetFont(16, "regular")
  })
  m.epiInfoCategories.setFields({
    font: ghGetFont(16, "regular")
    text: ghConvertToStringAndJoin(ghGetChild(m.top.contenido, "categories", []), ", ", 2)
  })
  m.epiInfoYearSeason.setFields({
    font: ghGetFont(16, "bold")
    text: ghGetChild(m.top.contenido, "year", "")
  })
  m.epiInfoRating.setFields({
    font: ghGetFont(16, "regular")
    backMap: ghGetImageByMode("2px_back.9.png")
    color: "0xFFFFFFFF"
    'text: ghGetRatingLabel(ghGetChild(m.top.contenido, "rating", "+ 18 Años"))
    text: ghGetRatingLabelV2(ghGetChild(m.top.contenido, "ratingcode", "50")) ' 50 = +18
    horizPadding: 10
    vertPadding: 6
  })
  m.top.findNode("groupInfoEpisode").visible = true
end sub

sub onWasShown() ' event
  if m.top.debug then print ghLogHead();"onWasShown *** "
  m.top.routerAddRouterTo = m.menu ' habilito al menu a usar router

  if m.top.loading <> true then
    if m.focusId <> invalid then
      turnFocusTo(m.focusId)
      m.focusId = invalid
    else
      turnFocusTo("botonera")
    end if
  end if
end sub
' manejo de foco
' ---------------------
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = false
  if press then
    if key <> "back" then
      turnFocusTo(guessFocusTo(key))
      handled = true
      if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";ghGetChild(m.top, "focusedChild.id", "");"]"
    end if
  end if
  return handled
end function

function guessFocusTo(direction, current = invalid) as string
  if current = invalid then current = getCurrentFocus()
  if m.top.debug then print ghLogHead();"guessFocusTo *** >>> ";current;" -- ";direction
  ' a donde voy?
  if m.map[current] <> invalid and m.map[current][direction] <> invalid then
    focusTo = m.map[current][direction]
    ' OH! hay cintas en esta pantalla que pueden estar escondidas !!!
    if m.top.findNode(focusTo).visible = false then
      newFocusTo = guessFocusTo(direction, focusTo) ' reentrante para seguir de largo
      if newFocusTo = focusTo then ' si me quedo en el mismo lugar
        focusTo = current ' ese lugar es el actual
      else
        focusTo = newFocusTo ' sino avanzo
      end if
    end if
  else
    focusTo = current
  end if
  if m.top.debug then print ghLogHead();"guessFocusTo *** <<< ";focusTo
  return focusTo
end function

sub turnFocusTo(id)
  if m.top.debug then print ghLogHead();"turnFocusTo *** ---------------------"
  current = getCurrentFocus()
  ' if current <> id then
  if current <> invalid then
    m.top.findNode(current).focus = false ' apago el actual
  end if
  if m.top.findNode(id).focus <> invalid then
    if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id

    episode = m.top.findNode("episodes").visible

    if id = "episodes" then
      m.top.translation = [0, -330]
    else
      if id = "botonera" then
        m.top.translation = [0, 0]
      else if id = "recommendations" then
        if episode = true then
          ' m.top.translation = [0, -330]
          m.top.translation = [0, -800]
        else
          m.top.translation = [0, -100]
        end if
      else if id = "talents" then
        if episode = true then
          ' m.top.translation = [0, -580]
          m.top.translation = [0, -800]
        else
          m.top.translation = [0, -350]
        end if
      end if
    end if

    item = m.top.findNode(id)
    item.focus = true
  end if
end sub

function getCurrentFocus()
  if m.top.debug then print ghLogHead();"getCurrentFocus *** ---------------------"
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
  return current
end function

sub OnExitToHome(event)
  data = event.getData()
  if data then
    if m.top.debug then print ghLogHead();"OnExitToHome *** ---------------------"

    ' para volver a la misma pantalla de donde llego a la vcard
    m.top.routerClose = true
    ' m.top.close = true
  end if
end sub

' -----------------------------
sub handleLoading(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"handleLoading *** data=";data

  if data = true then
    ' volver scroll a 0, para que se muestre bien el loading
    m.top.translation = [0, 0]

    m.spinner.visible = true
  else
    print "Tiempo de carga Vcard miliseconds: " + m.timerVcard.TotalMilliseconds().ToStr()
    m.spinner.visible = false
    setFavorited(m.top.favorite)

    ' solo enviar la primera ves que se carga la vcard
    if m.analytic_enviado = false then
      m.analytic_enviado = true

      sendFullAnalytic(0, "by subscription")
      sendSelectContentAnalytic()
    end if

    if m.focusId <> invalid then
      turnFocusTo(m.focusId)
      m.focusId = invalid
    else
      turnFocusTo("botonera")
    end if

  end if
end sub

sub sendFullAnalytic(price = 0.00, availability = "by subscription")
  data = m.top.contenido
  contentType = "movie"
  episode = "not apply"

  if ghGetChild(data, "serie_id") <> invalid then
    contentType = "series"
    episode = "season " + ghGetChild(data, "season", "") + " episode " + ghGetChild(data, "episodenumber", "") + " " + ghGetChild(data, "titleepisode", "")
  end if

  ' enviando a google analytics
  sell_type = ghGetChild(m, "datacontent.group.common.extendedcommon.format.sell_type")
  format_type = ghGetChild(m, "datacontent.group.common.extendedcommon.format.types")
  if sell_type = "free"
    availability = "free"
  else if sell_type = "ppe"
    availability = "rental"
  else if sell_type = "est"
    availability = "buy"
  end if

  GA4Event("content_detail", {
    content_id: m.id,
    content_name: ghGetChild(data, "title", "")
    content_type: contentType
    content_category: ghConvertToStringAndJoin(ghGetChild(data, "categories", []), ", ")

    content_episode: episode
    content_brand: m.proveedor_code
    content_availability: availability
    content_price: price

    content_list: "vcard"

    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "content detail"
    screen_class: "/content-detail"
  })
end sub

sub sendSelectContentAnalytic()

  data = m.top.contenido
  contentType = "movie"
  if ghGetChild(data, "is_series", false) = true or ghGetChild(data, "episodenumber", "") <> "" or ghGetChild(data, "season", "") <> "" then
    contentType = "series"
  end if
  ' ga4 event config
  contentId = data.group_id
  if contentId = invalid and data.groupId <> invalid
    contentId = data.groupId
  else if contentId = invalid and data.id <> invalid
    contentId = data.id
  end if

  content_list = "no apply"

  if ghGetChild(m.top.contenido, "season", "") <> "" then
    content_list = "Temporada " + ghGetChild(m.top.contenido, "season", "")
  end if

  episode = "not apply"

  if ghGetChild(data, "serie_id") <> invalid then
    contentType = "series"
    episode = "season " + ghGetChild(data, "season", "") + " episode " + ghGetChild(data, "episodenumber", "") + " " + ghGetChild(data, "titleepisode", "")
  end if

  eventBody = {
    content_id: contentId,
    content_name: ghGetChild(data, "title", "")
    content_type: contentType
    content_category: ghConvertToStringAndJoin(ghGetChild(data, "categories", []), ", ")
    content_availability: "by subscription"
    content_list: content_list
    content_episode: episode
    content_section: "vcard"
    user_type: getUserTypeGA4(true)
    country: ghGetRegistry("country_code", "user"),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "content selection"
    screen_class: "/content-selection"
  }

  GA4Event("select_content", eventBody)
end sub

sub sendActionToAnalytic(interaction_type = "reproduce")
  data = m.top.contenido
  contentType = "movie"

  if ghGetChild(data, "serie_id") <> invalid then
    contentType = "series"
  end if

  event_name = "interaction_content"

  ' enviando a google analytics
  GA4Event(event_name, {
    content_section: "vcard",
    interaction_type: interaction_type,
    content_id: m.id,
    content_name: ghGetChild(data, "title", ""),
    content_type: contentType,
    content_category: ghConvertToStringAndJoin(ghGetChild(data, "categories", []), ", "),
    content_availability: "by subscription",
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "content detail",
    screen_class: "/content-detail"
  })
end sub

function addButton(id, text, price, value, icon, backColor, selBackColor)
  if m.top.debug then print ghLogHead();"addButton >> ";id;":";text;":";value;":";icon;":";backColor;":";selBackColor
  ' la parte basica
  fields = {
    id: id
    value: value
    text: text
    backColor: backColor
    selBackColor: selBackColor
    translation: [m.buttonInitialPosition, 400]
    visible: true
  }
  if icon <> invalid then
    fields.icon = icon
  end if
  ' tipo de boton, si es de compra, va modo texto
  if Left(id, 3) = "buy" then
    fields.mode = "text"
    fields.buttonText = price
  end if

  buttons = []
  btnsContent = CreateObject("roSGNode", "GHButtonDesc")
  btnsContent.setFields(fields)
  buttons.push(btnsContent)
  m.botonera.Update({ children: buttons }, true)

  m.buttonInitialPosition = m.buttonInitialPosition + 100
end function

function handleButtons(data)
  if m.top.debug then print ghLogHead();"handleButtons *** ---------------------"
  ' data = event.getData()
  ' borro los botones que ya existen
  cant = m.botonera.getChildCount()
  m.botonera.removeChildrenIndex(cant, 0)

  ' si se redibuja los botones vuelvo initialPosition al valor inicial
  m.buttonInitialPosition = 50
  ' cant = data.getChildCount()
  cant = data.Count()
  for i = 0 to cant - 1
    ' r = data.getChild(i)
    r = ghGetChild(data, "#" + i.toStr())
    addButton(r.id, r.title, r.price, r.id, r.hdlistitemiconurl, r.backColor, r.selBackColor)
  end for
  ' poner el foco en el primer elemento
  ' r = data.getChild(0)
  r = ghGetChild(data, "#0")
  if r <> invalid and r.id <> invalid
    m.botonera.foco = r.id
  end if
  turnFocusTo("botonera")
end function

sub OnButtonSelected(event)
  if m.top.debug then print ghLogHead();"OnButtonSelected *** ---------------------"
  child = event.getRoSGNode()

  ' puedo hacer click, cuando no estoy cargando
  if m.top.loading = false and child.value <> "" then
    m.top.buttonSelected = child.value
  end if
end sub
' REDIBUJO
' -----------------------------
sub RefreshGlobal()
  if m.top.debug then print ghLogHead();"RefreshGlobal *** ---------------------"
  data = m.top.contenido

  fondo = ghGetChild(data, "fondo", "pkg:/images/mkh_back.jpg")
  m.fondo.setFields({
    uri: fondo
    loadDisplayMode: "noScale"
    'loadingBitmapUri: "pkg:/images/loading.png"
    loadingBitmapOpacity: "0.8"
  })

  m.title.setFields({
    translation: [58, 117.33]
    font: ghGetFont(50, "regular")
    width: 696
    horizAlign: "left"
    text: ghGetChild(data, "title", "")
  })

  m.infoTitle.setFields({
    font: ghGetFont(16, "bold")
    text: ghGetChild(data, "title", "")
  })

  m.infoGenres.setFields({
    font: ghGetFont(16, "regular")
    text: ghConvertToStringAndJoin(ghGetChild(data, "categories", []), ", ", 2)
  })

  m.infoYear.setFields({
    font: ghGetFont(16, "regular")
    text: ghGetChild(data, "year", "")
  })

  m.infoRating.setFields({
    font: ghGetFont(16, "regular")
    backMap: ghGetImageByMode("2px_back.9.png")
    color: "0xFFFFFFFF"
    'text: ghGetRatingLabel(ghGetChild(data, "rating", "+ 18 Años"))
    text: ghGetRatingLabelV2(ghGetChild(m.top.contenido, "ratingcode", "50")) ' 50 = +18
    horizPadding: 10
    vertPadding: 6
  })

  m.infoDuration.setFields({
    font: ghGetFont(16, "bold")
    text: ghFormatDuration(ghGetChild(data, "duration", invalid))
  })

  m.desc.setFields({
    font: ghGetFont(18, "regular")
    text: ghGetChild(data, "description", "")
    translation: [58, 261]
    width: 700
    height: 100
    wrap: true
    ellipsizeOnBoundary: true
  })

  ' progress
  max = ghGetChild(m.top, "vistime.duration.seconds", -1)
  curr = ghGetChild(m.top, "vistime.last.seconds", 0)
  if max <> invalid and curr <> invalid and max > 0 and curr > 0 then
    porc = curr / max * 100
    m.seen.setFields({
      width: 600
      translation: [58, 360]
      value: porc
      visible: true
    })
  else
    m.seen.visible = false
  end if

  ' series
  if ghGetChild(data, "serie_id") <> invalid then
    ' cargo carousel de episodios
    if m.top.seasons <> invalid then
      ' seasonNumber = ghGetChild(data, "season").toInt() - 1
      seasonNumber = ghGetChild(data, "season", "").toStr().toInt()
      ' season = ghGetChild(m.top.seasons, "#" + seasonNumber.toStr())
      season = m.top.seasons[seasonNumber.toStr()]
      m.top.seasonSelected = ghGetChild(data, "season", "").toStr().toInt()

      cargarEpisodes(ghGetChild(season, "episodes", []), seasonNumber)
    end if

    m.infoSeason.setFields({
      font: ghGetFont(16, "regular")
      text: ghTranslate("Temporada", "Temporada") + " " + ghGetChild(data, "season", "").toStr()
    })
    m.infoEpisode.setFields({
      font: ghGetFont(16, "regular")
      text: ghTranslate("vcard_access_abbreviationEpisode_label", "Episodio") + " " + ghGetChild(data, "episodenumber", "").toStr()
    })
    m.infoEpisodeTitle.setFields({
      font: ghGetFont(16, "bold")
      text: ghGetChild(data, "titleEpisode", "")
    })

    m.top.findNode("infoSerie").visible = true
  else
    m.top.findNode("infoSerie").visible = false
    'm.desc.translation="[58,221]"  REVISAR CON DISEÑO
  end if
end sub

sub mapFoco()
  carouseles = ["episodes", "recommendations", "talents"]

  cantidad = carouseles.count()
  for i = 0 to cantidad - 1
    carousel = m.top.findNode(ghGetChild(carouseles, "#" + i.toStr()))
    if carousel.content <> invalid and carousel.content.getChildCount() > 0 then
      cinta = carousel.content.getChild(0)
      items = cinta.getChildCount()

      if items > 0 then
        carousel.visible = true
      end if
    end if
  end for

  carEpisodes = m.top.findNode("episodes")
  carRecommendations = m.top.findNode("recommendations")
  carTalents = m.top.findNode("talents")

  carEpisodes.UnobserveField("value")
  carRecommendations.UnobserveField("value")
  carTalents.UnobserveField("value")

  carTalents.ObserveField("value", "OnTalentSelect")
  carEpisodes.ObserveField("value", "OnEpisodeSelect")
  carRecommendations.ObserveField("value", "OnCardSelect")

  ' reset si ya tenia seteado otra ubicacion
  carRecommendations.setFields({
    translation: [0, 550]
  })

  if carEpisodes.visible = true then
    carRecommendations.setFields({
      ' translation: [-140, 780]
      translation: [0, 1000]
    })
    carTalents.setFields({
      ' translation: [-140, 780]
      translation: [0, 1000]
    })

    if carRecommendations.visible = true then
      carTalents.setFields({
        ' translation: [-140, 1010]
        translation: [0, 1230]
      })
    end if
  else
    if carRecommendations.visible = true then
      carTalents.setFields({
        translation: [0, 780]
      })
    end if
  end if
end sub

sub chargeCarousel(nameCarousel, cintas, jumpToItem = 0)
  if m.top.debug then print ghLogHead();"chargeCarousel ***"
  carousel = m.top.findNode(nameCarousel)

  cantItems = cintas.getChild(0).getChildCount()
  if cantItems > 0 then
    carousel.itemComponentName = "ItemPolymorphic"
    carousel.itemSize = [1280, 210]
    carousel.itemSpacing = [0, 0]
    carousel.rowLabelOffset = [50, 15]
    carousel.showRowLabel = [true]
    carousel.rowFocusAnimationStyle = "fixedFocusWrap"
    carousel.rowCounterRightOffset = 0
    carousel.focusBitmapUri = ghGetImageByMode("4px_Focus.9.png")
    carousel.visible = true
    carousel.numRows = 1

    refreshTypesGrid(cintas, carousel)
    carousel.content = cintas
    carousel.jumpToRowItem = [0, jumpToItem]

    mapFoco()
  end if
end sub

sub chargeRecommentations(data = [])
  if m.top.debug then print ghLogHead();"chargeRecommentations ***"
  result = CreateObject("roSGNode", "GHContent")
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.id = "Peliculas_recomendadas"
  obCinta.title = ghTranslate("vcard_ribbon_recommendations_label", "Otras personas que vieron este contenido, también vieron:")
  obCinta.visible = true
  obCinta.type = "Carrouselhorizontal"

  cantidad = data.count()
  for i = 0 to cantidad - 1
    item = CreateObject("roSGNode", "GHContent")
    infoCard = ghGetChild(data, "#" + i.toStr())

    isSerie = ghGetChild(infocard, "is_series", false)
    ContentType = 1
    if isSerie = true then
      ContentType = 2
    end if

    infoCard.rowType = "Carrouselhorizontal"
    infoCard.ContentType = ContentType

    ghUtils_ForceSetFields(item, {
      data: infoCard
    })
    obCinta.appendChild(item)
  end for

  result.appendChild(obCinta)

  chargeCarousel("recommendations", result)
end sub

function getTalents(data)
  if data = invalid then
    return true
  end if

  if m.top.debug then print ghLogHead();"getTalents *** ---------------------"
  talents = ghGetChild(data, "external.gracenote.cast", invalid)
  if talents = invalid then
    talents = ghGetChild(data, "common.extendedcommon.roles.role", invalid)
  end if

  result = CreateObject("roSGNode", "GHContent")
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.id = "Talents"
  obCinta.title = ghTranslate("vcard_ribbon_talents_label", "Talentos") 'falta key de apa
  obCinta.visible = true
  obCinta.type = "talents"

  if talents = invalid or talents.count() = 0 then
    return true
  end if

  cant = talents.count()
  for i = 0 to cant - 1
    role = ghGetChild(talents, "#" + i.toStr())
    if ghGetChild(role, "name", "") = "Actor" or ghGetChild(role, "role_id", "") = "1" or ghGetChild(role, "role_id", "") = "16" then
      actors = ghGetChild(role, "talents")
      if Type(actors) <> "roArray" then
        actors = ghGetChild(role, "talents.talent")
      end if
      cant02 = actors.count()

      for x = 0 to cant02 - 1
        item = CreateObject("roSGNode", "GHContent")
        infoCard = ghGetChild(actors, "#" + x.toStr())

        rolName = ghGetChild(role, "role_name")
        if rolName = invalid then
          rolName = ghGetChild(role, "name")
        end if

        infoCard.rowType = "talent"
        infoCard.ContentType = 1
        infoCard.rolName = rolName
        infoCard.cantTotal = cant02

        ghUtils_ForceSetFields(item, {
          data: infoCard,
        })
        obCinta.appendChild(item)
      end for
    end if
  end for

  ' recalculo la cantidad total y se la asigno al data de cada objeto
  totalCantX = obCinta.getChildCount()
  for i = 0 to totalCantX - 1
    c = obCinta.getChild(i)
    if c <> invalid then
      data = c.data
      if data <> invalid then
        data.cantTotal = totalCantX
        c.setField("data", data)
      end if
    end if
  end for

  result.appendChild(obCinta)
  chargeCarousel("talents", result)
end function

sub CargarEpisodes(data = [], temporada = "1")
  if m.top.debug then print ghLogHead();"CargarEpisodes *** ---------------------"
  result = CreateObject("roSGNode", "GHContent")
  obCinta = CreateObject("roSGNode", "GHContent")
  obCinta.id = "Episodios"
  obCinta.visible = true
  obCinta.type = "episodes"

  jumpToItem = 0
  cantidad = data.count()
  for i = 0 to cantidad - 1
    item = CreateObject("roSGNode", "GHContent")

    infoCard = ghGetChild(data, "#" + i.toStr())
    infoCard.rowType = "episode"
    ghUtils_ForceSetFields(item, {
      data: infoCard
    })
    obCinta.appendChild(item)

    if ghGetChild(item, "data.episode_number", 0) = ghGetChild(m.top.contenido, "episodenumber", 0) then
      jumpToItem = i
    end if
  end for

  ' informacion especial para el titulo
  obCinta.title = FormatJson({
    "title": ghTranslate("vcard_access_abbreviationSeason_label", "Temporada"),
    "temporada": temporada.toStr() + "  ",
    "cantidad": cantidad
  })

  result.appendChild(obCinta)

  chargeCarousel("episodes", result, jumpToItem)
end sub

sub OnTalentSelect(event)
  data = event.getData()
  child = event.getRoSGNode()

  ' guardo el id, para poner foco cuando vuelvo a esta pantalla
  m.focusId = ghGetChild(child, "id", "")
  child.focus = false

  talent = CreateObject("roSGNode", "Talents")
  talent.ObserveField("reloadHome", "handleReload")

  info = ghGetChild(data, "data.data")

  m.top.routerChild = {
    page: talent,
    fields: {
      data: info
    }
  }
end sub

sub OnCardSelect(event)
  child = event.getRoSGNode()
  ' guardo el id, para poner foco cuando vuelvo a esta pantalla
  m.focusId = ghGetChild(child, "id", "")
  child.focus = false

  if m.top.debug then print ghLogHead();"OnCardSelect *** ---------------------"
  data = event.getData()

  if data <> invalid
    group_id = ghGetChild(data, "data.data.group_id", invalid)
    if group_id = invalid then
      group_id = ghGetChild(data, "data.data.id", invalid)
    end if

    vcard = CreateObject("roSGNode", "Vcard")
    vcard.id = "Vcard2"
    vcard.id = "Vcard-" + group_id.Tostr()
    vcard.ObserveField("reloadHome", "handleReload")

    contenido = ghGetChild(data, "data.data")
    contenido.groupId = group_id

    ' enviando a analytics
    data = contenido

    contentType = "movie"
    episode = "not apply"

    if ghGetChild(data, "is_series", false) = true or ghGetChild(data, "episode_number", "") <> "" or ghGetChild(data, "season_number", "") <> "" then
      contentType = "series"
      episode = "season " + ghGetChild(data, "season_number", "") + " episode " + ghGetChild(data, "episode_number", "") + " " + ghGetChild(data, "title_episode", "")
    end if
    ' ga4 event config
    contentId = data.group_id
    if contentId = invalid and data.groupId <> invalid
      contentId = data.groupId
    else if contentId = invalid and data.id <> invalid
      contentId = data.id
    end if
    order = ghGetChild(child, "currFocusColumn")

    eventBody = {
      content_id: contentId,
      content_name: ghGetChild(data, "title", "")
      content_type: contentType
      content_category: "not apply"
      content_availability: "by subscription"
      content_position: order
      content_episode: episode

      content_list: ghGetChild(child, "value.carousetitle")
      content_section: "vcard"

      user_type: getUserTypeGA4(true)
      country: ghGetRegistry("country_code", "user"),
      user_id: ghGetRegistry("user_id", "user"),
      screen_name: "content selection"
      screen_class: "/content-selection"
    }

    GA4Event("select_content", eventBody)

    m.top.routerChild = {
      page: vcard,
      fields: {
        data: contenido
      }
    }
  end if
end sub
sub OnEpisodeSelect(event)
  child = event.getRoSGNode()
  ' foco a los botones
  m.focusId = invalid
  child.focus = false

  if m.top.debug then print ghLogHead();"OnEpisodeSelect *** ---------------------"
  data = event.getData()

  if data <> invalid
    group_id = ghGetChild(data, "data.data.group_id", invalid)
    if group_id = invalid then
      group_id = ghGetChild(data, "data.data.id", invalid)
    end if

    data = ghGetChild(data, "data.data")

    contentType = "movie"
    if ghGetChild(data, "is_series", false) = true or ghGetChild(data, "episodenumber", "") <> "" or ghGetChild(data, "season", "") <> "" then
      contentType = "series"
    end if

    order = ghGetChild(child, "currFocusColumn")
    episode = "not apply"
    if ghGetChild(data, "is_series") = true then
      episode = "season " + ghGetChild(data, "season_number", "") + " episode " + ghGetChild(data, "episode_number", "") + " " + ghGetChild(data, "title", "")
    end if

    GA4Event("select_content", {
      content_id: data.groupId,
      content_name: ghGetChild(data, "title", "")
      content_type: contentType
      content_category: "not apply"
      content_availability: "by subscription"
      content_position: order
      content_episode: episode

      content_list: "Temporada " + ghGetChild(m.top.contenido, "season", "")
      content_section: "vcard"

      user_type: getUserTypeGA4(true),
      country: ghGetRegistry("country_code", "user"),
      user_id: ghGetRegistry("user_id", "user"),
      screen_name: "content selection"
      screen_class: "/content-selection"
    })

    getContent(group_id, false)
  end if
end sub
sub setFavorited(value)
  if m.top.debug then print ghLogHead();"setFavorited *** ---------------------"
  ' cuando viene apenas se abre la vcard
  ' despues del loading, tiene que venir aca despues de cargar los botones

  ' seteo cuando viene de un cambio en el boton
  m.top.favorite = value

  button = invalid
  ' position = invalid

  cant = m.botonera.getChildCount()
  for i = 0 to cant - 1
    item = m.botonera.getChild(i)
    if item.id = "favorite" then
      button = item
      ' position = i
    end if
  end for

  if button <> invalid then
    if value = true then
      button.text = ghReplaceStr(ghTranslate("vcard_access_option_button_deleteFavoritiesList", "Quitar de" + chr(10) + "Mi lista"), "{br}", chr(10))
      button.icon = ghGetAssetByMode("milista_remove_icon")

      m.botonera.replaceChild(button, i)
    else
      button.text = ghReplaceStr(ghTranslate("vcard_access_option_button_addFavoritiesList", "Agregar a" + chr(10) + "Mi lista"), "{br}", chr(10))
      button.icon = ghGetAssetByMode("milista_add_icon")

      m.botonera.replaceChild(button, i)
    end if
  end if
end sub
sub onDeepLink(event)
  print "|ddddddddddddddddddddddddddddddddddddddddddddddd"
  print "|ddddddddddddddddddddddddddddddddddddddddddddddd"
  ghDumpEvent(event)
  print "|ddddddddddddddddddddddddddddddddddddddddddddddd"
  print "|ddddddddddddddddddddddddddddddddddddddddddddddd"
end sub
' END OF FILE