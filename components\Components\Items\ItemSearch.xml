<?xml version="1.0" encoding="utf-8" ?>

<component name="ItemSearch" extends="Group">
  <script type="text/brightscript" uri="ItemSearch.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="drawUtils.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="width" type="float" alias="itemPoster.width" />
    <field id="height" type="float" alias="itemPoster.height" />
    <field id="itemContent" type="node" onChange="itemContentChanged"/>
    <!-- interfaz interna -->
    <field id="debug" type="boolean" value="false"/>
  </interface>

  <children>
    <Poster id="itemPoster"/>
    <Label id="title" visible="true" translation="[10,10]" width="150" color="0xCCCCCC" wrap="true" height="90" lineSpacing="0" vertAlign="bottom" />

    <GHPanel id="thePanels"/>
  </children>

</component>