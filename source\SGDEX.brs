' Copyright (c) 2018 Roku, Inc. All rights reserved.

sub RunUserInterface(args)
  ' print "RunUserInterface !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  ' print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  ' print args
  ' print "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  ' print
  ' print
  m.args = args
  if Type(GetSceneName) <> "<uninitialized>" and GetSceneName <> invalid and GetInterface(GetSceneName, "ifFunction") <> invalid then
    StartSGDEXChannel(GetSceneName(), args)
  else
    ? "Error: SGDEX, please implement 'GetSceneName() as String' function and return name of your scene that is extended from BaseScene"
  end if
end sub

sub StartSGDEXChannel(componentName, args)
  screen = CreateObject("roSGScreen")
  m.port = CreateObject("roMessagePort")
  screen.SetMessagePort(m.port)
  '
  ' CONFIG (!)
  ' -----------------------------------------
  app = CreateObject("roAppInfo")
  environment = app.GetValue("environment")
  m.global = screen.getGlobalNode()

  m.global.AddField("trailerSet", "string", false)
  m.global.trailerSet = "trailerTempo"
  ' m.global.AddField("WidgetManager", "node", false) ' nunca se uso
  m.global.AddField("config", "assocarray", false)
  m.global.config = loadJsonFile("pkg:/source/config/config." + environment + ".json")
  m.global.AddField("translations", "assocarray", false)
  m.global.AddField("assets", "assocarray", false)
  m.global.AddField("nav", "assocarray", false)
  m.global.AddField("nodes", "assocarray", false)
  m.global.AddField("navSelect", "string", false)
  m.global.AddField("search", "assocarray", false)
  m.global.AddField("filter_list", "assocarray", false)
  m.global.AddField("providers", "assocarray", false)
  m.global.AddField("tracker", "assocarray", false)
  m.global.AddField("user", "assocarray", false)
  m.global.setField("tracker", { "name": "tracker" })
  m.global.AddField("youbora", "assocarray", false)
  m.global.AddField("language", "assocarray", false)
  m.global.AddField("channels", "assocarray", false)
  m.global.AddField("channelProvider", "assocarray", false)
  m.global.AddField("favorites", "assocarray", false)
  m.global.AddField("epg", "assocarray", false)
  m.global.AddField("parental", "assocarray", false)
  m.global.AddField("status_pin", "assocarray", false)
  m.global.AddField("tvConfig", "assocarray", false)
  m.global.AddField("ga4", "assocarray", false)
  m.global.AddField("lastTouch", "assocarray", false)
  m.global.AddField("superhighlight", "array", false)
  ' CVLite
  m.global.AddField("model", "string", false)
  m.global.addField("cv_advertising_config", "assocarray", false)
  m.global.addField("user_experience", "assocarray", false)
  m.global.AddField("hombreMuerto", "assocarray", false)
  m.global.AddField("profiles_config", "assocarray", false)
  m.global.AddField("profiles_current", "assocarray", false)
  m.global.AddField("alert_autoHideTime", "assocarray", false)
  m.global.AddField("alert_rokupay_subscription", "assocarray", false)
  m.global.AddField("hidden_confirm_trans_config", "assocarray", false)
  m.global.AddField("providersads", "assocarray", false)
  m.global.AddField("paymentMethods", "array", false)
  m.global.setField("paymentMethods", [])
  m.global.AddField("subscriptions", "array", false)
  m.global.setField("subscriptions", [])
  'profile
  m.global.AddField("profile_img", "string", false)
  m.global.AddField("WarmReboot", "assocarray", false)
  ' -----------------------------------------
  m.global.AddField("deeplink", "assocarray", false)
  ' -----------------------------------------
  m.global.AddField("ItemTitle", "assocarray", false)
  m.global.AddField("paywayProfile", "assocarray", false) ' para Youbora
  ' -----------------------------------------
  m.global.AddField("currFocus", "node", false)

  ' DEVICE (!)
  ' -----------------------------------------
  m.global.AddField("videoMode", "string", false) ' HD/FHD
  m.global.AddField("device_id", "string", false) ' se pasa en todos los llamados de api.
  try
    dev = CreateObject("roDeviceInfo")
    resolution = dev.GetUIResolution()?.name
    ' print ":: ROKU RESOLUTION=";resolution
    m.global.videoMode = resolution
    uniqueId = dev.GetChannelClientId()
    ' print ":: ROKU ID=";uniqueId
    m.global.device_id = uniqueId
  catch error
    print "**************************************"
    print "** ERROR *****************************"
    print error
    print "**************************************"
    print "**************************************"
    m.global.videoMode = "HD"
    m.global.device_id = "???"
  end try
  ' -----------------------------------------

  scene = screen.CreateScene(componentName)
  screen.Show()
  ' vscode_rdb_on_device_component_entry -- NO BORRAR, para debug

  scene.ObserveField("exitChannel", m.port)
  scene.launch_args = args
  ' scene.dlInputArgs = args

  ' create roInput context for handling roInputEvent messages
  input = CreateObject("roInput")
  input.setMessagePort(m.port)

  while (true)
    try
      msg = Wait(0, m.port)
      msgType = Type(msg)

      if msgType = "roSGScreenEvent"
        if msg.IsScreenClosed() then
          return
        end if

      else if msgType = "roSGNodeEvent"
        field = msg.getField()
        data = msg.getData()
        if field = "exitChannel" and data = true
          TheEnd()
          END
        end if

      else if msgType = "roInputEvent"
        ' roInputEvent deep linking, pass arguments to the scene
        ' scene.input_args = msg.getInfo()
        ' --------------------------------------------------
        inputData = msg.getInfo()
        print "[]{source\SGDEX.brs} {DL} StartSGDEXChannel :: INCOMMING!", inputData
        if inputData.DoesExist("mediaType") and inputData.DoesExist("contentId")
          if not inputData.mediaType = "" and not inputData.contentId = ""
            scene.input_args = inputData
            ' scene.launch_args = inputData
          end if
        end if
      end if

    catch tryError
      print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
      print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
      print "%% ERROR %%%%%%%%%%%%%%%%%%%%%%%%%%%"
      print tryError
      print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
      print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
    end try

  end while
end sub

sub TheEnd()
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%% SALIDA %%%%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
  print "%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"
end sub
