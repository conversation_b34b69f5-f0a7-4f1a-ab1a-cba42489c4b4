sub Init()
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  if ghGetDisplayMode() = "FHD" then
    ejeX = -12.5
  else
    ejeX = -7
  end if

  ' creo que al tener el rowFocusAnimationStyle = floatingFocus no es necesario
  ' if ghGetChild(data, "cantTotal", 50) >= 6 then
  '   if ghGetDisplayMode() = "FHD" then
  '     ejeX = 18
  '   else
  '     ejeX = 17
  '   end if
  ' end if

  m.itemPoster.setFields({
    width: 190
    height: 190
    translation: [ejeX, 9]
    uri: ghGetChild(data, "user_image")
  })

  m.title.setFields({
    width: m.itemPoster.width - 25
    height: m.itemPoster.height - 25
    text: ghGetChild(data, "username", "")
    font: ghGetFont(21, "regular")
    translation: [ejeX + 10, 100]
    color: "0xCCCCCC"
    wrap: "true"
    lineSpacing: "0"
    horizAlign: "center"
    vertAlign: "bottom"
    visible: true
  })
end sub
