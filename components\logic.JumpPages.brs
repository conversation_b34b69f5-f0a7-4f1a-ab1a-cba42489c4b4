sub FlujoALaHome()
  m.logger.debug("FlujoALaHome")

  ghCallApi("LastTouch", "_JumpToHome", "_JumpToHome")
  Notify_Roku("Roku_Authenticated")
end sub

sub FlujoALosTerminosYCondiciones()
  m.logger.debug("FlujoALosTerminosYCondiciones")

  m.top.backgroundUri = ""
  pagina = CreateObject("roSGNode", "AceptarTerminosPage")
  pagina.id = "AceptarTerminosPage"

  if m.versionSuffix = "Lite" then
    pagina.ObserveField("value", "FlujoALaHome")
  end if

  pagina.setFields({})
  m.top.ComponentController.callFunc("show", { view: pagina })
  setLoading(false) ' TODO llevarlo a la pagina
end sub

sub FlujoALaLanding()
  m.logger.debug("FlujoALaLanding")

  m.top.backgroundUri = ""

  ' version classic
  ' si tiene algo, viene de un deep link y voy al login
  arguments = getDLArguments(m.top.launch_args)

  if arguments = invalid then
    pagina = CreateObject("roSGNode", "LandingPage")
    pagina.id = "LandingPage"
    pagina.setFields({})
  else
    pagina = CreateObject("roSGNode", "LoginPage")
    pagina.id = "LoginPage"
    pagina.setFields({})
  end if
  m.top.ComponentController.callFunc("show", { view: pagina })
  setLoading(false) ' TODO llevarlo a la pagina
end sub

sub _JumpToHome()
  m.logger.debug("_JumpToHome")

  m.top.backgroundUri = "" ' apago el logo de fondo

  Notify_Roku("Roku_Authenticated")
  m.top.signalBeacon("AppDialogComplete")

  pagina = CreateObject("roSGNode", "HomePage" + m.versionSuffix)
  ' pagina = CreateObject("roSGNode", "HomePageLite")
  pagina.id = "HomePage"
  params = getParamsDL()
  pagina.setFields(params)
  pagina.ObserveField("wasShown", "Home_wasShown")
  m.top.ComponentController.callFunc("show", { view: pagina })
  setLoading(false) ' TODO llevarlo a la pagina
end sub

function getParamsDL()
  m.logger.debug("getParamsDL")

  arguments = ghGetRegistry("arguments", "DL")

  fields = { nodo: "" }
  if arguments <> invalid and arguments <> "" then
    args = ParseJson(arguments)
    if args <> invalid then
      if args.DoesExist("mediaType") or args.DoesExist("mediatype") then
        contentType = 1
        if args.mediaType = "series" then
          contentType = 2
        else if args.mediaType = "season" then
          contentType = 3
        else if args.mediaType = "episode" then
          contentType = 4
        else if args.mediaType = "audio" then
          contentType = 5
        end if
        item = {
          contentType: contentType
          id: args.contentId
          mediaType: args.mediaType
          idPlayable: isPlayableMediaType(args.mediaType),
          deepLinking: true
        }
        fields = { "deeplink": item, nodo: "" }
      end if
    end if
  end if

  return fields
end function

sub Home_wasShown()
  m.top.signalBeacon("AppLaunchComplete")
end sub