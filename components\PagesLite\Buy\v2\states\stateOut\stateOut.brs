' stateOut
' -----------------------

sub stateOut()
  if m.top.debug then
    print ghloghead();"stateOut OUT ";m.buy.states["out"]
    print ghLogHead();"stateOut therWasLogin ";m.buy.states["login"].thereWasLogin
  end if

  substate = m.buy.states["out"].state
  if substate = invalid then
    Jumpto("out", "ok")
  else if substate = "ok" then
    outOk()
  else if substate = "fail" then
    outFail()
  else if substate = "error" then
    outError()
  end if
end sub

sub outOk()
  if m.top.debug then print ghloghead();"outOk"
  outGA4()
  m.top.routerClose = true
  if m.top.debug then print ghloghead();"outOk --------------------------------------------"
end sub

sub outFail()
  if m.top.debug then print ghloghead();"outFail"
  m.top.routerClose = false
  if m.top.debug then print ghloghead();"outFail -----------------------------------------"
end sub


sub outError()
  if m.top.debug then print ghloghead();"outError"
  ShowGenericErrorMessage("Error", "Network Error", "OK", { state: "out", substate: "out" }, { state: "out", substate: "out" })
end sub
' sub outErrorBack()
'   if m.top.debug then print ghloghead();"outErrorBack"
'   InitBuyStateMachine()
'   ' m.top.routerReturn = { content: false } ' parece que no usa el parametro
'   m.top.routerClose = false
'   if m.top.debug then print ghloghead();"outError -----------------------------------------"
' end sub

sub outGA4()
  if m.top.debug then print ghloghead();"outGA4"
  payMethod_text = ghGetChild(m.buy, "select.selected.gatewaytext", "number telcel")
  'buyType_text = ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
  buyPrice_text = ghGetChild(m.buy, "data.button.currency", "$") + ghGetChild(m.buy, "data.button.price", "00.00")

  event_name = "purchase_suscription"
  event_body = {
    content_name: "not apply",
    content_id: ghGetChild(m.top, "buyB.product_id"),
    content_price: buyPrice_text,
    content_brand: ghGetChild(m.top, "buyB.family"),
    content_type: "not apply",
    content_category: "not apply",
    content_availability: "by sucription addon",
    content_episode: "not apply",
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    content_payment_type: payMethod_text,
    screen_name: "buy",
    screen_class: "/buy"
  }
  GA4Event(event_name, event_body)
end sub

