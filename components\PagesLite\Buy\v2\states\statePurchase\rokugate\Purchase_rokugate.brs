' ROKUGATE

sub PurchaseRokuGate()
  m.logger.debug("Purchase ROKUGATE Init")

  RokuGateRun()
end sub

sub RokuGateRun(newState = invalid, info = {})
  m.logger.debug("Purchase ROKUGATE Run", { newState: newState, info: info })

  setLoading(false)

  if newState = invalid then
    Roku_DoRokuBuy()
  else if newState = "go" then
    setLoading(true)

    data = ghGetChild(m.buy.states, "purchase.paymentMethod")
    apiConfirm = ghCallApi("BuyConfirmLite", "Roku_Return", "Roku_ReturnError", false)
    apiConfirm.setFields({
      link: ghGetChild(data, "data.buylink", ""),
      obj_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(data, "data.access_code", "")
      buyToken: ghGetChild(data, "data.buyToken")
      extra_params: {
        transaction_id: m.buy.options.rokugate.transaction_id
      }
    })
    apiConfirm.control = "run"

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "back" then
    JumpTo("checkout")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")
  end if
end sub

sub Roku_DoRokuBuy()
  ' TODO mejorar de donde se obtiene
  idOrden = ghGetChild(m.top, "buyB.product_id")
  orden = ghRokupayBuildOrder(idOrden)

  m.logger.debug("Purchase ROKUGATE DoRokuBuy", { orden: orden })

  m.store = ghRokupayDoOrder(orden, "Roku_DoRokuBuy_Return")
end sub

sub Roku_DoRokuBuy_Return(event)
  data = event.getData()
  status = ghGetChild(data, "status", -20)

  m.logger.debug("Purchase ROKUGATE DoRokuBuy Return", { data: data, status: status })

  if status = 1 then
    operacion = data.getChild(0)

    m.buy.options.rokugate.transaction_id = ghGetChild(operacion, "purchaseId", "no consegui el codigo")

    RokuGateRun("go")

  else if status = 2 then
    RokuGateRun("error")

  else
    RokuGateRun("error")
  end if
end sub

sub Roku_CallApi()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")

  if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
    apiConfirm = ghCallApi("BuyConfirmLite", "Roku_Return", "Roku_ReturnError", false)
    apiConfirm.setFields(data)
    apiConfirm.control = "run"
  else
    RokuGateRun("missingparameters")
  end if
end sub

sub RokuGate_DoConfirm_Return(event)
  scr = event.getRoSGNode()

  m.logger.debug("Purchase ROKUGATE DoConfirm Return", { opcion: scr.value.opcion })

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    RokuGateRun("back")
  else if scr.value.opcion = "SELECT" then
    RokuGateRun("go")
  end if
end sub

sub Roku_Return(event)
  data = event.getData()

  m.logger.debug("Purchase ROKUGATE Return", { data: data })

  RokuGateRun("ok", { data: data })
end sub

sub Roku_ReturnError(event)
  data = event.getData()

  m.logger.error("Purchase ROKUGATE Return Error", { data: data })

  RokuGateRun("error", { data: data })
end sub