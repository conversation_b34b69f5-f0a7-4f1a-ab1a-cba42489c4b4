<?xml version="1.0" encoding="utf-8" ?>

<component name="ServiceManager" extends="Group">

  <script type="text/brightscript" uri="ServiceManager.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- <field id="backColor" type="string" value="0x00000000" onChange="backColorChange" /> -->
    <!-- <field id="panel" type="string" value="*" alias="panel.text" /> -->
    <!-- <field id="run" type="assocarray" alwaysNotify="true" onChange="onRun" /> -->

    <!-- services -->
    <field id="add" type="assocarray" alwaysNotify="true" onChange="onServiceAdd" />
    <field id="remove" type="string" alwaysNotify="true" onChange="onServiceRemove" />
    <field id="start" type="string" alwaysNotify="true" onChange="onServiceStart" />
    <field id="stop" type="string" alwaysNotify="true" onChange="onServiceStop" />
    <field id="restart" type="string" alwaysNotify="true" onChange="onServiceRestart" />

    <!-- loading -->
    <field id="loading" type="boolean" value="false" alwaysNotify="true" onChange="onLoadingChange" />

    <!-- general management -->
    <field id="shutdown" type="string" alwaysNotify="true" onChange="onManagerShutdown" />

    <!-- internal -->
    <field id="debug" type="boolean" value="false" onChange="onDebugTurn" />
  </interface>

  <children>
    <Group id="loadingPanel" visible="false">
      <!-- <GHLoading id="loading" visible="true" backColor="0x000000" /> -->
      <!-- loading transparente -->
      <GHLoading id="loading" visible="true" />
      <!-- <Rectangle color="0x00FF0066" width="1280" height="720"/> -->
    </Group>
    <Group id="display" visible="false">
      <Rectangle id="background" />
      <MultiStyleLabel id="panel" />
    </Group>
    <Group id="services"/>
  </children>

</component>
