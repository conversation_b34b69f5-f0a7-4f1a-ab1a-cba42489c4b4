<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->

<component name="RouterBase" extends="Scene">

    <script type="text/brightscript" uri="RouterBase.brs" />
    <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

    <interface>
        <!-- @Public -->
        <!-- @ReadOnly -->
        <!-- Reference to ComponentController node that is created and used inside library -->
        <field id="ComponentController" type="node" />

        <!-- @Public -->
        <!-- @ReadOnly -->
        <!-- Reference to ButtonBar node that is created and used inside the library -->
        <field id="buttonBar" type="node" />

        <!-- @Public -->
        <!-- @WriteOnly -->
        <!-- Exits channel if set to true -->
        <field id="exitChannel" type="bool" alwaysNotify="true" />

        <!-- Deep linking launch arguments will be passed via this field to Show() function -->
        <field id="launch_args" type="assocarray" onChange="LaunchArgumentsReceived" />
        <!-- roInput deep linking arguments will be passed via this field to Input() function -->
        <field id="input_args" type="assocarray" onChange="InputArgumentsReceived" />

        <!-- @Public -->
        <!-- Theme is used to customize the appearance of all SGDEX views. -->
        <field id="theme" type="assocarray" alwaysNotify="true" />

        <!-- @Public -->
        <!-- Field to update themes by passing the config -->
        <field id="updateTheme" type="assocarray" alwaysNotify="true" />

        <!-- @Private -->
        <!-- Store global theme parameters -->
        <field id="actualThemeParameters" type="assocarray" alwaysNotify="true" />

        <!-- Function that is used under the hood to create objects in channel context -->
        <function name="createObjectOnDemand"/>
    </interface>

    <children>
        <RouterController id="ComponentController" />
    </children>
</component>
