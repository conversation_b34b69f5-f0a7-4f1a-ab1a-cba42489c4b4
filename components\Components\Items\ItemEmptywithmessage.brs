sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init"
  ' componentes
  ' m.fondo = m.top.findNode("fondo")
  m.message = m.top.findNode("message")
  ' m.img1 = m.top.findNode("img1")
  ' m.img2 = m.top.findNode("img2")
  ' m.img3 = m.top.findNode("img3")
end sub

sub itemContentChanged(event)
  item = event.getData()
  titleKey = "MensajeVacio_" + ghGetChild(item, "TITLE", "default")

  if m.top.debug then
    print ghLogHead();item
    print ghLogHead();item.data
  end if

  ' m.fondo.setFields({
  '   color: "#ccff00"
  '   width: 1176
  '   height: 312
  ' })
  m.message.setFields({
    text: ghTranslate(titleKey, titleKey)
    color: "#767676"
    ' width: 350
    height: 200
    font: ghGetFont(21, "medium")
    ' horizAlign: "left"
    ' vertAlign: "center"
  })
  print titleKey
  ' m.img1.setFields({
  '   loadSync: true
  '   height: 230
  '   width: 230
  '   uri: "pkg:/images/loading_square.png"
  ' })
  ' m.img2.setFields({
  '   loadSync: true
  '   height: 230
  '   width: 230
  '   uri: "pkg:/images/loading_square.png"
  ' })
  ' m.img3.setFields({
  '   loadSync: true
  '   height: 230
  '   width: 230
  '   uri: "pkg:/images/loading_square.png"
  ' })
end sub
