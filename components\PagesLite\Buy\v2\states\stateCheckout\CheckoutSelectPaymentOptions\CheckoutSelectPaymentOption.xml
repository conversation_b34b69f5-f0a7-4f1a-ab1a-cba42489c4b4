<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2018 Roku, Inc. All rights reserved. -->
<component name="CheckoutSelectPaymentOption" extends="Page">
	<script type="text/brightscript" uri="CheckoutSelectPaymentOption.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/CardDefinitions.brs" />
	<interface>
		<!-- interfaz de entrada -->
		<field id="methods" type="array" onChange="refreshData" />
		<!-- interfaz de salida -->
		<field id="value" type="assocarray" />
	</interface>
	<children>
		<Label id="title" translation="[0,120]" width="1280" horizAlign="center" text="*" />
		<GHRowList id="theGrid" translation="[0, 200]" />
		<Label id="msgNoMethod" translation="[400,292]" visible="false" width="480" height="120" horizAlign="center" text="msgNoMethod" wrap="true" />
		<GHButtonGroup id="botonera" layout="childs" orientation="vertical" exitUp="true">
			<GHButton id="add" text="Agregar" value="ADD" visible="false" translation="[460,550]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
			<GHButton id="cancel" text="Cancelar" value="CANCEL" translation="[460,620]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" />
		</GHButtonGroup>
	</children>
</component>
