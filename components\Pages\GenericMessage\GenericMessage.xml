<?xml version="1.0" encoding="utf-8" ?>

<!-- <component name="GenericMessage" extends="SGDEXComponent"> -->
<component name="GenericMessage" extends="Page">

  <script type="text/brightscript" uri="GenericMessage.brs" />
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" /> -->
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" /> -->
  <!-- <script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" /> -->

  <interface>
    <!-- interfaz de entrada -->
    <field id="mode" type="string" value="scrError" />
    <field id="title" type="string" alias="lblTitle.text" />
    <field id="message" type="string" alias="lblDescrip.text" />
    <field id="accept" type="string" alias="btnAccept.text" />
    <field id="fwd" type="string" />
    <field id="back" type="string" />
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
    <!-- interfaz interna -->
    <!-- <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" /> -->
    <!-- <field id="debug" type="boolean" value="true" /> -->
  </interface>

  <children>
    <Group id="scrError">
    <Rectangle id="background" translation="[0,0]" width="1920" height="1080" color="#121212" visible="true">
      <Label id="lblTitle" translation="[0,220]" width="1280" height="48" text="*" />
      <Label id="lblDescrip" translation="[364,274]" width="550"  height="130" horizAlign="center" text="*" wrap="true" />
      <GHButton id="btnAccept" translation="[392,392]" value="OK" width="500" height="72" color= "0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
    </Rectangle>
    </Group>
  </children>

</component>
