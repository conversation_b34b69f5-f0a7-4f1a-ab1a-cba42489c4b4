<?xml version="1.0" encoding="utf-8" ?>
<component name="GHFramePoster" extends="Group">

  <script type="text/brightscript" uri="GHFramePoster.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- icon -->
    <field id="uri" type="string" value="" onChange="refresh" alias="img.uri" />
    <field id="color" type="string" value="0xFFFFFFFF" onChange="refresh" alias="img.blendColor" />
    <field id="width" type="int" value="20" onChange="refresh" alias="img.width" />
    <field id="height" type="int" value="20" onChange="refresh" alias="img.height" />
    <!-- icon OVER -->
    <field id="uriBorder" type="string" value="" onChange="refresh" alias="border.uri" />
    <field id="colorBorder" type="string" value="0xFFFFFFFF" onChange="refresh" alias="border.blendColor" />
    <field id="widthBorder" type="int" value="20" onChange="refresh" alias="border.width" />
    <field id="heightBorder" type="int" value="20" onChange="refresh" alias="border.height" />

    <field id="padding" type="array" value="[0,0]" onChange="refresh" alias="img.translation" />

    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children>
    <Poster id="img" />
    <Poster id="border" />
  </children>

</component>