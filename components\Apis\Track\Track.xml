<?xml version="1.0" encoding="UTF-8"?>

<component name="Track" extends="GHApiCall">
  <script type="text/brightscript" uri="Track.brs" />

  <interface>
    <!-- parametros generales -->
    <field id="mode" type="string" value="async"/>
    <!-- interfaz de entrada -->
    <field id="url" type="string" value="" />
    <field id="timecode" type="string" value="" />
    <field id="message" type="string" value="" />
    <field id="purchase_id" type="string" value="" />
    <field id="offer_id" type="string" value="" />
    <field id="preferred_audio" type="string" value="" />
    <field id="preferred_subtitle" type="string" value="" />
  </interface>
</component>
