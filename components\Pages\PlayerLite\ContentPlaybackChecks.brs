sub checkCreditsAndOmitirIntro(video)
	checkRollingCredits(video.position, video.duration, ghGetChild(video, "content.rollingcreditstime", "").toInt())
	checkOmitirIntro()
end sub

sub checkRollingCredits(position as integer, duration as integer, rollingCreditsTime as integer)
	' "1" es trailer
	' "0" no es trailer
	isTrailer = ghGetChild(m, "video.content.is_trailer")
	if ghGetChild(m.trk, "hasEndCard", false) = false or isTrailer = "1" then
		m.logger.debug("Comprobación de omitir intro omitida, no hay end card configurada.")
		return
	end if

	' en realidad resta al total el tiempo de los credits
	creditsStartThreshold = duration + rollingCreditsTime
	faltan = creditsStartThreshold - position

	m.logger.debug("Comprobando tiempo restante para mostrar end card ", { faltan: faltan.toStr() })

	if faltan < 30 and not ghGetChild(m.trk, "hasEndCardPrepare", false) then
		m.trk.hasEndCardPrepare = true
		m.logger.debug("Preparando siguiente episodio")
		updateState("prepareNext")
	end if

	if faltan <= 0 and not m.trk.inRolling then
		m.trk.inRolling = true
		m.logger.debug("Mostrando end card")
		updateState("showEndCard")
	else if faltan > 0 and m.trk.inRolling then
		m.trk.inRolling = false ' lo pongo en falso por las dudas que haya un seek para atras despues de los credits
		m.logger.debug("Ocultando end card")
		updateState("hideEndCard")
	end if
end sub

sub checkOmitirIntro()
	try
		' "1" es trailer
		' "0" no es trailer
		isTrailer = ghGetChild(m, "video.content.is_trailer")
		if not m.trk.oi.enable or isTrailer = "1" then
			'if not m.trk.oi.enable then
			m.logger.debug("Omitir intro no habilitado")
			return
		end if

		' levanto lenguaje y tiempos
		oiCurrLang = ghGetRegistry("language", "user")
		oiStartEndTimes = oiGetStartEndTimes(oiCurrLang)

		' si no hay datos, no hago nada
		if oiStartEndTimes = invalid or oiStartEndTimes.intro_start_time = invalid or oiStartEndTimes.intro_finish_time = invalid then
			m.logger.debug("No hay datos de omitir intro para el idioma actual.")
			m.trk.oi.enable = false
			return
		end if

		oiActualPos = m.video.position
		oiStart = val(oiStartEndTimes.intro_start_time)
		oiEnd = val(oiStartEndTimes.intro_finish_time)

		if oiStart <> invalid and oiEnd <> invalid then
			m.trk.oi.jumpTo = oiEnd ' hasta donde avanzo?

			m.logger.debug("omitir intro: posicion actual: ", { oi_pos: oiActualPos, oi_start: oiStart, oi_end: oiEnd })

			if oiActualPos < oiStart then
				m.trk.oi.desdeInicio = true
			end if

			if oiActualPos < oiStart or oiActualPos > oiEnd then
				m.logger.debug("No estoy en la franja de omitir intro")
				if m.trk.oi.shown then
					updateState("hideIntro")
				end if
			end if

			if oiActualPos >= oiStart and oiActualPos <= oiEnd then
				if not m.trk.oi.shown then
					m.logger.debug("Mostrar omitir intro", { desdeInicio: ghGetChild(m.trk, "oi.desdeInicio", false) })
					updateState("showIntro", { "desdeInicio": ghGetChild(m.trk, "oi.desdeInicio", false) })
					m.trk.oi.desdeInicio = false
				end if
			end if

		else
			m.logger.debug("No hay datos de omitir intro para el idioma actual.")
		end if
	catch error ' si hay algun error, no hago nada
		m.logger.error("Error en checkOmitirIntro: ", { error: error })
	end try
end sub

function oiGetStartEndTimes(lang)
	' para test
	' return {
	' 	intro_start_time: "5",
	' 	intro_finish_time: "30",
	' }

	langs = ghGetChild(m.top.trackInfo, "omitirintro.languages")

	if langs.Count() > 0 then
		times = langs[lang]

		if times <> invalid then
			m.logger.debug("Datos de omitir intro para el idioma: ", { lang: lang, datos: times })
		else
			firstKey = langs.Keys()[0]
			firstItem = langs[firstKey]
			times = firstItem

			m.logger.debug("No se encontraron datos de omitir intro para el idioma: ", { lang: lang })
			m.logger.debug("se usara el idioma, para omitir intro: ", { firstKey: firstKey })
		end if
	end if

	return times
end function

function isPlayerPanelVisible()
	cCount = m.video.getChildCount()

	for i = 0 to cCount - 1
		child = m.video.getChild(i)
		if child.id = "playerPanel" and child.visible then
			return true
		end if
	end for

	return false
end function