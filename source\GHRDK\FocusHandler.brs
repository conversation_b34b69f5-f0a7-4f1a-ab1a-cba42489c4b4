sub changeFocusBasedOnKey(key as string)
  m.logger.debug("OnKeyEvent key=", { key: key })

  currentId = getCurrentFocus()

  m.logger.debug("currenFocus ", { current: currentId })

  if currentId <> "" and currentId <> invalid and m.map[currentId] <> invalid and m.map[currentId][key] <> invalid then
    targetId = m.map[currentId][key]
  else
    targetId = invalid
  end if

  if targetId <> invalid then
    result = setNodeFocus(targetId, true)
    ' solo si se encontró un nodo destino, y se seteo correctamente
    if result = true then
      if currentId <> invalid and currentId <> "" then
        setNodeFocus(currentId, false)
      end if
    end if
  end if
end sub

function setNodeFocus(nodeId as string, state as boolean) as boolean
  node = m.top.findNode(nodeId)

  if node <> invalid and node.visible = true then
    if node.HasField("focus") then

      node.focus = state
      node.setFocus(state)

      m.logger.debug("turnFocusTo - cambio foco ", { nodeId: nodeId, state: state })
      return true
    end if
  else
    m.logger.debug("turnFocusTo - Nodo no encontrado: ", { nodeId: nodeId })
  end if

  return false
end function

sub turnFocusTo(targetId as string)
  m.logger.debug("turnFocusTo: ", { targetId: targetId })

  currentId = getCurrentFocus()

  ' pongo foco en el nodo destino
  result = setNodeFocus(targetId, true)

  ' solo si el destino existe y fue seteado correctamente
  if result = true and targetId <> currentId then
    ' busco y quito foco al nodo actual
    if currentId <> invalid and currentId <> "" then
      setNodeFocus(currentId, false)
    end if
  else
    m.logger.error("turnFocusTo: el destino no existe ", { target: targetId })
  end if
end sub

' function getCurrentFocus()
'   current = invalid
'   if m.top.focusedChild <> invalid then
'     if m.top.focusedChild.id <> "" then
'       if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
'         current = m.top.focusedChild.id
'       end if
'     end if
'   end if
'   if m.top.debug then print ghLogHead();"getCurrentFocus -- "; current
'   return current
' end function

function getCurrentFocus() as string
  for i = 0 to m.top.getChildCount() - 1
    child = m.top.getChild(i)

    if child.isInFocusChain()
      for i2 = 0 to child.getChildCount() - 1
        grandChild = child.getChild(i2)
        if grandChild.isInFocusChain()
          m.logger.debug("getCurrentFocus parent", { childId: child.id })
          m.logger.debug("getCurrentFocus child", { grandChild: grandChild.id })
          ' return grandChild.id
          ' retorno el padre, ya que el hijo es el que tiene el foco
          ' y el padre es el que esta en m.map
          return child.id
        end if
      end for

      m.logger.debug("getCurrentFocus parent", { childId: child.id })
      return child.id
    end if
  end for

  m.logger.debug("Ningun hijo o nieto de 'm.top' está en la cadena de foco")
  return ""
end function

sub setLoading(status as boolean)
  m.logger.debug("cambiando loading a:", { status: status })

  if m.global.ServiceManager <> invalid then
    m.global.ServiceManager.loading = status
  end if

  if status then
    m.focus = getCurrentFocus()
    m.logger.debug("apago el foco ", { focus: m.focus })
    setNodeFocus(m.focus, false)
  else
    if m.focus <> invalid then
      m.logger.debug("devuelvo el foco ", { focus: m.focus })
      turnFocusTo(m.focus)
    end if
  end if
end sub