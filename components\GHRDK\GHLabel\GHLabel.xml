<?xml version="1.0" encoding="utf-8" ?>
<component name="GHLabel" extends="Group" initialFocus="label">

  <script type="text/brightscript" uri="GHLabel.brs"/>
  <script type="text/brightscript" uri="GHLabel_fields.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />

  <interface>
    <!-- text -->
    <field id="text" type="string" value="MyLabel" onChange="updateFieldText" alias="label.text" />
    <!-- position -->
    <field id="translation" type="string" value="[0,0]" onChange="updateFieldTranslation" />
    <field id="width" type="string" value="0" alias="label.width" />
    <field id="height" type="string" value="0" alias="label.height" />
    <!-- align -->
    <field id="horizAlign" type="string" value="center" alias="label.horizAlign" />
    <field id="vertAlign" type="string" value="center" alias="label.vertAlign" />
    <!-- color -->
    <field id="color" type="string" value="0x000000" onChange="updateFieldColor" />
    <field id="selColor" type="string" value="0xFFFFFF" onChange="updateFieldSelColor" />
    <!-- focus -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" />
    <!-- ok button -->
    <field id="selected" type="boolean" value="false" alwaysNotify="true" />
    <field id="value" type="string" value="mybutton"/>
    <!-- debug -->
    <field id="debug" type="boolean" value="false" />
    <!-- end -->
  </interface>

  <children>
    <Label id="label" text="MyLabel" color="0x000000" translation="[0,0]" width="0" height="0" vertAlign="center" horizAlign="center" />
  </children>

</component>
