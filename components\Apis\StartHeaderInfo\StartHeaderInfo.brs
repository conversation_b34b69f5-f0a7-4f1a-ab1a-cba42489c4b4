' StartHeaderInfo
sub DataInit()
  m.top.debug = false
  m.api.method = "GET"
  m.api.url = m.config.mfwk.host + "/services/user/startheaderinfo"
end sub

sub ProcessData(res, raw)

  response = ghGetChild(res, "response")
  if response = invalid then
    m.top.error = {
      code: "INVALID_RESPONSE",
      message: "Invalid response"
      raw: raw
    }
    return
  end if

  if m.top.debug then
    print "****************************************"
    print "****************************************"
    print res
    print res.response
    print m.headers
    print "****************************************"
    print "****************************************"
  end if

  if res = invalid then
    m.top.content = {}
    return
  end if

  if ghGetChild(m.headers, "trueportandip") <> invalid then
    response.ipaddr = m.headers.trueportandip
  end if

  if m.top.debug then
    print ghLogHead();"ProcessData"
    print "-----------------------------"
    print response
    print "-----------------------------"
  end if

  ghSetRegistry("HKS", response.session_stringvalue)
  ghSetRegistry("region", response.region)
  ghSetRegistry("timezone", response.timezone)
  ghSetRegistry("utc", StrI(response.utc))

  ' ghSetRegistry("region", "argentina")

  if m.top.debug then print ghLogHead();"saveToRegistry -- HKS,region,timezone,utc"

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  ' print ">>>>>>>>> ";response
  m.top.content = response
end sub
