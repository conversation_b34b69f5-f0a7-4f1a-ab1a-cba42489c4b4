' GHButton
' by<PERSON><PERSON><PERSON>(2020) <EMAIL>

sub Init()
  if m.top.debug then print ghLogHead("WIDGET");"Init **"
end sub

sub onTick()
  if m.top.debug then print ghLogHead("WIDGET");"onTick **"
end sub

' EVENTS
' -----------------------------
sub onRun(event)
  data = event.getData()
  if m.top.debug then print ghLogHead("WIDGET");"onRun **"
  if data.cmd = "add" then
    addWidget(data)
  else if data.cmd = "remove" then
    removeWidget(data)
  else
    print ghLogHead("WIDGET");"ERROR comando desconocido [";data.cmd;"]"
  end if
end sub

sub addWidget(data) ' agrega un widget
  if data.componente <> invalid then
    if m.top.debug then print ghLogHead("WIDGET");"onRun ** prender", data.componente.id
    m.top.appendChild(data.componente)
    if m.top.debug then print ghLogHead("WIDGET");"onRun ** "data.componente.id;" agregado."
  else
    if m.top.debug then print ghLogHead("WIDGET");"onRun ** prender -- ERROR! COMPONENTE ", data.componente
  end if
end sub
sub removeWidget(data) ' remueve un widget
  if m.top.debug then print ghLogHead("WIDGET");"onRun ** apago", data.id
  cant = m.top.getChildCount()
  for i = 0 to cant - 1
    c = m.top.getChild(i)
    if c <> invalid then
      ' print "child ";i, c
      if c.id = data.id then
        m.top.removeChild(c)
        if m.top.debug then print ghLogHead("WIDGET");"onRun ** ";data.id;" eliminado."
      end if
    end if
  end for
end sub

' END FILE ------------------