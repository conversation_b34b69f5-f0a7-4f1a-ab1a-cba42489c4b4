' GHOption
' by<PERSON><PERSON><PERSON>(2021) <EMAIL>
' ---------------------------------------------

function init()
  ' componentes
  m.fondo = m.top.findNode("fondo")
  m.barra = m.top.findNode("barra")
  refresh()
end function


function refresh()
  m.barra.translation = [m.top.padding, m.top.padding]
  m.barra.height = m.fondo.height - (2 * m.top.padding)
  maxSize = m.fondo.width - (2 * m.top.padding)
  m.barra.width = maxSize * m.top.value / 100
  if m.top.debug then print ghLogHead();"refresh -- max=";maxSize;" %=";m.top.value;" width=";m.barra.width
end function


'
'
' END FILE ------------------
