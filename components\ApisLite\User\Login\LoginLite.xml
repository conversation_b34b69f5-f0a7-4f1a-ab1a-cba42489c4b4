<?xml version="1.0" encoding="UTF-8"?>

<component name="LoginLite" extends="GHApiTask">
  <script type="text/brightscript" uri="LoginLite.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="username" type="string" value="" />
    <field id="password" type="string" value="" />

    <!-- por defecto sigue el mismo, pero si lo cambio, usa el userhash del top -->
    <field id="mode" type="string" value="user-pass" />
    <field id="userhash" type="string" value="" />
  </interface>

</component>
