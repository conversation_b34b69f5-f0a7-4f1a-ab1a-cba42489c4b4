<?xml version="1.0" encoding="utf-8"?>
<component name="singlePackPage" extends="Page">
	<script type="text/brightscript" uri="singlePackPage.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Roku.brs" />
	<interface>
		<field id="data" type="assocarray" onChange="handleData" />
		<field id="selected" type="assocarray" />
		<field id="value" type="string" />
	</interface>
	<children>
		<Rectangle id="background" width="1280" height="720" color="0x000000FF" />
		<Label id="headerTitle" focusable="false" vertAlign="center" horizAlign="center" translation="[0, 110]" width="250" wrap="true" color="0xFFFFFF" font="font:SmallSystemFont" />
		<poster id="title" focusable="false" visible="true" loadSync="true" vertAlign="center" horizAlign="center" translation="[0, 165]" />
		<LayoutGroup id="priceRow" translation="[500, 230]" layoutDirection="horiz" itemSpacings="[3]">
			<Label id="priceAmount" focusable="false" color="0xFFFFFFFF" />
			<Label id="pricebar" focusable="false" color="0xFFFFFFFF" />
		</LayoutGroup>
			<LayoutGroup id="infoadd" translation="[460, 280]" layoutDirection="vert" itemSpacings="[10]">
		<Label id="trailPeriod" focusable="false" vertAlign="center" horizAlign="center" width="368" wrap="true" color="#00A9FF" font="font:SmallSystemFont" />
		<Label id="description" focusable="false" vertAlign="center" horizAlign="center" width="368" wrap="true" color="0xFFFFFF" font="font:SmallSystemFont" />
			</LayoutGroup >
		<GHButtonGroup id="buttons" layout="childs" orientation="vertical">
			<GHButton value="OK" id="subscribeButton" translation="[450, 470]" width="368" height="72" backcolor="#981C15" color="#FFFFFF" selColor="#FFFFFF" selBackColor="#981C15" focusColor="0xFFFFFFFF" focusable="true" />
			<GHButton value="CANCEL" id="cancelButton" translation="[450, 540]" width="368" height="72" color="0xFFFFFF" selColor="#FFFFFF" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" focusable="true" />
		</GHButtonGroup>
		<GHError id="error" />
		<GHLoading id="loading" visible="false" />
	</children>
</component>
