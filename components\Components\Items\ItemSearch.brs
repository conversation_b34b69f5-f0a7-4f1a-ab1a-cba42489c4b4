sub Init()
  ' m.top.debug = true
  if m.top.debug then print ghLogHead();"Init"
  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
end sub

sub itemContentChanged()
  ' item = m.top.itemContent
  data = ghGetChild(m.top.itemContent, "data")
  if m.top.debug then
    print ghLogHead();"Refresh --------------------"
    print ghLogHead();m.top.itemContent
    print ghLogHead();m.top.itemContent.data
    print ghLogHead();"----------------------------"
  end if
  ' general
  m.itemPoster.width = 252
  m.itemPoster.height = 150
  m.itemPoster.uri = ghGetChild(data, "image_small", "pkg:/images/loading.png")

  drawTitle(m.itemPoster.width, m.itemPoster.height)
  ' drawChapitas(m.itemPoster.width, m.itemPoster.height)
  initTimer()
end sub

