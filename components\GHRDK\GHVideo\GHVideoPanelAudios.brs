' GHVideoPanelAudios
' --------------------------
' SETUP
sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init ***"

  m.map = {
    "languages": {
      "up": invalid,
      "right": invalid,
      "down": invalid,
      "left": invalid
    }
  }
  ' Objects
  m.infoSeason = m.top.findNode("infoSeason")
  m.infoEpisode = m.top.findNode("infoEpisode")
  m.infoEpisodeTitle = m.top.findNode("infoEpisodeTitle")
  m.infoSerie = m.top.findNode("infoSerie")
  m.title = m.top.findNode("title")
  m.time = m.top.findNode("time")
  m.desc = m.top.findNode("description")
  m.panelLanguages = m.top.findNode("languages")
  m.panelLanguages.ObserveField("itemSelected", "onItemSelected")
  m.panelLanguages.ObserveField("itemFocused", "onItemFocused")
  ' eventos
  m.top.ObserveField("visible", "onPanelVisibleChange")
  ' formato
  setFormats()
end sub
sub setFormats()
  ' Fonts
  m.infoSeason.font = ghGetFont(24, "regular")
  m.infoEpisode.font = ghGetFont(24, "regular")
  m.infoEpisodeTitle.font = ghGetFont(24, "regular")
  ' Panel Left
  m.top.findNode("panelLeft").setFields({
    translation: [0, 0]
    width: 1920
    height: 720
    color: "#000000CC"
  })
  m.title.setFields({
    translation: [81, 187]
    font: ghGetFont(43, "regular")
    width: 664
    height: 55
  })
  m.time.setFields({
    ' translation: [50, 100]
    font: ghGetFont(21, "regular")
    translation: [75, 292]
    width: 500
    height: 32
    font: ghGetFont(24, "regular")
  })
  m.desc.setFields({
    translation: [81, 340]
    font: ghGetFont(21, "regular")
    width: 728
    height: 112
    lineSpacing: "0"
  })
  ' Panel Right
  m.top.findNode("panelRight").setFields({
    translation: [880, 0]
    width: 401
    height: 720
    color: "#282828"
  })
  m.top.findNode("langLabel").setFields({
    translation: [25, 100]
    text: ghTranslate("", "IDIOMA")
    color: "#999999"
    font: ghGetFont(21, "regular")
  })
  ' Lenguajes
  m.panelLanguages.setFields({
    translation: [00, 140]
    itemComponentName: "GHVideoLangItem"
    numColumns: 1
    numRows: 10
    color: "#FFFFFF"
    itemSize: [500, 30]
    itemSpacing: [0, 50]
    drawFocusFeedback: false
    vertFocusAnimationStyle: "floatingFocus"
  })
  ' -----------------------------
end sub
' EVENTS
sub onPanelVisibleChange(event)
  data = event.getData()
  if data then ' entro----
    position = 0
    for each l in m.top.langData
      if l.is_current then
        exit for
      end if
      position = position + 1
    end for
    m.panelLanguages.visible = true
    m.panelLanguages.jumpToItem = position
    m.panelLanguages.setFocus(true)
  else ' salgo ------------------
    m.panelLanguages.visible = false
    m.panelLanguages.setFocus(false)
    m.top.visible = false
    m.top.setFocus(false)
  end if
end sub
sub onInfoUpdate(event)
  data = event.getData()
  m.title.text = ghGetChild(data, "title", "")
  m.time.text = ghFormatDuration(ghGetChild(data, "duration", invalid))
  m.desc.text = ghGetChild(data, "description", "")
  if ghGetChild(data, "serie_id") <> invalid then
    m.infoSeason.text = ghTranslate("languaje_screen_label_temporada", "Temp.") + " " + ghGetChild(data, "season")
    m.infoEpisode.text = ghTranslate("languaje_screen_label_episodio", "Ep.") + " " + ghGetChild(data, "episodenumber")
    m.infoEpisodeTitle.text = ghGetChild(data, "titleEpisode")
    m.infoSerie.visible = true
  else
    m.infoSerie.visible = false
  end if
end sub
sub onLangDataUpdate(event)
  if m.top.debug then print ghLogHead();"onLangDataUpdate ***"

  ls = event.getData()
  langs = createObject("RoSGNode", "GHContent")
  for each l in ls
    if m.top.debug then print ghLogHead("LANGUAGES");l
    lang = createObject("RoSGNode", "GHContent")
    lang.data = l
    langs.appendChild(lang)
  end for

  m.panelLanguages.content = langs
end sub
sub onItemSelected(event)
  data = event.getData()
  if m.top.debug then print ghLogHead();"onItemSelected ** ";data
  selected = m.top.langData[data]

  ls = m.top.langData

  ' solo cambio el current si no es el item de activar/desactivar subtitulos
  if selected.itemActivacion = invalid then
    for each l in ls
      l.is_current = false
      if l.content_id = selected.content_id then
        l.is_current = true
      end if
    end for
  end if

  m.top.langData = ls
  m.top.selected = selected
  m.top.keypressed = "ok"
end sub
sub onItemFocused()
  if m.top.debug then print ghLogHead();"onItemFocused ** !!!!!!!"
  m.top.keypressed = "x"
end sub
' FOCUS
function onKeyEvent(key, press) as boolean
  if m.top.debug then print ghLogHead();"onKeyEvent -- key=";key;" press=";press
  handled = true
  if m.top.visible = true then
    if press then
      if key <> "back" then
        turnFocusTo(guessFocusTo(key))
        handled = true
        if m.top.debug then print ghLogHead();"onKeyEvent -- focusedChild [";m.top.focusedChild.id;"]"
      else
        if m.top.debug then print ghLogHead();"onKeyEvent -- BACK!"
        m.top.visible = false
        handled = true
        ' m.top.focus = false
      end if
      m.top.keypressed = key
    end if
  end if
  return handled
end function
function guessFocusTo(direction)
  focusTo = invalid
  current = getCurrentFocus()
  if current <> invalid then
    ' a donde voy?
    if m.map[current][direction] <> invalid then
      focusTo = m.map[current][direction]
    else
      focusTo = current
    end if
  end if
  return focusTo
end function
sub turnFocusTo(id)
  current = getCurrentFocus()
  if current <> id then
    if current <> invalid then
      m.top.findNode(current).focus = false ' apago el actual
    end if
    if m.top.findNode(id).focus <> invalid then
      if m.top.debug then print ghLogHead();"turnFocusTo -- "; current; " -> "; id
      m.top.findNode(id).focus = true
      m.top.findNode(id).setFocus(true)
    end if
  else
    if m.top.debug then print ghLogHead();"turnFocusTo -- me quedo en "; current
  end if
end sub
function getCurrentFocus()
  current = invalid
  if m.top.focusedChild <> invalid then
    if m.top.focusedChild.id <> "" then
      if m.top.findNode(m.top.focusedChild.id).focus <> invalid then
        current = m.top.focusedChild.id
      end if
    end if
  end if
  return current
end function