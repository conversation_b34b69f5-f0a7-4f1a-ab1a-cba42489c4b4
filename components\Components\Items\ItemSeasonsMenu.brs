sub Init()
  m.top.debug = false
  if m.top.debug then print ghLogHead();"Init"
  m.label = m.top.findNode("label")
  m.label.font = ghGetFont(16, "medium")
end sub


sub itemContentChanged()

  selected = ghGetChild(m.top.itemContent, "selected", false)

  m.label.setFields({ text: ghGetChild(m.top.itemContent, "TITLE", ""), color: "0xFFFFFF50" })

  if selected then
    itemTextInactivoAndSelected()
  end if
end sub

sub showfocus() ' event
  selected = ghGetChild(m.top.itemContent, "selected", false)

  if m.top.rowListHasFocus = true then
    ' el menu tiene el foco
    if m.top.focusPercent = 1 then
      itemTextActivo()
    else
      m.label.setFields({ color: "0xFFFFFF50" })

      if selected then
        itemTextInactivoAndSelected()
      end if
    end if
  else
    ' el menu no tiene el foco
    m.label.setFields({ color: "0xFFFFFF50" })
    if selected then
      itemTextInactivoAndSelected()
    end if
  end if

end sub

sub itemTextActivo()
  m.label.setFields({ color: "0xFFFFFF" })
end sub

sub itemTextInactivoAndSelected()
  m.label.setFields({ color: "0xFFFFFF" })
end sub