' AMCOGATE

sub PurchasePromogate()
  m.logger.debug("Purchase PromoGate Init")

  PromoGateRun(invalid)
end sub

sub PromoGateRun(newState = invalid, info = {})
  m.logger.debug("PromoGateRun", { state: newState, info: info })

  setLoading(false)

  if newState = invalid then
    ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
    ScrCodigo.id = "CodigoPromo"
    ScrCodigo.ObserveField("wasClosed", "CodigoPromo_Return")
    ' Get buyData from the current buy flow
    buyData = {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")
      oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")

      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
      screen_name: "promogate",
      screen_class: "/promogate",

    }
    GA4Event("purchase_promogate", buyData)
    m.top.routerChild = { page: ScrCodigo,
      fields: {
        checkoutFieldType: "promogate"
        buyData: buyData
      }
    }

  else if newState = "back" then
    JumpTo("checkout")

  else if newState = "go" then
    setLoading(true)

    apiConfirm = ghCallApi("PaywayConfirmLite", "CodigoPromo_Payway_Return", "CodigoPromo_Payway_ReturnError", false)
    apiConfirm.setFields({
      buylink: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
      pincode: ghGetChild(info, "pin", "")
    })
    apiConfirm.control = "run"

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else
    JumpTo("purchase", "fail")

  end if
end sub

sub CodigoPromo_Return(event)
  scr = event.getRoSGNode()

  m.logger.debug("CodigoPromo_return", { opcion: scr.value.opcion, data: scr.value.data })

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    PromoGateRun("back")

  else if scr.value.opcion = "SELECT" then
    PromoGateRun("go", { pin: scr.value.data })

  end if
end sub

sub CodigoPromo_Payway_Return(event)
  data = event.getData()

  m.logger.debug("CodigoPromo_Payway_return", { data: data })

  PromoGateRun("ok", { data: data })
end sub

sub CodigoPromo_Payway_ReturnError(event)
  data = event.getData()

  m.logger.error("CodigoPromo_Payway_returnError", { data: data })

  PromoGateRun("error", { data: data })
end sub