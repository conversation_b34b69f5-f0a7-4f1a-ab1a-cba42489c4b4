sub Init()
  ' m.top.debug = false
  if m.top.debug then print "GHPANEL> Init"
  m.background = m.top.findNode("background")
  m.panL = m.top.findNode("leftPanel")
  m.panR = m.top.findNode("rightPanel")
  if m.top.debug then print "GHPANEL> Init -- m =", m.top.width, m.top.height
  if m.top.debug then print "GHPANEL> Init -- background =", m.background.width, m.background.height
end sub

' -------------------------------------
' EVENTS
' -------------------------------------
sub resize()
  if m.top.debug then print "GHPANEL> resize -- "
  ' backgroundResize()
  m.panL.setFields({
    layoutDirection: "vert"
    horizAlignment: "left"
    vertAlignment: "top"
    itemSpacings: m.top.padItem
    translation: [m.top.padX + m.top.offsetX, m.top.padY]
  })
  m.panR.setFields({
    layoutDirection: "vert"
    horizAlignment: "right"
    vertAlignment: "top"
    itemSpacings: m.top.padItem
    translation: [m.top.width - m.top.padX, m.top.padY]
  })
end sub

' sub backgroundResize()
'   m.background.translation = [m.top.padX + m.top.offsetX, m.top.padY]
'   m.background.width = m.top.width - m.top.offsetX - (m.top.padX * 2)
'   m.background.height = m.top.height - (m.top.padY * 2)
'   if m.top.debug then
'     print  "backgroundResize ** "
'     print  "backgroundResize -- H";m.background.width" = ";m.top.width;" - ";m.top.offsetX;" - (";m.top.padX;" * 2)"
'     print  "backgroundResize -- W";m.background.height" = ";m.top.height;" - (";m.top.padY;" * 2)"
'   end if
' end sub