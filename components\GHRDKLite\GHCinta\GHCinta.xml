<?xml version="1.0" encoding="utf-8" ?>

<component name="GHCinta" extends="Group" >

  <script type="text/brightscript" uri="GHCinta.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="selected" type="string" />

    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
  </interface>

  <children>
    <Rectangle id="background" color="#0000ff" translation="[0,0]" width="1280" height="60"/>

    <Label id="titleText" text="ejemplo de texto" focusable="false" color="0xFFFFFF" translation="[20,0]" wrap="true" vertAlign="center" horizAlign="left" width="1280" height="72" />

    <GHButtonGroup id="botonera" layout="childs" orientation="horizontal" exitDown="true" >
      <GHButton id="ok" value="ok" text="Aceptar" width="200" height="50" translation="[500,0]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#981C15" focusColor="0xFFFFFF" selBackColor="#981C15" />
      <GHButton id="cancel" value="inicio" text="CANCELAR" width="200" height="50" translation="[750,0]" color="0xFFFFFF" selColor="0xFFFFFF" backcolor="#2E303D" focusColor="0xFFFFFF" selBackColor="#2E303D" />
    </GHButtonGroup>
  </children>

</component>
