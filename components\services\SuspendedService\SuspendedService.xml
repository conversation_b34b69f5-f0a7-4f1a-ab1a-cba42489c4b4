<?xml version="1.0" encoding="utf-8" ?>

<component name="SuspendedService" extends="Group">

  <script type="text/brightscript" uri="SuspendedService.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <!-- <field id="event" type="assocarray" alwaysNotify="true" onChange="onEvent" /> -->
    <field id="tick" type="boolean" alwaysNotify="true" onChange="onTick" />
    <field id="debug" type="boolean" value="false" onChange="onDebugTurn"/>
  </interface>

  <children>
    <Group id="display" visible="false">
      <Rectangle id="background"/>
      <InfoPane id="panel"/>
    </Group>

    <Group id="pantalla" visible="false">
      <Rectangle id="pantBackground"/>
      <Poster id="logoclaro"/>
      <Label id="title"/>
      <Label id="description" wrap="true" lineSpacing="0"/>
      <label id="text"/>
      <label id="textDos"/>
      <label id="textTres"/>
    </Group>

  </children>

</component>