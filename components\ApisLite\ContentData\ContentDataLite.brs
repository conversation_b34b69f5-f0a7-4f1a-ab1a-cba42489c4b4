' ContentDataLite
' https://app.swaggerhub.com/apis-docs/ClaroVideo/ContentData/1.0.1#/Content/get_services_content_v1_data

sub DataInit()
  m.top.debug = true
  m.api.url = m.config.mfwk.host + "/services/content/v1/data"

  m.api.query.delete("api_version") ' sin api version
  m.api.query.delete("HKS") ' sin hks
  m.api.query.delete("user_id") ' sin user_id
  ' m.api.headers.Append({ "Authorization": "Bearer " + ghGetRegistry("user_token", "user") })

  m.api.query.Append({
    ' "appversion": ghGetAppVersion(),
    ' "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
    "region": ghGetRegistry("region"),
    "group_id": m.top.group_id,
  })

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if raw <> invalid then m.logger.debug("ProcessData -- raw(50)= ", { raw: Left(raw, 50) })
  m.logger.debug("ProcessData -- neterror= ", { error: res?.neterror })

  response = ghGetChild(res, "response")

  if m.top.debug then
    print ghLogHead();"ProcessData -- body = ";res
    print ghLogHead();"ProcessData -- response = ";response
  end if

  errors = ghGetChild(res, "errors")
  if errors <> invalid then
    res.raw = raw
    m.top.error = res
    return
  end if

  m.top.content = response
end sub