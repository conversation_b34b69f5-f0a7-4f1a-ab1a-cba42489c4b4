<?xml version="1.0" encoding="UTF-8"?>

<component name="GetMedia2" extends="GHApiCall">
  <script type="text/brightscript" uri="GetMedia2.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/WideVineUtils.brs" />
  <!-- <script type="text/brightscript" uri="Request.brs" /> -->

  <interface>
    <!-- interfaz de entrada -->
    <field id="groupId" type="string" value="" />
    <field id="contentId" type="string" value="" />
    <field id="paywayToken" type="string" value="" />
    <field id="streamType" type="string" value="smooth_streaming_ma" />
    <field id="preview" type="string" value="0" />
    <field id="preferred_audio" type="string" value="" />
    <field id="preferred_subtitle" type="string" value="" />
  </interface>
</component>
