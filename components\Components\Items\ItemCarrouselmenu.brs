sub Init()
  if m.top.debug then print ghLogHead();"Init"

  m.imgFondo = m.top.findNode("icon")
  m.foco = m.top.findNode("foco")

  m.label = m.top.findNode("label")
  m.label.font = ghGetFont(21, "regular")

  m.background = m.top.findNode("background")

  m.offsetHeight = 8 ' cantidad que muevo hacia abajo para centrar el icono con respecto a los textos
end sub

sub itemContentChanged()
  ' para cuando se sobreescribe, entonces borro el text anterior
  m.label.setFields({ text: "" })
  ' para cuando se sobreescribe, entonces oculto imagen anterior
  m.imgFondo.setFields({ visible: false })

  m.background.setFields({ color: "#28292F", width: m.top.width })
  m.width = m.top.width

  selected = ghGetChild(m.top.itemContent, "selected", false)
  m.foco.setFields({ visible: false })

  if ghGetChild(m.top.itemContent, "data.optLabel.component") = "GHOptionImg" then
    ' data = ghGetChild(m.top.itemContent, "data")

    props = m.top.itemContent.data.optLabel.props
    m.imgFondo.setFields(m.top.itemContent.data.optLabel.props)
    m.imgFondo.setFields({
      visible: true,
      uri: props.imageURI,
      blendColor: "0xFFFFFF50"

      loadingBitmapUri: ghGetImageByMode("avatar.png") ' mientras se carga
      failedBitmapUri: ghGetImageByMode("avatar.png") ' si no tengo imagen
      width: props.imageWidth
      height: props.imageHeight,
      translation: [
        getImageHorizTranslation(props.imagehorizalign, m.top.width, props.imageWidth),
        getImageVertTranslation(props.imagevertalign, m.top.height, props.imageHeight) + m.offsetHeight,
      ]
    })

    if selected then
      itemImgInactivoAndSelected()
    end if
  else
    m.label.setFields({ text: ghGetChild(m.top.itemContent, "TITLE", ""), color: "0xFFFFFF50" })

    if selected then
      itemTextInactivoAndSelected()
    end if
  end if

end sub

sub showfocus()
  selected = ghGetChild(m.top.itemContent, "selected", false)

  m.foco.setFields({ visible: false })
  m.imgFondo.setFields({ visible: false })

  ' al hacer suma o resta del width, modifica el m.foco.width, corrijo el tamaño
  if m.foco.width <> m.width then
    m.foco.width = m.width
  end if

  if m.top.rowListHasFocus = true then
    ' el menu tiene el foco

    if ghGetChild(m.top.itemContent, "data.optLabel.component") = "GHOptionImg" then
      m.imgFondo.setFields({ visible: true })

      if m.top.focusPercent = 1 then
        itemImgActivo()
      else
        m.imgFondo.setFields({ blendColor: "0xFFFFFF50" })

        if selected then
          itemImgInactivoAndSelected()
        end if
      end if
    else

      if m.top.focusPercent = 1 then
        itemTextActivo()
      else
        m.label.setFields({ color: "0xFFFFFF50" })
        m.label.font = ghGetFont(21, "regular")
        if selected then
          itemTextInactivoAndSelected()
        end if
      end if
    end if
  else
    ' el menu no tiene el foco

    if ghGetChild(m.top.itemContent, "data.optLabel.component") = "GHOptionImg" then

      m.imgFondo.setFields({ visible: true, blendColor: "0xFFFFFF50" })

      if selected then
        itemImgInactivoAndSelected()
      end if
    else

      m.label.setFields({ color: "0xFFFFFF50" })

      if selected then
        itemTextInactivoAndSelected()
      end if
    end if
  end if

end sub

sub itemImgActivo()
  m.imgFondo.setFields({ blendColor: "#FFFFFF" })
  m.foco.setFields({ visible: true, height: 54, width: m.foco.width + 32, blendColor: "#DE1717", translation: "[-16,0]", uri: ghGetImageByMode("pildoraLupaA.9.png") })
end sub

sub itemImgInactivoAndSelected()
  m.imgFondo.setFields({ blendColor: "#FFFFFF" })
  m.foco.setFields({ visible: true, height: 40, width: m.foco.width, blendColor: "#9B0F0F", translation: "[0,5]", uri: ghGetImageByMode("pildoraLupaB.9.png") })
end sub

sub itemTextActivo()
  m.label.setFields({ color: "0xFFFFFF" })
  m.foco.setFields({ visible: true, height: 54, width: m.foco.width + 35, blendColor: "#DE1717", translation: "[-16,0]", uri: ghGetImageByMode("fondoDefinitivoA.9.png") })
  m.label.font = ghGetFont(21, "bold")
end sub

sub itemTextInactivoAndSelected()
  m.label.setFields({ color: "0xFFFFFF" })
  m.foco.setFields({ visible: true, height: 40, width: m.foco.width + 15, blendColor: "#9B0F0F", translation: "[-7,6]", uri: ghGetImageByMode("fondoDefinitivoB.9.png") })
  m.label.font = ghGetFont(21, "regular")
end sub

function getImageHorizTranslation(align = "left", width = 100, imgWidth = 50)
  if align = "left" then
    return 0
  else if align = "center" then
    return (width - imgWidth) / 2
  else if align = "right" then
    return width - imgWidth
  else
    return 0
  end if
end function

function getImageVertTranslation(align, height = 100, imgHeight = 50)
  if align = "top" then
    return 0
  else if align = "center" then
    return (height - imgHeight) / 2
  else if align = "bottom" then
    return height - imgHeight
  else
    return 0
  end if
end function