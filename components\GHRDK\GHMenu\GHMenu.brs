' -----------------------------
' GHMenu
' -----------------------------
function init()
  m.top.debug = false
  m.valueSelected = invalid
  m.valueFocused = invalid
end function

' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = true
  if press then
    if key = "back" then ' apreto el back?
      m.top.backSelected = true

      if m.top.debug then print ghLogHead();"onKeyEvent -- Salgo por BACK >>"
      return true ' forzado, sacame de aca.
    end if

    getChilds()
    focusTo = guessFocusTo(key)
    if focusTo <> invalid then
      if m.top.debug then print ghLogHead();"onKeyEvent -- foco interno key=";key
      handleFocus(focusTo)
    else
      if isValidExit(key) then
        if m.top.debug then print ghLogHead();"onKeyEvent -- foco invalido! exiting by ";key
        handled = false
      else
        if m.top.debug then print ghLogHead();"onKeyEvent -- foco invalido! sin bubbling... key=";key
        handled = true
      end if
    end if
  end if
  return handled
end function
function guessFocusTo(direction)
  focusTo = invalid
  ' en cual estoy?
  selectedChild = getChildIndexById(m.valueFocused)

  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- selectedChild ";selectedChild

  ' orientacion
  if m.top.orientation = "vertical" then
    dPrev = "up"
    dNext = "down"
  else if m.top.orientation = "horizontal" then
    dPrev = "left"
    dNext = "right"
  end if

  ' a cual voy?
  newChild = invalid
  if direction = dPrev then
    newChild = selectedChild - 1
  else if direction = dNext then
    newChild = selectedChild + 1
  end if

  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- newChild ";newChild

  if newChild <> invalid then
    if newChild > -1 and newChild < m.childs.Count() then ' si me pase me quedo
      focusTo = m.childs[newChild]
    end if
  end if

  if m.top.debug then print ghLogHead();"guessFocusToByChilds -- focusTo ";focusTo

  return focusTo
end function
function isValidExit(key)
  valid = false

  if key = "up" then
    if m.top.exitUp then valid = true
  else if key = "down" then
    if m.top.exitDown then valid = true
  else if key = "left" then
    if m.top.exitLeft then valid = true
  else if key = "right" then
    if m.top.exitRight then valid = true
  end if

  if m.top.debug then print ghLogHead();"isValidExit -- ";valid

  return valid
end function

' CHILDS
' -----------------------------
sub getChilds()
  m.childs = [] ' traigo todos

  for i = 0 to m.top.getChildCount() - 1
    m.childs.push(m.top.getChild(i).id)
  end for

  if m.top.debug then print ghLogHead();"getChilds -- childs ";m.childs
end sub
function getChildIndexById(id) as integer
  child = invalid

  for i = 0 to m.childs.Count() - 1
    if m.childs[i] = id then child = i
  end for

  if m.top.debug then print ghLogHead();"getChildIndexById -- id ";id;" is ";child

  return child
end function
sub handleSelected(id)
  if id <> invalid then
    idNode = m.top.findNode(id)
    if idNode = invalid then
      cant = m.top.getChildCount()
      nodo = invalid
      for i = 0 to cant - 1
        nodo = m.top.getChild(i)
        if nodo.id <> "search" then
          exit for
        end if
      end for
      idNode = m.top.findNode(nodo.id)
    end if
    if idNode <> invalid then
      if m.top.debug then print ghLogHead();"handleSelected id ";id
      ' apago seleccion anterior
      if m.valueSelected <> invalid then
        idNodePrev = m.top.findNode(m.valueSelected)
        idNodePrev.focus = false
        if idNodePrev.hasField("menuFocused") then idNodePrev.menuFocused = false
        if idNodePrev.hasField("menuSelected") then idNodePrev.menuSelected = false
      end if

      idNode.focus = false
      if idNode.hasField("menuFocused") then idNode.menuFocused = false
      if idNode.hasField("menuSelected") then idNode.menuSelected = true

      m.valueFocused = id
      m.valueSelected = id
    end if
  end if
end sub
sub handleFocus(id)
  if id <> invalid then
    idNode = m.top.findNode(id)
    if idNode <> invalid then
      if m.top.debug then print ghLogHead();"handleFocused id ";id

      ' apago seleccion anterior
      idNodePrev = m.top.findNode(m.valueFocused)
      if m.valueFocused <> id then
        idNodePrev.focus = false
      end if

      if idNode.hasField("menuFocused") then idNode.menuFocused = true
      idNode.focus = true

      m.valueFocused = id
    end if
  end if
end sub
sub handleFocusOut()
  if m.top.debug then print ghLogHead();"handleFocusOut"

  ' apago ultimo foco
  if m.valueFocused <> invalid then
    idNodePrev = m.top.findNode(m.valueFocused)
    idNodePrev.focus = false

    idNodeSel = m.top.findNode(m.valueSelected)
    if idNodeSel.hasField("menuFocused") then idNodeSel.menuFocused = false
    if idNodeSel.hasField("menuSelected") then idNodeSel.menuSelected = true

    m.valueFocused = m.valueSelected
  end if
end sub

'
' FIELDS
' -----------------------------
sub updateFieldFocus(event)
  if event.getData() then
    handleFocus(m.top.selected)

    getChilds()
    for i = 0 to m.childs.Count() - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
      child.ObserveField("selected", "onChildSelected")
    end for
  else
    handleFocusOut()

    for i = 0 to m.childs.Count() - 1
      child = m.top.getChild(i)
      child.UnobserveField("selected")
    end for
  end if
end sub
sub onChildSelected(event)
  child = event.getRoSGNode()

  ' apago el focos asi no queda seleccionado
  handleFocusOut()

  m.top.value = child.value
end sub
sub updateSelected()
  if m.top.selected = invalid or m.top.selected = "" then
    m.top.selected = m.top.getChild(0).id
  end if

  if m.top.selected <> m.valueSelected then
    handleSelected(m.top.selected)
  end if
end sub