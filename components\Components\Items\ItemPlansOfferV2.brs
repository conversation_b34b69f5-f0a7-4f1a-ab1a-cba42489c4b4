sub Init()
  m.logger = CreateLogger()
  m.top.debug = false

  ' componentes
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.progress = m.top.findNode("progress")
  m.infoCarrusel = m.top.findNode("infoCarrusel")
  m.logoAddon = m.top.findNode("logoAddon")
  m.price = m.top.findNode("price")
  m.currency = m.top.findNode("currency")
  m.iva = m.top.findNode("iva")
  m.rectangulo = m.top.findNode("rectangulo")
  m.boton = m.top.findNode("boton")
  m.suscInfo1 = m.top.findNode("suscInfo1")
  m.suscInfo2 = m.top.findNode("suscInfo2")

  m.price.font = ghGetFont(17, "bold")
  m.currency.font = ghGetFont(10, "regular")
  m.iva.font = ghGetFont(12, "regular")
  m.suscInfo1.font = ghGetFont(12, "regular")
  m.suscInfo2.font = ghGetFont(12, "regular")
  m.boton.font = ghGetFont(11, "bold")

end sub

sub itemContentChanged(event) ' event
  data = event.getData()
  data = ghGetChild(data, "data")

  m.logger.debug("item planOfferV2", { data: data })

  m.itemPoster.translation = [0, 0]
  m.itemPoster.width = 165
  m.itemPoster.height = 394

  background = ghGetChild(data, "assets.background")
  if background <> invalid then
    m.itemPoster.uri = background["165x394"]
  end if

  logo = ghGetChild(data, "assets.logo")
  if logo <> invalid then
    m.logoAddon.uri = logo["165x40"]
  end if

  translation = ghGetChild(data, "translation")
  if translation <> invalid then
    m.price.text = ghGetChild(data, "price.currency", "") + ghGetChild(data, "price.amount", "")
    m.currency.text = translation["labelTemporality"]
    m.iva.text = translation["labelTaxes"]

    txtPromo = ghGetChild(translation, "txtPromo.texts")
    if txtPromo <> invalid then
      m.suscInfo1.text = txtPromo.text1
      m.suscInfo2.text = txtPromo.text2
    end if

    btnSuscription = ghGetChild(translation, "btnSuscription")
    if btnSuscription <> invalid then
      btnSuscriptionText = btnSuscription.texts
      m.rectangulo.blendColor = btnSuscription.fgColor
      m.boton.text = btnSuscriptionText.text1 '"SUSCRIBETE" + chr(10) + "MENSUAL"
      m.boton.color = btnSuscriptionText.color1
      m.boton.visible = true
    end if
  end if

end sub

sub showfocus()
  data = ghGetChild(m.top.itemContent, "data")

  if data <> invalid then
    if data.rowtype = "PlansOfferV2" then
      if m.top.rowHasFocus = true then
        if m.top.itemHasFocus = 1
          m.title.setFields({
            color: "0xFFFFFF"
            width: 185
          })
          m.infoCarrusel.visible = true
        else
          m.title.setFields({
            color: "0xFFFFFF50"
            width: m.itemPoster.width - 20
          })
          m.infoCarrusel.visible = false
        end if
      else
        m.title.setFields({
          color: "0xFFFFFF50"
          width: m.itemPoster.width - 20
        })
        m.infoCarrusel.visible = false
      end if
    end if
  end if
end sub
