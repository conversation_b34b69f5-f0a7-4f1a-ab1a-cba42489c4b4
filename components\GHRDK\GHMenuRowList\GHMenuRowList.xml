<?xml version="1.0" encoding="utf-8" ?>
<component name="GHMenuRowList" extends="Group">

  <script type="text/brightscript" uri="GHMenuRowList.brs"/>
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Utils.brs" />
  <script type="text/brightscript" uri="pkg:/source/GHRDK/Fonts.brs" />

  <interface>
    <field id="data" type="array" onChange="changeContent" />
    <field id="profile_image" type="string" onChange="changeProfile" />
    <field id="backSelected" alwaysNotify="true" type="boolean" value="false" />
    <field id="value" type="assocarray" alwaysNotify="true" value="" />
    <field id="openProfile" type="boolean" value="" alwaysNotify="true" />

    <!-- interfaz interna -->
    <field id="focus" type="boolean" value="false" onChange="updateFieldFocus" alwaysNotify="true" />
    <field id="debug" type="boolean" value="false" />
  </interface>

  <children >
    <Label id= "label" translation="[5,10]" text="123" width="0" height="50" color="#FFFFFF" visible="false"/>

    <Poster id="logo" translation="[22,20]" width="123" height="24" loadDisplayMode="scaleToFit" visible="true"/>
    <Poster id="menuBack" translation="[180,11.5]" width="900" height="48" visible="true" />

    <GHRowList 
      visible="true"
      id="lista"
      itemComponentName="ItemCarrouselmenu"
      itemSize="[35,35]"
      numRows="1"
      rowItemSize="[[100,35]]"
      rowHeights="[65,35]"
      rowItemSpacing="[[28,20],[28,20]]"
      focusXOffset="[20,0]"
      rowLabelOffset="[[0,0]]"
      showRowLabel="[false]"
      showRowCounter="[false]"
      variableWidthItems="[true]"
      rowFocusAnimationStyle="floatingFocus"
      drawFocusFeedback="false"
      translation="[190,10]"
    />

    <GHOptionImg id="profile" value="perfil" width= "100" height= "55" color="#FFFFFF" selColor="#FFFFFF" />
  </children >

</component>
