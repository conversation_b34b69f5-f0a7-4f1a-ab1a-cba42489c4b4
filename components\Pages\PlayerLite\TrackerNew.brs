sub trackInit(urls = {}, retries = 3, interval = 3, hasEndCard = true)
  m.logger.debug("TrackInit")

  m.trk = {
    urls: urls
    retries: retries, ' TODO -- cuantas veces reintento el mensaje?
    interval: interval, ' cada cuanto reporto el tick?
    hasEndCardPrepare: false,
    hasEndCard: hasEndCard, ' seamos optimistas
    inTick: false, ' ya estoy mandando uno?
    lastState: "", ' para compara con el estado anterior
    lastTick: 0, ' ultimo tick que mande
    oi: {
      enable: ghGetChild(m.global, "timers.skipIntro.enable", true)
      shown: false
      jumpTo: invalid
    },
    inRolling: false, ' estoy en los rolling credits?
    has_view: false, ' ya mande el view?
    pausePos: 0, ' posision al momento de pausa, para detectar seek
  }

  ' al poner en pausa envio tick cada x tiempo
  m.Timer = CreateObject("roSGNode", "Timer")
  m.Timer.duration = 15
  m.Timer.repeat = true
  m.Timer.ObserveField("fire", "trackTriggerTick")

  ' ** YOUBORA * trackInit ********************
  ' print ghLogHead("YB");">> trackInit -- "
  ' m.global.YB_Plugin = m.global.YB_Plugin
  ' ** YOUBORA * trackInit ********************
end sub

sub trackTriggerTick()
  m.logger.debug("trackTriggerTick en pausa")

  trackEvent("tick")
end sub

sub trackEvent(eventType as string)
  mode = "async"
  position = invalid
  if eventType = "stop" then
    mode = "sync"
  end if
  if eventType = "view" then
    position = 0
  end if

  m.Timer.control = "stop"

  if eventType = "tick" then
    if Abs(ghGetChild(m.video, "position", 0) - ghGetChild(m.trk, "lastTick", 0)) < ghGetChild(m.trk, "interval", 0) or m.trk.inTick then
      return ' Salir si el intervalo no se cumple o si ya está en proceso
    end if
    m.trk.inTick = true
  else if eventType = "pause" then
    m.Timer.control = "start"
    m.trk.pausePos = m.video.position
  else if eventType = "resume" then
    if m.video.position <> m.trk.pausePos then
      m.logger.debug("trackEvent (resume) -- Seek detected", { pos: m.trk.pausePos, position: m.video.position })
      trackSendToApi("seek", mode, position)
    end if
  else if eventType = "stop" then
    m.trk.has_view = false
    youboraStop()
  else if eventType = "view" then
    if m.trk.has_view = true
      return
    end if

    m.trk.has_view = true
    youboraStart()

  else if eventType = "completion" or eventType = "qualitychange" or eventType = "seek" or eventType = "error" or eventType = "credits" or eventType = "dubsubchange" then
    ' no hago nada con estos eventos pero dejo que se manden
  else
    m.logger.debug("trackEvent Evento desconocido")
    return
  end if

  m.logger.debug("trackEvent -- ", { event: eventType })
  trackSendToApi(eventType, mode, position)
end sub

' API
sub trackSendToApi(message, mode = "async", position = invalid)
  m.logger.debug("trackSendToApi -- ", { message: message })

  api = CreateObject("roSGNode", "Track")
  api.mode = mode
  api.message = message
  url = m.trk.urls[message]

  if position = invalid then
    position = getTimeCode(message)
  end if

  if position <> invalid and position >= 0 then
    if url <> invalid then
      api.url = url
      api.timecode = position
      if message = "dubsubchange" then
        api.preferred_audio = ghGetChild(m.languageSelect, "audio", "")
        api.preferred_subtitle = ghGetChild(m.languageSelect, "subtitle", "")
      end if
      api.ObserveField("content", "onGenericTrackOk")
      api.ObserveField("error", "onGenericTrackError")
      api.control = "run"
    else
      m.logger.debug("trackSendToApi Error -- ", { message: message })
    end if
  end if
end sub

function getTimeCode(message)
  timecode = ghGetChild(m.video, "position", 0)

  ' siempre el completion
  if message = "completion" then
    m.logger.debug("getTimeCode ", { message: message, duration: m.video.duration })
    timecode = m.video.duration
  end if
  ' stop mas alla de la endcard
  if m.video <> invalid then
    if message = "stop" then
      actual = m.video.position
      final = m.video.duration
      ' rolling = Val(ghGetChild(m.video, "content.rollingcreditstime", 0)) 'viene en negativo -- viene en string
      rolling = ghGetChild(m.video, "content.rollingcreditstime", "").toInt() 'viene en negativo -- viene en string
      threshold = final + rolling
      if final > 0 and actual >= threshold then
        timecode = final
        m.logger.debug("getTimeCode ", { message: message, duration: m.video.duration })
      else
        if actual = 0 then ' no tengo el dato, esta buffereando
          actual = ghGetChild(m.top.content, "BOOKMARKPOSITION", 0)
          m.logger.debug("getTimeCode -- actual en 0, tomo el del top -- me quedo ", { actual: actual })
        end if
        timecode = actual
        m.logger.debug("getTimeCode -- ", { message: message, position: timecode.toStr() })
      end if
    end if
  end if
  return timecode
end function

sub onGenericTrackOk(event)
  data = event.getData()

  m.logger.debug("onGenericTrackOk ", { message: data.trkMessage, duration: ghGetChild(m.video, "duration", "--") })

  if data.trkMessage = "tick" then
    m.trk.inTick = false
    printBookmark(data)
  else if data.trkMessage = "view" then
    m.logger.debug("sucess=" + ghGetChild(data, "response.success", ""))
  else if data.trkMessage = "pause" then
    m.logger.debug("pos=" + ghGetChild(data, "response.bookmark.tc_last", ""))
    m.logger.debug("max=" + ghGetChild(data, "response.bookmark.tc_max", ""))
  else if data.trkMessage = "resume" then
    printBookmark(data)
  else if data.trkMessage = "stop" then
    printBookmark(data)
  else if data.trkMessage = "qualitychange" then
    printBookmark(data)
  else if data.trkMessage = "dubsubchange" then
    printBookmark(data)
  else if data.trkMessage = "completion" then
    printBookmark(data)
  else if data.trkMessage = "seek" then
    printBookmark(data)
  else if data.trkMessage = "credits" then
    printBookmark(data)
  else
    m.logger.debug("onGenericTrackOk sin mensaje valido", { data: ghGetChild(data, "response") })
  end if
  ' last tick
  tc_last = ghGetChild(data, "response.bookmark.tc_last") ' el ultimo anotado
  if tc_last <> invalid then m.trk.lastTick = tc_last
end sub

sub onGenericTrackError(event)
  data = event.getData()

  if data.trkMessage = "tick" then
    m.trk.inTick = false
  end if

  ' trato de avisar, pero un error de un error???
  if data.trkMessage <> "error" then

    trackEvent("error")
    ' trackSendError("onGenericTrackOk", "onGenericTrackError")
  end if
  ' despues sigo, error de red?
  if data.error_msg <> invalid then
    if data.error_msg = "NETWORK ERROR"
      ' problema de red... ver que hacemos
      ' entiendo que inicialmente para el tracking no hacemos nada
    end if
  else
    ' mostrar algo de info
    ' m.logger.debug("onGenericTrackError data =" + data) '' LOG QUE FALLA JOSE
    if data.trkMessage <> invalid then
      m.logger.debug("[T] << onGenericTrackError ", { message: data.trkMessage, data: data })
      m.logger.debug("[T] << onGenericTrackError ", { message: data.trkMessage, status: ghGetChild(data, "status") })
      m.logger.debug("[T] << onGenericTrackError ", { message: data.trkMessage, entry: ghGetChild(data, "entry") })
      m.logger.debug("[T] << onGenericTrackError ", { message: data.trkMessage, response: ghGetChild(data, "response") })
    end if
  end if
end sub

' ------------------------
' UTILITIES
' ------------------------
sub printBookmark(data)
  m.logger.debug("pos=", { data: ghGetChild(data, "response.bookmark.tc_last", "") })
  m.logger.debug("max=", { data: ghGetChild(data, "response.bookmark.tc_max", "") })
  m.logger.debug("concurrentStreaming=", { data: ghGetChild(data, "response.concurrentStreaming.enabled", "") })
end sub

' ------------------------
' ------------------------
' YOUBORA
' ------------------------
sub youboraStart()
  ' m.global.YB_Plugin = m.global.YB_Plugin
  if m.global.YB_Plugin <> invalid then
    try
      m.logger.debug("youboraStart --")
      m.global.YB_Plugin.videoplayer = m.video
      ' m.logger.debug("youboraStart -- content", { content: m.video.content })
      ' m.logger.debug("youboraStart -- ybData", { ybData: m.video.content.ybData })
      userRegion = ghGetRegistry("region")
      accountCode = ghGetChild(m.global, "youbora." + userRegion + ".tracking.accountcode")
      ybOptions = {
        "accountCode": accountCode 'Change for you accountcode
        ' "user": "Gustavo_Ripoll_DevAccount" ' NO HACE FALTA (!)
        "username": ghGetRegistry("user_id", "user") ' FALTA Id Usuario (!)
        "expectAds": false
        ' "content.transactionCode": "transaction_id", ' inicialmente mx no lo manda.
        "content.isLive": false,
        "content.title": ghGetChild(m.video.content, "ybTitle", "***") '  -- viene de la getMedia
        "content.program": ghGetChild(m.video.content, "ybTitleEpisode", "***") '  -- viene de la getMedia
        "content.metadata": {
          "year": ghGetChild(m.video.content, "ybPublishyear", ""), ' TODO: response.group.common.extendedcommon.media.publishyear
          "genre": ghGetChild(m.video.content, "ybGenres", ""),
          "isLive": false,
          "rendition": ghGetChild(m.video.content, "ybRendition", "")
          ' Ver si viene en la getmedia .profile.hd.detail
          ' this.rendition = null;
          ' "price": "???" ' TODO: LO TENEMOS???? tvod vod? -- no se estaria usando
        },
        ' (perfiles-no implementado) PQT --
        ' se obtiene a través de la user/isloggedin -- no viene
        ' response.paywayProfile.paymentMethods[0]
        ' user_category: "HC_VIP"
        "extraparam.1": ghGetRegistry("paymentMethods_user_category", "user"), ' NO LLEGA (!) -- chequear si viene.
        ' Tipo Suscriptor Claro (todas las suscripciones del usuario)
        ' Si es VOD -- tomar de la getRegistry() -- user/isloggedin - pawayProfile.subscriptions.key
        ' Si es Canal -- payway/linealchannels.key
        "extraparam.2": _getPaywayKeys(),
        ' Tipo de abono del contenido de la visualización ???????????????????????
        ' Si es Canal -- payway/linealchannels.key
        ' Si es VOD -- payway/purchasebuttoninfo response.playButton.key (m.video.info.tipo_abono_contenido)
        "extraparam.3": ghGetChild(m.video, "info.tipo_abono_contenido", ""), ' NO LLEGA (!) -soloVOD- chequear si viene.
        ' Tipo de Contenido visualizado
        "extraparam.4": ghGetChild(m.video.content, "tipo_contenido_visualizado", ""),
        "extraparam.5": ghGetRegistry("email", "user"), ' Email del usuario
        ' étodo de pago de la visualización ???????????????????????
        ' Si es VOD -- player/getmedia -- response.group.common.extendedcommon.format.name
        ' Si es Canal -- player/getmedia -- response.group.common.extendedcommon.format.name
        "extraparam.6": ghGetChild(m.video.content, "metodo_pago_visualizacion", ""), ' ??????????????????
        "extraparam.7": "", ' OK! Id_usuario_paga -- vacio
        "extraparam.8": "", ' OK! Id_usuario_hijo -- vacio
        "extraparam.9": ghGetRegistry("region"), ' Región
        "extraparam.10": ghGetChild(m.video.content, "is_trailer", ""), ' Meter en video.content
        "extraparam.11": ghGetDeviceId(), ' OK -- Device ID Se debe enviar el device id que se utiliza para los reportes de BI.
        "extraparam.12": ghGetFirmwareVersion(), ' FIRMWARE -- FW Se debe enviar la versión de FW utilizada en el device. Solo se envía en los devices que tienen FW
        "extraparam.13": ghGetAppVersion(), ' APK Se debe enviar la versión de APK utilizada en el device. Solo se envía en los devices que manejan APK
        "extraparam.14": ghGetAppVersion(), ' APP VERSION Se debe enviar la versión de APP_VERSION utilizada en el device. Solo se envía en los devices que manejan APP_VERSION
        "extraparam.15": "", ' ghGetChild(m.global, "epg.version", ""), ' EPG VERSION Se debe enviar la versión de EPG_VERSION utilizada en el device. Solo se envía en los devices que manejan EPG_VERSION epg/version: epg_version
        "extraparam.16": ghGetRegistry("subregion", "user"), ' NO SE USA -- SUB-REGION Se debe enviar la subregion que tiene asignado el usuario. Solo se envía en los devices que manejan subregion user/isloggedin: subregion
        "extraparam.17": "", ' -- no se usa
        "extraparam.18": "", ' -- no se usa
        "extraparam.19": "", ' -- no se usa
        "extraparam.20": "" ' -- no se usa
        ' "content.title": "Batman",   ' "content.rendition": "4.2Mbps",
        ' "content.cdn": "AKAMAI",   ' "network.ip": "***********",
        ' "network.isp": "Verizon"   ' "device.code" : "DEVICE_ID"
        ' "content.resource": "http://example.com/batman_movie.m3u8",   ' "content.duration": 4000,
      }
      m.global.YB_Plugin.options = ybOptions
      m.global.YB_Plugin.event = { handler: "init" }
      ' m.logger.debug("youboraStart -- YB.options=", { options: m.global.YB_Plugin.options]} )
      ' m.logger.debug("youboraStart -- YB.options[content.metadata]=", { options: m.global.YB_Plugin.options["content.metadata"]]} )
    catch err
      m.logger.debug("youboraStart -- ERROR > ", { error: err })
    end try
  else
    m.logger.debug("youboraStart -- DISBLED")
  end if
end sub

sub youboraStop()
  if m.global.YB_Plugin <> invalid then
    try
      m.logger.debug("youboraStop --")
      m.global.YB_Plugin.event = { handler: "stop" }
      m.global.YB_Plugin.taskState = "stop"
    catch err
      m.logger.debug("youboraStop --ERROR > ", { error: err })
    end try
  else
    m.logger.debug("youboraStop --DISBLED.")
  end if
end sub

function _getPaywayKeys()
  m.logger.debug("_getPaywayKeys --")
  tSubcriptions = ""
  tmpSubscriptions = ghGetChild(m.global, "paywayProfile.subscriptions", [])
  if tmpSubscriptions.Count() > 0 then
    for tmpS = 0 to tmpSubscriptions.Count() - 1
      tSubcriptions += tmpSubscriptions[tmpS].key + "-"
    end for
    if tSubcriptions <> "" then
      tSubcriptions = left(tSubcriptions, Len(tSubcriptions) - 1)
    end if
  end if
  return tSubcriptions
end function