sub Init()
  m.itemPoster = m.top.findNode("itemPoster")
  m.title = m.top.findNode("title")
  m.itemMask = m.top.findNode("itemMask")
end sub

sub itemContentChanged()
  data = ghGetChild(m.top.itemContent, "data")

  if ghGetDisplayMode() = "FHD" then
    ejeX = 2
    ejeY = 2
    ' mask, porque las imagenes no son redondas
    m.itemMask.setFields({
      translation: [ejeX, ejeY]
      width: 210
      height: 285
      uri: ghGetImageByMode("4px_RoundMask.png")
      visible: true
    })
  else
    ejeX = -1.5
    ejeY = 2.5
    ' mask, porque las imagenes no son redondas
    m.itemMask.setFields({
      translation: [ejeX, ejeY]
      width: 210
      height: 292
      uri: ghGetImageByMode("4px_RoundMask.png")
      visible: true
    })

  end if

  ' cuando tiene pocos items, se corre la imagen
  if ghGetChild(data, "cantTotal", 50) <= 5 then
    ' print "esto trae cant total"
    ' print ghGetChild (data, "cantTotal")
    ' print "esto trae cant total"
    if ghGetDisplayMode() = "FHD" then
      ejeX = -20
      ejeY = 2
      ' mask, porque las imagenes no son redondas
      m.itemMask.setFields({
        translation: [ejeX, ejeY]
        width: 210
        height: 285
        uri: ghGetImageByMode("4px_RoundMask.png")
        visible: true
      })
    else
      ejeX = -16.5
      ejeY = 2
      ' mask, porque las imagenes no son redondas
      m.itemMask.setFields({
        translation: [ejeX, ejeY]
        width: 210
        height: 292
        uri: ghGetImageByMode("4px_RoundMask.png")
        visible: true
      })
    end if
  end if

  imagen = ghGetChild(data, "image")
  if imagen = invalid or imagen = "" then
    imagen = ghGetImageByMode("TalentoDefault.png")
  else
    imagen = imagen + "?size=290x163"
  end if

  m.itemPoster.setFields({
    width: 210
    height: 210
    translation: [ejeX, ejeY]
    uri: imagen
  })

  m.title.setFields({
    width: m.itemPoster.width - 20
    ' height: m.itemPoster.height - 20
    text: getNames(data)
    font: ghGetFont(16, "medium")
    translation: [ejeX + 10, 210]
    color: "0xFFFFFF"
    wrap: "true"
    lineSpacing: "0"
    horizAlign: "center"
    vertAlign: "top"
    visible: true
  })
end sub

function getNames(data)
  fullName = ghGetChild(data, "first_name")
  if fullName <> invalid then
    fullName = fullName + " " + ghGetChild(data, "last_name", "")
  else
    fullName = ghGetChild(data, "name")
    if fullName <> invalid then
      fullName = fullName + " " + ghGetChild(data, "surname", "")
    else
      fullName = ghGetChild(data, "fullname", "")
      if fullName = invalid then
        fullName = "UnFullname"
      end if
    end if
  end if

  rolName = ghGetChild(data, "rolname")
  if rolName = invalid then
    rolName = "UnRolName"
  end if
  rolName = UCase(rolName)

  res = fullName + chr(10) + rolName
  return res
end function