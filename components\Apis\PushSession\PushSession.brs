sub DataInit()
  ' m.top.debug = true
  m.api.method = "POST"
  m.api.url = m.config.mfwk.host + "/services/user/push_session"
  m.api.query.Append({
    "appversion": ghGetAppVersion(),
    "region": ghGetRegistry("region"),
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })
  m.api.body = ghArray2Query({
    "user_session": ghGetRegistry("user_session", "user")
  }, "")

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.query
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";raw
    ' print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  m.top.content = res

  m.global.setFields({
    user: { email: ghGetChild(res, "response.usuario.storage.EMAIL") },
  })

  print ghLogHead();"saveToRegistry -- HKS,region,timezone,utc"
end sub