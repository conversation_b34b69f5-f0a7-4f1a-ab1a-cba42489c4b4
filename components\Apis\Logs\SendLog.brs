sub DataInit()
  m.top.debug = false
  desc = <PERSON><PERSON><PERSON><PERSON>(m.top.description)
  if GetInterface(desc, "ifAssociativeArray") = invalid then
    desc = m.top.description
  end if

  m.api.method = "POST"
  m.api.name = "logs"
  m.api.url = m.config.middleware.host + "/logs"
  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user"), ' para que no tenga cache ?
  })
  data = {
    type: m.top.type,
    description: desc,
    "user_token": ghGetRegistry("user_token", "user"),
    "authpn": m.config.mfwk.authpn,
    "authpt": m.config.mfwk.authpt,
    "device_type": m.config.mfwk.device_type,
    "device_model": m.config.mfwk.device_model,
    "device_manufacturer": m.config.mfwk.device_manufacturer,
    "device_category": m.config.mfwk.device_category,
    "format": m.config.mfwk.format,
    "HKS": ghGetRegistry("HKS"),
    "region": ghGetRegistry("region"),
    "appversion": ghGetAppVersion(),
    "user_id": ghGetRegistry("session_userhash", "user"),
  }
  m.api.body = FormatJSON(data)
  m.api.headers.addReplace("Content-Type", "application/json")

  if m.top.debug then
    print ghLogHead();"DataInit -- api="m.api
    print ghLogHead();"DataInit -- params="m.api.params
  end if
end sub

sub ProcessData(res, raw)
  if m.top.debug then
    print ghLogHead();"ProcessData -- Body= ";res
    print ghLogHead();"ProcessData -- raw= ";Left(raw, 20)
  end if
  if raw = "true" then
    m.top.content = { result: "OK" }
  else
    m.top.error = { result: "FAILED" }
  end if
end sub